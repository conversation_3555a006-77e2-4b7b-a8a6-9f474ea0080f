[tool.poetry]
name = "scoring-models"
version = "0.1.0"
description = "A repository containing all GRESB scoring models."
authors = ["GRESB Data Team"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = "^3.13"
numpy = "2.2.0"
pandas = "2.2.3"
s3fs = "2024.5.0"
botocore = "1.34.106"
boto3 = "1.34.106"
pytest = "^8.3.4"
sql = "2022.4.0"
mypy = "^1.9.0"
pre-commit-hooks = "^4.6.0"
pyarrow = "^18.1.0"
fastparquet = "^2024.2.0"
psycopg2-binary = "^2.9.9"
pandantic = "^0.3.0"
pytest-mock = "^3.14.0"
python-dotenv = "^1.0.0"
scipy="^1.14.0"
tqdm = "^4.66.5"
black = "^24.8.0"
autopep8 = "^2.3.1"
duckdb = "^1.1.0"
python-json-logger = "^2.0.7"
pandera= "^0.21.1"
multimethod = "^1.12" # this is required because multimethod 2.0 breaks stuff
data-io = { path = "shared/data-io", develop = true }
app-real-estate = { path = "contexts/app-real-estate", develop = true}
app-real-score-contribution = { path = "contexts/app-real-score-contribution", develop = true}


[tool.poetry.group.dev.dependencies]
pre-commit = "^3.6.2"
pytest = "^8.0.2"
black = "^24.2.0"


[tool.poetry.group.app-real-score-contribution.dependencies]
polars = "^1.23.0"
smart-open = "^7.1.0"
gresb_utils = { path = "shared/gresb-utils", develop = true }

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
