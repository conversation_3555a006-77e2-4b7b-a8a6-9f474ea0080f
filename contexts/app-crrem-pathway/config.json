{"s3_paths": {"input_bucket": "s3://gresb-{RUNTIME}-bronze-data/crrem_pathways/", "output_bucket": "s3://gresb-{RUNTIME}-gold-data/{BRANCH}/external/crrem_pathways/v2_04/"}, "output_folders": {"all_pathways": "all_pathways/", "global_pathways": "global_pathways/", "essential_pathways": "essential_pathways/", "north_america_pathways": "north_america_pathways/", "property_mappings": "property_mappings/", "subnational_mappings": "subnational_mappings/", "country_mappings": "country_mappings/", "zip_climate_mappings": "zip_climate_mappings/"}, "input_files": {"global_pathways": {"filename": "CRREM_Global_Pathways-V2.02_02-03-2023.xlsx", "version": "v2.04", "sheets": {"co2_pathways": {"name": "1 - 1.5C CO2", "description": "CO2 intensity pathways for global (non-US/Canada) countries", "location_type": "COUNTRY", "utility_suffix": "CO2-Int", "unit": "kgCO2/m²/yr"}, "kwh_pathways": {"name": "2 - 1.5 kWh", "description": "Energy intensity pathways for global (non-US/Canada) countries", "location_type": "COUNTRY", "utility_suffix": "kWh-Int", "unit": "kWh/m²/yr"}}}, "north_america_pathways": {"filename": "CRREM_North_America_Pathways-V2.04.xlsx", "version": "v2.04", "sheets": {"us_co2": {"name": "US - CO2 (sqft)", "description": "US CO2 intensity pathways by climate zone", "country": "US", "location_type": "CLIMATE_ZONE", "utility_suffix": "CO2-Int", "unit": "kgCO2/m²/yr"}, "us_kwh": {"name": "US - EUI (kWh)", "description": "US energy intensity pathways by climate zone", "country": "US", "location_type": "CLIMATE_ZONE", "utility_suffix": "kWh-Int", "unit": "kWh/m²/yr"}, "canada_co2": {"name": "CAN - CO2 (sqft)", "description": "Canada CO2 intensity pathways by province", "country": "CA", "location_type": "STATE_PROVINCE", "utility_suffix": "CO2-Int", "unit": "kgCO2/m²/yr"}, "canada_kwh": {"name": "CAN - EUI (kWh)", "description": "Canada energy intensity pathways by province", "country": "CA", "location_type": "STATE_PROVINCE", "utility_suffix": "kWh-Int", "unit": "kWh/m²/yr"}}}, "mapping_file": {"filename": "Property Types Mapping.xlsx", "sheets": {"property_types": {"name": "property_types", "description": "GRESB to CRREM property type mappings"}, "australia_mappings": {"name": "subnational_Aus", "description": "Australia subnational mappings"}, "country_mappings": {"name": "crrem_country_code_to_a2_code", "description": "CRREM country codes to GRESB country names and A2 codes"}}}, "zip_mapping_file": {"filename": "Zip_GEA_ClimateZone.xlsx", "description": "US zip code to climate zone mapping"}}, "processing": {"output_directory": "parquet_output", "target_degrees": 1.5, "validation": {"enabled": true, "reference_file": "s3://gresb-{RUNTIME}-data-scoring/master/rdd/external/crrem_pathways.rdd/CRREM_Global_Pathways-V2.02_02-03-2023.parquet", "description": "Compare new global pathways output with existing reference file to ensure consistency"}}, "output_files": {"global_pathways": {"filename_pattern": "crrem_global_pathways_{version}.parquet", "description": "Global (non-US/Canada) pathways by country and property type - same schema as North America"}, "north_america_pathways": {"filename_pattern": "crrem_north_america_pathways_{version}.parquet", "description": "US/Canada pathways by climate zone and property type - same schema as global"}, "property_mappings": {"filename": "crrem_property_mappings.parquet", "description": "GRESB to CRREM property type mappings"}, "australia_mappings": {"filename": "crrem_australia_mappings.parquet", "description": "Australia subnational mappings"}, "zip_climate_mapping": {"filename": "crrem_zip_climate_mapping.parquet", "description": "US zip code to climate zone mapping"}}, "output_schemas": {"essential": ["YEAR", "CRREM_LOCATION_CODE", "CRREM_PROP_TYPE_CODE", "INTENSITY", "UTILITY_CODE", "TARGET_DEGREES", "UNIT", "LOCATION_TYPE", "LOCATION_NAME", "COUNTRY_A2_CODE"], "expanded": ["YEAR", "CRREM_LOCATION_CODE", "CRREM_PROP_TYPE_CODE", "INTENSITY", "UTILITY_CODE", "TARGET_DEGREES", "UNIT", "LOCATION_TYPE", "LOCATION_NAME", "COUNTRY_A2_CODE", "GRESB_PROP_TYPE_NAME", "GRESB_PROP_TYPE_CODE"]}}