[tool.poetry]
name = "crrem-pathway-processor"
version = "0.1.0"
description = "CRREM pathway data processor using <PERSON><PERSON> and Parquet"
authors = ["GRESB Team"]
packages = [{include = "crrem_processor"}]

[tool.poetry.dependencies]
python = "^3.13"
polars = "^1.23.0"
pandas = "^2.0.0"
openpyxl = "^3.1.0"
xlsx2csv = "^0.8.0"
fastexcel = "^0.14.0"
gresb-utils = {path = "../../shared/gresb-utils", develop = true}
dotenv = "^0.9.9"

[tool.poetry.group.dev.dependencies]
black = "^23.0.0"
isort = "^5.12.0"
flake8 = "^6.0.0"
mypy = "^1.0.0"

[tool.poetry.group.test.dependencies]
pytest = "^7.0.0"
pytest-cov = "^4.0.0"
pytest-mock = "^3.10.0"

[tool.poetry.scripts]
process-crrem = "crrem_processor.main:main"
test-crrem = "run_tests:main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "-v",
    "--tb=short",
    "--color=yes",
    "--cov=crrem_processor",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov"
]

[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.mypy]
python_version = "3.13"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
