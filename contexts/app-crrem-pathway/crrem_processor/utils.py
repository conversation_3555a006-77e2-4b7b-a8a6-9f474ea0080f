"""
Utility functions for CRREM pathway processing
"""

import polars as pl
from pathlib import Path
from typing import Dict
import json
import logging
import os

from gresb_utils import (
    SQFT_TO_SQM,
    ENERGY_INTENSITY_UNIT_SQFT,
    GHG_INTENSITY_UNIT_SQFT,
)

logger = logging.getLogger(__name__)


def load_config(data_dir: Path) -> dict:
    """Load configuration from config.json and apply environment variable substitutions"""
    config_file = data_dir / "config.json"
    if not config_file.exists():
        raise FileNotFoundError(f"Configuration file not found: {config_file}")

    with open(config_file, "r") as f:
        config = json.load(f)

    # Apply environment variable substitutions
    config = _apply_env_substitutions(config)

    return config


def _apply_env_substitutions(config: dict) -> dict:
    """Apply environment variable substitutions to configuration values"""
    # Get runtime from environment, default to 'dev'
    runtime = os.environ.get("RUNTIME", "dev")

    # Convert config to string for substitution
    config_str = json.dumps(config)

    # Replace {RUNTIME} placeholders with actual runtime value
    config_str = config_str.replace("{RUNTIME}", runtime)

    # Parse back to dict
    return json.loads(config_str)


def load_mapping_data(
    mapping_file: Path,
    property_types_sheet: str,
    australia_sheet: str,
    country_mappings_sheet: str,
) -> Dict[str, pl.DataFrame]:
    """Load property type, country, and subnational mappings"""
    logger.info("Loading mapping data...")

    # Load property type mappings from the specified sheet
    property_types = pl.read_excel(mapping_file, sheet_name=property_types_sheet)

    # Load Australia subnational mappings from the specified sheet
    australia_mappings = pl.read_excel(mapping_file, sheet_name=australia_sheet)

    # Load country mappings from the specified sheet
    country_mappings = pl.read_excel(mapping_file, sheet_name=country_mappings_sheet)

    return {
        "property_types": property_types,
        "australia_mappings": australia_mappings,
        "country_mappings": country_mappings,
    }


def save_parquet_with_metadata(df: pl.DataFrame, filepath: Path) -> None:
    """Save DataFrame to parquet file with metadata"""
    # Create output directory if it doesn't exist
    filepath.parent.mkdir(parents=True, exist_ok=True)

    # Save DataFrame
    df.write_parquet(filepath)
    logger.info(f"Saved {len(df)} rows to {filepath}")


def sort_and_reorder_columns(df: pl.DataFrame) -> pl.DataFrame:
    """Sort CRREM pathways DataFrame by standard column order and row sorting for consistent output

    Works for both global pathways (country/subnational) and North America pathways (climate zones).
    Both include GRESB property type expansion.
    """
    # Standard column order: Unique identifiers, GRESB identifiers, values, metadata
    all_sorted_cols = [
        # Unique identifiers
        "TARGET_DEGREES",
        "UNIT",  # We provide both square meters and square feet
        "UTILITY_CODE",
        "CRREM_PROP_TYPE_CODE",
        "CRREM_LOCATION_CODE",
        # GRESB identifiers (not unique, but we repeat the pathways for each GRESB identifiers combination)
        "GRESB_PROP_TYPE_CODE",  # multiple GRESB_PROP_TYPE_CODE can map to the same CRREM_PROP_TYPE_CODE
        "YEAR",
        # values
        "INTENSITY",
        # Metadata columns, should be the same value for all the rows in a pathway (31 rows, 2020 - 2050)
        "LOCATION_TYPE",  # "COUNTRY", "SUBNATIONAL", "CLIMATE_ZONE", or "STATE_PROVINCE"
        "CRREM_PROP_TYPE_NAME",  # 1:1 mapping with CRREM_PROP_TYPE_CODE
        "GRESB_PROP_TYPE_NAME",  # 1:1 mapping with GRESB_PROP_TYPE_CODE
        "COUNTRY_A2_CODE",
        "LOCATION_NAME",  # Country name, city name, or climate zone name
    ]

    # Only select columns that exist in the DataFrame, make it work with essential dataframes which don't have all the columns.
    existing_cols = [col for col in all_sorted_cols if col in df.columns]

    # First reorder columns, then sort rows by all columns
    return df.select(existing_cols).sort(existing_cols)


def add_area_unit_column(df: pl.DataFrame) -> pl.DataFrame:
    """Add column called AREA_UNIT, can be M2 or SQFT to DataFrame.
    Check UNIT column if it contains m2 or sqft, and set AREA_UNIT accordingly."""
    if "AREA_UNIT" in df.columns:
        logger.warning("AREA_UNIT column already exists, not adding it again.")
    else:
        df = df.with_columns(
            pl.when(pl.col("UNIT").str.contains("m²"))
            .then(pl.lit("M2"))
            .when(pl.col("UNIT").str.contains("sqft"))
            .then(pl.lit("SQFT"))
            .otherwise(None)
            .alias("AREA_UNIT")
        )

    # Validate there is no null values in the AREA_UNIT column otherwise raise ValueError
    if df["AREA_UNIT"].is_null().any():
        raise ValueError(
            "AREA_UNIT column contains null values. Please check the UNIT column for inconsistencies."
        )
    return df


def convert_intensity_to_sqft(df: pl.DataFrame) -> pl.DataFrame:
    """Convert intensity from per m2 to per sqft and add UNIT column with appropriate unit"""
    return df.with_columns(
        [
            pl.col("INTENSITY") * SQFT_TO_SQM,
            pl.when(pl.col("UTILITY_CODE") == "en")
            .then(pl.lit(ENERGY_INTENSITY_UNIT_SQFT))
            .when(pl.col("UTILITY_CODE") == "ghg")
            .then(pl.lit(GHG_INTENSITY_UNIT_SQFT))
            .otherwise(None)
            .alias("UNIT"),
        ]
    )
