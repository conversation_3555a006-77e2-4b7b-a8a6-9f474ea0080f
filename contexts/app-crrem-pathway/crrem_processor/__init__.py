"""
CRREM Pathway Processor Package

A Python package for processing CRREM pathway data from Excel files to Parquet format.
"""

from crrem_processor.processor import CRREMPathwayProcessor
from crrem_processor.pathway_processing import (
    process_global_sheet,
    process_north_america_sheet,
    create_zip_to_climate_zone_mapping,
)
from crrem_processor.mappings import load_mappings, apply_property_mappings
from crrem_processor.validation import validate_expanded_global_pathways
from crrem_processor.utils import (
    load_config,
    load_mapping_data,
    save_parquet_with_metadata,
)

__version__ = "0.1.0"
__author__ = "GRESB Team"

__all__ = [
    "CRREMPathwayProcessor",
    "process_global_sheet",
    "process_north_america_sheet",
    "create_zip_to_climate_zone_mapping",
    "load_mappings",
    "apply_property_mappings",
    "validate_expanded_global_pathways",
    "load_config",
    "load_mapping_data",
    "save_parquet_with_metadata",
]
