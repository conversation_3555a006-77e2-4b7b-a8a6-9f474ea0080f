"""
CRREM Pathway Processor

Main processor class for handling CRREM pathway data from Excel files
and converting them to Parquet format.
"""

import os
from dotenv import load_dotenv

load_dotenv()
import polars as pl
import logging
from pathlib import Path

from crrem_processor.utils import (
    load_config,
    sort_and_reorder_columns,
    convert_intensity_to_sqft,
    add_area_unit_column,
)
from gresb_utils.file_io import write_data, download_and_read_excel
from gresb_utils.git_utils import get_git_output_run
from crrem_processor.pathway_processing import (
    process_global_sheet,
    process_north_america_sheet,
    create_zip_to_climate_zone_mapping,
    populate_subnational_location_names,
    apply_country_mappings,
    convert_resi_to_rmf,
)
from crrem_processor.mappings import apply_property_mappings
from crrem_processor.validation import validate_expanded_global_pathways

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class CRREMPathwayProcessor:
    """Process CRREM pathways from Excel files to Parquet format"""

    def __init__(self, data_dir: str):
        self.data_dir = Path(data_dir)
        self.output_dir = self.data_dir / "parquet_output"
        self.output_dir.mkdir(exist_ok=True)

        # Load configuration
        self.config = load_config(self.data_dir)

        # S3 paths from config with git-based versioning
        self.input_bucket = self.config["s3_paths"]["input_bucket"]
        git_version = get_git_output_run()
        base_output_bucket = self.config["s3_paths"]["output_bucket"]
        # Replace {BRANCH} placeholder with git version for branch-aware paths
        self.output_bucket = base_output_bucket.replace("{BRANCH}", git_version)

        # Log active runtime, AWS profile, and branch/commit
        runtime = os.environ.get("RUNTIME", "dev")
        aws_profile = os.environ.get("AWS_PROFILE", "not set")
        logger.info(f"Active RUNTIME: {runtime}")
        logger.info(f"Active AWS_PROFILE: {aws_profile}")
        logger.info(f"Active input bucket: {self.input_bucket}")
        logger.info(f"Active output bucket: {self.output_bucket}")
        logger.info(f"Active branch/commit: {git_version}")

        # File paths from config (S3)
        self.global_pathways_file = (
            self.input_bucket
            + self.config["input_files"]["global_pathways"]["filename"]
        )
        self.north_america_file = (
            self.input_bucket
            + self.config["input_files"]["north_america_pathways"]["filename"]
        )
        self.mapping_file = (
            self.input_bucket + self.config["input_files"]["mapping_file"]["filename"]
        )
        self.zip_mapping_file = (
            self.input_bucket
            + self.config["input_files"]["zip_mapping_file"]["filename"]
        )

        # Version metadata from config
        self.global_version = self.config["input_files"]["global_pathways"]["version"]
        self.north_america_version = self.config["input_files"][
            "north_america_pathways"
        ]["version"]

        # Load mapping data once and store as instance variables
        self._load_mapping_data()

    def _load_mapping_data(self):
        """Load property type, country, and subnational mappings once during initialization"""
        logger.info("Loading mapping data from S3...")

        # Get sheet names from config
        property_types_sheet = self.config["input_files"]["mapping_file"]["sheets"][
            "property_types"
        ]["name"]
        australia_sheet = self.config["input_files"]["mapping_file"]["sheets"][
            "australia_mappings"
        ]["name"]
        country_mappings_sheet = self.config["input_files"]["mapping_file"]["sheets"][
            "country_mappings"
        ]["name"]

        # Load all mapping data from S3 using download helper
        self.property_types = download_and_read_excel(
            self.mapping_file, sheet_name=property_types_sheet
        )
        self.australia_mappings = download_and_read_excel(
            self.mapping_file, sheet_name=australia_sheet
        )
        self.country_mappings = download_and_read_excel(
            self.mapping_file, sheet_name=country_mappings_sheet
        )

        logger.info("Mapping data loaded successfully from S3")

    def process_global_pathways_essential(self) -> pl.DataFrame:
        """Process global CRREM pathways - essential processing only (raw pathway data)"""
        logger.info("Processing global pathways (essential)...")

        # Process both CO2 and kWh sheets
        co2_pathways = self._process_global_sheet("co2_pathways")
        kwh_pathways = self._process_global_sheet("kwh_pathways")

        # Combine pathways
        all_pathways = pl.concat([co2_pathways, kwh_pathways])
        logger.info(
            f"Processed {len(co2_pathways)} rows CO2 pathways and {len(kwh_pathways)} rows kWh pathways"
        )

        # Convert RESI to RMF (exception in the excel sheet)
        return convert_resi_to_rmf(all_pathways)

    def _process_global_sheet(self, sheet_type: str) -> pl.DataFrame:
        """Process a single global sheet (CO2 or kWh)"""
        config = self.config["input_files"]["global_pathways"]["sheets"][sheet_type]

        sheet = download_and_read_excel(
            self.global_pathways_file,
            sheet_name=config["name"],
        )

        return process_global_sheet(
            sheet,
            config["utility_suffix"],
            config["unit"],
        )

    def process_global_pathways(self) -> tuple[pl.DataFrame, pl.DataFrame]:
        """Process global CRREM pathways"""
        logger.info("Processing global pathways...")

        # Get essential pathways (raw data only)
        essential_pathways = self.process_global_pathways_essential()

        # Apply metadata enrichment (location names, country mappings)
        expanded_pathways = populate_subnational_location_names(
            essential_pathways, self.australia_mappings
        )
        expanded_pathways = apply_country_mappings(
            expanded_pathways, self.country_mappings
        )
        expanded_pathways = apply_property_mappings(
            expanded_pathways, self.property_types, is_north_america=False
        )

        # Apply medical office mapping
        expanded_pathways = self._apply_medical_office_mapping(expanded_pathways)

        # Sort by standard column order and row sorting for consistent output
        essential_pathways = sort_and_reorder_columns(essential_pathways)
        expanded_pathways = sort_and_reorder_columns(expanded_pathways)
        expanded_pathways = expanded_pathways.with_columns(
            [
                pl.col("INTENSITY").cast(pl.Float64).alias("INTENSITY"),
                pl.col("YEAR").cast(pl.Int64).alias("YEAR"),
            ]
        )

        # Calculate new intensity value with a different unit: square feet unit kWh/sqft/yr, from current INTENSITY column (per square meter),
        # and stack the rows on existing dataframe
        sqft_df = convert_intensity_to_sqft(expanded_pathways)
        sqft_df = sort_and_reorder_columns(sqft_df)
        expanded_pathways = pl.concat([expanded_pathways, sqft_df])
        # keep consistent with R script
        expanded_pathways = add_area_unit_column(expanded_pathways)
        logger.info(
            f"Processed {len(essential_pathways)} essential pathway rows and {len(expanded_pathways)} expanded pathway rows"
        )
        # Validate expanded pathways against existing reference file
        validate_expanded_global_pathways(expanded_pathways, self.config)

        return essential_pathways, expanded_pathways

    def _apply_medical_office_mapping(self, pathways: pl.DataFrame) -> pl.DataFrame:
        """Apply medical office mapping: OFMO -> HEMO with Healthcare property type

        This mapping is required for REAL 2024 to update medical office from Office to Healthcare.
        Based on the R code logic that updates the mapping manually.
        """
        logger.info("Applying medical office mapping (OFMO -> HEMO)...")

        # Count rows that will be affected
        medical_office_rows = pathways.filter(pl.col("GRESB_PROP_TYPE_CODE") == "OFMO")
        affected_count = len(medical_office_rows)

        if affected_count > 0:
            logger.info(f"Found {affected_count} medical office rows to update")

            # Apply the mapping - create a flag for medical office rows first
            pathways = pathways.with_columns(
                [(pl.col("GRESB_PROP_TYPE_CODE") == "OFMO").alias("_is_medical_office")]
            )

            # Now apply the mappings using the flag
            pathways = pathways.with_columns(
                [
                    pl.when(pl.col("_is_medical_office"))
                    .then(pl.lit("HEMO"))
                    .otherwise(pl.col("GRESB_PROP_TYPE_CODE"))
                    .alias("GRESB_PROP_TYPE_CODE"),
                    pl.when(pl.col("_is_medical_office"))
                    .then(pl.lit("Healthcare: Medical Office"))
                    .otherwise(pl.col("GRESB_PROP_TYPE_NAME"))
                    .alias("GRESB_PROP_TYPE_NAME"),
                    pl.when(pl.col("_is_medical_office"))
                    .then(pl.lit("HEC"))
                    .otherwise(pl.col("CRREM_PROP_TYPE_CODE"))
                    .alias("CRREM_PROP_TYPE_CODE"),
                    pl.when(pl.col("_is_medical_office"))
                    .then(pl.lit("Healthcare"))
                    .otherwise(pl.col("CRREM_PROP_TYPE_NAME"))
                    .alias("CRREM_PROP_TYPE_NAME"),
                ]
            )

            # Remove the temporary flag column
            pathways = pathways.drop("_is_medical_office")

            logger.info("Medical office mapping applied successfully")
        else:
            logger.info("No medical office rows found to update")

        return pathways

    def process_north_america_pathways(self) -> pl.DataFrame:
        """Process North America pathways (US/Canada with climate zones) - same format as global pathways"""
        logger.info("Processing North America pathways...")

        # Process US pathways
        us_pathways = self._process_north_america_pathways_single_country("us")

        # Process Canada pathways
        canada_pathways = self._process_north_america_pathways_single_country("canada")

        logger.info(
            f"Processed {len(us_pathways)} US pathway rows and {len(canada_pathways)} Canada pathway rows"
        )

        north_america_pathways = pl.concat([us_pathways, canada_pathways])
        # Calculate new intensity value with a different unit: square feet unit kWh/sqft/yr, from current INTENSITY column (per square meter),
        # and stack the rows on existing dataframe
        sqft_df = convert_intensity_to_sqft(north_america_pathways)
        sqft_df = sort_and_reorder_columns(sqft_df)
        north_america_pathways = pl.concat([north_america_pathways, sqft_df])
        # keep consistent with R script
        north_america_pathways = add_area_unit_column(north_america_pathways)
        return north_america_pathways

    def _process_north_america_pathways_single_country(
        self, country: str
    ) -> pl.DataFrame:
        """Process pathways for a specific country (US or Canada)"""
        # Get sheet configs
        co2_config = self.config["input_files"]["north_america_pathways"]["sheets"][
            f"{country}_co2"
        ]
        kwh_config = self.config["input_files"]["north_america_pathways"]["sheets"][
            f"{country}_kwh"
        ]

        # Read sheets from S3
        co2_sheet = download_and_read_excel(
            self.north_america_file, sheet_name=co2_config["name"]
        )
        kwh_sheet = download_and_read_excel(
            self.north_america_file, sheet_name=kwh_config["name"]
        )

        # Process sheets
        co2_pathways = self._process_north_america_sheet(co2_sheet, co2_config)
        kwh_pathways = self._process_north_america_sheet(kwh_sheet, kwh_config)

        # Combine pathways
        return pl.concat([co2_pathways, kwh_pathways])

    def _process_north_america_sheet(
        self, sheet: pl.DataFrame, config: dict
    ) -> pl.DataFrame:
        """Process a single sheet with given configuration"""
        # Process the sheet
        pathways = process_north_america_sheet(
            sheet=sheet,
            country=config["country"],
            utility_suffix=config["utility_suffix"],
            unit=config["unit"],
            location_type=config["location_type"],
        )

        # Apply property mappings
        pathways = apply_property_mappings(
            pathways, self.property_types, is_north_america=True
        )

        # Apply medical office mapping to North America pathways as well
        pathways = self._apply_medical_office_mapping(pathways)

        # Sort and reorder columns
        return sort_and_reorder_columns(pathways)

    def process_all(self):
        """Process all CRREM pathway data"""
        logger.info("Starting CRREM pathway processing...")

        # Process all pathway data
        essential_pathways, expanded_pathways = (
            self.process_global_pathways()
        )  # 31620*8, 541880*14
        north_america_pathways = self.process_north_america_pathways()  # 294500*14
        zip_mapping = create_zip_to_climate_zone_mapping(self.zip_mapping_file)

        # Generate single output for North America and global
        all_pathways = pl.concat([expanded_pathways, north_america_pathways])
        # Save all outputs
        self._save_all_outputs(
            all_pathways,
            essential_pathways,
            expanded_pathways,
            north_america_pathways,
            zip_mapping,
        )

        logger.info("CRREM pathway processing completed!")

    def _save_all_outputs(
        self,
        all_pathways: pl.DataFrame,
        essential_pathways: pl.DataFrame,
        expanded_pathways: pl.DataFrame,
        north_america_pathways: pl.DataFrame,
        zip_mapping: pl.DataFrame,
    ):
        """
        Save all processed outputs to files with metadata
        all_pathways (pl.DataFrame): Combined pathways from global and North America
        essential_pathways (pl.DataFrame): Essential pathways
        expanded_pathways (pl.DataFrame): Expanded global pathways
        north_america_pathways (pl.DataFrame): North America pathways (US/Canada)
        zip_mapping (pl.DataFrame): Zip code to climate zone mapping for US assets

        """
        """Save all processed outputs to files"""
        # Save pathway files
        all_pathways_file = self._save_all_pathways_in_single_file(all_pathways)
        expanded_file = self._save_global_pathways(expanded_pathways, "expanded")
        essential_file = self._save_global_pathways(essential_pathways, "essential")
        north_america_file = self._save_north_america_pathways(north_america_pathways)

        # Save mapping files
        self._save_mapping_files(zip_mapping)

        # Log output summary
        logger.info(f"Output files (S3):")
        logger.info(f"  - All pathways combined: {all_pathways_file}")
        logger.info(f"  - Global expanded pathways: {expanded_file}")
        logger.info(f"  - Global essential pathways: {essential_file}")
        logger.info(f"  - North America pathways: {north_america_file}")
        logger.info(f"  - Mapping files: {self.output_bucket}*.parquet")

    def _save_all_pathways_in_single_file(self, pathways: pl.DataFrame) -> str:
        """Save all pathways in a single file with metadata to S3"""
        filename = f"crrem_all_pathways_{self.global_version}.parquet"
        all_pathways_folder = self.config.get("output_folders", {}).get(
            "all_pathways", ""
        )
        s3_path = f"{self.output_bucket}{all_pathways_folder}{filename}"

        # Save to S3 using shared file I/O utilities
        write_data(pathways, s3_path, "parquet")
        logger.info(f"Saved {len(pathways)} rows to S3: {s3_path}")

        return s3_path

    def _save_global_pathways(self, pathways: pl.DataFrame, format_type: str) -> str:
        """Save global pathways with metadata to S3"""
        filename = f"crrem_global_pathways_{format_type}_{self.global_version}.parquet"

        if format_type == "essential":
            folder = self.config.get("output_folders", {}).get("essential_pathways", "")
        else:
            folder = self.config.get("output_folders", {}).get("global_pathways", "")

        s3_path = f"{self.output_bucket}{folder}{filename}"

        write_data(pathways, s3_path, "parquet")
        logger.info(f"Saved {len(pathways)} rows to S3: {s3_path}")

        return s3_path

    def _save_north_america_pathways(self, pathways: pl.DataFrame) -> str:
        """Save North America pathways (US and Canada combined) with metadata to S3"""
        filename = f"crrem_north_america_pathways_expanded_{self.north_america_version}.parquet"
        north_america_pathways_folder = self.config.get("output_folders", {}).get(
            "north_america_pathways", ""
        )
        s3_path = f"{self.output_bucket}{north_america_pathways_folder}{filename}"

        write_data(pathways, s3_path, "parquet")
        logger.info(f"Saved {len(pathways)} rows to S3: {s3_path}")

        return s3_path

    def _save_mapping_files(self, zip_mapping: pl.DataFrame):
        """Save all mapping files to S3"""
        mappings = [
            (
                self.property_types,
                "crrem_property_mappings.parquet",
                "property_mappings",
                "GRESB to CRREM property type mappings",
            ),
            (
                self.australia_mappings,
                "crrem_subnational_mappings.parquet",
                "subnational_mappings",
                "Subnational location mappings",
            ),
            (
                self.country_mappings,
                "crrem_country_mappings.parquet",
                "country_mappings",
                "CRREM country codes to GRESB country names and A2 codes",
            ),
            (
                zip_mapping,
                "crrem_zip_climate_mapping.parquet",
                "zip_climate_mappings",
                "US zip code to climate zone mapping (required for North America pathway matching)",
            ),
        ]

        for df, filename, folder_key, description in mappings:
            folder = self.config.get("output_folders", {}).get(folder_key, "")
            s3_path = f"{self.output_bucket}{folder}{filename}"
            write_data(df, s3_path, "parquet")
            logger.info(f"Saved {len(df)} rows to S3: {s3_path}")


if __name__ == "__main__":
    import sys
    from pathlib import Path

    # Set up logging
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
    )

    # Get data directory from command line or use current directory
    data_dir = sys.argv[1] if len(sys.argv) > 1 else "."

    # Create and run processor
    processor = CRREMPathwayProcessor(data_dir)
    processor.process_all()
