import polars as pl
from pathlib import Path
from typing import Dict
import logging

logger = logging.getLogger(__name__)


def load_mappings(
    mapping_file: Path,
    zip_mapping_file: Path,
    property_types_sheet: str = "property_types",
    australia_sheet: str = "subnational_Aus",
) -> Dict[str, pl.DataFrame]:
    property_types = pl.read_excel(mapping_file, sheet_name=property_types_sheet)
    australia_mappings = pl.read_excel(mapping_file, sheet_name=australia_sheet)
    return {"property_types": property_types, "australia_mappings": australia_mappings}


def apply_property_mappings(
    df: pl.DataFrame, property_mappings: pl.DataFrame, is_north_america: bool = False
) -> pl.DataFrame:
    """Apply property type mappings to fill in GRESB and CRREM property type information"""

    logger.info(
        f"Applying property mappings for {'North America' if is_north_america else 'global'} pathways"
    )
    logger.debug(f"DataFrame columns: {df.columns}")

    if is_north_america:
        # For US/Canada, use US/Canada specific mappings
        mapping_df = property_mappings.select(
            [
                pl.col("crrem_code_us_canada").alias("CRREM_PROP_TYPE_CODE"),
                pl.col("gresb_name").alias("GRESB_PROP_TYPE_NAME"),
                pl.col("gresb_code").alias("GRESB_PROP_TYPE_CODE"),
                pl.col("crrem_name_us_canada").alias("CRREM_PROP_TYPE_NAME"),
            ]
        )
    else:
        # For rest of world, use global mappings
        mapping_df = property_mappings.select(
            [
                pl.col("crrem_code_global").alias("CRREM_PROP_TYPE_CODE"),
                pl.col("gresb_name").alias("GRESB_PROP_TYPE_NAME"),
                pl.col("gresb_code").alias("GRESB_PROP_TYPE_CODE"),
                pl.col("crrem_name_global").alias("CRREM_PROP_TYPE_NAME"),
            ]
        )
    result = df.join(mapping_df, on="CRREM_PROP_TYPE_CODE", how="left")

    # validate there is null values in the entire table
    has_any_null = sum(df.null_count().row(0)) > 0
    if has_any_null:
        logger.error(
            "The DataFrame contains null values after applying property mappings. Please check the input data."
        )
        raise ValueError(
            "The DataFrame contains null values after applying property mappings. Please check the input data."
        )

    return result
