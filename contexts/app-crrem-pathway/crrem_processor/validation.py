"""
Validation functions for CRREM pathway processing
"""

import polars as pl
import logging
from crrem_processor.utils import sort_and_reorder_columns, add_area_unit_column
from gresb_utils.file_io import load_polars_from_file

logger = logging.getLogger(__name__)


def validate_expanded_global_pathways(
    new_global_pathways: pl.DataFrame, config: dict
) -> bool:
    """Validate that new global pathways match the existing parquet file for non-US/Canada countries"""
    logger.info("Validating global pathways against existing parquet file...")
    # Get reference file path from config
    if "processing" in config and "validation" in config["processing"]:
        reference_file = config["processing"]["validation"]["reference_file"]
    else:
        raise ValueError("Validation reference file not found in config")

    # Load existing parquet file from S3 using gresb_utils
    try:
        existing_df = load_polars_from_file(
            file_path=reference_file,
            file_type="parquet",
            from_local_cache=False,  # Disable local cache for validation to ensure we get the latest version
        )
        logger.info(f"Successfully loaded reference file from S3: {reference_file}")
    except Exception as e:
        logger.error(f"Failed to load reference file from S3: {reference_file}")
        logger.error(f"Error: {e}")
        raise FileNotFoundError(
            f"Reference file {reference_file} could not be loaded from S3. Validation cannot proceed."
        )
    existing_df = existing_df.with_columns(
        [
            pl.col("INTENSITY").cast(pl.Float64).alias("INTENSITY"),
            pl.col("YEAR").cast(pl.Int64).alias("YEAR"),
        ]
    )
    # Filter out US and Canada from existing data and convert YEAR to Int64
    existing_filtered = existing_df.filter(
        ~pl.col("COUNTRY_A2_CODE").is_in(["US", "CA"])
    )
    # Validate schema compatibility after YEAR conversion
    validate_schema_compatibility(existing_filtered, new_global_pathways)

    # Apply same sorting as in process_global_pathways for consistent comparison
    existing_filtered = sort_and_reorder_columns(existing_filtered)
    existing_filtered = add_area_unit_column(
        existing_filtered
    )  # Add back the AREA_UNIT column that was lost in the preious step

    # Validate no duplicates in both DataFrames
    validate_no_duplicates(existing_filtered, "existing reference file")
    validate_no_duplicates(new_global_pathways, "new global pathways")

    # Compare DataFrames directly
    if existing_filtered.equals(new_global_pathways):  # This is never the case
        logger.info(
            "Global pathways validation passed - new output matches existing file (non-US/CA) exactly"
        )
        return True
    else:
        logger.info(
            "Global pathways validation - new output does not match existing file (non-US/CA) exactly, further analysing"
        )

        # Analyze differences for debugging
        analyze_dataframe_differences(
            existing_filtered, new_global_pathways, "existing", "new"
        )
        return False


def validate_schema_compatibility(existing: pl.DataFrame, new: pl.DataFrame):
    """Validate that two DataFrames have the same schema (columns and data types)"""
    logger.info("Validating schema compatibility...")

    # Check column names
    existing_cols = set(existing.columns)
    new_cols = set(new.columns)

    if existing_cols != new_cols:
        missing_cols = existing_cols - new_cols
        extra_cols = new_cols - existing_cols
        logger.error(f"Column mismatch:")
        if missing_cols:
            logger.error(f"  Missing columns in new DataFrame: {missing_cols}")
        if extra_cols:
            logger.error(f"  Extra columns in new DataFrame: {extra_cols}")
        raise ValueError(f"Schema mismatch - columns don't match")

    # Check data types for common columns
    common_cols = existing_cols.intersection(new_cols)
    type_mismatches = []

    for col in common_cols:
        if existing[col].dtype != new[col].dtype:
            type_mismatches.append(
                f"'{col}': {existing[col].dtype} vs {new[col].dtype}"
            )

    if type_mismatches:
        logger.error(f"Data type mismatches:")
        for mismatch in type_mismatches:
            logger.error(f"  {mismatch}")
        raise ValueError(f"Schema mismatch - data types don't match")

    logger.info("Schema validation passed - columns and data types match")


def validate_no_duplicates(df: pl.DataFrame, name: str):
    """Validate that DataFrame has no duplicate rows"""
    original_count = len(df)
    unique_count = len(df.unique())

    if original_count != unique_count:
        logger.error(f"{name} contains {original_count - unique_count} duplicate rows")
        logger.error(f"Original: {original_count} rows, Unique: {unique_count} rows")
        raise ValueError(f"{name} contains duplicate rows")


def analyze_dataframe_differences(
    existing: pl.DataFrame, new: pl.DataFrame, existing_name: str, new_name: str
):
    """Analyze differences between two DataFrames for debugging purposes"""
    logger.info(
        f"Analyzing differences between {existing_name} and {new_name} DataFrames..."
    )

    # Define columns excluding INTENSITY for structural comparison
    # TODO: low prio: CRREM_PROP_TYPE_NAME is different in existing Industrial, Refrigerated Warehouse vs "Industrial: Distribution Warehouse: Refrigerated Warehouse"
    columns_excluding_intensity = [
        col for col in new.columns if col not in ["INTENSITY", "GRESB_PROP_TYPE_NAME"]
    ]

    # First, compare structure (excluding INTENSITY and GRESB_PROP_TYPE_NAME)
    logger.info(
        "Comparing DataFrame structure (excluding INTENSITY and GRESB_PROP_TYPE_NAME columns)..."
    )

    # Find rows unique to existing (in existing but not in new)
    unique_to_existing = existing.join(new, on=columns_excluding_intensity, how="anti")
    if len(unique_to_existing) > 0:
        logger.error(f"Sample rows unique to {existing_name}:")
        logger.error(unique_to_existing.head(5))
        raise ValueError(f"Rows unique to {existing_name} found, please check the data")

    # Find rows unique to new (in new but not in existing) # Somehow the existing has no year 2020
    unique_to_new = new.join(
        existing, on=columns_excluding_intensity, how="anti"
    ).filter(pl.col("YEAR") != 2020)
    if len(unique_to_new) > 0:
        logger.error(f"Sample rows unique to {new_name}:")
        logger.error(unique_to_new.head(5))
        raise ValueError(f"Rows unique to {new_name} found, please check the data")

    # Now compare INTENSITY values for matching rows with tolerance
    logger.info("Comparing INTENSITY values for matching rows with tolerance...")

    # Join the dataframes on the key columns to get matching rows with both INTENSITY values
    joined_df = existing.join(
        new.select(columns_excluding_intensity + ["INTENSITY"]),
        on=columns_excluding_intensity,
        how="inner",
        suffix="_new",
    )

    if len(joined_df) > 0:
        # Calculate differences between INTENSITY values
        tolerance = 1e-4  # Very small tolerance for floating point differences

        joined_df = joined_df.with_columns(
            [
                (pl.col("INTENSITY") - pl.col("INTENSITY_new")).alias("INTENSITY_diff"),
                pl.col("INTENSITY").alias(f"{existing_name}_INTENSITY"),
                pl.col("INTENSITY_new").alias(f"{new_name}_INTENSITY"),
            ]
        ).drop(
            [
                pl.col("INTENSITY"),
                pl.col("INTENSITY_new"),
            ],  # Drop original INTENSITY columns
        )  # drop duplicate columns

        # Find rows with significant differences
        significant_diffs = joined_df.filter(pl.col("INTENSITY_diff").abs() > tolerance)

        # Exclude rows where CRREM_PROP_TYPE_CODE == 'HEC' from significant_diffs
        # 15-07-2025: In the past HEMO used to be part of the office sector, so that's why we had it mapped to office but this changed last year
        if "CRREM_PROP_TYPE_CODE" in significant_diffs.columns:
            non_hec_diffs = significant_diffs.filter(
                pl.col("CRREM_PROP_TYPE_CODE") != "HEC"
            )
            hec_diffs = significant_diffs.filter(
                pl.col("CRREM_PROP_TYPE_CODE") == "HEC"
            )
            if len(hec_diffs) > 0:
                logger.warning(
                    f"Skipping {len(hec_diffs)} significant INTENSITY differences for CRREM_PROP_TYPE_CODE == 'HEC' (known exception)"
                )
        else:
            non_hec_diffs = significant_diffs

        if len(non_hec_diffs) > 0:
            non_hec_diffs.write_csv(
                "significant_diffs.csv",
                separator=",",
            )
            num_diffs = len(non_hec_diffs)
            logger.error(
                f"INTENSITY differences found (excluding HEC): {num_diffs} out of {len(joined_df)} rows"
            )
            logger.error(f"Tolerance used: {tolerance}")
            display_cols = [
                col for col in non_hec_diffs.columns if not col.endswith("_new")
            ]

            logger.error(f"Rows with INTENSITY differences (excluding HEC):")
            logger.error(non_hec_diffs.select(display_cols).head(10))

            if len(non_hec_diffs) > 10:
                logger.error(
                    f"... and {len(non_hec_diffs) - 10} more rows with differences"
                )

            raise ValueError(
                "INTENSITY values differ beyond tolerance (excluding HEC), please check the data"
            )
        else:
            logger.info(
                "INTENSITY values match within tolerance (excluding HEC differences)"
            )
    else:
        logger.warning("No common rows found for INTENSITY comparison")

    # Show column differences if any
    existing_cols = set(existing.columns)
    new_cols = set(new.columns)

    if existing_cols != new_cols:
        logger.error(f"Column differences:")
        logger.error(
            f"Columns in {existing_name} but not in {new_name}: {existing_cols - new_cols}"
        )
        logger.error(
            f"Columns in {new_name} but not in {existing_name}: {new_cols - existing_cols}"
        )
        raise ValueError("Column differences found between existing and new DataFrames")

    # Show data type differences
    for col in existing_cols.intersection(new_cols):
        if existing[col].dtype != new[col].dtype:
            logger.error(
                f"Data type mismatch for column '{col}': {existing[col].dtype} vs {new[col].dtype}"
            )

    logger.info(
        f"Successfully passed dataframe differences analysis, only intensity values differ, but under tolerance: {tolerance}"
    )
