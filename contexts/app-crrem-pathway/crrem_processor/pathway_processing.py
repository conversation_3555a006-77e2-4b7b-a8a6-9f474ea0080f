"""
CRREM Pathway Processing Functions

Core functions for processing CRREM pathway data from Excel sheets.
"""

import polars as pl
from typing import List
import logging
from gresb_utils.file_io import download_and_read_excel

logger = logging.getLogger(__name__)


def process_global_sheet(
    sheet: pl.DataFrame, utility_suffix: str, unit: str
) -> pl.DataFrame:
    """
    Process a global CRREM pathway sheet (CO2 or kWh)

    Args:
        sheet: Raw Excel sheet data
        utility_suffix: Utility type suffix (e.g., "CO2", "kWh")
        unit: Unit value from config (e.g., "kgCO2/m²/yr", "kWh/m²/yr")

    Returns:
        Processed pathways DataFrame
    """
    logger.info(f"Processing global sheet with utility suffix: {utility_suffix}")

    # Clean headers and data
    sheet = _clean_headers(sheet)
    # Filter out US and Canada columns first
    sheet = _remove_us_canada_columns(sheet)
    sheet = _clean_missing_values(sheet)

    # Pivot to long format
    pathways = _pivot_table_longer(sheet, unit)

    # Parse location types and add metadata
    pathways = _parse_subnational_locations(pathways)
    pathways = _add_utility_details(pathways, utility_suffix)
    pathways = _add_target_degrees(pathways)
    pathways = _add_unit_details(pathways, unit)

    return pathways


def process_north_america_sheet(
    sheet: pl.DataFrame,
    country: str,
    utility_suffix: str,
    unit: str,
    location_type: str,
) -> pl.DataFrame:
    """
    Process a North America CRREM pathway sheet (US or Canada)

    Args:
        sheet: Raw Excel sheet data
        country: Country code ("US" or "CA")
        utility_suffix: Utility type suffix (e.g., "CO2", "kWh")
        unit: Unit value from config (e.g., "kgCO2/m²/yr", "kWh/m²/yr")
        location_type: Location type (e.g., "SUBNATIONAL", "COUNTRY", "CLIMATE_ZONE" or "STATE_PROVINCE")

    Returns:
        Processed pathways DataFrame
    """
    logger.info(
        f"Processing North America sheet for {country} with utility suffix: {utility_suffix}"
    )

    # For North America sheets, we don't clean headers since we handle multi-row headers manually
    # Just clean missing values from data rows
    sheet = _clean_missing_values(sheet)

    # Extract climate zone codes and property types from multi-row headers
    pathways = _extract_climate_zone_pathways(sheet, country, utility_suffix, unit)

    # Convert the columns into the correct data type, all columns except for year should be float64, year should be int 64
    pathways = pathways.with_columns(
        [pl.col("YEAR").cast(pl.Int64), pl.exclude("YEAR").cast(pl.Float64)]
    )

    # Expand complex climate zone codes before melting, only applicable for US, not Canada.
    if country == "US":
        pathways = expand_complex_climate_zones_wide(pathways)

    # Pivot to long format
    pathway_cols = [col for col in pathways.columns if col != "YEAR"]
    pathways_long = pathways.melt(
        id_vars=["YEAR"],
        value_vars=pathway_cols,
        variable_name="PATHWAY_CODE",
        value_name="INTENSITY",
    )

    # Parse pathway codes to extract climate zone and property type
    pathways_long = pathways_long.with_columns(
        [
            # This is CLIMATE_ZONE for US, STATE_PROVINCE for Canada
            pl.col("PATHWAY_CODE")
            .str.split(".")
            .list.get(0)
            .alias("CRREM_LOCATION_CODE"),
            pl.col("PATHWAY_CODE")
            .str.split(".")
            .list.get(1)
            .alias("CRREM_PROP_TYPE_CODE"),
        ]
    )

    # Drop PATHWAY_CODE as we don't need it anymore
    pathways_long = pathways_long.drop("PATHWAY_CODE")

    # Add required metadata columns for US pathways
    pathways_long = _add_utility_details(pathways_long, utility_suffix)
    pathways_long = _add_target_degrees(pathways_long)
    pathways_long = _add_unit_details(pathways_long, unit)

    # Add location type and country information
    pathways_long = pathways_long.with_columns(
        [
            pl.lit(location_type).alias("LOCATION_TYPE"),
            pl.lit(country).alias("COUNTRY_A2_CODE"),
            # For now, use CRREM_LOCATION_CODE as LOCATION_NAME, can be enhanced later with proper location names
            pl.col("CRREM_LOCATION_CODE").alias("LOCATION_NAME"),
        ]
    )

    # Convert RESI to RMF if needed (same as global pathways). Actually there is no instances for US/Canada sheet.
    pathways_long = convert_resi_to_rmf(pathways_long)

    # Ensure there are no null values
    has_any_null = sum(pathways_long.null_count().row(0)) > 0
    if has_any_null:
        raise ValueError(
            "The processed North America pathways DataFrame contains null values. Please check the input data."
        )

    logger.info(f"Processed {len(pathways_long)} rows for {country} pathways")

    return pathways_long


def create_zip_to_climate_zone_mapping(zip_mapping_file: str) -> pl.DataFrame:
    """
    Create mapping from zip codes to climate zones

    Args:
        zip_mapping_file: path to zip code mapping Excel file

    Returns:
        Mapping DataFrame with zip codes and climate zones
    """
    logger.info("Creating zip to climate zone mapping...")

    # Read the mapping file from S3 or local file
    mapping_df = download_and_read_excel(zip_mapping_file)

    # Clean and standardize the mapping - ensure zip codes are treated as strings with proper formatting
    mapping_df = mapping_df.with_columns(
        [
            pl.col("Zip").cast(pl.Utf8).str.zfill(5).alias("zip_code"),
            pl.col("GEA Region").cast(pl.Utf8).alias("GEA_region"),
            pl.col("CBECS Climate Zone").cast(pl.Utf8).alias("CBECS_climate_zone"),
            pl.col("ASHRAE Climate Zone").cast(pl.Utf8).alias("ASHRAE_climate_zone"),
            pl.col("ASHRAE Moisture Regime")
            .cast(pl.Utf8)
            .alias("ASHRAE_moisture_regime"),
        ]
    ).select(
        pl.col("zip_code"),
        pl.col("GEA_region"),
        pl.col("CBECS_climate_zone"),
        pl.col("ASHRAE_climate_zone"),
        pl.col("ASHRAE_moisture_regime"),
    )

    # check if there is any null values in the mapping DataFrame, if so raise an error. ASHRAE_moisture_regime is an exception, it can be null.
    has_any_null = (
        sum(
            mapping_df.select(
                [col for col in mapping_df.columns if col != "ASHRAE_moisture_regime"]
            )
            .null_count()
            .row(0)
        )
        > 0
    )
    if has_any_null:
        logger.error(
            "The zip to climate zone mapping DataFrame contains null values. Please check the input data."
        )
        raise ValueError(
            "The zip to climate zone mapping DataFrame contains null values. Please check the input data."
        )

    return mapping_df


def _clean_headers(sheet: pl.DataFrame) -> pl.DataFrame:
    """Clean column headers (make uppercase, handle None values)"""
    headers = [
        str(col).upper() if col is not None else f"COL_{i}"
        for i, col in enumerate(sheet.columns)
    ]

    # Only rename if headers actually changed
    if headers != sheet.columns:
        rename_dict = {col: header for col, header in zip(sheet.columns, headers)}
        sheet = sheet.rename(rename_dict)

    return sheet


def _clean_missing_values(sheet: pl.DataFrame) -> pl.DataFrame:
    """Remove rows and columns with all missing values"""
    # Remove rows where all values are null
    sheet = sheet.filter(~pl.all_horizontal(pl.all().is_null()))

    # Remove columns where all values are null
    sheet = sheet[[s.name for s in sheet if not (s.null_count() == sheet.height)]]

    return sheet


def _remove_us_canada_columns(sheet: pl.DataFrame) -> pl.DataFrame:
    """Filter out US and Canada-related columns from global pathway sheets"""
    # Remove columns that start with "USA-" (US city pathways) or "CA-" (Canada pathways)
    na_columns = [
        col for col in sheet.columns if col.startswith("USA") or col.startswith("CAN.")
    ]

    if na_columns:
        logger.info(
            f"Removing {len(na_columns)} North America-related columns from global pathways"
        )
        # Keep only non-North America columns
        non_na_columns = [
            col
            for col in sheet.columns
            if not (col.startswith("USA") or col.startswith("CAN."))
        ]
        sheet = sheet.select(non_na_columns)

    return sheet


def _pivot_table_longer(sheet: pl.DataFrame, unit: str) -> pl.DataFrame:
    """Pivot table from wide to long format"""

    # Identify pathway columns (those with dots or spaces)
    pathway_cols = []
    for col in sheet.columns:
        if col == "YEAR":
            continue
        # Check for dot-separated format (e.g., "BG.RHS.CO2-Int")
        if "." in col:
            pathway_cols.append(col)
        # Check for space-separated format (e.g., "AZNMc Cool 5B")
        elif " " in col and any(char.isdigit() for char in col):
            pathway_cols.append(col)
        else:
            # Log columns that might contain typos
            logger.warning(
                f"Column '{col}' does not match expected pathway format. It will be skipped."
            )

    if not pathway_cols:
        logger.error(
            "No pathway columns found with expected formats. Using alternative pattern matching."
        )
        raise ValueError(
            "No valid pathway columns found in the sheet. Please check the column names."
        )

    # the sheet columns that are not in pathway_cols are only "EU OFFICE CO2-Int" and "EU Retail High-street CO2-Int" and they
    # can be removed from the sheet df because they are actually empty columns.
    sheet = sheet.select(["YEAR"] + pathway_cols)

    logger.info(f"Found {len(pathway_cols)} pathway columns")

    # Pivot to long format
    sheet_long = sheet.melt(
        id_vars=["YEAR"],  # Is this right?
        value_vars=pathway_cols,
        variable_name="PATHWAY_CODE",
        value_name="INTENSITY",
    )

    # Parse pathway codes (location and property type only, unit comes from config)
    sheet_long = sheet_long.with_columns(
        [
            pl.col("PATHWAY_CODE")
            .str.split(".")
            .list.get(0)
            .alias("CRREM_LOCATION_CODE"),
            pl.col("PATHWAY_CODE")
            .str.split(".")
            .list.get(1)
            .alias("CRREM_PROP_TYPE_CODE"),
        ]
    )
    # drop PATHWAY_CODE as we don't need it anymore
    sheet_long = sheet_long.drop("PATHWAY_CODE")
    # Ensure there is no null values at all in the entire dataframe
    has_any_null = sum(sheet_long.null_count().row(0)) > 0
    if has_any_null:
        raise ValueError(
            "The pivoted DataFrame contains null values. Please check the input data."
        )

    return sheet_long


def convert_resi_to_rmf(pathways: pl.DataFrame) -> pl.DataFrame:
    """Convert RESI property type codes to RMF to handle this exception for Australia subnational pathways."""
    logger.info("Converting RESI property type codes to RMF...")

    pathways = pathways.with_columns(
        [
            pl.when(pl.col("CRREM_PROP_TYPE_CODE") == "RESI")
            .then(pl.lit("RMF"))
            .otherwise(pl.col("CRREM_PROP_TYPE_CODE"))
            .alias("CRREM_PROP_TYPE_CODE")
        ]
    )

    return pathways


def _parse_subnational_locations(pathways: pl.DataFrame) -> pl.DataFrame:
    """Parse and identify subnational locations
    Took the logic from ParseSubNationalLocations in the original R code: gresb.trt/R/parse_crrem_spreadsheet.R
    """
    pathways = pathways.with_columns(
        [
            pl.when(
                pl.col("CRREM_LOCATION_CODE").str.contains(
                    r"^[A-Z]{2,3}(-[A-Z]{2,3}|\d{1})$"
                )
            )
            .then(pl.lit("SUBNATIONAL"))
            .otherwise(pl.lit("COUNTRY"))
            .alias("LOCATION_TYPE")
        ]
    )

    return pathways


def _add_utility_details(pathways: pl.DataFrame, utility_suffix: str) -> pl.DataFrame:
    """Add utility code details"""  # We call it ghg, but it's just CO2.
    utility_code = "ghg" if "CO2" in utility_suffix.upper() else "en"

    pathways = pathways.with_columns([pl.lit(utility_code).alias("UTILITY_CODE")])

    return pathways


def _add_target_degrees(pathways: pl.DataFrame) -> pl.DataFrame:
    """Add target degrees - hardcoded to 1.5 for global pathways since we only have 1.5 degree pathways"""
    pathways = pathways.with_columns([pl.lit(1.5).alias("TARGET_DEGREES")])

    return pathways


def _add_unit_details(pathways: pl.DataFrame, unit: str) -> pl.DataFrame:
    """Add unit details based on utility suffix"""
    pathways = pathways.with_columns([pl.lit(unit).alias("UNIT")])

    return pathways


def _extract_climate_zone_pathways(
    sheet: pl.DataFrame, country: str, utility_suffix: str, unit: str
) -> pl.DataFrame:
    """Extract climate zone pathways from North America sheet with multi-row headers"""

    # For North America sheets, the first row (row 0) contains basic headers
    # Row 1 (index 0) contains climate zone codes
    # Row 2 (index 1) contains property type codes
    # Data starts from row 3 (index 1)

    # Get climate zone codes from row 1 (index 0)
    climate_zones = sheet.row(0, named=True)

    # Get property type codes from row 2 (index 1)
    property_types = sheet.row(1, named=True)

    # Get data rows starting from row 3 (index 2)
    data_rows = sheet.slice(2)
    # The first column is YEAR, so name the column correctly
    data_rows = data_rows.rename({data_rows.columns[0]: "YEAR"})

    logger.info(
        f"Processing {len(data_rows)} data rows with {len(climate_zones)} columns"
    )
    # create a list with the new column names from the climate zones and property types, they are mapped one-to-one
    new_column_names = {}
    for col in [col for col in data_rows.columns if col != "YEAR"]:
        if col in new_column_names:
            raise ValueError(
                f"Duplicate column name found: {col}. Please check the data structure."
            )
        property_type = property_types.get(col)
        climate_zone = climate_zones.get(col)
        new_column_names[col] = f"{climate_zone}.{property_type}"

    data_rows = data_rows.rename(new_column_names)

    return data_rows


def populate_subnational_location_names(
    all_pathways: pl.DataFrame, subnational_mappings: pl.DataFrame
) -> pl.DataFrame:
    """Populate LOCATION_NAME column using the subnational location mapping
    This would expand the dataframe to 99200 rows, since subnational_mappings has duplicate values in the CRREM_LOCATION_CODE column.
    It's expected, since we duplicate the pathways for each location, but multiple locations
    can have the same CRREM_LOCATION_CODE, therefor the same pathway.
    """
    logger.info(f"Total rows: {len(all_pathways)}")
    logger.info(
        f"Subnational rows: {len(all_pathways.filter(pl.col('LOCATION_TYPE') == 'SUBNATIONAL'))}"
    )

    # Join all rows with subnational mappings to get location names
    df_with_names = all_pathways.join(
        subnational_mappings,  # this adds LOCATION_NAME column to df_with_names
        left_on="CRREM_LOCATION_CODE",
        right_on="CRREM_LOCATION_CODE",
        how="left",
    )
    # Apply subnational location names to all subnational rows
    df_with_names = df_with_names.with_columns(
        [
            pl.when(pl.col("LOCATION_TYPE") == "SUBNATIONAL")
            .then(pl.col("LOCATION_NAME"))
            .otherwise(pl.col("LOCATION_NAME"))
            .alias("LOCATION_NAME")
        ]
    )

    # Add an assertion to make sure all the LOCATION_NAME is populated for subnational locations, if not, raise an error
    # Check for subnational codes without mappings
    subnational_without_mapping = df_with_names.filter(
        (pl.col("LOCATION_TYPE") == "SUBNATIONAL") & (pl.col("LOCATION_NAME").is_null())
    )

    if len(subnational_without_mapping) > 0:
        missing_codes = subnational_without_mapping.select(
            "CRREM_LOCATION_CODE"
        ).unique()
        logger.error(
            f"Subnational codes without mapping found: {missing_codes['CRREM_LOCATION_CODE'].to_list()}"
        )
        raise ValueError(
            f"Subnational codes without mapping found: {missing_codes['CRREM_LOCATION_CODE'].to_list()}. "
            "Please check the subnational location mappings."
        )

    return df_with_names


def apply_country_mappings(
    df: pl.DataFrame, country_mappings: pl.DataFrame
) -> pl.DataFrame:
    """Apply country mappings to populate LOCATION_NAME and COUNTRY_A2_CODE"""
    # Join with country mappings to get country names (LOCATION_NAME_right) and COUNTRY_A2_CODE
    df = df.join(
        country_mappings,
        left_on="CRREM_LOCATION_CODE",
        right_on="CRREM_LOCATION_CODE",
        how="left",
    )

    # Populate LOCATION_NAME for pathways with LOCATION_TYPE = country
    # LOCATION_NAME_right is the column from country_mappings that contains the country names
    df = df.with_columns(
        [
            # For subnational locations, keep existing LOCATION_NAME (already populated before in populate_subnational_location_names)
            pl.when(pl.col("LOCATION_TYPE") == "SUBNATIONAL")
            .then(pl.col("LOCATION_NAME"))
            # For non-subnational locations, use LOCATION_NAME_right
            .otherwise(pl.coalesce(pl.col("LOCATION_NAME_right")))
            .alias("LOCATION_NAME")
        ]
    )

    # Populate the COUNTRY_A2_CODE column for LOCATION_TYPE = subnational, since their CRREM_LOCATION_CODE is not a country code
    df = df.with_columns(
        [
            pl.when(pl.col("LOCATION_TYPE") == "SUBNATIONAL")
            .then(pl.lit("AU"))
            .otherwise(pl.col("COUNTRY_A2_CODE"))
            .alias("COUNTRY_A2_CODE")
        ]
    )

    # Drop the joined columns if they have _right suffix
    df = df.drop("LOCATION_NAME_right")

    # validate there is null values in the entire table
    has_any_null = sum(df.null_count().row(0)) > 0
    if has_any_null:
        logger.error(
            "The DataFrame contains null values after applying country mappings. Please check the input data."
        )
        raise ValueError(
            "The DataFrame contains null values after applying country mappings. Please check the input data."
        )

    return df


def expand_complex_climate_zones_wide(pathways: pl.DataFrame) -> pl.DataFrame:
    """Expand complex climate zone codes in the wide format DataFrame (before melting)

    This is more efficient than expanding after melting because we only iterate through
    columns (climate zones) rather than all the rows.
    """
    logger.info("Expanding complex climate zone codes in wide format...")

    # Get all columns except YEAR
    year_col = "YEAR"
    pathway_cols = [col for col in pathways.columns if col != year_col]

    # Identify columns that need expansion
    columns_to_expand = [col for col in pathway_cols if "/" in col]
    simple_columns = [col for col in pathway_cols if col not in columns_to_expand]

    # Start with simple columns and YEAR
    result_columns = [year_col] + simple_columns
    expanded_df = pathways.select(result_columns)

    # Expand complex columns using Polars operations
    for col in columns_to_expand:
        complex_zone, prop_type = col.split(".")[0], col.split(".")[1]
        climate_zones = _parse_complex_climate_zone(
            complex_zone
        )  # Get only the climate zone part

        # Create a column for each expanded climate zone
        for zone in climate_zones:
            simple_col = f"{zone}.{prop_type}"  # Put back the property type
            expanded_df = expanded_df.with_columns(pathways[col].alias(simple_col))
            logger.info(f"Expanded column '{col}' into '{simple_col}'")

    original_count = len(pathway_cols)
    expanded_count = len(expanded_df.columns) - 1  # Subtract 1 for YEAR column

    if expanded_count > original_count:
        logger.info(
            f"Expanded {original_count} pathway columns into {expanded_count} columns"
        )

    return expanded_df


def _parse_complex_climate_zone(location_code: str) -> List[str]:
    """Parse complex climate zone codes into individual zones (simplified implementation)

    Only expands slash patterns (like 2A/2B). Property types (like .OFF) are removed
    before calling this function.
    """
    # Handle "/" patterns (e.g., "ERCTc_Hot or very hot_2A/2B")
    # Find the last "/" to split on
    last_slash_idx = location_code.rfind("/")
    if last_slash_idx != -1:
        # Split the entire string on "/" to get all parts
        parts = location_code.split("/")

        # The base is everything before the first "/"
        base = parts[0]

        # Extract the base climate zone and the base number
        # For "ERCTc_Hot or very hot_2A/2B", base="ERCTc_Hot or very hot_2A"
        last_underscore = base.rfind("_")
        if last_underscore != -1:
            base_climate = base[:last_underscore]
            base_number = base[last_underscore + 1 :]

            # All numbers are the base number plus the remaining parts
            all_numbers = [base_number] + parts[1:]

            zones = []
            for num in all_numbers:
                zone = f"{base_climate}_{num}"
                zones.append(zone)
            return zones
        else:
            # Fallback: just split on "/"
            return [location_code]

    # Simple code, return as is (including codes with " or " in climate zone names)
    else:
        return [location_code]
