import argparse
import logging
import os
import sys
import pandas as pd
from pandera import DataFrameModel
from dotenv import load_dotenv

from data_io.data_s3_gateway import DataS3Gateway
from data_io.s3_file import S3File
import app_real_estate.models.output_models.building_certification_metrics_models as bcmodels
import app_real_estate.models.output_models.energy_rating_metrics_models as ermodels

"""
This script exists to check the output scores of the Real Estate scoring model.
It is a tool only useful during development to avoid introducing errors. It assumes
you are familiar with the output files, their format and variables but also with the S3
and local architectures.
The script fetches the "correct" scores on S3 or locally (saved in `app_real_estate/tmp/data/correct_scores`)
and compares them to the current local scores.

Usage:
    poetry run python -m contexts.app-real-estate.app_real_estate.run_output_checks 2024 --ast-regex "score_gh1" --agg-regex "SCORE.*GH"
or (if you want to exclude energy efficiency scores for example):
    poetry run python -m contexts.app-real-estate.app_real_estate.run_output_checks 2024 --run-local --ast-regex "^score((?\!energy).)*$" --agg-regex "^SCORE((?\!EFF).)*$"
"""


def import_re_parquet_from_prd(
    s3_gateway: DataS3Gateway,
    logger: logging.Logger,
    args: argparse.Namespace,
    filename: str,
) -> pd.DataFrame:
    survey_year = args.survey_year
    s3_filename = filename

    local_data_path = os.path.join(
        "app_real_estate", "tmp", "data", "correct_scores", f"{s3_filename}.parquet"
    )
    data_is_local = os.path.exists(local_data_path)

    if not args.run_local or not data_is_local:
        logger.info(f"Importing {s3_filename} data from S3...")

        s3_path = f"main/re/{survey_year}/output/{s3_filename}"
        data = s3_gateway.import_data(
            S3File(
                bucket_name="gresb-prd-gold-data",
                base_filename=s3_path,
                format="parquet",
            ),
        )

        # Create the directory structure if it doesn't exist
        if args.run_local:
            logger.info("The data file did not exist locally. Saving now...")
            os.makedirs(os.path.dirname(local_data_path), exist_ok=True)
            data.to_parquet(str(local_data_path))
    else:
        logger.info(f"Importing {s3_filename} locally...")
        data = pd.read_parquet(str(local_data_path))

    return data


def check_scores(
    s3_gateway: DataS3Gateway,
    logger: logging.Logger,
    args: argparse.Namespace,
    filename: str,
) -> None:
    """
    Compare the local score files to the "correct" scores.
    The regular expressions given in parameter of the script allow us
    to filter the variables to check.
    """
    correct_scores = import_re_parquet_from_prd(s3_gateway, logger, args, filename)
    # The S3 and local file architecture being different (maybe they shouldn't be),
    # we need to reshape the file path.
    local_filename = filename.split("/")[-1]
    local_scores = pd.read_parquet(
        f"contexts/app-real-estate/app_real_estate/tmp/data/{args.survey_year}/output/{local_filename}.parquet"
    )

    logger.info(f"Validating the local output for {local_filename}.")

    score_regex = (
        args.ast_regex if local_filename == "asset_level_scores" else args.agg_regex
    )
    if score_regex is None:
        score_regex = "score" if local_filename == "asset_level_scores" else "SCORE"
    scores_cols = correct_scores.filter(regex=score_regex).columns
    pd.testing.assert_frame_equal(
        correct_scores[scores_cols], local_scores[scores_cols]
    )
    logger.info(f"Local output for {local_filename} is valid!\n")


def check_bc_outputs(
    s3_gateway: DataS3Gateway,
    logger: logging.Logger,
    args: argparse.Namespace,
    filename: str,
    model: type[DataFrameModel],
) -> None:
    """
    Compare the local bc output files to the "correct" ones.
    """
    correct_output = import_re_parquet_from_prd(s3_gateway, logger, args, filename)
    # The S3 and local file architecture being different (maybe they shouldn't be),
    # we need to reshape the file path.
    local_filename = filename.split("/")[-1]
    local_output = pd.read_parquet(
        f"contexts/app-real-estate/app_real_estate/tmp/data/{args.survey_year}/output/{local_filename}.parquet"
    )

    # First, validate the data in the local output
    logger.info(f"Validating the local output for {local_filename}.")
    model.validate(local_output)

    cols_to_check = correct_output.columns.intersection(local_output.columns).tolist()
    pd.testing.assert_frame_equal(
        correct_output.sort_values(cols_to_check).reset_index(drop=True)[cols_to_check],
        local_output.sort_values(cols_to_check).reset_index(drop=True)[cols_to_check],
    )
    logger.info(f"Local output for {local_filename} is valid!\n")


if __name__ == "__main__":
    load_dotenv()

    # Parse script arguments
    parser = argparse.ArgumentParser(
        description="Run Real Estate scoring model output checks"
    )
    parser.add_argument(
        "survey_year", help="the reporting year of the data to check", type=int
    )
    parser.add_argument(
        "--run-local",
        action="store_true",
        help="whether the script should take the files with correct scores locally.",
    )
    parser.add_argument(
        "--agg-regex",
        action="store",
        help="a regular expression to match aggregated data columns.",
    )
    parser.add_argument(
        "--ast-regex",
        action="store",
        help="a regular expression to match asset-level data columns.",
    )
    parser.add_argument(
        "--score",
        action="store_true",
        help="whether the script should only run checks on the scores.",
    )

    # Parse arguments and set up logger
    args = parser.parse_args()
    # TODO: decide if this really should be read from prd
    s3_gateway = DataS3Gateway(profile_name="gresb-prd-developer-break-glass")
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(logging.DEBUG)
    logger.addHandler(handler)

    # Check scores for all levels
    check_scores(s3_gateway, logger, args, "asset_level_scores/asset_level_scores")
    check_scores(
        s3_gateway,
        logger,
        args,
        "aggregated_scores/propertysubtype_country_responseid_scores",
    )
    check_scores(
        s3_gateway, logger, args, "aggregated_scores/propertysubtype_responseid_scores"
    )
    check_scores(s3_gateway, logger, args, "aggregated_scores/portfolio_level_scores")

    if not args.score:
        bc_filenames = [
            "aggregated_certification_metrics/ctr_prt_responseid_brand_scheme_certification_metrics",
            "aggregated_certification_metrics/ctr_prt_responseid_brand_certification_metrics",
            "aggregated_certification_metrics/ctr_prt_responseid_certification_metrics",
            "aggregated_certification_metrics/portfolio_brand_scheme_certification_metrics",
            "aggregated_certification_metrics/portfolio_brand_certification_metrics",
            "aggregated_certification_metrics/portfolio_certification_metrics",
            "aggregated_energy_ratings_metrics/ctr_prt_responseid_brand_er_metrics",
            "aggregated_energy_ratings_metrics/ctr_prt_responseid_er_metrics",
            "aggregated_energy_ratings_metrics/portfolio_brand_er_metrics",
            "aggregated_energy_ratings_metrics/portfolio_er_metrics",
        ]
        bc_schemas = [
            bcmodels.BC_ResponseID_PropertySector_Country_Brand_Scheme_Model,
            bcmodels.BC_ResponseID_PropertySector_Country_Brand_Model,
            bcmodels.BC_ResponseID_PropertySector_Country_Model,
            bcmodels.BC_ResponseID_Brand_Scheme_Model,
            bcmodels.BC_ResponseID_Brand_Model,
            bcmodels.BC_ResponseID_Model,
            ermodels.ER_ResponseID_PropertySector_Country_Brand_Model,
            ermodels.ER_ResponseID_PropertySector_Country_Model,
            ermodels.ER_ResponseID_Brand_Model,
            ermodels.ER_ResponseID_Model,
        ]
        # Check BC metric aggregation outputs
        for i in range(len(bc_filenames)):
            check_bc_outputs(s3_gateway, logger, args, bc_filenames[i], bc_schemas[i])
