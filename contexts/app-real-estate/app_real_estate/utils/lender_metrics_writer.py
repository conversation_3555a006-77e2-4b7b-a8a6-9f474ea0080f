import logging
import pandas as pd

from data_io.data_s3_gateway import DataS3Gateway
from data_io.s3_file import S3File
from config import GOLD_DATA_BUCKET

class LenderAssetMetricsWriter:
    def __init__(self, survey_year: int, logger: logging.Logger):
        self.survey_year = survey_year
        self.logger = logger
        self.s3_path = f"lender-asset-aggregation/rel/{self.survey_year}/output"
        self.local_path = f"contexts/app-real-estate/app_real_estate/tmp/data/{self.survey_year}/rel/output"

    def _er_mapping(self, er_metrics: dict[str, pd.DataFrame]):
        pass

    def _bc_mapping(self, bc_metrics: dict[str, pd.DataFrame]):
        if len(bc_metrics) == 0:
            raise ValueError("No BC metrics provided")


    def save_lender_asset_metrics_to_s3(self, lender_metrics: pd.DataFrame):
        pass

    def save_lender_asset_metrics_to_local(self, lender_metrics: pd.DataFrame):
        pass

    def save_lender_bc_metrics_to_s3(self, lender_metrics: pd.DataFrame):
        pass
    
    def save_lender_bc_metrics_to_local(self, lender_metrics: pd.DataFrame):
        pass

    def save_lender_er_metrics_to_s3(self, lender_metrics: pd.DataFrame):
        pass

    def save_lender_er_metrics_to_local(self, lender_metrics: pd.DataFrame):
        pass

    def write_lender_metrics(self, lender_metrics: pd.DataFrame, is_local: bool = False):
        if is_local:
            self.save_lender_metrics_to_local(lender_metrics)
        else:
            self.save_lender_metrics_to_s3(lender_metrics)