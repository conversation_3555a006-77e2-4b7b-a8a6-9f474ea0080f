import pandas as pd
import numpy as np

from app_real_estate.models.input_models.r1_table_model import R1TableModel
from app_real_estate.constants.helper_enumerators import Utility
from app_real_estate.transformation.metric_calculation.asset_characteristics_calculation import (
    AssetCharacteristicsCalculations,
)
from app_real_estate.transformation.metric_calculation.asset_filters import (
    AssetFilters,
)
from app_real_estate.transformation.metric_calculation.consumption_calculations import (
    ConsumptionCalculations,
)
from app_real_estate.transformation.metric_calculation.coverage_calculations import (
    CoverageCalculations,
)
from app_real_estate.transformation.metric_calculation.intensity_calculations import (
    IntensityCalculations,
)
from app_real_estate.transformation.metric_calculation.lfl_calculations import (
    LFLCalculations,
)
from app_real_estate.transformation.metric_calculation.sustainable_utility_calculations import (
    SustainableUtilityCalculations,
)
from app_real_estate.transformation.metric_calculation.asset_aggregation_calculation import (
    AssetAggregationCalculation,
)
from app_real_estate.transformation.metric_calculation.building_certification_calculations import (
    BuildingCertificationsCalculations,
)
from app_real_estate.transformation.metric_calculation.building_certification_filters import (
    BuildingCertificationFilters,
)
from app_real_estate.transformation.metric_calculation.energy_ratings_calculations import (
    EnergyRatingsCalculations,
)
from app_real_estate.transformation.metric_calculation.common_operations import (
    CommonOperations,
)

import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.building_certification_columns as bc
import app_real_estate.constants.column_names.ghg_columns as gc
import app_real_estate.constants.column_names.water_columns as wc


class DataPreparer:
    """
    RunDataPreparation defines the data preparation pipeline functions for each
    indicator.

    The data preparation pipeline involves calculating the metrics useful that
    are useful for scoring (whether they are weights, checks or actual scored metrics)
    and calculating boolean lists used as filter for the DataFrames.
    Each function outputs DataFrames (example: asset data with both years and
    current year operational assets data) and can also return boolean lists that
    are the results of filters functions and used in benchmarking/scoring.

    Usage example for EN1:
        data_preparator = RunDataPreparation()
        asset_data, asset_data_cy, is_positive_ren, is_not_outlier = (
            self.data_preparator.prepare_data_en1(asset_data)
        )
    """

    def __init__(self) -> None:
        self.lfl_metric_calculator = LFLCalculations()

    def prepare_data_en1(
        self,
        asset_data: pd.DataFrame,
        weather_stations: pd.DataFrame,
        ashrae_thresholds: pd.DataFrame,
    ) -> dict[str, pd.DataFrame]:
        """
        Runs the data preparation pipeline for EN1.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame with both data years.
        weather_stations (pd.DataFrame): the lookup snapshot from the `asset_portal_ashrae_weather_stations` table.
        ashrae_thresholds (pd.DataFrame): the lookup snapshot from the `asset_portal_ashrae_energy_use_intensity_thresholds` table.

        Returns:
        tuple[pd.DataFrame, pd.DataFrame, list[bool], list[bool], list[bool], list[bool], list[bool]]:
            a tuple containing the prepared asset data with both years,
            the prepared asset data with current year operational data,
            a boolean list describing whether assets are eligible for renewable
            energy benchmarking, a boolean list describing whether assets are
            LFL outliers or not, a boolean list describing whether assets are
            LFL hard outliers or not, one describing whether assets are eligible
            for intensity benchmarking and one describing wheter assets are hard
            intensity outliers.
        """
        # Some metrics should only be calculated for current year operational assets
        is_cy_op = AssetFilters.filter_current_year_operational_assets(asset_data)

        # Calculate asset metrics
        asset_data = (
            SustainableUtilityCalculations().add_renewable_energy_percent_change_rate(
                asset_data
            )
        )
        asset_data = (
            self.lfl_metric_calculator.add_energy_lfl_consumption_per_subspaces(
                asset_data
            )
        )
        asset_data = (
            self.lfl_metric_calculator.add_lfl_consumption_per_control_per_utility(
                asset_data, Utility.Energy
            )
        )
        asset_data = (
            self.lfl_metric_calculator.add_previous_year_lfl_consumption_per_utility(
                asset_data, Utility.Energy
            )
        )
        asset_data = (
            self.lfl_metric_calculator.add_lfl_percent_change_per_control_per_utility(
                asset_data, Utility.Energy
            )
        )

        asset_data = IntensityCalculations.add_intensity_per_utility(
            asset_data, Utility.Energy
        )

        asset_data = IntensityCalculations.add_climate_zones(
            asset_data, weather_stations
        )
        asset_data = IntensityCalculations.add_ashrae_thresholds(
            asset_data, ashrae_thresholds
        )

        asset_data_cy = asset_data[is_cy_op]
        asset_data_cy = (
            SustainableUtilityCalculations.add_renewable_generation_per_area(
                asset_data_cy
            )
        )

        # Filter negative renewable percent change values for benchmarking.
        is_positive_ren = AssetFilters.filter_negative_percent_change_rates(
            asset_data_cy, "en"
        )

        # Filter LFL outliers for benchmarking.
        is_not_lfl_outlier = AssetFilters.filter_outliers(
            asset_data_cy, ec.en_lfl_outlier_status, ["none"]
        )

        is_rejected_lfl_outlier = AssetFilters.filter_outliers(
            asset_data_cy, ec.en_lfl_outlier_status, ["rejected"]
        )

        # Filter intensity assets for benchmarking
        is_intensity_eligible_100 = AssetFilters.filter_intensity_assets(
            asset_data_cy, Utility.Energy, 100
        )
        is_not_int_outlier = AssetFilters.filter_outliers(
            asset_data_cy, ec.en_int_outlier_status, ["none"]
        )
        is_intensity_benchmarkable = np.logical_and(
            is_intensity_eligible_100, is_not_int_outlier
        )

        is_rejected_int_outlier = AssetFilters.filter_outliers(
            asset_data_cy, ec.en_int_outlier_status, ["rejected"]
        )

        data = {
            "is_positive_ren": is_positive_ren,
            "is_not_lfl_outlier": is_not_lfl_outlier,
            "is_rejected_lfl_outlier": is_rejected_lfl_outlier,
            "is_intensity_benchmarkable": is_intensity_benchmarkable,
            "is_rejected_int_outlier": is_rejected_int_outlier,
        }

        descriptions = pd.DataFrame(data)

        return {
            "asset_data": asset_data,
            "asset_data_cy": asset_data_cy,
            "descriptions": descriptions,
        }

    def prepare_data_gh1(self, asset_data: pd.DataFrame) -> dict[str, pd.DataFrame]:
        """
        Runs the data preparation pipeline for GH1.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame with both data years.

        Returns:
        tuple[pd.DataFrame, pd.DataFrame, list[bool], list[bool]]:
            a tuple containing the prepared asset data with both years,
            the prepared asset data with current year operational data,
            a boolean list describing whether assets are outlier or not and a
            boolean list describing whether assets are LFL hard outliers or not.
        """
        # Some metrics should only be calculated for current year operational assets
        is_cy_op = AssetFilters.filter_current_year_operational_assets(asset_data)

        # Calculate asset metrics
        asset_data = (
            self.lfl_metric_calculator.add_lfl_consumption_per_control_per_utility(
                asset_data, Utility.GHG
            )
        )
        asset_data = (
            self.lfl_metric_calculator.add_previous_year_lfl_consumption_per_utility(
                asset_data, Utility.GHG
            )
        )
        asset_data = (
            self.lfl_metric_calculator.add_lfl_percent_change_per_control_per_utility(
                asset_data, Utility.GHG
            )
        )

        asset_data = IntensityCalculations.add_intensity_per_utility(
            asset_data, Utility.GHG
        )

        asset_data_cy = asset_data[is_cy_op]

        # Filter LFL outliers for benchmarking.
        is_not_outlier = AssetFilters.filter_outliers(
            asset_data_cy, gc.ghg_lfl_outlier_status, ["none"]
        )

        is_rejected_lfl_outlier = AssetFilters.filter_outliers(
            asset_data_cy, gc.ghg_lfl_outlier_status, ["rejected"]
        )

        # Filter intensity assets for benchmarking
        is_intensity_eligible_100 = AssetFilters.filter_intensity_assets(
            asset_data_cy, Utility.GHG, 100
        )
        is_not_int_outlier = AssetFilters.filter_outliers(
            asset_data_cy, gc.ghg_int_outlier_status, ["none"]
        )
        is_intensity_benchmarkable = np.logical_and(
            is_intensity_eligible_100, is_not_int_outlier
        )

        is_rejected_int_outlier = AssetFilters.filter_outliers(
            asset_data_cy, gc.ghg_int_outlier_status, ["rejected"]
        )

        data = {
            "is_not_outlier": is_not_outlier,
            "is_rejected_lfl_outlier": is_rejected_lfl_outlier,
            "is_intensity_benchmarkable": is_intensity_benchmarkable,
            "is_rejected_int_outlier": is_rejected_int_outlier,
        }

        descriptions = pd.DataFrame(data)

        return {
            "asset_data": asset_data,
            "asset_data_cy": asset_data_cy,
            "descriptions": descriptions,
        }

    @staticmethod
    def prepare_data_ws1(asset_data: pd.DataFrame) -> dict[str, pd.DataFrame]:
        """
        Runs the data preparation pipeline for WS1.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame with both data years.

        Returns:
        tuple[pd.DataFrame, pd.DataFrame, list[bool], list[bool]]:
            a tuple containing the prepared asset data with both years and
            the prepared asset data with current year operational data.
        """
        # WS1 doesn't require any metrics calculated over two data years.
        is_cy_op = AssetFilters.filter_current_year_operational_assets(asset_data)

        asset_data_cy = asset_data[is_cy_op]

        return {"asset_data": asset_data, "asset_data_cy": asset_data_cy}

    def prepare_data_wt1(self, asset_data: pd.DataFrame) -> dict[str, pd.DataFrame]:
        """
        Runs the data preparation pipeline for WT1.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame with both data years.

        Returns:
        tuple[pd.DataFrame, pd.DataFrame, list[bool], list[bool]]:
            a tuple containing the prepared asset data with both years,
            the prepared asset data with current year operational data,
            a boolean list describing whether assets are eligible for recycled water
            benchmarking, a boolean list describing whether assets are
            LFL outliers or not and a boolean list describing whether assets are
            LFL hard outliers or not.
        """
        # Some metrics should only be calculated for current year operational assets
        is_cy_op = AssetFilters.filter_current_year_operational_assets(asset_data)

        # Calculate asset metrics
        asset_data = (
            SustainableUtilityCalculations().add_recycled_water_percent_change_rate(
                asset_data
            )
        )
        asset_data = (
            self.lfl_metric_calculator.add_lfl_consumption_per_control_per_utility(
                asset_data, Utility.Water
            )
        )
        asset_data = (
            self.lfl_metric_calculator.add_previous_year_lfl_consumption_per_utility(
                asset_data, Utility.Water
            )
        )
        asset_data = (
            self.lfl_metric_calculator.add_lfl_percent_change_per_control_per_utility(
                asset_data, Utility.Water
            )
        )

        asset_data = IntensityCalculations.add_intensity_per_utility(
            asset_data, Utility.Water
        )

        asset_data_cy = asset_data[is_cy_op]
        asset_data_cy = (
            SustainableUtilityCalculations.add_recycled_water_consumption_on_site(
                asset_data_cy
            )
        )

        # Filter negative renewable percent change values for benchmarking.
        is_positive_rec = AssetFilters.filter_negative_percent_change_rates(
            asset_data_cy, "wat"
        )

        # Filter LFL outliers for benchmarking.
        is_not_outlier = AssetFilters.filter_outliers(
            asset_data_cy, wc.wat_lfl_outlier_status, ["none"]
        )

        is_rejected_lfl_outlier = AssetFilters.filter_outliers(
            asset_data_cy, wc.wat_lfl_outlier_status, ["rejected"]
        )

        # Filter intensity assets for benchmarking
        is_intensity_eligible_100 = AssetFilters.filter_intensity_assets(
            asset_data_cy, Utility.Water, 100
        )
        is_not_int_outlier = AssetFilters.filter_outliers(
            asset_data_cy, wc.wat_int_outlier_status, ["none"]
        )
        is_intensity_benchmarkable = np.logical_and(
            is_intensity_eligible_100, is_not_int_outlier
        )

        is_rejected_int_outlier = AssetFilters.filter_outliers(
            asset_data_cy, wc.wat_int_outlier_status, ["rejected"]
        )

        data = {
            "is_positive_rec": is_positive_rec,
            "is_not_outlier": is_not_outlier,
            "is_rejected_lfl_outlier": is_rejected_lfl_outlier,
            "is_intensity_benchmarkable": is_intensity_benchmarkable,
            "is_rejected_int_outlier": is_rejected_int_outlier,
        }

        descriptions = pd.DataFrame(data)

        return {
            "asset_data": asset_data,
            "asset_data_cy": asset_data_cy,
            "descriptions": descriptions,
        }

    def prepare_data_bc1(
        self,
        asset_data: pd.DataFrame,
        certification_data: pd.DataFrame,
        certification_table: pd.DataFrame,
    ) -> tuple[pd.DataFrame, pd.DataFrame, list[bool], list[bool]]:
        """
        Runs the data preparation pipeline for indicators BC1.1 and BC1.2.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame with both data years.
        certification_data (pd.DataFrame): the DataFrame with all the assets' certifications.
        certification_table (pd.DataFrame): the certifications information.

        Returns:
        tuple[pd.DataFrame, pd.DataFrame, list[bool], list[bool]: a tuple containing
            the prepared asset and certifications data, a boolean list describing
            whether the certifications type is 'operational' or not and a boolean
            list describing whether the row data is current year and operational or not.
        """
        # Filter and clean the certification data
        certification_data = (
            BuildingCertificationFilters().filter_certifications_for_scoring(
                certification_data, asset_data
            )
        )

        # Get current year operational assets data
        is_cy_op = AssetFilters.filter_current_year_operational_assets(asset_data)
        asset_data_cy = asset_data[is_cy_op]

        # Calculate building certifications metrics
        certification_data = CommonOperations.rename_covered_floor_area_column(
            certification_data
        )
        certification_data = BuildingCertificationsCalculations.add_uncertified_assets_mock_certifications(
            certification_data, asset_data_cy
        )
        certification_data = (
            BuildingCertificationsCalculations.add_covered_floor_area_m2(
                certification_data, asset_data_cy
            )
        )
        certification_data = BuildingCertificationsCalculations.get_asset_size(
            certification_data, asset_data_cy
        )
        certification_data = BuildingCertificationsCalculations.get_asset_ownership(
            certification_data, asset_data_cy
        )
        certification_data = CommonOperations.get_location_property_type_detail(
            certification_data, asset_data_cy
        )
        certification_data = BuildingCertificationsCalculations.add_data_coverage(
            certification_data
        )
        data_year = asset_data[ac.survey_year].unique()[0] - 1
        certification_data = BuildingCertificationsCalculations.add_age(
            certification_data, data_year
        )
        certification_data = BuildingCertificationsCalculations.add_time_factor(
            certification_data
        )

        certification_data = BuildingCertificationsCalculations.add_validation_status(
            certification_data, certification_table
        )

        # Get the type filter
        is_operational = BuildingCertificationFilters.filter_certifications_per_type(
            certification_data, "operational"
        )

        return (certification_data, asset_data_cy, is_operational, is_cy_op)

    def prepare_data_bc1_asset_level(
        self,
        asset_data: pd.DataFrame,
        certification_data: pd.DataFrame,
        certification_table: pd.DataFrame,
    ) -> tuple[pd.DataFrame, pd.DataFrame, list[bool], list[bool]]:
        """
        Runs the data preparation pipeline for indicators BC1.1 and BC1.2.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame with both data years.
        certification_data (pd.DataFrame): the DataFrame with all the assets' certifications.
        certification_table (pd.DataFrame): the certifications information.

        Returns:
        tuple[pd.DataFrame, pd.DataFrame, list[bool], list[bool]: a tuple containing
            the prepared asset and certifications data, a boolean list describing
            whether the certifications type is 'operational' or not and a boolean
            list describing whether the row data is current year and operational or not.
        """
        # Add usual certification calculations
        certification_data, asset_data_cy, is_operational, is_cy_op = (
            self.prepare_data_bc1(asset_data, certification_data, certification_table)
        )

        # Calculate asset metrics
        asset_data = AssetAggregationCalculation.add_certification_coverage(
            asset_data, certification_data
        )
        asset_data = AssetAggregationCalculation.add_certified_floor_area(asset_data)

        return (asset_data, certification_data, is_operational, is_cy_op)

    def prepare_data_bc1_aggregated_level(
        self,
        asset_data: pd.DataFrame,
        certification_data: pd.DataFrame,
        certification_table: pd.DataFrame,
        r1_table: pd.DataFrame,
    ) -> tuple[pd.DataFrame, pd.DataFrame]:
        """
        Runs the data preparation pipeline for indicators BC1.1 and BC1.2.

        Parameters:
            asset_data (pd.DataFrame): the asset DataFrame with both data years.
            certification_data (pd.DataFrame): the DataFrame with all the assets' certifications.
            certification_table (pd.DataFrame): the certifications information.
            r1_table (pd.DataFrame): the information from the R1 table of the survey.
        Returns:
            tuple[pd.DataFrame, pd.DataFrame]: a tuple containing
            the aggregated certification data and certifications data.
        """
        # Add usual certification calculations
        certification_data, asset_data_cy, is_operational, is_cy_op = (
            self.prepare_data_bc1(asset_data, certification_data, certification_table)
        )

        # Add some extra calculations for aggregated-level scoring
        certification_data = (
            BuildingCertificationsCalculations.add_scored_certified_area(
                certification_data
            )
        )
        certification_data = (
            BuildingCertificationsCalculations.add_owned_certified_area(
                certification_data
            )
        )
        certification_data = BuildingCertificationsCalculations.add_asset_size_owned_m2(
            certification_data
        )

        # Add the indicator for which the certification should be scored
        certification_data[bc.indicator] = np.where(is_operational, "BC1.2", "BC1.1")

        # Aggregate the certifications at the property subtype and country level
        # TODO: put all that in a function
        aggregated_certification_data = (
            certification_data.groupby(
                [ac.response_id, ac.country, ac.property_type_code, bc.indicator]
            )
            .agg(
                {
                    bc.scoring_covered_floor_area: "sum",
                    ac.asset_size_owned_m2: "sum",
                    bc.owned_covered_floor_area: "sum",
                    ac.portfolio_asset_id: lambda x: len(x.unique()),
                }
            )
            .reset_index()
        )
        aggregated_certification_data = aggregated_certification_data.rename(
            columns={ac.portfolio_asset_id: bc.unique_asset_count}
        )

        # Add PGAV to the property subtype / country scores for aggregation
        r1_table = r1_table.rename(
            columns={
                R1TableModel.RESPONSE_ID: ac.response_id,
                R1TableModel.PRT_TYPE: ac.property_type_code,
                R1TableModel.COUNTRY: ac.country,
                R1TableModel.R_1_TBL_PGAV: sc.pgav,
                R1TableModel.R_1_TBL_AREA: ac.asset_size_owned,
            }
        )
        aggregated_certification_data = aggregated_certification_data.merge(
            r1_table, on=[ac.response_id, ac.country, ac.property_type_code], how="left"
        )

        # Compute the groups' certification coverages
        aggregated_certification_data[bc.scoring_coverage] = (
            aggregated_certification_data[bc.scoring_covered_floor_area]
            / aggregated_certification_data[ac.asset_size_owned]
            * 100
        )
        aggregated_certification_data[bc.scoring_coverage] = np.minimum(
            100, aggregated_certification_data[bc.scoring_coverage]
        )

        aggregated_certification_data[bc.coverage] = (
            aggregated_certification_data[bc.owned_covered_floor_area]
            / aggregated_certification_data[ac.asset_size_owned]
            * 100
        )
        aggregated_certification_data[bc.coverage] = np.minimum(
            100, aggregated_certification_data[bc.coverage]
        )

        # Validate that there are no NAs in "coverage"
        # Validate that there are all possible country/response/subtype combinations

        # Add to the aggregated data the extra information useful for scoring and benchmarking
        location_prt_types_cols = ac.location_columns + ac.property_type_columns
        location_prt_types_cols.remove("global")
        location_prt_types_cols.append(ac.property_type_code)
        location_prt_types_cols.append(ac.country_name)
        location_prt_types_cols.append(ac.data_year)
        location_prt_types_cols.append(ac.survey_year)
        location_prt_types = asset_data_cy[location_prt_types_cols].drop_duplicates()

        aggregated_certification_data = aggregated_certification_data.merge(
            location_prt_types, on=[ac.country, ac.property_type_code], how="left"
        )

        # Add a unique ID to aggregated certification data in order to benchmark them
        aggregated_certification_data[bc.building_data_certifications_scoring_id] = (
            list(range(1, len(aggregated_certification_data.index) + 1))
        )

        return aggregated_certification_data, certification_data

    def prepare_data_bc2(
        self, asset_data: pd.DataFrame, energy_ratings_data: pd.DataFrame
    ) -> tuple[pd.DataFrame, pd.DataFrame, list[bool]]:
        """
        Runs the data preparation pipeline for BC2.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame with both data years.
        energy_ratings_data (pd.DataFrame): the energy ratings DataFrame.

        Returns:
        tuple[pd.DataFrame, pd.DataFrame, list[bool]]: a tuple containing the
            prepared asset and energy ratings DataFrames and a boolean list
            describing whether row data is current year and operational data.
        """
        # Only keep ratings belonging to present assets
        is_performance_rating = CommonOperations.filter_active_certifications(
            energy_ratings_data, asset_data
        )
        energy_ratings_data = energy_ratings_data[is_performance_rating]

        # Get current year operational assets data
        is_cy_op = AssetFilters.filter_current_year_operational_assets(asset_data)
        asset_data_cy = asset_data[is_cy_op]

        # Calculate coverage per rating
        energy_ratings_data = CommonOperations.rename_covered_floor_area_column(
            energy_ratings_data
        )
        energy_ratings_data = EnergyRatingsCalculations.get_asset_ownership(
            energy_ratings_data, asset_data_cy
        )
        energy_ratings_data = EnergyRatingsCalculations.get_asset_size(
            energy_ratings_data, asset_data_cy
        )
        energy_ratings_data = CommonOperations.get_location_property_type_detail(
            energy_ratings_data, asset_data_cy
        )
        energy_ratings_data = EnergyRatingsCalculations.add_covered_floor_area_m2(
            energy_ratings_data, asset_data_cy
        )
        energy_ratings_data = EnergyRatingsCalculations.add_data_coverage(
            energy_ratings_data
        )
        # Calculate asset metrics
        asset_data = AssetAggregationCalculation.add_rating_coverage(
            asset_data, energy_ratings_data
        )
        asset_data = AssetAggregationCalculation.add_rated_floor_area(asset_data)

        return asset_data, energy_ratings_data, is_cy_op

    @staticmethod
    def calculate_asset_level_metrics(asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate all asset-level metrics for utilities for both scoring and reporting.

        Parameters:
            asset_data (pd.DataFrame): the asset DataFrame with both data years.

        Returns:
            pd.DataFrame: a DataFrame with all asset-level metrics.
        """
        asset_data = (
            AssetCharacteristicsCalculations().add_asset_characteristic_calculations(
                asset_data
            )
        )
        asset_data = ConsumptionCalculations().add_consumption_metrics(asset_data)
        asset_data = CoverageCalculations().add_coverage_metrics(asset_data)
        asset_data = IntensityCalculations().add_intensity_metrics(asset_data)
        asset_data = LFLCalculations().add_lfl_metrics(asset_data)
        asset_data = (
            SustainableUtilityCalculations().add_sustainable_utility_calculations(
                asset_data
            )
        )

        return asset_data
