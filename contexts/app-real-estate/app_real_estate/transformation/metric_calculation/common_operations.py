import numpy as np
import pandas as pd
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.asset_characteristics as asc_const
import app_real_estate.constants.column_names.key_columns as key_columns
import app_real_estate.constants.column_names.building_certification_energy_rating_common_columns as bc_er_common


class CommonOperations:
    @staticmethod
    def get_asset_size(
        left_data: pd.DataFrame, right_data: pd.DataFrame, key: str
    ) -> pd.DataFrame:
        """
        Merge the asset size columns from right_data into left_data based on the specified key.

        Args:
            left_data (pd.DataFrame): The left DataFrame to merge into.
            right_data (pd.DataFrame): The right DataFrame containing the asset size columns.
            key (str): The column to use as the merge key.

        Returns:
            pd.DataFrame: The merged DataFrame with the asset size columns added to left_data.
        """
        columns = [ac.asset_size, ac.asset_size_m2, key]

        return pd.merge(left_data, right_data[columns], on=key, how="left")

    @staticmethod
    def add_covered_floor_area_m2(
        data: pd.DataFrame, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Add the covered floor area in m2 to the asset data.
        Common between BuildingCertificationCalculation and EnergyRatingCalculation.

        Args:
            data: The building certification or energy rating data.
            asset_data: The asset data.

        Returns:
            pd.DataFrame: The asset data with the covered floor area in m2 added and
            area_unit
        """
        if not all(asset_data[ac.area_unit].isin([asc_const.m2, asc_const.sqft])):
            raise ValueError(
                "filter_asset_is_unit: area_unit should be one of 'm2' or 'sqft', cite app_real_estate.constants.asset_characteristics."
            )

        merged = pd.merge(
            data,
            asset_data[[ac.portfolio_asset_id, ac.area_unit]],
            on=key_columns.portfolio_asset_id,
            how="left",
        )

        data[bc_er_common.covered_floor_area_m2] = np.where(
            merged[ac.area_unit] == asc_const.m2,
            merged[bc_er_common.covered_floor_area],
            merged[bc_er_common.covered_floor_area] * asc_const.sqft_to_sqm,
        )

        return data

    @staticmethod
    def rename_covered_floor_area_column(
        data: pd.DataFrame,
    ) -> pd.DataFrame:
        """
        Renames the column `size` to `covered_floor_area`.

        Parameters:
        certification_data (pd.DataFrame): a DataFrame containing the building certifications.

        Returns:
        pd.DataFrame: the input DataFrame with the renamed column.
        """

        return data.rename(columns={"size": bc_er_common.covered_floor_area})

    @staticmethod
    def filter_active_certifications(
        data: pd.DataFrame, asset_data: pd.DataFrame
    ) -> list[bool]:
        """
        Filters the certifications or energy ratings based on snapshots present
        in the asset data.

        Parameters:
        data (pd.DataFrame): contains the certifications or energy ratings data.
        asset_data (pd.DataFrame): contains the performance asset data.

        Returns:
        list[bool]: boolean list indicating which certification or rating belongs
            to an active performance asset.
        """
        active_snapshot_ids = asset_data[ac.snapshot_id].unique()
        active_asset_ids = asset_data[ac.portfolio_asset_id].unique()

        return np.logical_and(
            data[key_columns.snapshot_id].isin(active_snapshot_ids),
            data[key_columns.portfolio_asset_id].isin(active_asset_ids),
        )

    @staticmethod
    def get_location_property_type_detail(
        certification_data: pd.DataFrame, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Get the country, region, property subtype,
        property type and property sector of the building certification with
        merge.

        Parameters:
        certification_data (pd.DataFrame): a DataFrame containing the building certifications.
        asset_data (pd.DataFrame): a DataFrame containing the asset data.

        Returns:
        pd.DataFrame: a DataFrame with the merged data including country, region, property subtype,
                      property type, and property sector.
        """
        key = bc_er_common.portfolio_asset_id

        columns = [
            ac.country,
            ac.country_name,
            ac.sub_region,
            ac.region,
            ac.super_region,
            ac.property_type_code,
            ac.property_subtype,
            ac.property_type,
            ac.property_sector,
            key,
        ]

        return pd.merge(certification_data, asset_data[columns], on=key, how="left")

    @staticmethod
    def get_asset_ownership(
        left_data: pd.DataFrame, right_data: pd.DataFrame, key: str
    ) -> pd.DataFrame:
        """
        Merge the asset ownership from right_data into left_data based on the specified key.

        Args:
            left_data (pd.DataFrame): The left DataFrame to merge into.
            right_data (pd.DataFrame): The right DataFrame containing the asset size columns.
            key (str): The column to use as the merge key.

        Returns:
            pd.DataFrame: The merged DataFrame with the asset ownership added to left_data.
        """
        columns = [ac.asset_ownership]
        columns.append(key)

        return pd.merge(left_data, right_data[columns], on=key, how="left")

    @staticmethod
    def add_previous_year_variables(
        asset_data: pd.DataFrame, cy_cols: list[str], ly_cols: list[str]
    ) -> pd.DataFrame:
        """
        Add the data in the "previous year" rows of the asset data into
        the "current year" rows.

        Parameters:
            asset_data (pd.DataFrame): a DataFrame containing the asset data with both years.
            cy_cols (list[str]): a list of columns names of the columns being copied.
            ly_cols (list[str]): a list of columns names of the columns being added.
        Returns:
            pd.DataFrame: the merged DataFrame with the asset data added.
        """
        if any(col in asset_data.columns for col in ly_cols) or any(
            not col in asset_data.columns for col in cy_cols
        ):
            return asset_data

        survey_year = asset_data[ac.survey_year].unique()[0]
        is_current_year = asset_data[ac.data_year] == survey_year - 1
        ly_data = asset_data.loc[
            ~is_current_year, [ac.portfolio_asset_id, ac.data_year, cy_cols]
        ]
        ly_data = ly_data.rename(columns=dict(zip(cy_cols, ly_cols)))
        ly_data[ac.data_year] = survey_year - 1

        result = asset_data.merge(
            ly_data, on=[ac.portfolio_asset_id, ac.data_year], how="left"
        )

        return result
