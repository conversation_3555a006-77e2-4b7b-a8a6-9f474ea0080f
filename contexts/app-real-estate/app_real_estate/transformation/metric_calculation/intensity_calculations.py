import pandas as pd
import numpy as np
from app_real_estate.constants.helper_enumerators import Utility
from app_real_estate.models.input_models.weather_stations_model import (
    WeatherStationsModel,
)
from app_real_estate.transformation.metric_calculation.asset_filters import (
    AssetFilters as af,
)
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.water_columns as wc
import app_real_estate.constants.column_names.ghg_columns as gc
import app_real_estate.constants.column_names.score_columns as sc


class IntensityCalculations:

    def add_intensity_metrics(self, asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate the intensity metrics for all utilities.

        Parameters:
            asset_data (pd.DataFrame): the asset data.
        Returns:
            pd.DataFrame: the asset data with the intensity metrics added.
        """
        asset_data = self.add_intensities(asset_data)

        # For aggregation
        asset_data = self.add_int_eligibility(asset_data)
        asset_data = self.add_vacancy_rate_for_intensity_assets(asset_data)
        asset_data = self.add_asset_size_for_intensity_assets(asset_data)
        asset_data = self.add_intensity_columns_without_outliers(asset_data)

        return asset_data

    def add_intensity_metrics_per_utility(
        self, asset_data: pd.DataFrame, utility: Utility
    ) -> pd.DataFrame:
        """
        Calculate the intensity metrics for the given utility.

        Parameters:
            asset_data (pd.DataFrame): the asset data.
            utility (Utility): the utility for which to calculate the intensity metrics.
        Returns:
            pd.DataFrame: the asset data with the intensity metrics added.
        """
        asset_data = self.add_intensity_per_utility(asset_data, utility)

        # For aggregation
        asset_data = self.add_int_eligibility_per_utility(asset_data, utility)
        asset_data = self.add_vacancy_rate_for_intensity_assets_per_utility(
            asset_data, utility
        )
        asset_data = self.add_asset_size_for_intensity_assets_per_utility(
            asset_data, utility
        )
        asset_data = self.add_intensity_columns_without_outliers_per_utility(
            asset_data, utility
        )

        return asset_data

    def add_intensities(self, asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate the intensities in m2 and sqft.

        Parameters:
            asset_data (pd.DataFrame): the asset data.
        Returns:
            pd.DataFrame: the asset data with the new variables.
        """
        asset_data = self.add_intensity_per_utility(asset_data, Utility.Energy)
        asset_data = self.add_intensity_per_utility(asset_data, Utility.GHG)
        asset_data = self.add_intensity_per_utility(asset_data, Utility.Water)

        return asset_data

    @staticmethod
    def add_intensity_per_utility(
        asset_data: pd.DataFrame, utility: Utility
    ) -> pd.DataFrame:
        """
        Calculate the intensities in m2 and sqft per utility.

        Parameters:
            asset_data (pd.DataFrame): the asset data.
            utility (Utility): the utility for which to compute the intensities.
        Returns:
            pd.DataFrame: the asset data with the new variables.
        """
        is_eligible_for_intensity_scoring = af.filter_intensity_assets(
            asset_data, utility, 75
        )

        int_m2_column, int_sqft_column, indoor_consumption_column, coverage_column = {
            Utility.Energy: (
                ec.en_efficiency_int_kwh_m2,
                ec.en_efficiency_int_kwh_sqft,
                ec.en_abs_in,
                ec.en_area_time_cov_p,
            ),
            Utility.GHG: (
                gc.ghg_scored_int_ton_m2,
                gc.ghg_scored_int_ton_sqft,
                gc.ghg_abs_in,
                gc.ghg_area_time_cov_p,
            ),
            Utility.Water: (
                wc.wat_scored_int_m3_m2,
                wc.wat_scored_int_m3_sqft,
                wc.wat_abs_in,
                wc.wat_area_time_cov_p,
            ),
        }[utility]

        def _compute_intensity(cons_col: str, cov_col: str, is_m2=True) -> pd.Series:
            floor_area_column = ac.asset_size_m2 if is_m2 else ac.asset_size_sqft

            return (
                asset_data[cons_col]
                / asset_data[floor_area_column]
                / (asset_data[cov_col] / 100)
            )

        asset_data[int_m2_column] = np.where(
            is_eligible_for_intensity_scoring,
            _compute_intensity(indoor_consumption_column, coverage_column),
            np.nan,
        )

        asset_data[int_sqft_column] = np.where(
            is_eligible_for_intensity_scoring,
            _compute_intensity(indoor_consumption_column, coverage_column, False),
            np.nan,
        )

        if utility == Utility.Water:
            asset_data[wc.wat_scored_int_dm3_m2] = np.where(
                is_eligible_for_intensity_scoring,
                _compute_intensity(indoor_consumption_column, coverage_column).mul(
                    1000
                ),
                np.nan,
            )

            asset_data[wc.wat_scored_int_dm3_sqft] = np.where(
                is_eligible_for_intensity_scoring,
                _compute_intensity(
                    indoor_consumption_column, coverage_column, False
                ).mul(1000),
                np.nan,
            )

        return asset_data

    @staticmethod
    def add_climate_zones(
        asset_data: pd.DataFrame, weather_stations: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Map the climate zones associated to the assets' weather stations.

        Parameters:
            asset_data (pd.DataFrame): the annual asset-level data.
            weather_stations (pd.DataFrame): the snapshot of the Portal `asset_portal_ashrae_weather_stations` table.

        Returns:
            pd.DataFrame: the asset data with the associated climate zones.
        """
        asset_data = asset_data.merge(
            weather_stations,
            left_on=ac.nearest_ashrae_weather_station_id,
            right_on=WeatherStationsModel.id,
            how="left",
        )
        asset_data = asset_data.drop(WeatherStationsModel.id, axis=1)
        return asset_data

    @staticmethod
    def add_ashrae_thresholds(
        asset_data: pd.DataFrame, thresholds: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Add an ASHRAE thresholds to all assets based on climate zone and property subtype.
        Parameters:
            asset_data (pd.DataFrame): the asset data.
            thresholds (pd.DataFrame): the ASHRAE thresholds distributed per climate zone and property subtype.
        Returns:
            pd.DataFrame: the asset data with the new ASHRAE thresholds.
        """
        asset_data = asset_data.merge(
            thresholds,
            on=[ac.climate_zone, ac.property_type_code],
            how="left",
        )

        return asset_data

    # The following calculations are for aggregation.

    def add_vacancy_rate_for_intensity_assets(
        self, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Add vacancy rates for assets eligible for Energy efficiency (75% and
        100% coverage) and other intensities.

        Parameters:
        asset_data (pd.DataFrame): the asset data.

        Returns:
        pd.DataFrame: the input data with the added columns for vacancy rates.
        """
        asset_data = self.add_vacancy_rate_for_intensity_assets_per_utility(
            asset_data, Utility.Energy
        )
        asset_data = self.add_vacancy_rate_for_intensity_assets_per_utility(
            asset_data, Utility.GHG
        )
        asset_data = self.add_vacancy_rate_for_intensity_assets_per_utility(
            asset_data, Utility.Water
        )

        return asset_data

    @staticmethod
    def add_vacancy_rate_for_intensity_assets_per_utility(
        asset_data: pd.DataFrame, utility: Utility
    ) -> pd.DataFrame:
        """
        Add vacancy rates for assets eligible for intensity aggregation, for the given utility.

        Parameters:
        asset_data (pd.DataFrame): the asset data.
        utility (Utility): the utility to calculate the vacancy rate for.

        Returns:
        pd.DataFrame: the input data with the added columns for vacancy rates.
        """
        is_intensity_eligible = af().filter_intensity_metric_for_aggregation(
            asset_data, utility
        )

        vacancy_column = {
            Utility.Energy: ec.asset_vacancy_energy_intensity,
            Utility.Water: wc.asset_vacancy_water_intensity,
            Utility.GHG: gc.asset_vacancy_ghg_intensity,
        }[utility]

        result = asset_data.copy()
        result[vacancy_column] = np.where(
            is_intensity_eligible, asset_data[ac.asset_vacancy], np.nan
        )

        return result

    def add_asset_size_for_intensity_assets(
        self, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Add asset size for assets eligible for intensity scoring for the given utility.

        Parameters:
        asset_data (pd.DataFrame): the asset data.
        utility (Utility): the utility to calculate the asset size for.

        Returns:
        pd.DataFrame: the input data with the added columns for vacancy rates.
        """
        asset_data = self.add_asset_size_for_intensity_assets_per_utility(
            asset_data, Utility.Energy
        )
        asset_data = self.add_asset_size_for_intensity_assets_per_utility(
            asset_data, Utility.GHG
        )
        asset_data = self.add_asset_size_for_intensity_assets_per_utility(
            asset_data, Utility.Water
        )

        return asset_data

    @staticmethod
    def add_asset_size_for_intensity_assets_per_utility(
        asset_data: pd.DataFrame, utility: Utility
    ) -> pd.DataFrame:
        """
        Add vacancy rates for assets eligible for Energy efficiency (75% and
        100% coverage) and other intensities.

        Parameters:
        asset_data (pd.DataFrame): the asset data.
        utility (Utility): the utility to calculate the asset size for.

        Returns:
        pd.DataFrame: the input data with the added columns for vacancy rates.
        """
        is_intensity_eligible = af().filter_intensity_metric_for_aggregation(
            asset_data, utility
        )

        (
            asset_size_m2_column,
            asset_size_sqft_column,
        ) = {
            Utility.Energy: (
                ec.asset_size_energy_intensity_m2,
                ec.asset_size_energy_intensity_sqft,
            ),
            Utility.Water: (
                wc.asset_size_water_intensity_m2,
                wc.asset_size_water_intensity_sqft,
            ),
            Utility.GHG: (
                gc.asset_size_ghg_intensity_m2,
                gc.asset_size_ghg_intensity_sqft,
            ),
        }[utility]

        m2_asset_size_owned = (
            asset_data[ac.asset_size_m2] * asset_data[ac.asset_ownership] / 100
        )
        sqft_asset_size_owned = (
            asset_data[ac.asset_size_sqft] * asset_data[ac.asset_ownership] / 100
        )

        if utility == Utility.Energy:
            is_ee_scored = ~asset_data[sc.score_en1_energy_efficiency].isna()
            asset_data[ec.energy_efficiency_area_m2] = np.where(
                is_ee_scored, m2_asset_size_owned, np.nan
            )
            asset_data[ec.energy_efficiency_area_sqft] = np.where(
                is_ee_scored, sqft_asset_size_owned, np.nan
            )

        asset_data[asset_size_m2_column] = np.where(
            is_intensity_eligible, m2_asset_size_owned, np.nan
        )

        asset_data[asset_size_sqft_column] = np.where(
            is_intensity_eligible, sqft_asset_size_owned, np.nan
        )

        return asset_data

    def add_intensity_columns_without_outliers(
        self, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Add new intensity columns where outliers' values are removed.

        Parameters:
        asset_data (pd.DataFrame): the asset data.

        Returns:
        pd.DataFrame: the input data with the added column.
        """
        asset_data = self.add_intensity_columns_without_outliers_per_utility(
            asset_data, Utility.Energy
        )
        asset_data = self.add_intensity_columns_without_outliers_per_utility(
            asset_data, Utility.GHG
        )
        asset_data = self.add_intensity_columns_without_outliers_per_utility(
            asset_data, Utility.Water
        )

        return asset_data

    @staticmethod
    def add_intensity_columns_without_outliers_per_utility(
        asset_data: pd.DataFrame, utility: Utility
    ) -> pd.DataFrame:
        """
        Add a new intensity column where outliers' values are removed per utility.

        Parameters:
        asset_data (pd.DataFrame): the asset data.
        utility (Utility): the utility to get the intensity for.

        Returns:
        pd.DataFrame: the input data with the added column.
        """
        (
            int_m2_col,
            int_sqft_col,
            accepted_int_m2_col,
            accepted_int_sqft_col,
        ) = {
            Utility.Energy: (
                ec.en_efficiency_int_kwh_m2,
                ec.en_efficiency_int_kwh_sqft,
                ec.en_efficiency_int_kwh_m2_accepted,
                ec.en_efficiency_int_kwh_sqft_accepted,
            ),
            Utility.Water: (
                wc.wat_scored_int_m3_m2,
                wc.wat_scored_int_m3_sqft,
                wc.wat_scored_int_m3_m2_accepted,
                wc.wat_scored_int_m3_sqft_accepted,
            ),
            Utility.GHG: (
                gc.ghg_scored_int_ton_m2,
                gc.ghg_scored_int_ton_sqft,
                gc.ghg_scored_int_ton_m2_accepted,
                gc.ghg_scored_int_ton_sqft_accepted,
            ),
        }[utility]

        is_int_agg_eligible = af().filter_intensity_metric_for_aggregation(
            asset_data, utility
        )

        asset_data[accepted_int_m2_col] = np.where(
            is_int_agg_eligible, asset_data[int_m2_col], np.nan
        )
        asset_data[accepted_int_sqft_col] = np.where(
            is_int_agg_eligible, asset_data[int_sqft_col], np.nan
        )

        if utility == Utility.Water:
            asset_data[wc.wat_scored_int_dm3_m2_accepted] = np.where(
                is_int_agg_eligible, asset_data[wc.wat_scored_int_dm3_m2], np.nan
            )
            asset_data[wc.wat_scored_int_dm3_sqft_accepted] = np.where(
                is_int_agg_eligible, asset_data[wc.wat_scored_int_dm3_sqft], np.nan
            )
        elif utility == Utility.GHG:
            asset_data[gc.ghg_scored_int_kg_m2_accepted] = (
                asset_data[gc.ghg_scored_int_ton_m2_accepted] * 1000
            )
            asset_data[gc.ghg_scored_int_kg_sqft_accepted] = (
                asset_data[gc.ghg_scored_int_ton_sqft_accepted] * 1000
            )

        return asset_data

    def add_int_eligibility(self, asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Add intensity eligibility columns for each utility and type of eligibility.

        Parameters:
        asset_data (pd.DataFrame): the asset data.

        Returns:
        pd.DataFrame: the input data with the added columns.
        """
        asset_data = self.add_int_eligibility_per_utility(asset_data, Utility.Energy)
        asset_data = self.add_int_eligibility_per_utility(asset_data, Utility.GHG)
        asset_data = self.add_int_eligibility_per_utility(asset_data, Utility.Water)

        return asset_data

    @staticmethod
    def add_int_eligibility_per_utility(
        asset_data: pd.DataFrame, utility: Utility
    ) -> pd.DataFrame:
        """
        Add intensity eligibility columns for the specified utility and type of eligibility.

        Parameters:
        asset_data (pd.DataFrame): the asset data.
        utility (Utility): the utility to get the calculation for.

        Returns:
        pd.DataFrame: the input data with the added columns.
        """
        int_eligible_column = {
            Utility.Energy: ec.en_int_eligible,
            Utility.GHG: gc.ghg_int_eligible,
            Utility.Water: wc.wat_int_eligible,
        }[utility]

        if utility == Utility.Energy:
            asset_data[ec.energy_efficiency_score_eligible] = ~asset_data[
                sc.score_en1_energy_efficiency
            ].isna()

        asset_data[int_eligible_column] = af().filter_intensity_metric_for_aggregation(
            asset_data, utility
        )

        return asset_data
