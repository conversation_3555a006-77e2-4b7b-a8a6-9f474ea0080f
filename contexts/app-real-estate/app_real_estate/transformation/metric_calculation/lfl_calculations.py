import numpy as np
import pandas as pd

from app_real_estate.constants.helper_enumerators import Utility
from app_real_estate.transformation.metric_calculation.asset_filters import (
    AssetFilters as af,
)
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.water_columns as wc
import app_real_estate.constants.column_names.ghg_columns as gc


class LFLCalculations:

    def add_lfl_metrics(self, asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Performs all the LFL related calculations.

        Parameters:
            asset_data (pd.DataFrame): the asset data with both years.
        Returns:
        (pd.DataFrame): the asset data with all LFL metrics added.
        """
        asset_data = self.add_energy_lfl_consumption_per_subspaces(asset_data)
        asset_data = self.add_lfl_consumption_per_control(asset_data)
        asset_data = self.add_previous_year_lfl_consumption(asset_data)
        asset_data = self.add_lfl_percent_change_per_control(asset_data)

        # For aggregation
        asset_data = self.add_lfl_aggregation_abs_change(asset_data)
        asset_data = self.add_lfl_aggregation_abs_change_per_control(asset_data)
        asset_data = self.add_lfl_eligibility(asset_data)
        asset_data = self.add_lfl_area(asset_data)
        asset_data = self.add_lfl_aggregation_abs_cons(asset_data)

        return asset_data

    def add_lfl_metrics_per_utility(
        self, asset_data: pd.DataFrame, utility: Utility
    ) -> pd.DataFrame:
        """
        Performs all the LFL related calculations for the given utility.

        Parameters:
            asset_data (pd.DataFrame): the asset data with both years.
            utility (Utility): the utility for which to calcuate the LFL metrics.
        Returns:
        (pd.DataFrame): the asset data with all LFL metrics added.
        """
        if utility == Utility.Energy:
            asset_data = self.add_energy_lfl_consumption_per_subspaces(asset_data)
        asset_data = self.add_lfl_consumption_per_control_per_utility(
            asset_data, utility
        )
        asset_data = self.add_previous_year_lfl_consumption_per_utility(
            asset_data, utility
        )
        asset_data = self.add_lfl_percent_change_per_control_per_utility(
            asset_data, utility
        )

        # For aggregation
        asset_data = self.add_lfl_aggregation_abs_change_per_utility(
            asset_data, utility
        )
        asset_data = self.add_lfl_aggregation_abs_change_per_control_per_utility(
            asset_data, utility
        )
        asset_data = self.add_lfl_eligibility_per_utility(asset_data, utility)
        asset_data = self.add_lfl_area_per_utility(asset_data, utility)
        asset_data = self.add_lfl_aggregation_abs_cons_per_utility(asset_data, utility)

        return asset_data

    def add_energy_lfl_consumption_per_subspaces(
        self, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        # Define the columns to sum per control per area.
        energy_tc_tenant_spaces_lfl_columns = [
            ec.en_lfl_abs_tc_td,
            ec.en_lfl_abs_tc_te,
            ec.en_lfl_abs_tc_tf,
        ]

        energy_lc_tenant_spaces_lfl_columns = [
            ec.en_lfl_abs_lc_td,
            ec.en_lfl_abs_lc_te,
            ec.en_lfl_abs_lc_tf,
        ]

        energy_tc_outdoor_areas_lfl_columns = [
            ec.en_lfl_abs_tc_oe,
            ec.en_lfl_abs_tc_of,
        ]

        energy_lc_outdoor_areas_lfl_columns = [
            ec.en_lfl_abs_lc_oe,
            ec.en_lfl_abs_lc_of,
        ]

        energy_bc_lfl_columns = [
            ec.en_lfl_abs_lc_bcd,
            ec.en_lfl_abs_lc_bce,
            ec.en_lfl_abs_lc_bcf,
        ]

        energy_bs_lfl_columns = [
            ec.en_lfl_abs_lc_bsd,
            ec.en_lfl_abs_lc_bse,
            ec.en_lfl_abs_lc_bsf,
        ]

        # For energy and water, sum the consumption columns.
        asset_data[ec.en_lfl_abs_tc_t] = asset_data[
            energy_tc_tenant_spaces_lfl_columns
        ].sum(axis=1, min_count=1)
        asset_data[ec.en_lfl_abs_lc_t] = asset_data[
            energy_lc_tenant_spaces_lfl_columns
        ].sum(axis=1, min_count=1)
        asset_data[ec.en_lfl_abs_tc_o] = asset_data[
            energy_tc_outdoor_areas_lfl_columns
        ].sum(axis=1, min_count=1)
        asset_data[ec.en_lfl_abs_lc_o] = asset_data[
            energy_lc_outdoor_areas_lfl_columns
        ].sum(axis=1, min_count=1)
        asset_data[ec.en_lfl_abs_lc_bc] = asset_data[energy_bc_lfl_columns].sum(
            axis=1, min_count=1
        )
        asset_data[ec.en_lfl_abs_lc_bs] = asset_data[energy_bs_lfl_columns].sum(
            axis=1, min_count=1
        )

        return asset_data

    def add_lfl_consumption_per_control(
        self,
        asset_data: pd.DataFrame,
    ) -> pd.DataFrame:
        """
        Adds the absolute consumption per control for energy, GHG and water
        filtered for Like-For-Like calculation.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame with both data years
            containing utility and asset characteristics columns.

        Returns:
        pd.DataFrame: The input DataFrame with the absolute consumption columns
            per control filtered for LFL calculation.
        """
        asset_data = self.add_energy_lfl_consumption_per_control(asset_data)
        asset_data = self.add_ghg_lfl_consumption_per_scope(asset_data)
        asset_data = self.add_water_lfl_consumption_per_control(asset_data)

        return asset_data

    def add_lfl_consumption_per_control_per_utility(
        self, asset_data: pd.DataFrame, utility: Utility
    ) -> pd.DataFrame:
        lfl_cons_func = {
            Utility.Energy: self.add_energy_lfl_consumption_per_control,
            Utility.GHG: self.add_ghg_lfl_consumption_per_scope,
            Utility.Water: self.add_water_lfl_consumption_per_control,
        }[utility]

        asset_data = lfl_cons_func(asset_data)

        return asset_data

    @staticmethod
    def add_energy_lfl_consumption_per_control(
        asset_data: pd.DataFrame,
    ) -> pd.DataFrame:
        """
        Adds the absolute consumption per control for energy filtered for
        Like-For-Like calculation.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame with both data years
            containing utility and asset characteristics columns.

        Returns:
        pd.DataFrame: The input DataFrame with the absolute consumption columns
            per control filtered for LFL calculation.
        """
        # The composition of the LC/TC LFL consumption metrics depends on whether
        # the asset is reported as whole building or by subspaces (see Asset Spreadsheet).
        is_whole_building_en = af.filter_whole_building_assets_per_utility(
            asset_data, Utility.Energy
        )
        is_whole_tenant = asset_data[ac.tenant_ctrl].astype(bool)

        # For example here, if the asset is reported as whole building then the
        # LC LFL consumption is the sum of the outdoor consumptions. Add to that
        # the indoor consumption if the building is not entirely controlled by
        # tenant.
        sum_whole_building_en_lc = asset_data[ec.en_lfl_abs_lc_o].copy()
        sum_whole_building_en_lc[~is_whole_tenant] = sum_whole_building_en_lc[
            ~is_whole_tenant
        ].add(asset_data.loc[~is_whole_tenant, ec.en_lfl_abs_in], fill_value=0)

        sum_whole_building_en_tc = asset_data[ec.en_lfl_abs_tc_o].copy()
        sum_whole_building_en_tc[is_whole_tenant] = sum_whole_building_en_tc[
            is_whole_tenant
        ].add(asset_data.loc[is_whole_tenant, ec.en_lfl_abs_in], fill_value=0)

        sum_subspaces_en_lc = asset_data[
            [
                ec.en_lfl_abs_lc_bs,
                ec.en_lfl_abs_lc_bc,
                ec.en_lfl_abs_lc_t,
                ec.en_lfl_abs_lc_o,
            ]
        ].sum(min_count=1, axis=1)
        sum_subspaces_en_tc = asset_data[
            [
                ec.en_lfl_abs_tc_t,
                ec.en_lfl_abs_tc_o,
            ]
        ].sum(min_count=1, axis=1)

        # Assign the values to LFL consumption per control metrics based on
        # the reporting method of the assets.
        asset_data[ec.en_lfl_abs_lc] = np.where(
            is_whole_building_en, sum_whole_building_en_lc, sum_subspaces_en_lc
        )
        asset_data[ec.en_lfl_abs_tc] = np.where(
            is_whole_building_en, sum_whole_building_en_tc, sum_subspaces_en_tc
        )

        return asset_data

    @staticmethod
    def add_ghg_lfl_consumption_per_scope(asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Adds the absolute consumption per control for GHG filtered for
        Like-For-Like calculation.

        Parameters:
            asset_data (pd.DataFrame): the asset DataFrame with both data years
            containing utility and asset characteristics columns.

        Returns:
            pd.DataFrame: The input DataFrame with the absolute consumption columns
            per control filtered for LFL calculation.
        """
        # GHG is much less dramatic as there is no area control separation.
        asset_data[gc.ghg_lfl_abs_s12] = asset_data[
            [
                gc.ghg_lfl_abs_s1_w,
                gc.ghg_lfl_abs_s1_o,
                gc.ghg_lfl_abs_s2_lb_w,
                gc.ghg_lfl_abs_s2_lb_o,
            ]
        ].sum(axis=1)

        asset_data[gc.ghg_lfl_abs_s3] = asset_data[
            [gc.ghg_lfl_abs_s3_w, gc.ghg_lfl_abs_s3_o]
        ].sum(axis=1)

        return asset_data

    @staticmethod
    def add_water_lfl_consumption_per_control(asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Adds the absolute consumption per control for Water filtered for
        Like-For-Like calculation.

        Parameters:
            asset_data (pd.DataFrame): the asset DataFrame with both data years
            containing utility and asset characteristics columns.

        Returns:
            pd.DataFrame: The input DataFrame with the absolute consumption columns
            per control filtered for LFL calculation.
        """
        # The composition of the LC/TC LFL consumption metrics depends on whether
        # the asset is reported as whole building or by subspaces (see Asset Spreadsheet).
        is_whole_building_wat = af.filter_whole_building_assets_per_utility(
            asset_data, Utility.Water
        )
        is_whole_tenant = asset_data[ac.tenant_ctrl].astype(bool)

        # For example here, if the asset is reported as whole building then the
        # LC LFL consumption is the sum of the outdoor consumptions. Add to that
        # the indoor consumption if the building is not entirely controlled by
        # tenant.
        sum_whole_building_wat_lc = asset_data[wc.wat_lfl_abs_lc_o].copy()
        sum_whole_building_wat_lc[~is_whole_tenant] = sum_whole_building_wat_lc[
            ~is_whole_tenant
        ].add(asset_data.loc[~is_whole_tenant, wc.wat_lfl_abs_in], fill_value=0)

        sum_whole_building_wat_tc = asset_data[wc.wat_lfl_abs_tc_o].copy()
        sum_whole_building_wat_tc[is_whole_tenant] = sum_whole_building_wat_tc[
            is_whole_tenant
        ].add(asset_data.loc[is_whole_tenant, wc.wat_lfl_abs_in], fill_value=0)

        sum_subspaces_wat_lc = asset_data[
            [
                wc.wat_lfl_abs_lc_bs,
                wc.wat_lfl_abs_lc_bc,
                wc.wat_lfl_abs_lc_t,
                wc.wat_lfl_abs_lc_o,
            ]
        ].sum(min_count=1, axis=1)
        sum_subspaces_wat_tc = asset_data[
            [wc.wat_lfl_abs_tc_t, wc.wat_lfl_abs_tc_o]
        ].sum(min_count=1, axis=1)

        asset_data[wc.wat_lfl_abs_lc] = np.where(
            is_whole_building_wat,
            sum_whole_building_wat_lc,
            sum_subspaces_wat_lc,
        )
        asset_data[wc.wat_lfl_abs_tc] = np.where(
            is_whole_building_wat,
            sum_whole_building_wat_tc,
            sum_subspaces_wat_tc,
        )

        return asset_data

    def add_previous_year_lfl_consumption(
        self,
        asset_data: pd.DataFrame,
    ) -> pd.DataFrame:
        """
        Calculates, for each asset eligible to LFL, the consumption of energy, GHG
        and water of this asset the previous year. The consumption is calculated
        on different subspaces and per control: tenant/landlord controlled tenant
        spaces and outdoor areas.

        Parameters:
        asset_data (pd.DataFrame): contains the asset information and data for
            both data years.

        Returns:
        pd.DataFrame: The input DataFrame with the previous year LFL consumption
        metrics only on current year rows.
        """
        asset_data = self.add_previous_year_lfl_consumption_per_utility(
            asset_data, Utility.Energy
        )
        asset_data = self.add_previous_year_lfl_consumption_per_utility(
            asset_data, Utility.Water
        )
        asset_data = self.add_previous_year_lfl_consumption_per_utility(
            asset_data, Utility.GHG
        )

        return asset_data

    @staticmethod
    def add_previous_year_lfl_consumption_per_utility(
        asset_data: pd.DataFrame, utility: Utility
    ) -> pd.DataFrame:
        """
        Calculates, for each asset eligible to LFL, the consumption of the specific
        utility of this asset the previous year. The consumption is calculated
        on different subspaces and per control: tenant/landlord controlled tenant
        spaces and outdoor areas.

        Parameters:
        asset_data (pd.DataFrame): contains the asset information and data for
            both data years.
        utility (Utility): the utility to get metrics for.

        Returns:
        pd.DataFrame: The input DataFrame with the previous year LFL consumption
        metrics only on current year rows.
        """
        cols_to_add = {
            Utility.Energy: {
                ec.en_lfl_abs: ec.en_lfl_abs_ly,
                ec.en_lfl_abs_in: ec.en_lfl_abs_in_ly,
                ec.en_lfl_abs_lc: ec.en_lfl_abs_lc_ly,
                ec.en_lfl_abs_tc: ec.en_lfl_abs_tc_ly,
                ec.en_lfl_abs_lc_t: ec.en_lfl_abs_lc_t_ly,
                ec.en_lfl_abs_tc_t: ec.en_lfl_abs_tc_t_ly,
                ec.en_lfl_abs_lc_o: ec.en_lfl_abs_lc_o_ly,
                ec.en_lfl_abs_tc_o: ec.en_lfl_abs_tc_o_ly,
                ec.en_lfl_abs_lc_bc: ec.en_lfl_abs_lc_bc_ly,
                ec.en_lfl_abs_lc_bs: ec.en_lfl_abs_lc_bs_ly,
            },
            Utility.Water: {
                wc.wat_lfl_abs_tc_t: wc.wat_lfl_abs_tc_t_ly,
                wc.wat_lfl_abs_lc_t: wc.wat_lfl_abs_lc_t_ly,
                wc.wat_lfl_abs_tc_o: wc.wat_lfl_abs_tc_o_ly,
                wc.wat_lfl_abs_lc_o: wc.wat_lfl_abs_lc_o_ly,
                wc.wat_lfl_abs: wc.wat_lfl_abs_ly,
                wc.wat_lfl_abs_in: wc.wat_lfl_abs_in_ly,
                wc.wat_lfl_abs_lc: wc.wat_lfl_abs_lc_ly,
                wc.wat_lfl_abs_tc: wc.wat_lfl_abs_tc_ly,
                wc.wat_lfl_abs_lc_bc: wc.wat_lfl_abs_lc_bc_ly,
                wc.wat_lfl_abs_lc_bs: wc.wat_lfl_abs_lc_bs_ly,
            },
            Utility.GHG: {
                gc.ghg_lfl_abs_s12: gc.ghg_lfl_abs_s12_ly,
                gc.ghg_lfl_abs_s3: gc.ghg_lfl_abs_s3_ly,
                gc.ghg_lfl_abs: gc.ghg_lfl_abs_ly,
            },
        }[utility]

        if not set(cols_to_add.keys()).issubset(set(asset_data.columns)):
            raise ValueError("Asset data does not contain required columns.")

        # First, let's filter the previous year data to fetch the correct data.
        is_previous_year = asset_data[ac.survey_year] == asset_data[ac.data_year] + 2
        previous_year_data = asset_data.loc[is_previous_year].copy()

        # Rename the columns in the previous year data.
        previous_year_data = previous_year_data.rename(columns=cols_to_add)

        # Merge the newly calculated previous year metrics into the DataFrame.
        # In case the columns are already there, remove them from the asset data
        # to avoid a merge conflict.
        new_ly_cols = list(cols_to_add.values())

        # For some reason, the merge call loses the index, so we manually re-set it.
        cols_to_keep = [c for c in asset_data.columns if c not in new_ly_cols] + [
            "index"
        ]
        asset_data = (
            asset_data.reset_index()[cols_to_keep]
            .merge(
                previous_year_data[
                    [
                        ac.portfolio_asset_id,
                    ]
                    + new_ly_cols
                ],
                on=ac.portfolio_asset_id,
                how="left",
            )
            .set_index("index")
        )
        # Remove the data in the 'previous year' rows.
        asset_data.loc[is_previous_year, new_ly_cols] = np.nan

        return asset_data

    def add_lfl_percent_change_per_control(
        self, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Adds the Like-For-Like percent change in consumption per control for
        energy, GHG and water relative to the previous year.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame with both data years
            containing utility and asset characteristics columns.

        Returns:
        pd.DataFrame: The input DataFrame with the Like-For-Like percent changes
            columns per control.
        """
        asset_data = self.add_lfl_percent_change_per_control_per_utility(
            asset_data, Utility.Energy
        )
        asset_data = self.add_lfl_percent_change_per_control_per_utility(
            asset_data, Utility.GHG
        )
        asset_data = self.add_lfl_percent_change_per_control_per_utility(
            asset_data, Utility.Water
        )

        return asset_data

    def add_lfl_percent_change_per_control_per_utility(
        self, asset_data: pd.DataFrame, utility: Utility
    ) -> pd.DataFrame:
        """
        Adds the Like-For-Like percent change in consumption per utility relative
        to the previous year.

        Parameters:
            asset_data (pd.DataFrame): the asset DataFrame with both data years
                containing utility and asset characteristics columns.
            utility (Utility): the utility DataFrame with both data years

        Returns:
            pd.DataFrame: The input DataFrame with the Like-For-Like percent changes
            columns per control for the given utility.
        """
        (
            lfl_abs_col_1_cy,
            lfl_abs_col_1_ly,
            lfl_abs_col_2_cy,
            lfl_abs_col_2_ly,
            lfl_p_col_1,
            lfl_p_col_2,
        ) = {
            Utility.Energy: (
                ec.en_lfl_abs_lc,
                ec.en_lfl_abs_lc_ly,
                ec.en_lfl_abs_tc,
                ec.en_lfl_abs_tc_ly,
                ec.en_lfl_percent_change_lc,
                ec.en_lfl_percent_change_tc,
            ),
            Utility.GHG: (
                gc.ghg_lfl_abs_s12,
                gc.ghg_lfl_abs_s12_ly,
                gc.ghg_lfl_abs_s3,
                gc.ghg_lfl_abs_s3_ly,
                gc.ghg_lfl_percent_change_s12,
                gc.ghg_lfl_percent_change_s3,
            ),
            Utility.Water: (
                wc.wat_lfl_abs_lc,
                wc.wat_lfl_abs_lc_ly,
                wc.wat_lfl_abs_tc,
                wc.wat_lfl_abs_tc_ly,
                wc.wat_lfl_percent_change_lc,
                wc.wat_lfl_percent_change_tc,
            ),
        }[
            utility
        ]

        # Requires the calculation of previous year LFL consumption per control
        if (
            lfl_abs_col_1_ly not in asset_data.columns
            or lfl_abs_col_2_ly not in asset_data.columns
        ):
            raise ValueError(
                "Calculating the LFL percent change per control requires the calculation of the previous year LFL consumption per control."
            )

        asset_data[lfl_p_col_1] = self._calculate_lfl_percent_change(
            asset_data[lfl_abs_col_1_cy], asset_data[lfl_abs_col_1_ly]
        )
        asset_data[lfl_p_col_2] = self._calculate_lfl_percent_change(
            asset_data[lfl_abs_col_2_cy], asset_data[lfl_abs_col_2_ly]
        )

        return asset_data

    @staticmethod
    def _calculate_lfl_percent_change(
        current_year_consumption_column: pd.Series,
        previous_year_consumption_column: pd.Series,
    ) -> pd.Series:
        """
        Calculates the Like-For-Like percent change.

        Parameters:
        current_year_consumption_column (pd.Series): a Series containing the
            values of the absolute consumption for the current year.
        previous_year_consumption_column (pd.Series): a Series containing the
            values of the absolute consumption for the previous year.

        Returns:
        pd.Series: a Series resulting containing the LFL percent change.
        """
        lfl_percent_change = (
            (current_year_consumption_column - previous_year_consumption_column)
            / previous_year_consumption_column
            * 100
        )
        lfl_percent_change.replace([np.inf, -np.inf], np.nan, inplace=True)

        return lfl_percent_change

    @staticmethod
    def _calculate_lfl_abs_change(
        asset_data: pd.DataFrame, cy_cons_col: str, ly_cons_col: str
    ) -> pd.Series:
        """
        Compute the absolute change in consumption between current year and previous year LFL consumptions.

        Parameters:
            asset_data (pd.DataFrame): the current year asset data.
            cy_cons_col (str): the column name of the current year consumption column.
            ly_cons_col (str): the column name of the last year consumption column.

        Returns:
            pd.Series: a series containing the absolute change in consumption.
        """
        return asset_data[cy_cons_col].sub(asset_data[ly_cons_col], fill_value=0)

    def add_lfl_aggregation_abs_change(self, asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Add new columns for LFL absolute change where outliers' values are removed for all utilities.

        Parameters:
        asset_data (pd.DataFrame): the asset data.

        Returns:
        pd.DataFrame: the input data with the added column.
        """
        asset_data = self.add_lfl_aggregation_abs_change_per_utility(
            asset_data, Utility.Energy
        )
        asset_data = self.add_lfl_aggregation_abs_change_per_utility(
            asset_data, Utility.GHG
        )
        asset_data = self.add_lfl_aggregation_abs_change_per_utility(
            asset_data, Utility.Water
        )

        return asset_data

    def add_lfl_aggregation_abs_change_per_utility(
        self, asset_data: pd.DataFrame, utility: Utility
    ) -> pd.DataFrame:
        """
        Add a new column for LFL absolute change where outliers' values are removed
        for the given utility.

        Parameters:
        asset_data (pd.DataFrame): the asset data.
        utility (Utility): the utility to get the LFL percent change for.

        Returns:
        pd.DataFrame: the input data with the added column.
        """
        (
            outlier_column,
            lfl_column,
            filtered_lfl_column,
            accepted_lfl_column,
            lfl_abs_cy_column,
            lfl_abs_ly_column,
        ) = {
            Utility.Energy: (
                ec.en_lfl_outlier_status,
                ec.en_lfl_p,
                ec.en_lfl_abs_change_agg,
                ec.en_lfl_abs_change_accepted,
                ec.en_lfl_abs,
                ec.en_lfl_abs_ly,
            ),
            Utility.Water: (
                wc.wat_lfl_outlier_status,
                wc.wat_lfl_p,
                wc.wat_lfl_abs_change_agg,
                wc.wat_lfl_abs_change_accepted,
                wc.wat_lfl_abs,
                wc.wat_lfl_abs_ly,
            ),
            Utility.GHG: (
                gc.ghg_lfl_outlier_status,
                gc.ghg_lfl_p,
                gc.ghg_lfl_abs_change_agg,
                gc.ghg_lfl_abs_change_accepted,
                gc.ghg_lfl_abs,
                gc.ghg_lfl_abs_ly,
            ),
        }[
            utility
        ]

        is_agg_portfolio_metric = af().filter_lfl_metric_for_aggregation(
            asset_data, utility, False
        )
        is_agg_benchmark_metric = af().filter_lfl_metric_for_aggregation(
            asset_data, utility, True, lfl_column
        )

        abs_change_values = self._calculate_lfl_abs_change(
            asset_data, lfl_abs_cy_column, lfl_abs_ly_column
        )

        asset_data[filtered_lfl_column] = np.where(
            is_agg_benchmark_metric, abs_change_values, np.nan
        )
        asset_data[accepted_lfl_column] = np.where(
            is_agg_portfolio_metric, abs_change_values, np.nan
        )

        if utility == Utility.Energy:
            asset_data[ec.en_lfl_abs_change_accepted_mwh] = (
                asset_data[accepted_lfl_column] / 1000
            )

        return asset_data

    def add_lfl_aggregation_abs_change_per_control(
        self, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Add new columns for the LFL absolute change per control for all utilities.

        Parameters:
        asset_data (pd.DataFrame): the asset data.

        Returns:
        pd.DataFrame: the input data with the added columns.
        """
        asset_data = self.add_lfl_aggregation_abs_change_per_control_per_utility(
            asset_data, Utility.Energy
        )
        asset_data = self.add_lfl_aggregation_abs_change_per_control_per_utility(
            asset_data, Utility.GHG
        )
        asset_data = self.add_lfl_aggregation_abs_change_per_control_per_utility(
            asset_data, Utility.Water
        )

        return asset_data

    def add_lfl_aggregation_abs_change_per_control_per_utility(
        self,
        asset_data: pd.DataFrame,
        utility: Utility,
    ) -> pd.DataFrame:
        """
        Add new columns for the LFL absolute change per control for the given utility.

        Parameters:
        asset_data (pd.DataFrame): the asset data.
        utility (Utility): the utility to get the LFL absolute change for.

        Returns:
        pd.DataFrame: the input data with the added columns.
        """
        (
            outlier_column,
            lfl_p_1,
            lfl_p_2,
            lfl_abs_cy_1,
            lfl_abs_ly_1,
            lfl_abs_cy_2,
            lfl_abs_ly_2,
            lfl_c_accepted_1,
            lfl_c_accepted_2,
            lfl_c_agg_1,
            lfl_c_agg_2,
        ) = {
            Utility.Energy: (
                ec.en_lfl_outlier_status,
                ec.en_lfl_percent_change_lc,
                ec.en_lfl_percent_change_tc,
                ec.en_lfl_abs_lc,
                ec.en_lfl_abs_lc_ly,
                ec.en_lfl_abs_tc,
                ec.en_lfl_abs_tc_ly,
                ec.en_lfl_abs_change_lc_accepted,
                ec.en_lfl_abs_change_tc_accepted,
                ec.en_lfl_abs_change_lc_agg,
                ec.en_lfl_abs_change_tc_agg,
            ),
            Utility.GHG: (
                gc.ghg_lfl_outlier_status,
                gc.ghg_lfl_percent_change_s12,
                gc.ghg_lfl_percent_change_s3,
                gc.ghg_lfl_abs_s12,
                gc.ghg_lfl_abs_s12_ly,
                gc.ghg_lfl_abs_s3,
                gc.ghg_lfl_abs_s3_ly,
                gc.ghg_lfl_abs_change_s12_accepted,
                gc.ghg_lfl_abs_change_s3_accepted,
                gc.ghg_lfl_abs_change_s12_agg,
                gc.ghg_lfl_abs_change_s3_agg,
            ),
            Utility.Water: (
                wc.wat_lfl_outlier_status,
                wc.wat_lfl_percent_change_lc,
                wc.wat_lfl_percent_change_tc,
                wc.wat_lfl_abs_lc,
                wc.wat_lfl_abs_lc_ly,
                wc.wat_lfl_abs_tc,
                wc.wat_lfl_abs_tc_ly,
                wc.wat_lfl_abs_change_lc_accepted,
                wc.wat_lfl_abs_change_tc_accepted,
                wc.wat_lfl_abs_change_lc_agg,
                wc.wat_lfl_abs_change_tc_agg,
            ),
        }[
            utility
        ]

        is_agg_portfolio_metric = af().filter_lfl_metric_for_aggregation(
            asset_data, utility, False
        )
        is_agg_benchmark_metric_1 = af().filter_lfl_metric_for_aggregation(
            asset_data, utility, True, lfl_p_1
        )
        is_agg_benchmark_metric_2 = af().filter_lfl_metric_for_aggregation(
            asset_data, utility, True, lfl_p_2
        )

        lc_s12_agg_values = self._calculate_lfl_abs_change(
            asset_data, lfl_abs_cy_1, lfl_abs_ly_1
        )
        tc_s3_agg_values = self._calculate_lfl_abs_change(
            asset_data, lfl_abs_cy_2, lfl_abs_ly_2
        )

        asset_data[lfl_c_accepted_1] = np.where(
            is_agg_portfolio_metric, lc_s12_agg_values, np.nan
        )
        asset_data[lfl_c_agg_1] = np.where(
            is_agg_benchmark_metric_1, lc_s12_agg_values, np.nan
        )
        asset_data[lfl_c_accepted_2] = np.where(
            is_agg_portfolio_metric, tc_s3_agg_values, np.nan
        )
        asset_data[lfl_c_agg_2] = np.where(
            is_agg_benchmark_metric_2, tc_s3_agg_values, np.nan
        )

        return asset_data

    def add_lfl_eligibility(self, asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Add variables indicating whether the asset is eligible for LFL scoring.

        Parameters:
        asset_data (pd.DataFrame): the asset data with both years.

        Returns:
        pd.DataFrame: the input data with the added columns.
        """
        asset_data = self.add_lfl_eligibility_per_utility(asset_data, Utility.Energy)
        asset_data = self.add_lfl_eligibility_per_utility(asset_data, Utility.Water)
        asset_data = self.add_lfl_eligibility_per_utility(asset_data, Utility.GHG)

        return asset_data

    @staticmethod
    def add_lfl_eligibility_per_utility(
        asset_data: pd.DataFrame, utility: Utility
    ) -> pd.DataFrame:
        """
        Add variables indicating whether the asset is eligible for LFL scoring
        for the given utility.

        Parameters:
        asset_data (pd.DataFrame): the asset data with both years.
        utility (Utility): the utility to get the eligibility for.

        Returns:
        pd.DataFrame: the input data with the added columns.
        """
        eligible_col = {
            Utility.Energy: ec.en_lfl_eligible_for_aggregation,
            Utility.Water: wc.wat_lfl_eligible_for_aggregation,
            Utility.GHG: gc.ghg_lfl_eligible_for_aggregation,
        }[utility]

        asset_data[eligible_col] = af().filter_lfl_metric_for_aggregation(
            asset_data, utility, False
        )

        return asset_data

    def add_lfl_area(self, asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Add the absolute floor area on which the LFL data is reported.

        Parameters:
        asset_data (pd.DataFrame): the asset data.

        Returns:
        pd.DataFrame: the input data with the added columns.
        """
        asset_data = self.add_lfl_area_per_utility(asset_data, Utility.Energy)
        asset_data = self.add_lfl_area_per_utility(asset_data, Utility.GHG)
        asset_data = self.add_lfl_area_per_utility(asset_data, Utility.Water)

        return asset_data

    @staticmethod
    def add_lfl_area_per_utility(
        asset_data: pd.DataFrame, utility: Utility
    ) -> pd.DataFrame:
        """
        Add the absolute floor area on which the LFL data is reported for the given utility.

        Parameters:
        asset_data (pd.DataFrame): the asset data.
        utility (Utility): the utility to get the lfl_area for.

        Returns:
        pd.DataFrame: the input data with the added columns.
        """
        (
            lfl_area_m2,
            lfl_area_m2_1,
            lfl_area_m2_2,
            lfl_area_sqft,
            lfl_area_sqft_1,
            lfl_area_sqft_2,
            lfl_area,
            lfl_area_1,
            lfl_area_2,
            lfl_cov,
            lfl_cov_1,
            lfl_cov_2,
            weight_1,
            weight_2,
        ) = {
            Utility.Energy: (
                ec.en_lfl_area_m2,
                ec.en_lfl_area_m2_lc,
                ec.en_lfl_area_m2_tc,
                ec.en_lfl_area_sqft,
                ec.en_lfl_area_sqft_lc,
                ec.en_lfl_area_sqft_tc,
                ec.en_lfl_area,
                ec.en_lfl_area_lc,
                ec.en_lfl_area_tc,
                ec.en_lfl_area_cov_p,
                ec.en_lfl_area_cov_p_lc,
                ec.en_lfl_area_cov_p_tc,
                ec.en_area_weight_lc,
                ec.en_area_weight_tc,
            ),
            Utility.GHG: (
                gc.ghg_lfl_area_m2,
                gc.ghg_lfl_area_m2_s12,
                gc.ghg_lfl_area_m2_s3,
                gc.ghg_lfl_area_sqft,
                gc.ghg_lfl_area_sqft_s12,
                gc.ghg_lfl_area_sqft_s3,
                gc.ghg_lfl_area,
                gc.ghg_lfl_area_s12,
                gc.ghg_lfl_area_s3,
                gc.ghg_lfl_area_cov_p,
                gc.ghg_lfl_area_cov_p_s12,
                gc.ghg_lfl_area_cov_p_s3,
                gc.ghg_area_weight_s12,
                gc.ghg_area_weight_s3,
            ),
            Utility.Water: (
                wc.wat_lfl_area_m2,
                wc.wat_lfl_area_m2_lc,
                wc.wat_lfl_area_m2_tc,
                wc.wat_lfl_area_sqft,
                wc.wat_lfl_area_sqft_lc,
                wc.wat_lfl_area_sqft_tc,
                wc.wat_lfl_area,
                wc.wat_lfl_area_lc,
                wc.wat_lfl_area_tc,
                wc.wat_lfl_area_cov_p,
                wc.wat_lfl_area_cov_p_lc,
                wc.wat_lfl_area_cov_p_tc,
                wc.wat_area_weight_lc,
                wc.wat_area_weight_tc,
            ),
        }[
            utility
        ]

        is_eligible_for_agg = af().filter_lfl_metric_for_aggregation(
            asset_data, utility, False
        )

        def _calculate_lfl_area(
            area_weight_col: str, lfl_area_cov: str, floor_area_col: str
        ) -> np.ndarray:
            normalised_area_weight = asset_data[area_weight_col]
            if area_weight_col not in [
                ac.asset_size_m2,
                ac.asset_size_sqft,
                ac.asset_size,
            ]:
                # If we are calculating LFL area per control, since the area
                # weight is based on the area unit reported by the entity,
                # we need to normalise it to fit either m2 or sqft based on `floor_area_col`.
                normalised_area_weight = (
                    asset_data[area_weight_col]
                    / asset_data[ac.asset_size]
                    * asset_data[floor_area_col]
                )

            lfl_area_values = (
                normalised_area_weight.mul(asset_data[ac.asset_ownership])
                .div(100)
                .mul(asset_data[lfl_area_cov])
                .div(100)
            )
            return np.where(is_eligible_for_agg, lfl_area_values, np.nan)

        asset_data[lfl_area_m2] = _calculate_lfl_area(
            ac.asset_size_m2, lfl_cov, ac.asset_size_m2
        )
        asset_data[lfl_area_m2_1] = _calculate_lfl_area(
            weight_1, lfl_cov_1, ac.asset_size_m2
        )
        asset_data[lfl_area_m2_2] = _calculate_lfl_area(
            weight_2, lfl_cov_2, ac.asset_size_m2
        )

        asset_data[lfl_area_sqft] = _calculate_lfl_area(
            ac.asset_size_sqft, lfl_cov, ac.asset_size_sqft
        )
        asset_data[lfl_area_sqft_1] = _calculate_lfl_area(
            weight_1, lfl_cov_1, ac.asset_size_sqft
        )
        asset_data[lfl_area_sqft_2] = _calculate_lfl_area(
            weight_2, lfl_cov_2, ac.asset_size_sqft
        )

        asset_data[lfl_area] = _calculate_lfl_area(
            ac.asset_size, lfl_cov, ac.asset_size
        )
        asset_data[lfl_area_1] = _calculate_lfl_area(weight_1, lfl_cov_1, ac.asset_size)
        asset_data[lfl_area_2] = _calculate_lfl_area(weight_2, lfl_cov_2, ac.asset_size)

        return asset_data

    def add_lfl_aggregation_abs_cons(self, asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Add new LFL consumption columns for all utilities where rejected
        outliers' values are removed.

        Parameters:
        asset_data (pd.DataFrame): the current year asset data.
        utility (Utility): the utility to get the LFL consumptions for.

        Returns:
        pd.DataFrame: the input data with the added column.
        """
        asset_data = self.add_lfl_aggregation_abs_cons_per_utility(
            asset_data, Utility.Energy
        )
        asset_data = self.add_lfl_aggregation_abs_cons_per_utility(
            asset_data, Utility.Water
        )
        asset_data = self.add_lfl_aggregation_abs_cons_per_utility(
            asset_data, Utility.GHG
        )

        return asset_data

    @staticmethod
    def add_lfl_aggregation_abs_cons_per_utility(
        asset_data: pd.DataFrame, utility: Utility
    ) -> pd.DataFrame:
        """
        Add new previous year LFL consumption columns for the given utility where rejected
        outliers' values are removed. These are used as denominator for calculating the
        aggregated LFL percent changes.

        Parameters:
        asset_data (pd.DataFrame): the current year asset data.
        utility (Utility): the utility to get the LFL consumptions for.

        Returns:
        pd.DataFrame: the input data with the added column.
        """
        (
            lfl_abs_ly_column,
            lfl_abs_ly_column_1,
            lfl_abs_ly_column_2,
            lfl_abs_ly_column_accepted,
            lfl_abs_ly_column_accepted_1,
            lfl_abs_ly_column_accepted_2,
            lfl_abs_ly_column_bench_1,
            lfl_abs_ly_column_bench_2,
            lfl_p_1,
            lfl_p_2,
        ) = {
            Utility.Energy: (
                ec.en_lfl_abs_ly,
                ec.en_lfl_abs_lc_ly,
                ec.en_lfl_abs_tc_ly,
                ec.en_lfl_abs_ly_accepted,
                ec.en_lfl_abs_lc_ly_accepted,
                ec.en_lfl_abs_tc_ly_accepted,
                ec.en_lfl_abs_lc_ly_bench,
                ec.en_lfl_abs_tc_ly_bench,
                ec.en_lfl_percent_change_lc,
                ec.en_lfl_percent_change_tc,
            ),
            Utility.Water: (
                wc.wat_lfl_abs_ly,
                wc.wat_lfl_abs_lc_ly,
                wc.wat_lfl_abs_tc_ly,
                wc.wat_lfl_abs_ly_accepted,
                wc.wat_lfl_abs_lc_ly_accepted,
                wc.wat_lfl_abs_tc_ly_accepted,
                wc.wat_lfl_abs_lc_ly_bench,
                wc.wat_lfl_abs_tc_ly_bench,
                wc.wat_lfl_percent_change_lc,
                wc.wat_lfl_percent_change_tc,
            ),
            Utility.GHG: (
                gc.ghg_lfl_abs_ly,
                gc.ghg_lfl_abs_s12_ly,
                gc.ghg_lfl_abs_s3_ly,
                gc.ghg_lfl_abs_ly_accepted,
                gc.ghg_lfl_abs_s12_ly_accepted,
                gc.ghg_lfl_abs_s3_ly_accepted,
                gc.ghg_lfl_abs_s12_ly_bench,
                gc.ghg_lfl_abs_s3_ly_bench,
                gc.ghg_lfl_percent_change_s12,
                gc.ghg_lfl_percent_change_s3,
            ),
        }[
            utility
        ]

        # Define the filters for the portfolio metrics and the per-control/scope benchmarks
        is_eligible_for_aggregation = af().filter_lfl_metric_for_aggregation(
            asset_data, utility, False
        )
        is_eligible_for_bench_aggregation_1 = af().filter_lfl_metric_for_aggregation(
            asset_data, utility, True, lfl_p_1
        )
        is_eligible_for_bench_aggregation_2 = af().filter_lfl_metric_for_aggregation(
            asset_data, utility, True, lfl_p_2
        )

        # Create the new columns with the filtered values
        asset_data[lfl_abs_ly_column_accepted] = np.where(
            is_eligible_for_aggregation, asset_data[lfl_abs_ly_column], np.nan
        )

        asset_data[lfl_abs_ly_column_accepted_1] = np.where(
            is_eligible_for_aggregation, asset_data[lfl_abs_ly_column_1], np.nan
        )
        asset_data[lfl_abs_ly_column_bench_1] = np.where(
            is_eligible_for_bench_aggregation_1, asset_data[lfl_abs_ly_column_1], np.nan
        )
        asset_data[lfl_abs_ly_column_accepted_2] = np.where(
            is_eligible_for_aggregation, asset_data[lfl_abs_ly_column_2], np.nan
        )
        asset_data[lfl_abs_ly_column_bench_2] = np.where(
            is_eligible_for_bench_aggregation_2, asset_data[lfl_abs_ly_column_2], np.nan
        )

        return asset_data
