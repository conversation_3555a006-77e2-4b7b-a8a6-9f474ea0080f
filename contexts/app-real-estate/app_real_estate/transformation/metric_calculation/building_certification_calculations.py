import pandas as pd
import numpy as np
import app_real_estate.constants.column_names.building_certification_columns as bc
import app_real_estate.constants.column_names.building_certification_table_columns as bctc
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
from app_real_estate.transformation.metric_calculation.common_operations import (
    CommonOperations,
)
import app_real_estate.constants.building_certification_type as bct
import app_real_estate.constants.building_certification_validation_points as bcvp
from app_real_estate.transformation.metric_calculation.asset_filters import (
    AssetFilters,
)


class TimeFactor:
    """
    Class responsible of defining the time factor table.
    """

    @staticmethod
    def get_time_factor_table() -> pd.DataFrame:
        """
        Returns the time factor table used for Building Certification scoring.
        It is hard-coded here for now but could very well be imported from S3 in the future.
        As of now it is safer for version control to have it here.

        Parameters:

        Returns:
        pd.DataFrame: a DataFrame containing the time factor weights for each certification age and type combination.
        """
        return pd.DataFrame(
            data=[
                [0, 1, 1, 1],
                [1, 1, 1, 1],
                [2, 1, 1, 1],
                [3, 1, 1, 0.67],
                [4, 0.5, 0.9, 0.33],
                [5, 0, 0.8, 0],
                [6, 0, 0.74, 0],
                [7, 0, 0.67, 0],
                [8, 0, 0.61, 0],
                [9, 0, 0.54, 0],
                [10, 0, 0.48, 0],
                [11, 0, 0.40, 0],
                [12, 0, 0.32, 0],
                [13, 0, 0.24, 0],
                [14, 0, 0.16, 0],
                [15, 0, 0.08, 0],
            ],
            columns=["certification age", "operational", "design", "interior"],
        )


class BuildingCertificationsCalculations:
    """
    Class responsible for calculating and adding in the building certifications data the metrics used in scoring.
    """

    @staticmethod
    def add_time_factor(certification_data: pd.DataFrame) -> pd.DataFrame:
        """
        Adds the time factor weight to each building certification.

        Parameters:
        certification_data (pd.DataFrame): a DataFrame containing the building certifications.

        Returns:
        pd.DataFrame: a DataFrame containing the newly added 'time_factor_weight' column.
        """

        def _add_time_factor(certification) -> int:
            age = certification[bc.age] if certification[bc.age] < 15 else 15
            return TimeFactor.get_time_factor_table().iloc[age][certification[bc.type]]

        certification_data[bc.time_factor_weight] = certification_data.apply(
            _add_time_factor, axis=1
        )

        return certification_data

    @staticmethod
    def add_covered_floor_area_m2(
        certification_data: pd.DataFrame, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Adds the covered floor area for each building certification.
        """
        return CommonOperations.add_covered_floor_area_m2(
            data=certification_data, asset_data=asset_data
        )

    @staticmethod
    def add_data_coverage(certification_data: pd.DataFrame) -> pd.DataFrame:
        """
        Adds the floor data coverage for each building certification.

        Parameters:
        certification_data (pd.DataFrame): a DataFrame containing the building certifications.

        Returns:
        pd.DataFrame: a DataFrame containing the newly added 'coverage' column.
        """
        certification_data[bc.scoring_coverage] = (
            certification_data[bc.covered_floor_area_m2]
            / certification_data[bc.asset_size_m2]
            * 100
        )
        return certification_data

    @staticmethod
    def add_age(certification_data: pd.DataFrame, data_year: int) -> pd.DataFrame:
        """
        Adds the age of the building certification.

        Parameters:
        certification_data (pd.DataFrame): a DataFrame containing the building certifications.

        Returns:
        pd.DataFrame: a DataFrame containing the newly added 'age' column.
        """
        # raise value error if certification_data year is bigger then data year
        if certification_data[bc.year].gt(data_year).any():
            raise ValueError("Certification year is bigger than data year.")

        certification_data[bc.age] = data_year - certification_data[bc.year]
        return certification_data

    @staticmethod
    def get_asset_size(
        certification_data: pd.DataFrame, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Retrieve the asset size of the building certification.

        Parameters:
        certification_data (pd.DataFrame): a DataFrame containing the building certifications.
        asset_data (pd.DataFrame): a DataFrame containing the asset data.

        Returns:
        pd.DataFrame: a DataFrame with the asset size information merged.
        """
        return CommonOperations.get_asset_size(
            left_data=certification_data,
            right_data=asset_data,
            key=bc.portfolio_asset_id,
        )

    @staticmethod
    def add_uncertified_assets_mock_certifications(
        certification_data: pd.DataFrame, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Add mock certification types for assets without certification data.
        There are 2 types of certifications, design and operational.
        design certifications(development) are of type design and interior.
        operational certifications(operational) are of type operational.
        when design certifcations are missing we have to add mocked operational certification to the aset
        with 0 coverage, same goes for the opposite process

        Parameters:
        certification_data (pd.DataFrame): A DataFrame containing the building certifications.
        asset_data (pd.DataFrame): A DataFrame containing the asset data.

        Returns:
        pd.DataFrame: Original certifications plus default types for uncertified assets.
        """

        def _construct_mock_certifications(
            portfolio_asset_id: np.ndarray,
            snapshot_id: np.ndarray,
            company_fund_id: np.ndarray,
            response_id: np.ndarray,
            type: str,
        ) -> dict[str, np.ndarray]:
            return {
                ac.portfolio_asset_id: portfolio_asset_id,
                ac.snapshot_id: snapshot_id,
                ac.company_fund_id: company_fund_id,
                ac.response_id: response_id.astype(int),
                bc.type: type,
                bc.scoring_coverage: 0,
                bc.covered_floor_area: 0,
                bc.year: 0,
            }

        has_design_certification = AssetFilters.filter_asset_on_certification_condition(
            certification_data=certification_data,
            asset_data=asset_data,
            condition=np.logical_or(
                certification_data[bc.type] == bct.design,
                certification_data[bc.type] == bct.interior,
            ),
        )
        has_operational_certification = (
            AssetFilters.filter_asset_on_certification_condition(
                certification_data=certification_data,
                asset_data=asset_data,
                condition=(certification_data[bc.type] == bct.operational),
            )
        )

        no_design_certification_assets = asset_data[~np.array(has_design_certification)]
        no_operational_certification_assets = asset_data[
            ~np.array(has_operational_certification)
        ]

        mocked_cert_design = _construct_mock_certifications(
            no_design_certification_assets[ac.portfolio_asset_id].values,
            no_design_certification_assets[ac.snapshot_id].values,
            no_design_certification_assets[ac.company_fund_id].values,
            no_design_certification_assets[ac.response_id].values,
            bct.design,
        )

        mocked_cert_operational = _construct_mock_certifications(
            no_operational_certification_assets[ac.portfolio_asset_id].values,
            no_operational_certification_assets[ac.snapshot_id].values,
            no_operational_certification_assets[ac.company_fund_id].values,
            no_operational_certification_assets[ac.response_id].values,
            bct.operational,
        )

        certification_data_with_mocks = pd.concat(
            [
                certification_data,
                pd.DataFrame(mocked_cert_design),
                pd.DataFrame(mocked_cert_operational),
            ],
            ignore_index=True,
        )

        # In order to benchmark these certifications, we need to give them an ID.
        # Since these mocked certifications will stay in the data in the future,
        # we don't want the ID to clash with future building certification IDs,
        # as a result we create a new ID column.
        certification_data_with_mocks[bc.building_data_certifications_scoring_id] = (
            list(range(1, len(certification_data_with_mocks.index) + 1))
        )

        return certification_data_with_mocks

    @staticmethod
    def add_validation_status(
        certification_data: pd.DataFrame, certification_table: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Add the validation status to the certification data by merging with the certification table.

        Parameters:
        certification_data: containing the building certifications.
        certification_table: containing the certification table with scoring status.

        Returns:
        merged data including the validation status.

        Raises:
        ValueError: If any scoring status is NA after merging.
        """
        certification_table_cols = [bctc.certification_id, bctc.scoring_status]
        certification_table = certification_table[certification_table_cols]
        certification_data = pd.merge(
            certification_data,
            certification_table[certification_table_cols],
            left_on=bc.certification_id,
            right_on=bctc.certification_id,
            how="left",
        )

        # Replace missing statuses from mock certifications by "No points".
        missing_status = certification_data[bctc.scoring_status].isna()
        certification_data.loc[missing_status, bctc.scoring_status] = bcvp.no_points

        certification_data[bc.validation_status] = certification_data[
            bctc.scoring_status
        ].apply(lambda x: bcvp.validation_points[x])

        return certification_data

    @staticmethod
    def get_asset_ownership(
        certification_data: pd.DataFrame, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Retrieve the asset ownership of the building certification.

        Parameters:
        certification_data (pd.DataFrame): a DataFrame containing the building certifications.
        asset_data (pd.DataFrame): a DataFrame containing the asset data.

        Returns:
        pd.DataFrame: a DataFrame with the asset ownership information merged.
        """
        return CommonOperations.get_asset_ownership(
            left_data=certification_data,
            right_data=asset_data,
            key=bc.portfolio_asset_id,
        )

    @staticmethod
    def add_scored_certified_area(certification_data: pd.DataFrame) -> pd.DataFrame:
        """
        Add a variable containing the certified area weighted by time factor, ownership and validation status.
        In the case of aggregated-level scoring (as opposed to certification-level scoring), the variable is
        then aggregated and used for benchmarking and scoring.

        Parameters:
            certification_data: containing the building certifications.
        Returns:
            (pd.DataFrame): the certification data containing the new variable for scoring certified area.
        """
        certification_data[bc.scoring_covered_floor_area] = (
            certification_data[bc.covered_floor_area].fillna(value=0)
            * certification_data[bc.validation_status]
            * certification_data[bc.time_factor_weight]
            * certification_data[bc.asset_ownership]
            / 100
        )

        return certification_data

    @staticmethod
    def add_owned_certified_area(certification_data: pd.DataFrame) -> pd.DataFrame:
        """
        Add a variable containing the certified area weighted ownership.
        In the case of aggregated-level scoring (as opposed to certification-level scoring), the variable is
        then aggregated, benchmarked and used for reporting.

        Parameters:
            certification_data: containing the building certifications.
        Returns:
            (pd.DataFrame): the certification data containing the new variable for owned certified area.
        """
        certification_data[bc.owned_covered_floor_area] = (
            certification_data[bc.covered_floor_area].fillna(value=0)
            * certification_data[bc.asset_ownership]
            / 100
        )

        return certification_data

    @staticmethod
    def add_asset_size_owned_m2(certification_data: pd.DataFrame) -> pd.DataFrame:
        """
        Add the owned floor area in m2.
        """
        certification_data[ac.asset_size_owned_m2] = (
            certification_data[bc.asset_size_m2]
            * certification_data[bc.asset_ownership]
            / 100
        )
        return certification_data
