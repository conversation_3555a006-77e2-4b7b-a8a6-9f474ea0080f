import pandas as pd
from app_real_estate.constants.helper_enumerators import Utility
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.ghg_columns as gc
import app_real_estate.constants.column_names.water_columns as wc
import app_real_estate.constants.column_names.waste_columns as wsc


class ConsumptionCalculations:

    def add_consumption_metrics(self, asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Performs all the consumption related calculations.

        Parameters:
            asset_data (pd.DataFrame): the asset data with both years.
        Returns:
        (pd.DataFrame): the asset data with all coverage metrics added.
        """
        asset_data = self.add_energy_consumption_metrics(asset_data)
        asset_data = self.add_ghg_consumption_metrics(asset_data)

        asset_data = self.add_previous_year_consumption_per_utility(
            asset_data, Utility.Water
        )
        asset_data = self.add_previous_year_consumption_per_utility(
            asset_data, Utility.Waste
        )

        return asset_data

    def add_energy_consumption_metrics(self, asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Performs all the energy consumption related calculations.
        Parameters:
            asset_data (pd.DataFrame): the asset data with both years.
        Returns:
            (pd.DataFrame): the asset data with renewable energy consumption added.
        """
        asset_data = self.add_consumed_renewable_energy_consumption(asset_data)
        asset_data = self.add_mwh_energy_consumption(asset_data)
        asset_data = self.add_previous_year_consumption_per_utility(
            asset_data, Utility.Energy
        )
        asset_data = self.add_energy_consumption_per_energy_type(asset_data)

        return asset_data

    def add_ghg_consumption_metrics(self, asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Performs all the GHG consumption related calculations.
        Parameters:
            asset_data (pd.DataFrame): the asset data with both years.
        Returns:
            (pd.DataFrame): the asset data with renewable GHG consumption added.
        """
        asset_data = self.add_ghg_consumption_per_scope(asset_data)
        asset_data = self.add_previous_year_consumption_per_utility(
            asset_data, Utility.GHG
        )
        asset_data = self.add_net_ghg_emissions(asset_data)

        return asset_data

    @staticmethod
    def add_consumed_renewable_energy_consumption(
        asset_data: pd.DataFrame,
    ) -> pd.DataFrame:
        """
        Add a variable containing the amount of renewable energy that was actually
        consumed and not exported.

        Parameters:
            asset_data (pd.DataFrame): the asset data with both years.
        Returns:
        (pd.DataFrame): the asset data with renewable energy consumption added.
        """
        asset_data[ec.en_ren_abs_consumed_kwh] = asset_data[
            [
                ec.en_ren_ofs,
                ec.en_ren_ons_tpt,
                ec.en_ren_ons_con,
            ]
        ].sum(min_count=1, axis=1)

        return asset_data

    @staticmethod
    def add_mwh_energy_consumption(asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Convert energy consumption from kWh to MWh.

        Parameters:
        asset_data (pd.DataFrame): the asset data with both years.

        Returns:
        pd.DataFrame: the input data with the added columns.
        """
        asset_data[ec.en_abs_mwh] = asset_data[ec.en_abs] / 1000
        asset_data[ec.en_ren_abs_mwh] = asset_data[ec.en_ren_abs] / 1000
        asset_data[ec.en_lfl_abs_mwh] = asset_data[ec.en_lfl_abs] / 1000
        asset_data[ec.en_abs_nopr_ev_mwh] = asset_data[ec.en_abs_nopr_ev] / 1000
        asset_data[ec.en_ren_abs_consumed_mwh] = (
            asset_data[ec.en_ren_abs_consumed_kwh] / 1000
        )

        return asset_data

    @staticmethod
    def add_ghg_consumption_per_scope(asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Add GHG consumption variables per scopes.

        Parameters:
        asset_data (pd.DataFrame): the asset data with both years.

        Returns:
        pd.DataFrame: the input data with the added columns.
        """
        asset_data[gc.ghg_abs_s1] = asset_data[[gc.ghg_abs_s1_w, gc.ghg_abs_s1_o]].sum(
            axis=1, min_count=1
        )
        asset_data[gc.ghg_abs_s2_lb] = asset_data[
            [gc.ghg_abs_s2_lb_w, gc.ghg_abs_s2_lb_o]
        ].sum(axis=1, min_count=1)
        asset_data[gc.ghg_abs_s2_mb] = asset_data[
            [gc.ghg_abs_s2_mb_w, gc.ghg_abs_s2_mb_o]
        ].sum(axis=1, min_count=1)
        asset_data[gc.ghg_abs_s3] = asset_data[[gc.ghg_abs_s3_w, gc.ghg_abs_s3_o]].sum(
            axis=1, min_count=1
        )

        return asset_data

    def add_previous_year_consumption(self, asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Add variables containing the previous year absolute consumptions
        for all utility.
        """
        asset_data = self.add_previous_year_consumption_per_utility(
            asset_data, Utility.Energy
        )
        asset_data = self.add_previous_year_consumption_per_utility(
            asset_data, Utility.GHG
        )
        asset_data = self.add_previous_year_consumption_per_utility(
            asset_data, Utility.Water
        )
        asset_data = self.add_previous_year_consumption_per_utility(
            asset_data, Utility.Waste
        )

        return asset_data

    @staticmethod
    def add_previous_year_consumption_per_utility(
        asset_data: pd.DataFrame, utility: Utility
    ) -> pd.DataFrame:
        """
        Add a variable containing the previous year absolute consumption for the given utility.
        """
        consumption_col_cy, consumption_col_ly = {
            Utility.Energy: (ec.en_abs, ec.en_abs_ly),
            Utility.Water: (wc.wat_abs, wc.wat_abs_ly),
            Utility.Waste: (wsc.was_abs, wsc.was_abs_ly),
            Utility.GHG: (gc.ghg_abs, gc.ghg_abs_ly),
        }[utility]

        if consumption_col_ly in asset_data.columns:
            return asset_data

        survey_year = asset_data[ac.survey_year].unique()[0]
        is_current_year = asset_data[ac.data_year] == survey_year - 1
        ly_data = asset_data.loc[
            ~is_current_year, [ac.portfolio_asset_id, ac.data_year, consumption_col_cy]
        ]
        ly_data = ly_data.rename(columns={consumption_col_cy: consumption_col_ly})
        ly_data[ac.data_year] = survey_year - 1

        result = asset_data.merge(
            ly_data, on=[ac.portfolio_asset_id, ac.data_year], how="left"
        )

        return result

    @staticmethod
    def add_energy_consumption_per_energy_type(
        asset_data: pd.DataFrame,
    ) -> pd.DataFrame:
        """
        Calculate the energy consumption of the assets for different energy types.

        Parameters:
            asset_data (pd.DataFrame): the asset data with both years.

        Returns:
            pd.DataFrame: the input data with the added columns.
        """
        fuel_columns = [
            ec.en_abs_wf,
            ec.en_abs_lc_bsf,
            ec.en_abs_lc_bcf,
            ec.en_abs_lc_tf,
            ec.en_abs_tc_tf,
            ec.en_abs_lc_of,
            ec.en_abs_tc_of,
        ]

        heating_columns = [
            ec.en_abs_wd,
            ec.en_abs_lc_bsd,
            ec.en_abs_lc_bcd,
            ec.en_abs_lc_td,
            ec.en_abs_tc_td,
        ]

        electricity_columns = [
            ec.en_abs_we,
            ec.en_abs_lc_bse,
            ec.en_abs_lc_bce,
            ec.en_abs_lc_te,
            ec.en_abs_tc_te,
            ec.en_abs_lc_oe,
            ec.en_abs_tc_oe,
        ]

        asset_data[ec.en_abs_f_kwh] = asset_data[fuel_columns].sum(axis=1)
        asset_data[ec.en_abs_d_kwh] = asset_data[heating_columns].sum(axis=1)
        asset_data[ec.en_abs_e_kwh] = asset_data[electricity_columns].sum(axis=1)

        # Add them in MWh too.
        asset_data[ec.en_abs_f_mwh] = asset_data[ec.en_abs_f_kwh] / 1000
        asset_data[ec.en_abs_d_mwh] = asset_data[ec.en_abs_d_kwh] / 1000
        asset_data[ec.en_abs_e_mwh] = asset_data[ec.en_abs_e_kwh] / 1000

        return asset_data

    @staticmethod
    def add_net_ghg_emissions(asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Add the net GHG emissions, meaning the total GHG emissions minus the offsets.

        Parameters:
            asset_data (pd.DataFrame): the asset data with both years.
        Returns:
            pd.DataFrame: the input data with the added columns.
        """
        asset_data[gc.ghg_abs_net] = (
            asset_data[gc.ghg_abs] - asset_data[gc.ghg_abs_offset]
        )

        return asset_data
