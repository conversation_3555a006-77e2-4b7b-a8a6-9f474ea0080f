import pandas as pd
import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.water_columns as wc
import app_real_estate.constants.column_names.ghg_columns as gc
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
from app_real_estate.constants.helper_enumerators import Utility
import app_real_estate.constants.asset_characteristics as asc_const


class AssetCharacteristicsCalculations:
    """
    Class responsible for calculating and adding in the asset data the metrics used in the scoring.
    """

    def add_asset_characteristic_calculations(
        self, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Performs all the asset characteristic related calculations.

        Parameters:
            asset_data (pd.DataFrame): the asset data with both years.
        Returns:
        (pd.DataFrame): the asset data with asset characteristic metrics added.
        """
        asset_data = self.add_asset_size_sqft(asset_data)
        asset_data = self.add_asset_size_owned(asset_data)
        asset_data = self.add_asset_ownership_fraction(asset_data)
        asset_data = self.add_owned_area_weight(asset_data)

        return asset_data

    @staticmethod
    def add_asset_size_owned(asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Add the asset size owned by entity.
        Could be in sqft or m2 depending on the unit chosen by the asset.

        Parameters:
        asset_data (pd.DataFrame): the asset data with both years.

        Returns:
        pd.DataFrame: the input data with the added columns.
        """
        asset_data[ac.asset_size_owned_sqft] = (
            asset_data[ac.asset_size_sqft].mul(asset_data[ac.asset_ownership]).div(100)
        )

        return asset_data

    @staticmethod
    def add_asset_size_sqft(asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Add the asset's total floor area in sqft.

        Parameters:
        asset_data (pd.DataFrame): the asset data with both years.

        Returns:
        pd.DataFrame: the input data with the added columns.
        """
        asset_data[ac.asset_size_sqft] = (
            asset_data[ac.asset_size_m2] / asc_const.sqft_to_sqm
        )

        return asset_data

    @staticmethod
    def add_asset_ownership_fraction(asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Add the asset ownership as a fraction.
        """
        asset_data[ac.asset_ownership_fraction] = asset_data[ac.asset_ownership].div(
            100
        )

        return asset_data

    def add_owned_area_weight(self, asset_data: pd.DataFrame) -> pd.DataFrame:
        asset_data = self.add_owned_area_weight_per_utility(asset_data, Utility.Energy)
        asset_data = self.add_owned_area_weight_per_utility(asset_data, Utility.GHG)
        asset_data = self.add_owned_area_weight_per_utility(asset_data, Utility.Water)

        return asset_data

    @staticmethod
    def add_owned_area_weight_per_utility(
        asset_data: pd.DataFrame, utility: Utility
    ) -> pd.DataFrame:
        owned_weight_col1, owned_weight_col2, base_weight_col1, base_weight_col2 = {
            Utility.Energy: (
                sc.en_area_weight_lc_owned,
                sc.en_area_weight_tc_owned,
                ec.en_area_weight_lc,
                ec.en_area_weight_tc,
            ),
            Utility.GHG: (
                sc.ghg_area_weight_s12_owned,
                sc.ghg_area_weight_s3_owned,
                gc.ghg_area_weight_s12,
                gc.ghg_area_weight_s3,
            ),
            Utility.Water: (
                sc.wat_area_weight_lc_owned,
                sc.wat_area_weight_tc_owned,
                wc.wat_area_weight_lc,
                wc.wat_area_weight_tc,
            ),
        }[utility]

        asset_data[owned_weight_col1] = (
            asset_data[base_weight_col1] * asset_data[ac.asset_ownership] / 100
        )
        asset_data[owned_weight_col2] = (
            asset_data[base_weight_col2] * asset_data[ac.asset_ownership] / 100
        )

        return asset_data
