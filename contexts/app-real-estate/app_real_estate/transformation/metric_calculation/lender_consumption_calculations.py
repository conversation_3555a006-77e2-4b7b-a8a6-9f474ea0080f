import pandas as pd
from app_real_estate.constants.helper_enumerators import Utility
from app_real_estate.transformation.metric_calculation.consumption_calculations import (
    ConsumptionCalculations,
)


class LenderConsumptionCalculations(ConsumptionCalculations):

    def add_previous_year_consumption(self, asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Add variables containing the previous year absolute consumptions
        for all utility.
        """
        asset_data = self.add_previous_year_consumption_per_utility(
            asset_data, Utility.Energy
        )
        asset_data = self.add_previous_year_consumption_per_utility(
            asset_data, Utility.GHG
        )
        return asset_data
