import pandas as pd
from app_real_estate.transformation.metric_calculation.coverage_calculations import (
    CoverageCalculations,
)
from app_real_estate.constants.helper_enumerators import Utility
import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.ghg_columns as gc
import app_real_estate.constants.column_names.score_columns as sc


class LenderCoverageCalculations(CoverageCalculations):

    @staticmethod
    def add_coverage_weighted_lender(asset_data: pd.DataFrame):
        """
        This function is only used during aggregation.
        For lender assessment.
        """
        asset_data[ec.en_area_time_cov_p] = asset_data[ec.en_area_time_cov_p].mul(
            asset_data[sc.en_area_time_weight_owned]
        )

        asset_data[gc.ghg_area_time_cov_p] = asset_data[gc.ghg_area_time_cov_p].mul(
            asset_data[sc.ghg_area_time_weight_owned]
        )

        return asset_data

    def add_owned_area_time_weight_per_control(
        self, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Add the owned area-time weight used to perform the weighted mean on
        LC/TC or S12/S3 coverage metrics.

        Parameters:
        asset_data (pd.DataFrame): the asset data.

        Returns:
        pd.DataFrame: the input data with the added columns.
        """
        asset_data = self.add_owned_area_time_weight_per_control_per_utility(
            asset_data, Utility.Energy
        )
        asset_data = self.add_owned_area_time_weight_per_control_per_utility(
            asset_data, Utility.GHG
        )

        return asset_data

    def add_cross_control_area_time_weights(
        self, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Add the cross-control area-time weight used to perform the weighted mean on
        LC/TC or S12/S3 coverage metrics.
        """
        asset_data = self.add_cross_control_area_time_weights_per_utility(
            asset_data, Utility.Energy
        )
        asset_data = self.add_cross_control_area_time_weights_per_utility(
            asset_data, Utility.GHG
        )

        return asset_data

    def add_coverage_weighted_per_control(
        self, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Add the coverage weighted per control used to perform the weighted mean on
        LC/TC or S12/S3 coverage metrics.
        """
        asset_data = self.add_coverage_weighted_per_control_per_utility(
            asset_data, Utility.Energy
        )
        asset_data = self.add_coverage_weighted_per_control_per_utility(
            asset_data, Utility.GHG
        )
        return asset_data
