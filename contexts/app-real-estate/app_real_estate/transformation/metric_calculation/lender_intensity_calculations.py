import pandas as pd
import numpy as np
from app_real_estate.constants.helper_enumerators import Utility
from app_real_estate.transformation.metric_calculation.intensity_calculations import (
    IntensityCalculations,
)
from app_real_estate.transformation.metric_calculation.asset_filters import (
    AssetFilters as af,
)
import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.ghg_columns as gc
import app_real_estate.constants.column_names.asset_characteristics_columns as ac


class LenderIntensityCalculations(IntensityCalculations):

    def add_vacancy_rate_for_intensity_assets(
        self, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Add vacancy rates for assets eligible for Energy efficiency (75% and
        100% coverage) and other intensities.
        """
        asset_data = self.add_vacancy_rate_for_intensity_assets_per_utility(
            asset_data, Utility.Energy
        )
        asset_data = self.add_vacancy_rate_for_intensity_assets_per_utility(
            asset_data, Utility.GHG
        )

        return asset_data

    def add_asset_size_for_intensity_assets(
        self, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Add asset size for assets eligible for intensity scoring for the given utility.
        """
        asset_data = self.add_asset_size_for_intensity_assets_per_utility(
            asset_data, Utility.Energy
        )
        asset_data = self.add_asset_size_for_intensity_assets_per_utility(
            asset_data, Utility.GHG
        )

        return asset_data

    def add_intensity_columns_without_outliers(
        self, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Add intensity columns without outliers for the given utility.
        """
        asset_data = self.add_intensity_columns_without_outliers_per_utility(
            asset_data, Utility.Energy
        )
        asset_data = self.add_intensity_columns_without_outliers_per_utility(
            asset_data, Utility.GHG
        )
        return asset_data

    @staticmethod
    def add_asset_size_for_intensity_assets_per_utility(
        asset_data: pd.DataFrame, utility: Utility
    ) -> pd.DataFrame:
        """
        Add asset size for assets eligible for intensity scoring for the given utility.
        Overrides the parent method, adjust for the lender specific utilities.
        """
        is_intensity_eligible = af().filter_intensity_metric_for_aggregation(
            asset_data, utility
        )

        (
            asset_size_m2_column,
            asset_size_sqft_column,
        ) = {
            Utility.Energy: (
                ec.asset_size_energy_intensity_m2,
                ec.asset_size_energy_intensity_sqft,
            ),
            Utility.GHG: (
                gc.asset_size_ghg_intensity_m2,
                gc.asset_size_ghg_intensity_sqft,
            ),
        }[utility]

        m2_asset_size_owned = (
            asset_data[ac.asset_size_m2] * asset_data[ac.asset_ownership] / 100
        )
        sqft_asset_size_owned = (
            asset_data[ac.asset_size_sqft] * asset_data[ac.asset_ownership] / 100
        )

        if utility == Utility.Energy:
            asset_data[ec.energy_efficiency_area_m2] = m2_asset_size_owned
            asset_data[ec.energy_efficiency_area_sqft] = sqft_asset_size_owned

        asset_data[asset_size_m2_column] = np.where(
            is_intensity_eligible, m2_asset_size_owned, np.nan
        )

        asset_data[asset_size_sqft_column] = np.where(
            is_intensity_eligible, sqft_asset_size_owned, np.nan
        )

        return asset_data

    def add_int_eligibility(self, asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Add eligibility for intensity scoring for the given utility.
        """
        asset_data = self.add_int_eligibility_per_utility(asset_data, Utility.Energy)
        asset_data = self.add_int_eligibility_per_utility(asset_data, Utility.GHG)

        return asset_data

    @staticmethod
    def add_int_eligibility_per_utility(
        asset_data: pd.DataFrame, utility: Utility
    ) -> pd.DataFrame:
        """
        Add intensity eligibility columns for the specified utility and type of eligibility.

        Parameters:
        asset_data (pd.DataFrame): the asset data.
        utility (Utility): the utility to get the calculation for.

        Returns:
        pd.DataFrame: the input data with the added columns.
        """
        int_eligible_column = {
            Utility.Energy: ec.en_int_eligible,
            Utility.GHG: gc.ghg_int_eligible,
        }[utility]

        asset_data[int_eligible_column] = af().filter_intensity_metric_for_aggregation(
            asset_data, utility
        )

        return asset_data
