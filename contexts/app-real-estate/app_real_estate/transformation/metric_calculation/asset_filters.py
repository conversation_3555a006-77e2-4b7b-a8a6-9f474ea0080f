import pandas as pd
import numpy as np
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.ghg_columns as gc
from app_real_estate.constants.helper_enumerators import Utility
import app_real_estate.constants.column_names.water_columns as wc
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.outlier_columns as oc
import app_real_estate.constants.column_names.building_certification_columns as bc
import app_real_estate.constants.building_certification_type as bct
from app_real_estate.transformation.aggregation.validator.validator import (
    Validator,
)


class AssetFilters:
    """
    Class with static asset filters that can be applied on asset input data.
    """

    @staticmethod
    def filter_current_year_operational_assets(
        data: pd.DataFrame,
    ) -> list[bool]:
        """
        Filter a DataFrame based on the 'data_year', 'standing_investment_for_aggregation'
        and 'owned_for_aggregation' columns.
        Here "operational" refers to an asset with no ongoing construction and owned for a period of time respecting the reporting boundaries.

        Parameters:
        data (DataFrame): Input DataFrame containing the required columns and the column 'survey_year'.

        Returns:
        list: Boolean list indicating whether each asset (row) contains data from the current year, is operational (standing investment) and owned.
        """
        data_current_year = data[ac.survey_year].unique()[0] - 1

        is_owned_operational_cy = (
            (data[ac.data_year] == data_current_year)
            & data[ac.standing_investment_for_aggregation]
            & data[ac.owned_for_aggregation]
        ).tolist()

        return is_owned_operational_cy

    @staticmethod
    def filter_outliers(
        data: pd.DataFrame, outlier_status_column: str, accepted_statuses: list
    ) -> list[bool]:
        """
        Filter a DataFrame based on the outlier column `outlier_status_column`.

        Parameters:
        data (DataFrame): Input DataFrame containing the column `outlier_status_column`.
        outlier_status_column (str): String describing the column containing the outlier statuses for the relevant metric and utility.
        accepted_statuses (list): List containing the accepted statuses for outliers between 'none', 'accepted' and 'rejected'.

        Returns:
        list: Boolean list indicating whether each asset's `outlier_status_column` value is in `statuses`.
        """
        if outlier_status_column not in oc.outlier_columns:
            raise ValueError(
                "filter_outliers: outlier_status_column must be one of the columns listed in `app_real_estate.column_names.outlier_columns.py`."
            )

        if not all(
            [status in ["none", "accepted", "rejected"] for status in accepted_statuses]
        ):
            raise ValueError(
                "filter_outliers: accepted_statuses must contain a combination of 'none', 'accepted' and 'rejected'."
            )

        return [status in accepted_statuses for status in data[outlier_status_column]]

    @staticmethod
    def filter_intensity_assets(
        data: pd.DataFrame,
        utility: Utility,
        coverage_threshold: int,
        vacancy_threshold: int = 20,
    ) -> list[bool]:
        """
        Filter a DataFrame to obtain assets eligible for intensity scoring.
        Here "operational" refers to assets that have been standing investments since the beginning of the reported year, hence the use of the column `ncmr_status`.

        Parameters:
        data (DataFrame): Input DataFrame containing the columns `owned_entire_period`, `ncmr_status`, `en_area_time_cov_p` and `asset_vacancy`.
        utility (Utility): String describing the utility to filter for. Should be one of energy, greenhouse gas or water.
        coverage_threshold (int): Integer indicating the minimum accepted asset data coverage for the selected utility.
        vacancy_threshold (int): Integer indicating the maximum accepted asset vacancy rate.

        Returns:
        list: Boolean list indicating whether each asset is eligible for intensity scoring or benchmarking (based on `used_for_benchmark`).
        """
        # Non-standing investment non-performance assets should be removed before running this function
        if any(~data[ac.standing_investment_for_aggregation]):
            raise ValueError("Some assets are not standing investment.")

        (coverage_column, data_availability_column, consumption_column) = {
            Utility.Energy: (
                ec.en_area_time_cov_p,
                ec.en_days_data_avail,
                ec.en_abs_in,
            ),
            Utility.Water: (
                wc.wat_area_time_cov_p,
                wc.wat_days_data_avail,
                wc.wat_abs_in,
            ),
            Utility.GHG: (
                gc.ghg_area_time_cov_p,
                gc.ghg_days_data_avail,
                gc.ghg_abs_in,
            ),
        }[utility]

        is_data_available_entire_year = data[data_availability_column] >= 355
        meets_coverage_threshold = round(data[coverage_column], 4) >= coverage_threshold
        meets_vacancy_threshold = round(data[ac.asset_vacancy], 4) < vacancy_threshold
        has_positive_consumption = data[consumption_column] > 0

        meets_intensity_scoring_conditions = (
            is_data_available_entire_year
            & meets_coverage_threshold
            & meets_vacancy_threshold
            & has_positive_consumption
        ).tolist()

        return meets_intensity_scoring_conditions

    @staticmethod
    def filter_negative_percent_change_rates(
        data: pd.DataFrame, utility: str
    ) -> list[bool]:
        """
        Filter a DataFrame based on the asset's percent change in renewable energy generation or recycled water consumption (depending on `is_energy`).
        The asset should be filtered out if its percent change is negative.
        This is useful for benchmark creation for both EN1 renewable score and WT1 recycling score as those benchmarks should only contain actual "improvements".

        Parameters:
        data (DataFrame): Input DataFrame containing the columns `en_ren_percent_change` and `wat_rec_percent_change`.
        utility (str): String describing the utility to filter for. Should be one of 'en' or 'wat' for energy and water.

        Returns:
        list: Boolean list indicating whether each asset is eligible for EN1 renewable/WT1 recycling benchmark construction.
        """
        if utility not in ["en", "wat"]:
            raise ValueError(
                "filter_negative_percent_change_rates: utility should be one of 'en' or 'wat'."
            )

        percent_change_column = (
            ec.en_ren_percent_change if utility == "en" else wc.wat_rec_percent_change
        )

        is_percent_change_positive = [
            round(percent_change, 4) > 0
            for percent_change in data[percent_change_column]
        ]

        return is_percent_change_positive

    @staticmethod
    def validate_certification_data(certification_data: pd.DataFrame) -> None:
        """
        Validate the certification data.
        """

        if not all(
            certification_data[bc.type].isin(
                [bct.operational, bct.design, bct.interior]
            )
        ):
            raise ValueError(
                "filter_has_certification_data: certification type column contains invalid values."
            )

    @staticmethod
    def filter_asset_on_certification_condition(
        certification_data: pd.DataFrame,
        asset_data: pd.DataFrame,
        condition: np.array,
    ) -> list[bool]:
        """
        Filter assets data based on a condition of the certification_data

        Parameters:
        certification_data (DataFrame): Input DataFrame containing the columns `type`.
        asset_data (DataFrame): Input DataFrame containing the columns `portfolio_asset_id`.
        condition (np.array): conditon resulting in boolean array of the certification_data (DataFrame).
             Example: np.logical_or(
                certification_data[bc.type] == bct.design,
                certification_data[bc.type] == bct.interior
            )

        Returns:
        list: Boolean list
        """
        AssetFilters.validate_certification_data(certification_data)

        certification_on_type = certification_data[condition]

        if certification_on_type.empty:
            return [False] * len(asset_data)

        return (
            asset_data[ac.portfolio_asset_id]
            .isin(certification_on_type[ac.portfolio_asset_id])
            .to_list()
        )

    @staticmethod
    def is_operational_asset(df: pd.DataFrame) -> list[bool]:
        """
        filter for current year, and last year operational data.

        Parameters:
        df (DataFrame): Input DataFrame containing the columns
        `portfolio_asset_id`, `data_year`, `owned_for_aggregation`,
        `standing_investment_for_aggregation`.

        Returns:
        list: Boolean list indicating whether each asset is operational.
        """
        req_columns = [
            ac.portfolio_asset_id,
            ac.data_year,
            ac.owned_for_aggregation,
            ac.standing_investment_for_aggregation,
            ac.survey_year,
        ]

        Validator.validate_missing_column(df, req_columns)

        current_year = np.max(df[ac.data_year].values)
        is_current_year_data = np.equal(df[ac.data_year].values, current_year)

        is_operational_cy = AssetFilters.filter_current_year_operational_assets(df)

        opr_asset_id_cy = df[is_operational_cy][ac.portfolio_asset_id]
        is_opr_asset_cy = df[ac.portfolio_asset_id].isin(opr_asset_id_cy)

        is_operational_ly = np.logical_and(
            ~is_current_year_data,
            np.logical_and(is_opr_asset_cy, df[ac.standing_investment_for_aggregation]),
        )

        is_opr_data = np.logical_or(is_operational_cy, is_operational_ly)

        return is_opr_data

    @staticmethod
    def is_performance_component_asset(df: pd.DataFrame) -> list[bool]:
        """Execute the filter"""
        Validator.validate_missing_column(df, [ac.comp_perf])
        is_opr_data = AssetFilters.is_operational_asset(df)

        is_perf_comp_data = np.logical_and(is_opr_data, df[ac.comp_perf])

        return is_perf_comp_data

    @staticmethod
    def filter_whole_building_assets_per_utility(
        asset_data: pd.DataFrame, utility: Utility
    ) -> pd.Series:
        """
        Filter assets on whether they are reported as whole building or not.
        The methodology is different based on utility.

        Parameters:
        asset_data (pd.DataFrame): containing the asset information.
        utility (Utility): indicating for which utility to get the assets.

        Returns:
            pd.Series: a boolean iterable indicating whether the asset is reported
            as whole building for this utility.
        """
        if utility not in [Utility.Energy, Utility.Water]:
            raise ValueError(
                "filter_whole_building_assets_per_utility: utility should be one of Energy or Water."
            )

        if utility == Utility.Energy:
            return asset_data[ac.whole_building].astype(bool)

        return ~asset_data[wc.wat_tot_w].isna()

    def filter_lfl_metric_for_aggregation(
        self,
        data: pd.DataFrame,
        utility: Utility,
        for_benchmark: bool,
        lfl_metric: str = None,
    ) -> list[bool]:
        """
        Filter assets based on their outlier status and whether they are scored for LFL or Energy Efficiency.
        Parameters:
            data (pd.DataFrame): containing the asset information.
            utility (Utility): indicating for which utility to check the outlier status.
            for_benchmark (bool): indicating whether to remove soft outliers and values lower or higher than -30 and 30.
            lfl_metric (str): LFL metric column name to filter values on.

        Returns:
            list[bool]: Boolean list indicating whether the asset is eligible for LFL aggregation.
        """
        if for_benchmark and not lfl_metric:
            raise ValueError(
                "No LFL metric provided though the filter is called `for benchmark`."
            )
        # Filter outliers
        outlier_col = {
            Utility.Energy: ec.en_lfl_outlier_status,
            Utility.Water: wc.wat_lfl_outlier_status,
            Utility.GHG: gc.ghg_lfl_outlier_status,
        }[utility]

        accepted_statuses = ["none"] if for_benchmark else ["none", "accepted"]
        is_not_outlier = self.filter_outliers(data, outlier_col, accepted_statuses)

        # Filter on LFL eligibility
        lfl_p_col = {
            Utility.Energy: ec.en_lfl_p,
            Utility.Water: wc.wat_lfl_p,
            Utility.GHG: gc.ghg_lfl_p,
        }[utility]
        is_lfl_eligible = ~data[lfl_p_col].isna()

        is_eligible_for_lfl_aggregation = np.logical_and(
            is_not_outlier, is_lfl_eligible
        )

        # If calculating an energy non-benchmark metric, remove assets that have an energy efficiency score
        if utility == Utility.Energy and not for_benchmark:
            is_not_eligible_for_en_efficiency = pd.Series(
                data[sc.score_en1_energy_efficiency].isna()
            )
            is_eligible_for_lfl_aggregation = np.logical_and(
                is_eligible_for_lfl_aggregation, is_not_eligible_for_en_efficiency
            )

        # If the filter is for benchmark metrics, remove values above/below 30/-30.
        if for_benchmark:
            is_lfl_normal = data[lfl_metric].between(-30, 30)
            is_eligible_for_lfl_aggregation = np.logical_and(
                is_eligible_for_lfl_aggregation, is_lfl_normal
            )

        return is_eligible_for_lfl_aggregation.tolist()

    def filter_intensity_metric_for_aggregation(
        self,
        data: pd.DataFrame,
        utility: Utility,
    ) -> list[bool]:
        """
        Filter assets based on their outlier status and whether they are eligible for an energy intensity calculation.
        Parameters:
            data (pd.DataFrame): containing the asset information.
            utility (Utility): indicating for which utility to check the aggregation eligibility.

        Returns:
            list[bool]: Boolean list indicating whether the asset is eligible for intensity aggregation.
        """
        outlier_col = {
            Utility.Energy: ec.en_int_outlier_status,
            Utility.Water: wc.wat_int_outlier_status,
            Utility.GHG: gc.ghg_int_outlier_status,
        }[utility]

        is_calculation_eligible = self.filter_intensity_assets(data, utility, 75)
        is_intensity_outlier = self.filter_outliers(
            data, outlier_col, ["none", "accepted"]
        )

        return np.logical_and(is_calculation_eligible, is_intensity_outlier).tolist()
