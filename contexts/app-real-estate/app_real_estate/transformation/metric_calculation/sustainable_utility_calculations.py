import numpy as np
import pandas as pd

from app_real_estate.constants.helper_enumerators import Utility
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.water_columns as wc
import app_real_estate.constants.column_names.waste_columns as wsc


class SustainableUtilityCalculations:

    def add_sustainable_utility_calculations(
        self, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Performs all the sustainable utility related calculations.

        Parameters:
            asset_data (pd.DataFrame): the asset data with both years.
        Returns:
        (pd.DataFrame): the asset data with all sustainable utility metrics added.
        """
        asset_data = self.add_previous_year_sustainable_consumption(asset_data)
        asset_data = self.add_renewable_generation_per_area(asset_data)
        asset_data = self.add_recycled_water_consumption_on_site(asset_data)

        asset_data = self.add_renewable_energy_rate(asset_data)
        asset_data = self.add_recycled_water_rate(asset_data)

        asset_data = self.add_previous_year_sustainable_utility_rates(asset_data)

        asset_data = self.add_renewable_energy_percent_change_rate(asset_data)
        asset_data = self.add_recycled_water_percent_change_rate(asset_data)

        return asset_data

    def add_sustainable_utility_calculates_per_utility(
        self, asset_data: pd.DataFrame, utility: Utility
    ) -> pd.DataFrame:
        """
        Performs all the sustainable utility related calculations per utility.

        Parameters:
            asset_data (pd.DataFrame): the asset data with both years.
            utility (Utility): the utility for which to compute the metrics.
        Returns:
        (pd.DataFrame): the asset data with all sustainable utility metrics added.
        """
        asset_data = self.add_previous_year_sustainable_consumption_per_utility(
            asset_data, utility
        )
        if utility == Utility.Energy:
            asset_data = self.add_renewable_generation_per_area(asset_data)
            asset_data = self.add_renewable_energy_rate(asset_data)
        elif utility == Utility.Water:
            asset_data = self.add_recycled_water_consumption_on_site(asset_data)
            asset_data = self.add_recycled_water_rate(asset_data)

        asset_data = self.add_previous_year_sustainable_utility_rates(asset_data)

        if utility == Utility.Energy:
            asset_data = self.add_renewable_energy_percent_change_rate(asset_data)
        elif utility == Utility.Water:
            asset_data = self.add_recycled_water_percent_change_rate(asset_data)

        return asset_data

    @staticmethod
    def add_renewable_energy_rate(asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Add the renewable energy generation/consumption as a percentage
        of total energy consumption.

        Parameters:
            asset_data (pd.DataFrame): the asset DataFrame with both data years containing energy and asset characteristics columns.

        Returns:
            pd.DataFrame: The input DataFrame with the renewable energy rate.
        """
        asset_data[ec.en_ren_rate] = (
            asset_data[ec.en_ren_abs].div(asset_data[ec.en_abs], fill_value=0) * 100
        ).copy()
        asset_data[ec.en_ren_rate] = asset_data[ec.en_ren_rate].replace(
            {np.inf: 0, np.nan: 0}
        )

        return asset_data

    @staticmethod
    def add_recycled_water_rate(asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Add the water recycled/reused as a percentage of total water consumption.

        Parameters:
            asset_data (pd.DataFrame): the asset DataFrame with both data years containing water and asset characteristics columns.

        Returns:
            pd.DataFrame: The input DataFrame with the recycled water rate.
        """
        asset_data[wc.wat_rec_rate] = (
            asset_data[wc.wat_rec_abs].div(asset_data[wc.wat_abs], fill_value=0) * 100
        )
        asset_data[wc.wat_rec_rate] = asset_data[wc.wat_rec_rate].replace(
            {np.inf: 0, np.nan: 0}
        )

        return asset_data

    def add_renewable_energy_percent_change_rate(
        self,
        asset_data: pd.DataFrame,
    ) -> pd.DataFrame:
        """
        Adds the renewable energy percent change.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame with both data years containing energy and asset characteristics columns.

        Returns:
        pd.DataFrame: The input DataFrame with the renewable energy consumption and percent change.
        """
        # First, compute the percent rates of renewable energy for current and previous year.
        # If no renewable energy is generated, then the percentage is 0, not missing.
        asset_data = self.add_renewable_energy_rate(asset_data)

        # Split data into two datasets
        is_current_year = asset_data[ac.data_year] == asset_data[ac.survey_year] - 1
        cy_data = asset_data[is_current_year]
        ly_data = asset_data[~is_current_year][[ac.portfolio_asset_id, ec.en_ren_rate]]

        # Rename columns for previous year data to avoid conflicts during merge
        ly_data.rename(columns={ec.en_ren_rate: "en_ren_rate_ly"}, inplace=True)

        # Merge them together into a wider (one row per asset) DataFrame for calculation
        merged_data = cy_data.merge(ly_data, on=ac.portfolio_asset_id, how="left")

        # Compute the percent change for each asset, assets without previous year data get NA.
        merged_data[ec.en_ren_percent_change] = (
            merged_data[ec.en_ren_rate] - merged_data.en_ren_rate_ly
        )

        # Merge back into the longer format, keeping the percent change values in the "current year" rows.
        asset_data = asset_data.merge(
            merged_data[
                [ac.portfolio_asset_id, ac.data_year, ec.en_ren_percent_change]
            ],
            on=[ac.portfolio_asset_id, ac.data_year],
            how="left",
        )

        return asset_data

    def add_recycled_water_percent_change_rate(
        self,
        asset_data: pd.DataFrame,
    ) -> pd.DataFrame:
        """
        Adds the recycled/reused water percent change.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame with both data years containing water and asset characteristics columns.

        Returns:
        pd.DataFrame: The input DataFrame with the recycled/reused water rate and the percent change.
        """
        # First, compute the percent rates of recycled water for current and previous year.
        asset_data = self.add_recycled_water_rate(asset_data)

        # Split data into two datasets
        is_current_year = asset_data[ac.data_year] == asset_data[ac.survey_year] - 1
        cy_data = asset_data[is_current_year]
        ly_data = asset_data[~is_current_year][[ac.portfolio_asset_id, wc.wat_rec_rate]]

        # Merge them together into a wider (one row per asset) DataFrame for calculation
        merged_data = cy_data.merge(
            ly_data,
            on=ac.portfolio_asset_id,
            how="left",
            suffixes=(None, "_ly"),
        )

        # Compute the percent change for each asset, assets without previous year data get NA.
        merged_data[wc.wat_rec_percent_change] = (
            merged_data[wc.wat_rec_rate] - merged_data.wat_rec_rate_ly
        )

        # Merge back into the longer format, keeping the percent change values in the "current year" rows.
        asset_data = asset_data.merge(
            merged_data[
                [
                    ac.portfolio_asset_id,
                    ac.data_year,
                    wc.wat_rec_percent_change,
                ]
            ],
            on=[ac.portfolio_asset_id, ac.data_year],
            how="left",
        )

        return asset_data

    @staticmethod
    def add_renewable_generation_per_area(
        asset_data: pd.DataFrame,
    ) -> pd.DataFrame:
        """
        Adds the absolute amount of renewable energy generated on and off site.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame containing energy and
            asset characteristics columns.

        Returns:
        pd.DataFrame: The input DataFrame with the on and off site generated
            renewable energy amount.
        """
        # Define columns to sum for each area.
        renewable_onsite_columns = [
            ec.en_ren_ons_con,
            ec.en_ren_ons_exp,
            ec.en_ren_ons_tpt,
        ]
        renewable_offsite_columns = [ec.en_ren_ofs_pbl, ec.en_ren_ofs_pbt]

        # Sum the columns
        asset_data = asset_data.copy()
        asset_data[ec.en_ren_ons] = asset_data[renewable_onsite_columns].sum(axis=1)
        asset_data[ec.en_ren_ofs] = asset_data[renewable_offsite_columns].sum(axis=1)

        return asset_data

    @staticmethod
    def add_recycled_water_consumption_on_site(
        asset_data: pd.DataFrame,
    ) -> pd.DataFrame:
        """
        Adds the absolute amount of water reused/captured/extracted on-site.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame containing water and
            asset characteristics columns.

        Returns:
        pd.DataFrame: The input DataFrame with the on-site recycled
            water amount.
        """
        # Define columns to sum for each area.
        recycled_onsite_columns = [
            wc.wat_rec_ons_reu,
            wc.wat_rec_ons_cap,
            wc.wat_rec_ons_ext,
        ]
        # Sum the columns
        asset_data = asset_data.copy()
        asset_data[wc.wat_rec_ons] = asset_data[recycled_onsite_columns].sum(axis=1)

        return asset_data

    def add_previous_year_sustainable_utility_rates(
        self, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Add the previous year renewable energy, recycled water and diverted waste
        rates to the "current year" rows of the asset data.

        Parameters:
        asset_data (pd.DataFrame): the asset data with both years.
        utility (Utility): the utility.

        Returns:
        pd.DataFrame: the input data still with both years with "previous year"
            columns for sustainable utility consumption rates in the "current year"
            rows.
        """
        asset_data = self.add_previous_year_sustainable_utility_rates_per_utility(
            asset_data, Utility.Energy
        )
        asset_data = self.add_previous_year_sustainable_utility_rates_per_utility(
            asset_data, Utility.Water
        )
        asset_data = self.add_previous_year_sustainable_utility_rates_per_utility(
            asset_data, Utility.Waste
        )

        return asset_data

    @staticmethod
    def add_previous_year_sustainable_utility_rates_per_utility(
        asset_data: pd.DataFrame, utility: Utility
    ) -> pd.DataFrame:
        """
        Add the previous year renewable energy, recycled water or diverted waste (depending on `utility`)
        rates to the "current year" rows of the asset data.

        Parameters:
        asset_data (pd.DataFrame): the asset data with both years.
        utility (Utility): the utility.

        Returns:
        pd.DataFrame: the input data still with both years with "previous year"
            columns for sustainable utility consumption rates in the "current year"
            rows.
        """
        cy_rate, ly_rate = {
            Utility.Energy: (ec.en_ren_rate, ec.en_ren_rate_ly),
            Utility.Water: (wc.wat_rec_rate, wc.wat_rec_rate_ly),
            Utility.Waste: (wsc.was_pabs_div, wsc.was_pabs_div_ly),
        }[utility]

        # Split data into two datasets
        is_current_year = asset_data[ac.data_year] == asset_data[ac.survey_year] - 1
        cy_data = asset_data[is_current_year][[ac.portfolio_asset_id, ac.data_year]]
        ly_data = asset_data[~is_current_year][[ac.portfolio_asset_id, cy_rate]]

        # Rename columns from previous year data to the set "previous year" names
        ly_data = ly_data.rename(columns={cy_rate: ly_rate})

        # Merge them together into a wider (one row per asset) DataFrame
        merged_data = cy_data.merge(ly_data, on=ac.portfolio_asset_id, how="left")

        # Merge back into the longer format, keeping the "previous year" columns in the "current year" rows.
        asset_data = asset_data.merge(
            merged_data[[ac.portfolio_asset_id, ac.data_year, ly_rate]],
            on=[ac.portfolio_asset_id, ac.data_year],
            how="left",
        )

        return asset_data

    def add_previous_year_sustainable_consumption(
        self, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Add variables containing the previous year absolute consumptions
        for all utility.
        """
        asset_data = self.add_previous_year_sustainable_consumption_per_utility(
            asset_data, Utility.Energy
        )
        asset_data = self.add_previous_year_sustainable_consumption_per_utility(
            asset_data, Utility.Water
        )
        asset_data = self.add_previous_year_sustainable_consumption_per_utility(
            asset_data, Utility.Waste
        )

        return asset_data

    @staticmethod
    def add_previous_year_sustainable_consumption_per_utility(
        asset_data: pd.DataFrame, utility: Utility
    ) -> pd.DataFrame:
        """
        Add a variable containing the previous year absolute consumption for the given utility.
        """
        sus_cons_col_cy, sus_cons_col_ly = {
            Utility.Energy: (ec.en_ren_abs, ec.en_ren_abs_ly),
            Utility.Water: (wc.wat_rec_abs, wc.wat_rec_abs_ly),
            Utility.Waste: (wsc.was_abs_div, wsc.was_abs_div_ly),
        }[utility]

        if sus_cons_col_ly in asset_data.columns:
            return asset_data

        survey_year = asset_data[ac.survey_year].unique()[0]
        is_current_year = asset_data[ac.data_year] == survey_year - 1
        ly_data = asset_data.loc[
            ~is_current_year, [ac.portfolio_asset_id, ac.data_year, sus_cons_col_cy]
        ]
        ly_data = ly_data.rename(columns={sus_cons_col_cy: sus_cons_col_ly})
        ly_data[ac.data_year] = survey_year - 1

        result = asset_data.merge(
            ly_data, on=[ac.portfolio_asset_id, ac.data_year], how="left"
        )

        return result
