import pandas as pd
from app_real_estate.transformation.metric_calculation.asset_characteristics_calculation import (
    AssetCharacteristicsCalculations,
)
from app_real_estate.constants.helper_enumerators import Utility


class LenderAssetCharacteristicsCalculations(AssetCharacteristicsCalculations):

    def add_owned_area_weight(self, asset_data: pd.DataFrame) -> pd.DataFrame:
        asset_data = self.add_owned_area_weight_per_utility(asset_data, Utility.Energy)
        asset_data = self.add_owned_area_weight_per_utility(asset_data, Utility.GHG)

        return asset_data
