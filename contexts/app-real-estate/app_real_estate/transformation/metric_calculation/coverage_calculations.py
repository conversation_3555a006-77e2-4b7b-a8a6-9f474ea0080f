import pandas as pd
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.water_columns as wc
import app_real_estate.constants.column_names.waste_columns as wsc
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.ghg_columns as gc
from app_real_estate.constants.helper_enumerators import Utility


class CoverageCalculations:

    def add_coverage_metrics(self, asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Add all coverage metrics to the asset data.

        Parameters:
            asset_data (pd.DataFrame): the asset data.
        Returns:
            pd.DataFrame: the asset data with all coverage metrics added.
        """
        asset_data = self.add_cross_control_area_time_weights(asset_data)
        asset_data = self.add_owned_area_time_weight_per_control(asset_data)

        return asset_data

    def add_coverage_metrics_per_utility(
        self, asset_data: pd.DataFrame, utility: Utility
    ) -> pd.DataFrame:
        """
        Add all energy coverage metrics to the asset data.

        Parameters:
            asset_data (pd.DataFrame): the asset data.
            utility (Utility): the utility for which to compute the metrics.
        Returns:
            pd.DataFrame: the asset data with all energy coverage metrics added.
        """
        asset_data = self.add_cross_control_area_time_weights_per_utility(
            asset_data, utility
        )
        asset_data = self.add_owned_area_time_weight_per_control_per_utility(
            asset_data, utility
        )

        return asset_data

    def add_owned_area_time_weight_per_control(
        self, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Add the owned area-time weight used to perform the weighted mean on
        LC/TC or S12/S3 coverage metrics.

        Parameters:
        asset_data (pd.DataFrame): the asset data.

        Returns:
        pd.DataFrame: the input data with the added columns.
        """
        asset_data = self.add_owned_area_time_weight_per_control_per_utility(
            asset_data, Utility.Energy
        )
        asset_data = self.add_owned_area_time_weight_per_control_per_utility(
            asset_data, Utility.GHG
        )
        asset_data = self.add_owned_area_time_weight_per_control_per_utility(
            asset_data, Utility.Water
        )
        asset_data = self.add_owned_area_time_weight_per_control_per_utility(
            asset_data, Utility.Waste
        )

        return asset_data

    @staticmethod
    def add_owned_area_time_weight_per_control_per_utility(
        asset_data: pd.DataFrame, utility: Utility
    ) -> pd.DataFrame:
        """
        Add the owned area-time weight used to perform the weighted mean on
        LC/TC or S12/S3 coverage metrics for a given utility.

        Parameters:
        asset_data (pd.DataFrame): the asset data.

        Returns:
        pd.DataFrame: the input data with the added columns.
        """
        owned_weight_column_1, weight_column_1 = {
            Utility.Energy: (
                sc.en_area_time_weight_lc_owned,
                ec.en_area_time_weight_lc,
            ),
            Utility.GHG: (
                sc.ghg_area_time_weight_s12_owned,
                gc.ghg_area_time_weight_s12,
            ),
            Utility.Water: (
                sc.wat_area_time_weight_lc_owned,
                wc.wat_area_time_weight_lc,
            ),
            Utility.Waste: (
                sc.was_area_weight_lc_owned,
                wsc.was_area_weight_lc,
            ),
        }[utility]

        owned_weight_column_2, weight_column_2 = {
            Utility.Energy: (
                sc.en_area_time_weight_tc_owned,
                ec.en_area_time_weight_tc,
            ),
            Utility.GHG: (sc.ghg_area_time_weight_s3_owned, gc.ghg_area_time_weight_s3),
            Utility.Water: (
                sc.wat_area_time_weight_tc_owned,
                wc.wat_area_time_weight_tc,
            ),
            Utility.Waste: (
                sc.was_area_weight_tc_owned,
                wsc.was_area_weight_tc,
            ),
        }[utility]

        asset_data[owned_weight_column_1] = (
            asset_data[weight_column_1].mul(asset_data[ac.asset_ownership]).div(100)
        )
        asset_data[owned_weight_column_2] = (
            asset_data[weight_column_2].mul(asset_data[ac.asset_ownership]).div(100)
        )

        return asset_data

    def add_cross_control_area_time_weights(
        self, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Add a new area-time weight column for cross-control data coverage.
        Parameters:
            asset_data (pd.DataFrame): the asset data.
        Returns:
        pd.DataFrame: the input data with the added columns.
        """
        asset_data = self.add_cross_control_area_time_weights_per_utility(
            asset_data, Utility.Energy
        )
        asset_data = self.add_cross_control_area_time_weights_per_utility(
            asset_data, Utility.GHG
        )
        asset_data = self.add_cross_control_area_time_weights_per_utility(
            asset_data, Utility.Water
        )

        return asset_data

    @staticmethod
    def add_cross_control_area_time_weights_per_utility(
        asset_data: pd.DataFrame, utility: Utility
    ) -> pd.DataFrame:
        """
        Add a new area-time weight column for cross-control data coverage
        for the given utility.

        Parameters:
        asset_data (pd.DataFrame): the asset data.

        Returns:
        pd.DataFrame: the input data with the added columns.
        """
        weight_column, data_time_avail_column = {
            Utility.Energy: (sc.en_area_time_weight_owned, ec.en_max_days_data_avail),
            Utility.GHG: (sc.ghg_area_time_weight_owned, gc.ghg_max_days_data_avail),
            Utility.Water: (sc.wat_area_time_weight_owned, wc.wat_max_days_data_avail),
        }[utility]

        asset_data[weight_column] = (
            asset_data[ac.asset_size]
            .mul(asset_data[ac.asset_ownership])
            .div(100)
            .mul(asset_data[data_time_avail_column])
        )

        return asset_data

    def add_coverage_weighted_per_control(
        self, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        This function is only used for aggregation.
        Add a new area-time coverage column weighted by the area-time weight per control.

        Parameters:
        asset_data (pd.DataFrame): the current year asset data.

        Returns:
        pd.DataFrame: the input data with the added columns.
        """
        asset_data = self.add_coverage_weighted_per_control_per_utility(
            asset_data, Utility.Energy
        )
        asset_data = self.add_coverage_weighted_per_control_per_utility(
            asset_data, Utility.GHG
        )
        asset_data = self.add_coverage_weighted_per_control_per_utility(
            asset_data, Utility.Water
        )
        asset_data = self.add_coverage_weighted_per_control_per_utility(
            asset_data, Utility.Waste
        )
        return asset_data

    @staticmethod
    def add_coverage_weighted_per_control_per_utility(
        asset_data: pd.DataFrame, utility: Utility
    ) -> pd.DataFrame:
        """
        This function is only used for aggregation.
        Add a new area-time coverage column weighted by the area-time weight per control for the given utility.

        Parameters:
        asset_data (pd.DataFrame): the current year asset data.
        utility (Utility): the utility to weight the coverage for.

        Returns:
        pd.DataFrame: the input data with the added columns.
        """
        (
            cov_column_1,
            weight_column_1,
            result_column_1,
            cov_column_2,
            weight_column_2,
            result_column_2,
        ) = {
            Utility.Energy: (
                ec.en_area_time_cov_p_lc,
                sc.en_area_time_weight_lc_owned,
                ec.en_area_time_cov_p_lc_agg,
                ec.en_area_time_cov_p_tc,
                sc.en_area_time_weight_tc_owned,
                ec.en_area_time_cov_p_tc_agg,
            ),
            Utility.GHG: (
                gc.ghg_area_time_cov_p_s12,
                sc.ghg_area_time_weight_s12_owned,
                gc.ghg_area_time_cov_p_s12_agg,
                gc.ghg_area_time_cov_p_s3,
                sc.ghg_area_time_weight_s3_owned,
                gc.ghg_area_time_cov_p_s3_agg,
            ),
            Utility.Water: (
                wc.wat_area_time_cov_p_lc,
                sc.wat_area_time_weight_lc_owned,
                wc.wat_area_time_cov_p_lc_agg,
                wc.wat_area_time_cov_p_tc,
                sc.wat_area_time_weight_tc_owned,
                wc.wat_area_time_cov_p_tc_agg,
            ),
            Utility.Waste: (
                wsc.was_area_cov_p_lc,
                sc.was_area_weight_lc_owned,
                wsc.was_area_cov_p_lc_agg,
                wsc.was_area_cov_p_tc,
                sc.was_area_weight_tc_owned,
                wsc.was_area_cov_p_tc_agg,
            ),
        }[
            utility
        ]

        asset_data[result_column_1] = (
            asset_data[cov_column_1] * asset_data[weight_column_1]
        )
        asset_data[result_column_2] = (
            asset_data[cov_column_2] * asset_data[weight_column_2]
        )

        return asset_data

    @staticmethod
    def add_coverage_weighted(asset_data: pd.DataFrame):
        """
        This function is only used during aggregation.
        TODO: Let's find a way to make the framework do that without requiring a function here. Maybe by simply moving this function.
        Weights the area-time coverage column by the area-time weight for
        cross-control data coverage and LFL percent changes.

        Parameters:
        asset_data (pd.DataFrame): the asset-level or property subtype / country level data.

        Returns:
        pd.DataFrame: the input data with the weighted columns.
        """
        asset_data[ec.en_area_time_cov_p] = asset_data[ec.en_area_time_cov_p].mul(
            asset_data[sc.en_area_time_weight_owned]
        )

        asset_data[gc.ghg_area_time_cov_p] = asset_data[gc.ghg_area_time_cov_p].mul(
            asset_data[sc.ghg_area_time_weight_owned]
        )

        asset_data[wc.wat_area_time_cov_p] = asset_data[wc.wat_area_time_cov_p].mul(
            asset_data[sc.wat_area_time_weight_owned]
        )

        asset_data[wsc.was_area_cov_p] = asset_data[wsc.was_area_cov_p].mul(
            asset_data[sc.asset_size_owned_m2]
        )

        return asset_data
