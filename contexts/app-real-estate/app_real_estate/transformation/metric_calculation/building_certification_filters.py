import numpy as np
import pandas as pd
import app_real_estate.constants.column_names.building_certification_columns as bcc
from app_real_estate.transformation.metric_calculation.common_operations import (
    CommonOperations,
)


class BuildingCertificationFilters:

    def filter_certifications_for_scoring(
        self, certification_data: pd.DataFrame, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Apply all filters and cleaning steps to the certification data.

        Parameters:
            certification_data: Dataframe containing certification data.
            asset_data: Dataframe containing asset data.
        Returns:
            (pd.DataFrame): Dataframe containing filtered and cleaned certification data.
        """
        # Only keep certifications belonging to present assets
        is_performance_certification = CommonOperations.filter_active_certifications(
            certification_data, asset_data
        )
        certification_data = certification_data[is_performance_certification]

        # Filter out the certifications with 'both' type
        is_both_type = BuildingCertificationFilters.filter_certifications_per_type(
            certification_data, "both"
        )
        certification_data = certification_data[~np.array(is_both_type)]

        # Convert the certification year to integer
        certification_data = BuildingCertificationFilters.convert_certification_year(
            certification_data
        )

        return certification_data

    @staticmethod
    def filter_certifications_per_type(
        data: pd.DataFrame, building_type: str
    ) -> list[bool]:
        """
        Filter a DataFrame based on the column 'ype'.
        Building certifications are treated differently whether they apply to operational buildings or not.
        They are separated into two indicators BC1.1 and BC1.2.

        Parameters:
        data (DataFrame): Input DataFrame containing the column 'type'.
        building_type (str): String indicating the type of building to filter for.

        Returns:
        list: Boolean list indicating whether each certification (row) is of the chosen 'building_type'.
        """
        # Check if 'type' column exists in the DataFrame
        if bcc.type not in data.columns:
            raise ValueError(
                "filter_certifications_per_type: The input DataFrame must contain a 'type' column."
            )

        # Validate input type
        if not isinstance(building_type, str):
            raise TypeError(
                "filter_certifications_per_type: The building_type parameter must be a string."
            )

        # Perform the filtering based on the 'type' column
        return data[bcc.type] == building_type

    @staticmethod
    def convert_certification_year(
        certification_data: pd.DataFrame,
    ) -> pd.DataFrame:
        """
        Converts the certification `year` column to integer.
        The `year` column dtype is automatically set to float because it has missing
        values, however we want it to be of integer type in order to get an integer
        age.

        Parameters:
        certification_data (pd.DataFrame): contains the building certifications data.

        Returns:
        pd.DataFrame: the input DataFrame with the `year` column converted to int.
        """

        certification_data[bcc.year] = certification_data[bcc.year].astype(int)

        return certification_data
