import pandas as pd
import numpy as np
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.building_certification_columns as bc
import app_real_estate.constants.column_names.energy_rating_columns as ec


class AssetAggregationCalculation:
    """
    Class responsible for aggregating from the building certifications and energy ratings datasets
    and adding in the asset data the metrics used in scoring.
    """

    @staticmethod
    def add_certification_coverage(
        asset_data: pd.DataFrame, certification_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Adds the percentage of floor area certified by at least one building certification
        to the asset dataset by aggregating the assets' certifications data.

        Parameters:
        asset_data (pd.DataFrame): a DataFrame containing the asset data.
        certification_data (pd.DataFrame): a DataFrame containing the building certifications data.

        Returns:
        pd.DataFrame: the asset_data with the newly added 'certification_coverage' column.
        """
        # Sum the certifications coverages per asset, if bigger than 100, round to 100.
        agg_certification_data = certification_data.groupby(
            bc.portfolio_asset_id, as_index=False
        )[bc.scoring_coverage].sum()
        agg_certification_data[bc.scoring_coverage] = np.minimum(
            agg_certification_data[bc.scoring_coverage], 100
        )
        agg_certification_data.rename(
            columns={bc.scoring_coverage: ac.certification_coverage}, inplace=True
        )

        # Add the newly calculated coverage to the asset data.
        asset_data = asset_data.merge(
            agg_certification_data, on=ac.portfolio_asset_id, how="left"
        )

        return asset_data

    @staticmethod
    def add_certified_floor_area(asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Adds the total floor area certfied by at least one building certification to the asset dataset.

        Parameters:
        asset_data (pd.DataFrame): a DataFrame containing the asset data.

        Returns:
        pd.DataFrame: the asset_data with the newly added 'certified_floor_area_m2' column.
        """
        asset_data[ac.certified_floor_area_m2] = (
            asset_data[ac.asset_size_m2] * asset_data[ac.certification_coverage] / 100
        )

        return asset_data

    @staticmethod
    def add_rating_coverage(
        asset_data: pd.DataFrame, rating_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Adds the percentage of floor area certified by at least one energy rating
        to the asset dataset by aggregating the assets' energy ratings data.

        Parameters:
        asset_data (pd.DataFrame): a DataFrame containing the asset data.
        rating_data (pd.DataFrame): a DataFrame containing the energy ratings data.

        Returns:
        pd.DataFrame: the asset_data with the newly added 'rating_coverage' column.
        """
        # Sum the ratings coverages per asset, if bigger than 100, round to 100.
        agg_rating_data = rating_data.groupby(bc.portfolio_asset_id, as_index=False)[
            ec.coverage
        ].sum()
        agg_rating_data[ec.coverage] = np.minimum(agg_rating_data[ec.coverage], 100)
        agg_rating_data.rename(columns={ec.coverage: ac.rating_coverage}, inplace=True)

        # Add the newly calculated coverage to the asset data.
        asset_data = asset_data.merge(
            agg_rating_data, on=ac.portfolio_asset_id, how="left"
        )

        is_coverage_missing = asset_data[ac.rating_coverage].isna()
        asset_data.loc[is_coverage_missing, ac.rating_coverage] = 0

        return asset_data

    @staticmethod
    def add_rated_floor_area(asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Adds the total floor area certified by at least one energy rating to the asset dataset.

        Parameters:
        asset_data (pd.DataFrame): a DataFrame containing the asset data.

        Returns:
        pd.DataFrame: the asset_data with the newly added 'rated_floor_area_m2' column.
        """
        asset_data[ac.rated_floor_area_m2] = (
            asset_data[ac.asset_size_m2] * asset_data[ac.rating_coverage] / 100
        )

        return asset_data
