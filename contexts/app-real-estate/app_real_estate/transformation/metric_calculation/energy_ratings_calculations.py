import pandas as pd
import app_real_estate.constants.column_names.energy_rating_columns as ec
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
from app_real_estate.transformation.metric_calculation.common_operations import (
    CommonOperations,
)


class EnergyRatingsCalculations:
    """
    Class responsible for calculating and adding in the energy ratings data the metrics used in scoring.
    """

    @staticmethod
    def add_data_coverage(rating_data: pd.DataFrame) -> pd.DataFrame:
        """
        Adds the floor data coverage for each energy rating.

        Parameters:
        rating_data (pd.DataFrame): a DataFrame containing the energy ratings.

        Returns:
        pd.DataFrame: a DataFrame containing the newly added 'coverage' column.
        """
        rating_data[ec.coverage] = (
            rating_data[ec.covered_floor_area_m2] / rating_data[ec.asset_size_m2] * 100
        )
        return rating_data

    @staticmethod
    def get_asset_size(
        rating_data: pd.DataFrame, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Get the asset size of the energy rating.
        """
        return CommonOperations.get_asset_size(
            left_data=rating_data,
            right_data=asset_data,
            key=ac.portfolio_asset_id,
        )

    @staticmethod
    def add_covered_floor_area_m2(
        rating_data: pd.DataFrame, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Adds the covered floor area in m2 for each energy rating.
        """
        return CommonOperations.add_covered_floor_area_m2(
            data=rating_data, asset_data=asset_data
        )

    @staticmethod
    def get_asset_ownership(
        energy_ratings_data: pd.DataFrame, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Retrieve the asset ownership of the building certification.

        Parameters:
        energy_ratings_data (pd.DataFrame): a DataFrame containing the assets' energy ratings.
        asset_data (pd.DataFrame): a DataFrame containing the asset data.

        Returns:
        pd.DataFrame: a DataFrame with the asset ownership information merged.
        """
        return CommonOperations.get_asset_ownership(
            left_data=energy_ratings_data,
            right_data=asset_data,
            key=ec.portfolio_asset_id,
        )
