import numpy as np
import pandas as pd
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.ghg_columns as gc
import app_real_estate.constants.column_names.water_columns as wc
from app_real_estate.constants.helper_enumerators import Utility
from app_real_estate.transformation.metric_calculation.asset_filters import AssetFilters
from functools import reduce


class LFLMetricAggregator:
    """
    Class providing the functions to aggregate asset-level data to various levels of aggregation
    and calculate the LFL-related metrics shown in the Benchmark Reports.
    The process of aggregating metrics for the Reports is usually done after scoring.

    These metrics are:
    - LFL absolute change
    - LFL percent change (per control/scope and cross-control)
    - Benchmark LFL percent change (per control/scope only)
    - LFL asset count
    - Total area eligible for LFL scoring (in m2/sqft)
    - Percentage of area eligible for LFL scoring (per control/scope and cross-control)

    Usage:
    ```
        asset_data = pd.read_parquet(path_to_asset_data_file)
        is_performance_asset = AssetFilters.is_performance_component_asset(asset_data)
        asset_data = asset_data[is_performance_asset]
        asset_data = AssetCharacteristicsCalculations.add_asset_size_sqft(asset_data)

        aggregator = LFLMetricAggregator(["country", "property_sector"])
        sector_country_lfl_metrics = aggregator.aggregate_lfl_metrics(asset_data)
    ```
    """

    def __init__(self, group_by_columns: list[str]):
        # Since we are always aggregating values for a specific portfolio,
        # add the `response_id` column to the `group_by_columns` if not already present.
        self._group_by_columns = group_by_columns
        if ac.response_id not in self._group_by_columns:
            self._group_by_columns.append(ac.response_id)

        # However, benchmark values are calculated with the average
        # of values across all portfolios, so let's remove `response_id`.
        self._benchmark_group_by_columns = list(
            set(group_by_columns).difference({ac.response_id})
        )

    def validate_group_by_columns_presence(self, asset_data: pd.DataFrame):
        """
        Check the presence of the columns used for aggregation in the provided dataframe.
        Parameters:
            asset_data (pd.DataFrame): the dataframe to check the presence on.

        Raises:
            ValueError: if the columns used for aggregation are not present.
        """
        missing_columns = [
            col
            for col in self._group_by_columns
            if not col in asset_data.columns.tolist()
        ]
        if missing_columns:
            raise ValueError(
                f"Missing one or more required merge columns: {', '.join(missing_columns)}"
            )

    def aggregate_lfl_metrics(self, asset_data) -> pd.DataFrame:
        self.validate_group_by_columns_presence(asset_data)

        aggregated_lfl_metrics_list = [
            self.aggregate_lfl_absolute_change(asset_data),
            self.aggregate_lfl_percent_change(asset_data),
            self.aggregate_lfl_percent_change(asset_data, True),
            self.aggregate_lfl_asset_count(asset_data),
            self.aggregate_lfl_area(asset_data),
            self.aggregate_lfl_area_percentage(asset_data),
        ]

        aggregated_lfl_metrics = reduce(
            lambda x, y: pd.merge(x, y, on=self._group_by_columns),
            aggregated_lfl_metrics_list,
        )

        return aggregated_lfl_metrics

    def aggregate_lfl_absolute_change(self, asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Aggregate the asset-level LFL absolute change for all utilities.

        Parameters:
            asset_data (pd.DataFrame): the asset-level data with LFL metrics.

        Returns:
            pd.DataFrame: the aggregated asset data with the LFL absolute change.
        """

        # Define the output columns
        abs_change_cols = [
            ec.en_lfl_abs_change,
            gc.ghg_lfl_abs_change,
            wc.wat_lfl_abs_change,
        ]

        # Calculate the absolute change for assets eligible for LFL aggregation
        asset_data[ec.en_lfl_abs_change] = self._calc_filtered_lfl_absolute_change(
            asset_data,
            Utility.Energy,
            ec.en_lfl_abs,
            ec.en_lfl_abs_ly,
            False,
        )
        asset_data[wc.wat_lfl_abs_change] = self._calc_filtered_lfl_absolute_change(
            asset_data,
            Utility.Water,
            wc.wat_lfl_abs,
            wc.wat_lfl_abs_ly,
            False,
        )
        asset_data[gc.ghg_lfl_abs_change] = self._calc_filtered_lfl_absolute_change(
            asset_data,
            Utility.GHG,
            gc.ghg_lfl_abs,
            gc.ghg_lfl_abs_ly,
            False,
        )

        # Aggregate the absolute change
        agg_data = (
            asset_data.groupby(self._group_by_columns)[abs_change_cols]
            .agg("sum")
            .reset_index()
        )

        return agg_data

    def aggregate_lfl_percent_change(
        self, asset_data: pd.DataFrame, for_benchmark: bool = False
    ) -> pd.DataFrame:
        """
        Aggregate the asset-level LFL metrics for all utilities to compute the aggregated
        LFL percent changes.

        Parameters:
            asset_data (pd.DataFrame): the asset-level data with LFL metrics.
            for_benchmark (bool, optional): whether to aggregate LFL metrics to calculate a benchmark.

        Returns:
            pd.DataFrame: the aggregated LFL percent changes.
        """
        agg_data_list = []

        # If calculating the benchmarks, do not aggregate the cross-control
        # LFL percent change as we do not show any benchmark for it.
        if not for_benchmark:
            agg_data_list.append(
                self._aggregate_lfl_percent_change(
                    asset_data,
                    Utility.Energy,
                    ec.en_lfl_abs,
                    ec.en_lfl_abs_ly,
                    ec.en_lfl_abs_change,
                    ec.en_lfl_p,
                    for_benchmark,
                )
            )
            agg_data_list.append(
                self._aggregate_lfl_percent_change(
                    asset_data,
                    Utility.GHG,
                    gc.ghg_lfl_abs,
                    gc.ghg_lfl_abs_ly,
                    gc.ghg_lfl_abs_change,
                    gc.ghg_lfl_p,
                    for_benchmark,
                )
            )
            agg_data_list.append(
                self._aggregate_lfl_percent_change(
                    asset_data,
                    Utility.Water,
                    wc.wat_lfl_abs,
                    wc.wat_lfl_abs_ly,
                    wc.wat_lfl_abs_change,
                    wc.wat_lfl_p,
                    for_benchmark,
                )
            )

        # Aggregate the LFL percent changes per control
        agg_data_list.append(
            self._aggregate_lfl_percent_change(
                asset_data,
                Utility.Energy,
                ec.en_lfl_abs_lc,
                ec.en_lfl_abs_lc_ly,
                ec.en_lfl_abs_change_lc,
                ec.en_lfl_percent_change_lc,
                for_benchmark,
            )
        )
        agg_data_list.append(
            self._aggregate_lfl_percent_change(
                asset_data,
                Utility.GHG,
                gc.ghg_lfl_abs_s12,
                gc.ghg_lfl_abs_s12_ly,
                gc.ghg_lfl_abs_change_s12,
                gc.ghg_lfl_percent_change_s12,
                for_benchmark,
            )
        )
        agg_data_list.append(
            self._aggregate_lfl_percent_change(
                asset_data,
                Utility.Water,
                wc.wat_lfl_abs_lc,
                wc.wat_lfl_abs_lc_ly,
                wc.wat_lfl_abs_change_lc,
                wc.wat_lfl_percent_change_lc,
                for_benchmark,
            )
        )

        agg_data_list.append(
            self._aggregate_lfl_percent_change(
                asset_data,
                Utility.Energy,
                ec.en_lfl_abs_tc,
                ec.en_lfl_abs_tc_ly,
                ec.en_lfl_abs_change_tc,
                ec.en_lfl_percent_change_tc,
                for_benchmark,
            )
        )
        agg_data_list.append(
            self._aggregate_lfl_percent_change(
                asset_data,
                Utility.GHG,
                gc.ghg_lfl_abs_s3,
                gc.ghg_lfl_abs_s3_ly,
                gc.ghg_lfl_abs_change_s3,
                gc.ghg_lfl_percent_change_s3,
                for_benchmark,
            )
        )
        agg_data_list.append(
            self._aggregate_lfl_percent_change(
                asset_data,
                Utility.Water,
                wc.wat_lfl_abs_tc,
                wc.wat_lfl_abs_tc_ly,
                wc.wat_lfl_abs_change_tc,
                wc.wat_lfl_percent_change_tc,
                for_benchmark,
            )
        )

        agg_data = reduce(
            lambda x, y: pd.merge(x, y, on=self._group_by_columns), agg_data_list
        )

        return agg_data

    def aggregate_lfl_asset_count(self, asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Aggregate the asset data to obtain the count of assets eligible for LFL scoring
        per bucket.
        Parameters:
            asset_data (pd.DataFrame): the asset-level data with LFL metrics.

        Returns:
            pd.DataFrame: the aggregated data with the number of assets eligible for LFL scoring.
        """
        eligibility_col_dict = {
            Utility.Energy: ec.en_lfl_asset_count,
            Utility.GHG: gc.ghg_lfl_asset_count,
            Utility.Water: wc.wat_lfl_asset_count,
        }
        for utility, col in eligibility_col_dict.items():
            asset_data[col] = AssetFilters().filter_lfl_metric_for_aggregation(
                asset_data, utility, False
            )

        agg_data = (
            asset_data.groupby(self._group_by_columns)[
                list(eligibility_col_dict.values())
            ]
            .agg("sum")
            .reset_index()
        )

        return agg_data

    def aggregate_lfl_area(self, asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Aggregate the asset data to obtain the LFL eligible area for the entire asset in M2 and SQFT.
        Parameters:
            asset_data (pd.DataFrame): the asset-level data with LFL metrics.
        Returns:
            pd.DataFrame: the aggregated data with the LFL eligible area of the bucket.
        """
        # Define the ouptut columns
        lfl_area_col = [
            ec.en_lfl_area_m2,
            ec.en_lfl_area_sqft,
            wc.wat_lfl_area_m2,
            wc.wat_lfl_area_sqft,
            gc.ghg_lfl_area_m2,
            gc.ghg_lfl_area_sqft,
        ]

        # Calculate the filtered asset-level LFL area per unit and utility
        asset_data[ec.en_lfl_area_m2] = self._calculate_filtered_lfl_area(
            asset_data, Utility.Energy, ac.asset_size_m2, ec.en_lfl_area_cov_p
        )
        asset_data[ec.en_lfl_area_sqft] = self._calculate_filtered_lfl_area(
            asset_data, Utility.Energy, ac.asset_size_sqft, ec.en_lfl_area_cov_p
        )
        asset_data[wc.wat_lfl_area_m2] = self._calculate_filtered_lfl_area(
            asset_data, Utility.Water, ac.asset_size_m2, wc.wat_lfl_area_cov_p
        )
        asset_data[wc.wat_lfl_area_sqft] = self._calculate_filtered_lfl_area(
            asset_data, Utility.Water, ac.asset_size_sqft, wc.wat_lfl_area_cov_p
        )
        asset_data[gc.ghg_lfl_area_m2] = self._calculate_filtered_lfl_area(
            asset_data, Utility.GHG, ac.asset_size_m2, gc.ghg_lfl_area_cov_p
        )
        asset_data[gc.ghg_lfl_area_sqft] = self._calculate_filtered_lfl_area(
            asset_data, Utility.GHG, ac.asset_size_sqft, gc.ghg_lfl_area_cov_p
        )

        # Aggregate those metrics
        agg_data = (
            asset_data.groupby(self._group_by_columns)[lfl_area_col]
            .agg("sum")
            .reset_index()
        )

        return agg_data

    def aggregate_lfl_area_percentage(self, asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate the percentage of area eligible for LFL scoring per bucket, for the entire bucket
        and also for each control/scope.
        In order to do this, first aggregate the absolute owned area eligible for LFL scoring
        and the total owned floor area, then divide the first by the second.
        Parameters:
            asset_data (pd.DataFrame): the asset-level data with LFL metrics.
        Returns:
            pd.DataFrame: the aggregated data with the percentage of area eligible for LFL scoring.
        """
        # Aggregate all metrics
        agg_data_list = [
            self._aggregate_lfl_area_percentage(
                asset_data,
                Utility.Energy,
                ac.asset_size,
                ec.en_lfl_area_cov_p,
                ec.en_lfl_area_p,
            ),
            self._aggregate_lfl_area_percentage(
                asset_data,
                Utility.GHG,
                ac.asset_size,
                gc.ghg_lfl_area_cov_p,
                gc.ghg_lfl_area_p,
            ),
            self._aggregate_lfl_area_percentage(
                asset_data,
                Utility.Water,
                ac.asset_size,
                wc.wat_lfl_area_cov_p,
                wc.wat_lfl_area_p,
            ),
            self._aggregate_lfl_area_percentage(
                asset_data,
                Utility.Energy,
                ec.en_area_weight_lc,
                ec.en_lfl_area_cov_p_lc,
                ec.en_lfl_area_p_lc,
            ),
            self._aggregate_lfl_area_percentage(
                asset_data,
                Utility.GHG,
                gc.ghg_area_weight_s12,
                gc.ghg_lfl_area_cov_p_s12,
                gc.ghg_lfl_area_p_s12,
            ),
            self._aggregate_lfl_area_percentage(
                asset_data,
                Utility.Water,
                wc.wat_area_weight_lc,
                wc.wat_lfl_area_cov_p_lc,
                wc.wat_lfl_area_p_lc,
            ),
            self._aggregate_lfl_area_percentage(
                asset_data,
                Utility.Energy,
                ec.en_area_weight_tc,
                ec.en_lfl_area_cov_p_tc,
                ec.en_lfl_area_p_tc,
            ),
            self._aggregate_lfl_area_percentage(
                asset_data,
                Utility.GHG,
                gc.ghg_area_weight_s3,
                gc.ghg_lfl_area_cov_p_s3,
                gc.ghg_lfl_area_p_s3,
            ),
            self._aggregate_lfl_area_percentage(
                asset_data,
                Utility.Water,
                wc.wat_area_weight_tc,
                wc.wat_lfl_area_cov_p_tc,
                wc.wat_lfl_area_p_tc,
            ),
        ]

        # Merge the list of aggregated results into one file
        agg_data = reduce(
            lambda x, y: pd.merge(x, y, on=self._group_by_columns), agg_data_list
        )

        return agg_data

    def _aggregate_lfl_percent_change(
        self,
        asset_data: pd.DataFrame,
        utility: Utility,
        cy_col: str,
        ly_col: str,
        change_col: str,
        percent_col: str,
        for_benchmark: bool = False,
    ) -> pd.DataFrame:
        """
        Aggregate an asset-level LFL absolute change for the given utility.

        Parameters:
            asset_data (pd.DataFrame): the asset-level data with LFL metrics.
            utility (Utility): the utility to filter the LFL values on.
            cy_col (str): the current year LFL consumption column name.
            ly_col (str): the previous year LFL consumption column name.
            change_col (str): the LFL absolute consumption change column name.
            percent_col (str): the column name of the output metric, also the asset-level
                LFL percent change to filter on in the case of a benchmark calculation.
            for_benchmark (bool): whether to calculate the benchmark LFL percent change.

        Returns:
            pd.DataFrame: the aggregated asset data with an LFL percent change.
        """
        # Define the benchmark column, only useful if calculating the benchmark value
        benchmark_percent_col_dict = {
            ec.en_lfl_percent_change_lc: ec.en_lfl_percent_change_lc_benchmark,
            ec.en_lfl_percent_change_tc: ec.en_lfl_percent_change_tc_benchmark,
            wc.wat_lfl_percent_change_lc: wc.wat_lfl_percent_change_lc_benchmark,
            wc.wat_lfl_percent_change_tc: wc.wat_lfl_percent_change_tc_benchmark,
            gc.ghg_lfl_percent_change_s12: gc.ghg_lfl_percent_change_s12_benchmark,
            gc.ghg_lfl_percent_change_s3: gc.ghg_lfl_percent_change_s3_benchmark,
        }

        # Calculate the absolute change for assets eligible for LFL aggregation
        asset_data[change_col] = self._calc_filtered_lfl_absolute_change(
            asset_data, utility, cy_col, ly_col, for_benchmark, percent_col
        )

        # Filter the previous year consumption for assets eligible for LFL aggregation
        asset_data[ly_col] = np.where(
            AssetFilters().filter_lfl_metric_for_aggregation(
                asset_data, utility, for_benchmark, percent_col
            ),
            asset_data[ly_col] * asset_data[ac.asset_ownership] / 100,
            np.nan,
        )

        # Aggregate the absolute change
        agg_data = (
            asset_data.groupby(self._group_by_columns)[[change_col, ly_col]]
            .agg("sum")
            .reset_index()
        )

        # Calculate the aggregated percent change
        agg_data[percent_col] = agg_data[change_col] / agg_data[ly_col] * 100

        # Remove variables that are not the output
        agg_data.drop(columns=[change_col, ly_col], inplace=True)

        # If for benchmark, calculate the benchmark and drop the percent change
        if for_benchmark:
            benchmark_percent_col = benchmark_percent_col_dict[percent_col]
            agg_data[benchmark_percent_col] = agg_data.groupby(
                self._benchmark_group_by_columns
            )[percent_col].transform("mean")
            agg_data.drop(columns=[percent_col], inplace=True)

        return agg_data

    @staticmethod
    def _calc_filtered_lfl_absolute_change(
        asset_data: pd.DataFrame,
        utility: Utility,
        cy_col: str,
        ly_col: str,
        for_benchmark: bool,
        percent_col: str = None,
    ) -> np.ndarray:
        """
        Calculate the asset-level LFL absolute change for the given utility and control level.
        Asset values are filtered based on their eligibility to LFL aggregation
        (see `filter_lfl_metric_for_aggregation`).
        Parameters:
            asset_data (pd.DataFrame): the asset-level data with LFL metrics.
            utility (Utility): the utility to filter the LFL values on.
            cy_col (str): the current year LFL consumption column name.
            ly_col (str): the previous year LFL consumption column name.
            for_benchmark (bool): whether to calculate the LFL absolute change
                for the benchmark LFL percent changes.
            percent_col (str): the LFL percent change to filter on in the case
                of a benchmark calculation.

        Returns:
            np.ndarray: the series with the filtered asset LFL absolute change.
        """
        # Check the asset data to make sure it has the required columns
        if not all(col in asset_data.columns.tolist() for col in [cy_col, ly_col]):
            raise ValueError(
                "Previous year LFL consumption are not calculated, run `add_previous_year_lfl_consumption_per_utility`."
            )

        # Filter the values based on their eligibility to LFL aggregation.
        is_aggregatable = AssetFilters().filter_lfl_metric_for_aggregation(
            asset_data, utility, for_benchmark, percent_col
        )

        # Calculate the LFL absolute change.
        abs_change_values = (asset_data[cy_col] - asset_data[ly_col]) * (
            asset_data[ac.asset_ownership] / 100
        )

        return np.where(is_aggregatable, abs_change_values, np.nan)

    @staticmethod
    def _calculate_filtered_lfl_area(
        asset_data: pd.DataFrame, utility: Utility, area_col: str, lfl_area_cov: str
    ) -> np.ndarray:
        """
        Calculate the asset-level LFL area for the given utility.
        Asset values are filtered based on their eligibility to LFL aggregation
        (see `filter_lfl_metric_for_aggregation`).
        Parameters:
            asset_data (pd.DataFrame): the asset-level data with LFL metrics.
            utility (Utility): the utility to filter the LFL values on.
            area_col (str): the column name of the area to weight and aggregate.
            lfl_area_cov (str): the column name of the LFL area coverage.

        Returns:
            np.ndarray: the series with the filtered asset LFL area.
        """
        # Check the asset data to make sure it has the required columns.
        if not all(
            col in asset_data.columns.tolist() for col in [area_col, lfl_area_cov]
        ):
            raise ValueError("Missing some input columns.")

        # Filter the values based on their eligibility to LFL aggregation.
        is_eligible_for_agg = AssetFilters().filter_lfl_metric_for_aggregation(
            asset_data, utility, False
        )

        # Calculate the LFL area.
        lfl_area_values = (
            asset_data[area_col]
            .mul(asset_data[ac.asset_ownership])
            .div(100)
            .mul(asset_data[lfl_area_cov])
            .div(100)
        )
        return np.where(is_eligible_for_agg, lfl_area_values, np.nan)

    def _aggregate_lfl_area_percentage(
        self,
        asset_data: pd.DataFrame,
        utility: Utility,
        area_col: str,
        lfl_area_cov: str,
        lfl_area_p_col: str,
    ) -> pd.DataFrame:
        """
        Calculate a percentage of area eligible to LFL scoring.
        First, calculate and aggregate the LFL area, then aggregate the
        total floor area (whole asset, or control-specific), finally
        calculate the percentage at the aggregated level.
        Parameters:
            asset_data (pd.DataFrame): the asset-level data with LFL metrics.
            utility (Utility): the utility to filter the LFL values on.
            area_col (str): the column name of the area to weight and aggregate.
            lfl_area_cov (str): the column name of the LFL area coverage.
            lfl_area_p_col (str): the column name of the LFL area percentage.

        Returns:
            pd.DataFrame: the aggregated data with the LFL area percentage.
        """
        # Define a temporary column to store the LFL area in.
        tmp_lfl_area_col = "tmp_lfl_area"

        # Calculate this LFL area
        asset_data[tmp_lfl_area_col] = self._calculate_filtered_lfl_area(
            asset_data, utility, area_col, lfl_area_cov
        )

        # Weight the denominator of the weighted average and filter the values
        # based on eligibility to LFL aggregation.
        asset_data[area_col] = np.where(
            AssetFilters().filter_lfl_metric_for_aggregation(
                asset_data, utility, False
            ),
            asset_data[area_col] * asset_data[ac.asset_ownership] / 100,
            np.nan,
        )

        # Aggregate both areas
        agg_data = (
            asset_data.groupby(self._group_by_columns)[[tmp_lfl_area_col, area_col]]
            .agg("sum")
            .reset_index()
        )

        # Calculate the percentage at the aggregated level
        agg_data[lfl_area_p_col] = (
            agg_data[tmp_lfl_area_col] / agg_data[area_col] * 100
        ).fillna(0)

        # Drop the operands
        agg_data.drop(columns=[tmp_lfl_area_col, area_col], inplace=True)

        return agg_data
