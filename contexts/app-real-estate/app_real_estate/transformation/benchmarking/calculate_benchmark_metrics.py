from typing import Callable
import pandas as pd
import numpy as np
from scipy import stats
import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.water_columns as wc
import app_real_estate.constants.column_names.ghg_columns as gc
import app_real_estate.constants.column_names.waste_columns as wsc
import app_real_estate.constants.column_names.benchmark_group_memberships_columns as mc
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.building_certification_columns as bc


class BenchmarkMetricCalculator:
    """
    Class responsible for the calculations of benchmark values and metrics.

    All functions will have assets and memberships datasets.
    Asset and memberships datasets in a given function should both have one row
    per asset and the same number of assets, however the order might be
    different so we use merge calls to define the benchmark values from the metrics.
    """

    @staticmethod
    def add_benchmark_values_to_memberships(
        data: pd.DataFrame,
        memberships: pd.DataFrame,
        unique_id_col: str,
        benchmark_value_column: str,
    ) -> pd.DataFrame:
        """
        Validate the input data and add benchmark values in the memberships.

        Parameters:
        data (pd.DataFrame): DataFrame containing the asset or certification data.
        memberships (pd.DataFrame): DataFrame containing the pairs asset (or certification)
            / benchmark group.
        unique_id_col (str): a string containing the unique ID column per row.
        benchmark_value_column (str): column containing the benchmark
            values in the memberships data.

        Returns:
        pd.DataFrame: the memberships DataFrame with the added benchmark values
            per row.
        """
        if ac.data_year in data.columns and any(
            data[ac.data_year] != data[ac.survey_year] - 1
        ):
            raise ValueError(
                "calculate_benchmark: the benchmark should be created using current year data. Please remove previous year data."
            )

        if len(data[unique_id_col].unique()) != len(data.index):
            raise ValueError(
                "calculate_benchmark: benchmark construction only accepts one value/row per asset (or certification)."
            )

        # Add the benchmark values to the memberships dataset.
        memberships = memberships.merge(
            data[[unique_id_col, benchmark_value_column]],
            how="left",
            on=unique_id_col,
        )

        # For intensity metrics, the hard outliers should be removed prior to the benchmark
        # group creation. If it was done properly, the memberships dataframe should
        # not include any of the outlier asset (even after the merge).
        # If not then the benchmark value column should have missing values, hence
        # the check below.
        if any(memberships[benchmark_value_column].isna()):
            raise ValueError(
                "calculate_benchmark: missing values (and hard outliers for intensity) of the benchmark-ed metric should be filtered out from the benchmark groups."
            )

        return memberships

    def calculate_percent_metric_benchmark(
        self,
        data: pd.DataFrame,
        memberships: pd.DataFrame,
        metric_column: str,
        benchmark_value_column: str,
        benchmark_metric_column: str,
        benchmark_function: Callable,
        unique_id_col: str = ac.portfolio_asset_id,
    ) -> pd.DataFrame:
        """
        Calculates the benchmark values and metrics for the percentage metrics.

        Parameters:
        data (pd.DataFrame): DataFrame containing the asset or certification data.
        memberships (pd.DataFrame): DataFrame containing the pairs asset (or certification)
            / benchmark group.
        metric_column (str): column containing the metric value in the dataset.
        benchmark_value_column (str): column containing the metric benchmark
            values in the memberships data.
        benchmark_metric_column (str): column containing the relevant metric's
            benchmark metric in the memberships data.
        benchmark_function (str): the name of the function used to aggregate
            the benchmark groups' values into benchmark metrics.
        unique_id_col (str): a string containing the unique ID column per row.

        Returns:
        pd.DataFrame: the memberships DataFrame with the added benchmark values
            and metrics per row.
        """
        # There is no outlier filtering to perform for these metrics.
        # However, because they are percentages, we should transform them back
        # to fractions.
        data = data.copy()
        data[benchmark_value_column] = data[metric_column] / 100

        memberships = self._calculate_benchmark(
            data,
            memberships,
            benchmark_value_column,
            benchmark_metric_column,
            benchmark_function,
            unique_id_col,
        )

        return memberships

    def _calculate_benchmark(
        self,
        data: pd.DataFrame,
        memberships: pd.DataFrame,
        benchmark_value_column: str,
        benchmark_metric_column: str,
        benchmark_function: Callable,
        unique_id_col: str = ac.portfolio_asset_id,
    ) -> pd.DataFrame:
        """
        Calculates the benchmark metric for a given metric.

        Parameters:
        data (pd.DataFrame): DataFrame containing the asset or certification data.
        memberships (pd.DataFrame): DataFrame containing the pairs asset (or certification)
            / benchmark group.
        benchmark_value_column (str): column containing the metric benchmark
            values in the memberships data.
        benchmark_metric_column (str): column containing the relevant metric's
            benchmark metric in the memberships data.
        benchmark_function (Callable): the function used to aggregate
            the benchmark groups' values into benchmark metrics.
        unique_id_col (str): a string containing the unique ID column per row.

        Returns:
        pd.DataFrame: the memberships DataFrame with the added metrics per row.
        """
        memberships = self.add_benchmark_values_to_memberships(
            data, memberships, unique_id_col, benchmark_value_column
        )

        group_metrics = memberships.groupby(mc.group_id).apply(
            lambda x: benchmark_function(
                x[x[mc.is_benchmarkable]][benchmark_value_column]
            ),
            include_groups=False,
        )
        memberships[benchmark_metric_column] = memberships[mc.group_id].map(
            group_metrics
        )

        return memberships

    def calculate_en1_coverage_benchmark(
        self, asset_data: pd.DataFrame, memberships: pd.DataFrame, control: str
    ) -> pd.DataFrame:
        """
        Calculates the benchmark values and metrics for the area-time data
        coverage percentage separated by landlord/tenant controlled areas
        for indicator EN1.

        Parameters:
        asset_data (pd.DataFrame): DataFrame containing the asset data and energy columns.
        memberships (pd.DataFrame): DataFrame containing the pairs asset/benchmark group.
        control (str): a string indicating whether the metric to compute is based on
            landlord controlled (lc) or tenant conrolled (tc) areas.

        Returns:
        pd.DataFrame: the memberships DataFrame with the added benchmark values
            and metrics per asset.
        """
        if control not in ["lc", "tc"]:
            raise ValueError(
                "calculate_en1_coverage_benchmark: control should be one of 'lc' or 'tc'."
            )

        if control == "lc":
            memberships = self.calculate_percent_metric_benchmark(
                asset_data,
                memberships,
                ec.en_area_time_cov_p_lc,
                mc.bench_en_area_time_cov_p_lc,
                mc.mean_en_area_time_cov_p_lc,
                pd.Series.mean,
            )
        elif control == "tc":
            memberships = self.calculate_percent_metric_benchmark(
                asset_data,
                memberships,
                ec.en_area_time_cov_p_tc,
                mc.bench_en_area_time_cov_p_tc,
                mc.mean_en_area_time_cov_p_tc,
                pd.Series.mean,
            )

        return memberships

    def calculate_gh1_coverage_benchmark(
        self, asset_data: pd.DataFrame, memberships: pd.DataFrame, scope: str
    ) -> pd.DataFrame:
        """
        Calculates the benchmark values and metrics for the area-time data
        coverage percentage separated by scopes (1/2 and 3) for indicator GH1.

        Parameters:
        asset_data (pd.DataFrame): DataFrame containing the asset data and GHG columns.
        memberships (pd.DataFrame): DataFrame containing the pairs asset/benchmark group.
        scope (str): a string indicating whether the metric to compute is based on
            scopes 1 and 2 (s12) or scope 3 (s3) emissions.

        Returns:
        pd.DataFrame: the memberships DataFrame with the added benchmark values
            and metrics per asset.
        """
        if scope not in ["s12", "s3"]:
            raise ValueError(
                "calculate_gh1_coverage_benchmark: scope should be one of 's12' or 's3'."
            )

        if scope == "s12":
            memberships = self.calculate_percent_metric_benchmark(
                asset_data,
                memberships,
                gc.ghg_area_time_cov_p_s12,
                mc.bench_ghg_area_time_cov_p_s12,
                mc.mean_ghg_area_time_cov_p_s12,
                pd.Series.mean,
            )
        elif scope == "s3":
            memberships = self.calculate_percent_metric_benchmark(
                asset_data,
                memberships,
                gc.ghg_area_time_cov_p_s3,
                mc.bench_ghg_area_time_cov_p_s3,
                mc.mean_ghg_area_time_cov_p_s3,
                pd.Series.mean,
            )

        return memberships

    def calculate_wt1_coverage_benchmark(
        self, asset_data: pd.DataFrame, memberships: pd.DataFrame, control: str
    ) -> pd.DataFrame:
        """
        Calculates the benchmark values and metrics for the area-time data
        coverage percentage separated by landlord/tenant controlled areas
        for indicator WT1.

        Parameters:
        asset_data (pd.DataFrame): DataFrame containing the asset data and water columns.
        memberships (pd.DataFrame): DataFrame containing the pairs asset/benchmark group.
        control (str): a string indicating whether the metric to compute is based on
            landlord controlled (lc) or tenant conrolled (tc) areas.

        Returns:
        pd.DataFrame: the memberships DataFrame with the added benchmark values
            and metrics per asset.
        """
        if control not in ["lc", "tc"]:
            raise ValueError(
                "calculate_wt1_coverage_benchmark: control should be one of 'lc' or 'tc'."
            )

        if control == "lc":
            memberships = self.calculate_percent_metric_benchmark(
                asset_data,
                memberships,
                wc.wat_area_time_cov_p_lc,
                mc.bench_wat_area_time_cov_p_lc,
                mc.mean_wat_area_time_cov_p_lc,
                pd.Series.mean,
            )
        elif control == "tc":
            memberships = self.calculate_percent_metric_benchmark(
                asset_data,
                memberships,
                wc.wat_area_time_cov_p_tc,
                mc.bench_wat_area_time_cov_p_tc,
                mc.mean_wat_area_time_cov_p_tc,
                pd.Series.mean,
            )

        return memberships

    def calculate_ws1_coverage_benchmark(
        self, asset_data: pd.DataFrame, memberships: pd.DataFrame, control: str
    ) -> pd.DataFrame:
        """
        Calculates the benchmark values and metrics for the area-time data
        coverage percentage separated by landlord/tenant controlled areas
        for indicator WS1.

        Parameters:
        asset_data (pd.DataFrame): DataFrame containing the asset data and waste columns.
        memberships (pd.DataFrame): DataFrame containing the pairs asset/benchmark group.

        Returns:
        pd.DataFrame: the memberships DataFrame with the added benchmark values
            and metrics per asset.
        """
        if control not in ["lc", "tc"]:
            raise ValueError(
                "calculate_ws1_coverage_benchmark: control should be one of 'lc' or 'tc'."
            )

        if control == "lc":
            memberships = self.calculate_percent_metric_benchmark(
                asset_data,
                memberships,
                wsc.was_area_cov_p_lc,
                mc.bench_was_area_cov_p_lc,
                mc.mean_was_area_cov_p_lc,
                pd.Series.mean,
            )
        elif control == "tc":
            memberships = self.calculate_percent_metric_benchmark(
                asset_data,
                memberships,
                wsc.was_area_cov_p_tc,
                mc.bench_was_area_cov_p_tc,
                mc.mean_was_area_cov_p_tc,
                pd.Series.mean,
            )

        return memberships

    def calculate_diverted_waste_coverage_benchmark(
        self, asset_data: pd.DataFrame, memberships: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Calculates the benchmark values and metrics for the diverted waste
        percentage for the indicator WS1.

        Parameters:
        asset_data (pd.DataFrame): DataFrame containing the asset data and waste columns.
        memberships (pd.DataFrame): DataFrame containing the pairs
            asset/benchmark group for the diverted waste percentage.

        Returns:
        pd.DataFrame: the memberships DataFrame with the added benchmark values
            and metrics per asset.
        """
        memberships = self.calculate_percent_metric_benchmark(
            asset_data,
            memberships,
            wsc.was_pabs_div,
            mc.bench_was_diverted_percent,
            mc.mean_was_diverted_percent,
            pd.Series.mean,
        )

        return memberships

    def calculate_certification_coverage_benchmark(
        self,
        certification_data: pd.DataFrame,
        memberships: pd.DataFrame,
        for_scoring: bool = True,
    ) -> pd.DataFrame:
        """
        Calculates the benchmark value and the benchmark mean for each group for
        certification coverage percentage for indicator BC1.1 and BC1.2.
        This function should be used separately for design and operational data.

        Parameters:
        certification_data (pd.DataFrame): DataFrame containing the certification
            data.
        memberships (pd.DataFrame): DataFrame containing the pairs
            certification/benchmark group for the certified floor coverage.

        Returns:
        pd.DataFrame: the memberships DataFrame with the added benchmark values
            and metrics per certification.
        """
        metric = bc.scoring_coverage if for_scoring else bc.coverage

        memberships = self.calculate_percent_metric_benchmark(
            certification_data,
            memberships,
            metric,
            mc.bench_certification_coverage,
            mc.mean_certification_coverage,
            pd.Series.mean,
            bc.building_data_certifications_scoring_id,
        )

        return memberships

    def calculate_energy_rating_coverage_benchmark(
        self, asset_data: pd.DataFrame, memberships: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Calculates the benchmark values and metrics for the energy rating
        coverage percentage for indicator BC2.

        Parameters:
        asset_data (pd.DataFrame): DataFrame containing the asset data.
        memberships (pd.DataFrame): DataFrame containing the pairs
            asset/benchmark group for the energy rating coverage percentage.

        Returns:
        pd.DataFrame: the memberships DataFrame with the added benchmark values
            and metrics per asset.
        """
        memberships = self.calculate_percent_metric_benchmark(
            asset_data,
            memberships,
            ac.rating_coverage,
            mc.bench_energy_rating_coverage,
            mc.mean_energy_rating_coverage,
            pd.Series.mean,
        )

        return memberships

    def calculate_intensity_benchmark(
        self,
        asset_data: pd.DataFrame,
        memberships: pd.DataFrame,
        metric_colum: str,
        benchmark_value_column: str,
        benchmark_metric_column: str,
        percentile_10_column: str,
        percentile_90_column: str,
    ) -> pd.DataFrame:
        """
        Calculates the benchmark values and metrics for the intensity metrics.

        Parameters:
        asset_data (pd.DataFrame): DataFrame containing the asset data.
        memberships (pd.DataFrame): DataFrame containing the pairs
            asset/benchmark group for the given intensity metric.
        metric_column (str): column containing the metric value in the asset data.
        benchmark_value_column (str): column containing the metric benchmark
            values in the memberships data.
        benchmark_metric_column (str): column containing the relevant metric's
            benchmark metric in the memberships data.
        percentile_10_column (str): column containing the 10th percentile of the
            group's values.
        percentile_90_column (str): column containing the 90th percentile of the
            group's values.

        Returns:
        pd.DataFrame: the memberships DataFrame with the added benchmark values
            and metrics per asset.
        """
        # Add benchmark values to the memberships.
        asset_data = asset_data.copy()
        asset_data[benchmark_value_column] = asset_data[metric_colum]

        memberships = self.add_benchmark_values_to_memberships(
            asset_data,
            memberships,
            ac.portfolio_asset_id,
            benchmark_value_column,
        )

        # Compute the 10th and 90th percentiles for each benchmark group, excluding
        # non-benchmarkable assets.
        grouper = memberships[memberships[mc.is_benchmarkable]].groupby(mc.group_id)
        percentiles = grouper.agg(
            p10=(benchmark_value_column, lambda c: c.quantile(0.1)),
            p90=(benchmark_value_column, lambda c: c.quantile(0.9)),
        ).reset_index()
        percentiles.rename(
            columns={"p10": percentile_10_column, "p90": percentile_90_column},
            inplace=True,
        )
        memberships = memberships.merge(percentiles, on=mc.group_id, how="left")

        # Get the benchmark distribution of each group using rankable and benchmarkable assets.
        is_bench_int_above_90th = (
            memberships[benchmark_value_column] > memberships[percentile_90_column]
        )
        is_bench_int_below_10th = (
            memberships[benchmark_value_column] < memberships[percentile_10_column]
        )
        is_rankable = np.logical_and(~is_bench_int_below_10th, ~is_bench_int_above_90th)

        ranked_memberships = memberships[
            np.logical_and(is_rankable, memberships[mc.is_benchmarkable])
        ]
        benchmark_distributions = ranked_memberships.groupby(mc.group_id)[
            benchmark_value_column
        ].agg(stats.ecdf)

        # Compute the percentile of observation of each asset using the groups benchmark distribution.
        memberships[benchmark_metric_column] = memberships.apply(
            lambda m: benchmark_distributions.loc[m[mc.group_id]].cdf.evaluate(
                m[benchmark_value_column]
            ),
            axis=1,
        )

        # Assets below and above the percentile thresholds get 0 and 100 for
        # percentile of observation.
        memberships.loc[is_bench_int_below_10th, benchmark_metric_column] = 0
        memberships.loc[is_bench_int_above_90th, benchmark_metric_column] = 1

        # Convert to scalar type
        memberships[benchmark_metric_column] = memberships[
            benchmark_metric_column
        ].apply(lambda x: x.item() if isinstance(x, np.ndarray) else x)

        return memberships

    def calculate_energy_efficiency_benchmark(
        self, asset_data: pd.DataFrame, memberships: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Calculates the benchmark values and metrics for the energy efficiency.

        Parameters:
        asset_data (pd.DataFrame): DataFrame containing the asset data and energy columns.
        memberships (pd.DataFrame): DataFrame containing the pairs
            asset/benchmark group for the energy efficiency.

        Returns:
        pd.DataFrame: the memberships DataFrame with the added benchmark values
            and metrics per asset.
        """
        memberships = self.calculate_intensity_benchmark(
            asset_data,
            memberships,
            ec.en_efficiency_int_kwh_m2,
            mc.bench_en_efficiency_intensity,
            mc.rank_en_efficiency_intensity,
            mc.percentile_10th_en_efficiency_intensity,
            mc.percentile_90th_en_efficiency_intensity,
        )

        return memberships

    def calculate_water_intensity_benchmark(
        self, asset_data: pd.DataFrame, memberships: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Calculates the benchmark values and metrics for the water intensity.

        Parameters:
        asset_data (pd.DataFrame): DataFrame containing the asset data and water columns.
        memberships (pd.DataFrame): DataFrame containing the pairs
            asset/benchmark group for the water intensity.

        Returns:
        pd.DataFrame: the memberships DataFrame with the added benchmark values
            and metrics per asset.
        """
        memberships = self.calculate_intensity_benchmark(
            asset_data,
            memberships,
            wc.wat_scored_int_m3_m2,
            mc.bench_wat_intensity,
            mc.rank_wat_intensity,
            mc.percentile_10th_wat_efficiency_intensity,
            mc.percentile_90th_wat_efficiency_intensity,
        )

        return memberships

    def calculate_ghg_intensity_benchmark(
        self, asset_data: pd.DataFrame, memberships: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Calculates the benchmark values and metrics for the GHG intensity.

        Parameters:
        asset_data (pd.DataFrame): DataFrame containing the asset data and GHG columns.
        memberships (pd.DataFrame): DataFrame containing the pairs
            asset/benchmark group for the GHG intensity.

        Returns:
        pd.DataFrame: the memberships DataFrame with the added benchmark values
            and metrics per asset.
        """
        memberships = self.calculate_intensity_benchmark(
            asset_data,
            memberships,
            gc.ghg_scored_int_ton_m2,
            mc.bench_ghg_intensity,
            mc.rank_ghg_intensity,
            mc.percentile_10th_ghg_efficiency_intensity,
            mc.percentile_90th_ghg_efficiency_intensity,
        )

        return memberships
