import pandas as pd
import numpy as np
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.benchmark_group_memberships_columns as mc
import app_real_estate.constants.column_names.benchmark_groups_columns as gc


class BenchmarkGroupCreator:
    """
    Class responsible of creating and assigning a benchmark group for every
    asset or building certification.
    """

    group_columns = [
        gc.location_granularity,
        gc.property_type_granularity,
        gc.location,
        gc.property_type,
    ]

    def __init__(
        self, min_peer_row_count: int = 20, min_peer_entity_count: int = 5
    ) -> None:
        self.min_peer_row_count = min_peer_row_count
        self.min_peer_entity_count = min_peer_entity_count

    def create_benchmark_groups(
        self,
        data: pd.DataFrame,
        metric: str,
        is_benchmarkable: list[bool] = None,
        is_excluded: list[bool] = None,
        unique_id_col: str = ac.portfolio_asset_id,
    ) -> tuple[pd.DataFrame, pd.DataFrame]:
        """
        Creates and assign a benchmark to each row in the input DataFrame.
        This can be used for both asset and building certifications DataFrames.
        The parameter `is_benchmarkable` is used to exclude outliers in the case
        of LFL or negative renewable / recycled percent change values.

        Parameters:
        data (pd.DataFrame): a DataFrame containing location and property type columns.
        metric (str): a string containing the column name of the metric we are
            building benchmark groups for.
        is_benchmarkable (list[bool]): indicates whether the asset should be counted
            in the group creation asset count. Set to None if all assets are benchmarkable.
        is_excluded (list[bool]): indicates whether the asset is completely excluded
            from benchmarking and scoring.
        unique_id_col (str): a string containing the unique ID column per row.

        Returns:
        tuple[pd.DataFrame, pd.DataFrame]: a tuple containing the groups and memberships DataFrames.
        """
        # Filter the data based on existence of the metric and add universal
        # group characteristics for benchmark group creation.
        data = self._prepare_group_creation_data(
            data,
            metric,
            is_benchmarkable=is_benchmarkable,
            is_excluded=is_excluded,
        )

        # Create the allocation parameters order
        allocation_parameters = self._get_allocation_parameters_order()

        memberships = pd.DataFrame()
        for parameters in enumerate(allocation_parameters):
            # Create new memberships based on the benchmark parameters
            new_memberships = self._create_new_memberships(
                data, unique_id_col, parameters
            )

            memberships = pd.concat([memberships, new_memberships])

        # Clean memberships dataset and add ID columns
        memberships = self._add_memberships_info(memberships, unique_id_col)

        # Create groups dataset
        groups = self._create_groups(memberships)

        return groups, memberships.drop(["order"] + self.group_columns, axis=1)

    def _prepare_group_creation_data(
        self,
        data: pd.DataFrame,
        metric: str,
        is_benchmarkable: list[bool] = None,
        is_excluded: list[bool] = None,
    ) -> pd.DataFrame:
        """
        Filters the data based on metric existence and adds universal group
        characteristics.

        Parameters:
        data (pd.DataFrame): a DataFrame containing the data to create the
            benchmark groups from.
        metric (str): a string containing the column name of the metric we are
            building benchmark groups for.
        is_benchmarkable (list[bool]): indicates whether the asset should be counted
            in the group creation asset count.
        is_excluded (list[bool]): indicates whether the asset is completely excluded
            from benchmarking and scoring.

        Returns:
        pd.DataFrame: the input filtered DataFrame with added group characteristics.
        """
        # Add a column to flag assets that are not bencharmked.
        data = data.copy()
        data["is_benchmarkable"] = (
            True if is_benchmarkable is None else is_benchmarkable
        )

        # Filter excluded assets out (rejected outliers for LFL)
        if is_excluded:
            data = data[~np.array(is_excluded)]

        # Filter the data based on the existence of the metric
        data = data[~data[metric].isna()]

        # Add universal group characteristics
        data["global"] = "global"
        data["all_property_types"] = "all_property_types"

        return data

    @staticmethod
    def _get_allocation_parameters_order() -> list[tuple[str, str]]:
        """
        Creates the list of benchmarking parameters
        (location granularity and property type granularity) in the right order.

        Parameters:

        Returns:
        list[tuple[str, str]]: a list of tuples containing for each a specific
            location column and property type column decreasing in granularity.
        """
        allocation_parameters = [
            (loc, prt)
            for loc in ac.location_columns
            for prt in ac.property_type_columns
        ]
        allocation_parameters.append(("global", "all_property_types"))

        return allocation_parameters

    def _create_new_memberships(
        self,
        data: pd.DataFrame,
        unique_id_col: str,
        parameters: tuple[int, tuple[str, str]],
    ) -> pd.DataFrame:
        """
        Creates new memberships based on the given benchmark parameters.

        Parameters:
        data (pd.DataFrame): a DataFrame containing location and property type columns.
        unique_id_col (str): a string containing the unique ID column per row.
        parameters (tuple[int, tuple[str, str]]): a tuple containing the order of
            the parameters and the location and property type parameter columns.
        peer_count_function (Callable[[pd.Series], int]): a function used for
            counting the number of unique values in a column.

        Returns:
        pd.DataFrame: a DataFrame containing the new memberships.
        """
        # Group the asset/certification data per group for this granularity
        # Keep the list of asset/certification ids, keep a trace of whether
        # the rows are benchmarkable and keep the response IDs. Then, compute
        # the benchmarkable entities count per group.
        order, (loc_group, prt_group) = parameters
        potential_groups = data.groupby([loc_group, prt_group], as_index=False).agg(
            row_ids=(unique_id_col, list),
            is_benchmarkable=("is_benchmarkable", list),
            benchmarkable_row_count=("is_benchmarkable", "sum"),
            parent_entities=(ac.response_id, list),
        )

        if len(potential_groups.index) == 0:
            return pd.DataFrame()

        potential_groups["benchmarkable_entity_count"] = potential_groups.apply(
            lambda x: len(
                np.unique(np.array(x["parent_entities"])[x["is_benchmarkable"]])
            ),
            axis=1,
        )

        # Check validity of group
        is_enough_rows = potential_groups["benchmarkable_row_count"].ge(
            self.min_peer_row_count
        )
        is_enough_entities = (
            potential_groups["benchmarkable_entity_count"] >= self.min_peer_entity_count
        )
        is_valid_group = is_enough_rows & is_enough_entities

        # Transform group dataframe to memberships dataframe
        valid_group_to_memberships = lambda row: pd.DataFrame(
            {
                unique_id_col: row["row_ids"],
                "location": row[loc_group],
                "property_type": row[prt_group],
                "is_benchmarkable": row["is_benchmarkable"],
            }
        )
        memberships_list = []
        for _, valid_group in potential_groups[is_valid_group].iterrows():
            new_memberships = valid_group_to_memberships(valid_group)
            memberships_list.append(new_memberships)

        if memberships_list == []:
            return pd.DataFrame()

        memberships = pd.concat(memberships_list)

        # Add group information
        memberships[gc.location_granularity] = loc_group
        memberships[gc.property_type_granularity] = prt_group

        # Add the allocation order to filter out the unused groups
        memberships["order"] = order

        return memberships

    @staticmethod
    def _assign_active_groups(row_orders):
        min_allocation = row_orders.min()
        is_active = row_orders == min_allocation

        if sum(is_active) != 1:
            raise ValueError("There should be exactly one active group per asset.")

        return is_active

    def _add_memberships_info(
        self, memberships: pd.DataFrame, unique_id_col: str
    ) -> pd.DataFrame:
        """
        Adds a flag to mark active groups per row, adds the unique
        group and membership IDs columns and creates the groups dataset.

        Parameters:
        memberships (pd.DataFrame): a DataFrame containing the relationships
            between each asset and its benchmark group.
        unique_id_col (str): a string containing the unique ID column per row.

        Returns:
        pd.DataFrame: a memberships DataFrame cleaned from unused groups and
            with new columns for group and membership IDs.
        """
        # Mark active groups per row based on allocation order.
        memberships[mc.is_active] = memberships.groupby(unique_id_col)[
            "order"
        ].transform(self._assign_active_groups)

        # Add unique membership and group IDs
        memberships.insert(0, mc.id, memberships[unique_id_col].map(hash))

        memberships[mc.group_id] = memberships.groupby(self.group_columns).ngroup()

        return memberships

    def _create_groups(self, memberships: pd.DataFrame) -> pd.DataFrame:
        """
        Creates the groups dataset.

        Parameters:
        memberships (pd.DataFrame): a DataFrame containing the relationships
            between each asset and its benchmark group.

        Returns:
        pd.DataFrame: a DataFrame containing the groups characteristics.
        """
        # Create group dataset out of the membership information
        groups = (
            memberships[[mc.group_id] + self.group_columns]
            .drop_duplicates()
            .reset_index(drop=True)
        )
        groups.rename(columns={mc.group_id: gc.id}, inplace=True)

        return groups
