import pandas as pd
import numpy as np
import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.water_columns as wc
import app_real_estate.constants.column_names.ghg_columns as gc
import app_real_estate.constants.column_names.benchmark_group_memberships_columns as mc
import app_real_estate.constants.column_names.asset_characteristics_columns as ac


class PercentChangeMetricBenchmarkCalculator:
    """
    Class responsible for the calculations of benchmark values and metrics for
    percent change metrics (LFL, renewable energy and recycled water).

    All functions will have assets and memberships datasets.
    Asset and memberships datasets in a given function should both have one row
    per asset and the same number of assets, however the order might be
    different so we use merge calls to define the benchmark values from the metrics.
    """

    lfl_smallest_maximum = 0.02
    lfl_largest_maximum = 0.2

    def calculate_percent_change_benchmark(
        self,
        asset_data: pd.DataFrame,
        memberships: pd.DataFrame,
        percent_change_column: str,
        benchmark_value_column: str,
        benchmark_percentile_column: str,
        benchmark_metric_column: str,
        is_lfl: bool = False,
    ) -> pd.DataFrame:
        """
        Calculates the benchmark values and metrics for the percent change
        metrics (LFL, renewable energy in EN1 and recycled water in WT1).

        Parameters:
        asset_data (pd.DataFrame): DataFrame containing the asset data and utility columns.
        memberships (pd.DataFrame): DataFrame containing the pairs asset/benchmark group.
        percent_change_column (str): column containing the percent change value in the asset data.
        bench_column (str): column containing the benchmark value in the membership data.
        percentile_column (str): column containing the 75th percentile of the benchmark group.
        metric_column (str): column containing the benchmark metric.
        is_lfl (bool): boolean indicating whether the percent change is LFL or not.

        Returns:
        pd.DataFrame: the memberships DataFrame with the added benchmark values
            and metrics per asset.
        """
        if ac.data_year in asset_data.columns and any(
            asset_data[ac.data_year] != asset_data[ac.survey_year] - 1
        ):
            raise ValueError(
                "calculate_percent_change_benchmark: the benchmark should be created using current year data. Please remove previous year data."
            )

        if len(asset_data[ac.portfolio_asset_id].unique()) != len(asset_data.index):
            raise ValueError(
                "calculate_percent_change_benchmark: benchmark construction only accepts one value/row per asset."
            )

        # Because the percent change metrics are percentages we first want to
        # turn them back to fractions.
        asset_data = asset_data.copy()
        asset_data[benchmark_value_column] = asset_data[percent_change_column] / 100

        # Because LFL values are improvements when negative, we want to change
        # their sign before re-scaling them.
        if is_lfl:
            asset_data[benchmark_value_column] = -asset_data[benchmark_value_column]

        # Add the benchmark values to the memberships dataset.
        memberships = memberships.merge(
            asset_data[[ac.portfolio_asset_id, benchmark_value_column]],
            how="left",
            on=ac.portfolio_asset_id,
        )

        # Assets with missing values for this metric should not impact the
        # benchmark process, so let's make sure of that.
        if any(memberships[benchmark_value_column].isna()):
            raise ValueError(
                "calculate_percent_change_benchmark: missing values of the benchmark-ed metric should be filtered out from the benchmark groups."
            )

        # Because the percent change values can be out of the [0;100] interval, we want to
        # rescale them for scoring.
        # In order to re-scale, let's grab the 75th percentile (same as -25th).
        # TODO: I think it's faster to filter before groupby, then use `agg` + `reset_index`
        # then you can merge instead of map.
        group_75th_perc = memberships.groupby(mc.group_id).apply(
            lambda x: x[x[mc.is_benchmarkable]][benchmark_value_column].quantile(0.75),
            include_groups=False,
        )
        memberships[benchmark_percentile_column] = memberships[mc.group_id].map(
            group_75th_perc
        )

        # Re-scale the percent change benchmark value using the 75th percentile value.
        memberships[benchmark_value_column] = memberships.apply(
            self._rescale_percent_change_benchmark_value,
            axis=1,
            args=(benchmark_value_column, benchmark_percentile_column),
        )

        # Calculate the benchmark metric.
        group_metrics = memberships.groupby(mc.group_id).apply(
            lambda x: pd.Series.mean(x[x[mc.is_benchmarkable]][benchmark_value_column]),
            include_groups=False,
        )
        memberships[benchmark_metric_column] = memberships[mc.group_id].map(
            group_metrics
        )

        return memberships

    def _rescale_percent_change_benchmark_value(
        self,
        membership: pd.Series,
        benchmark_value_column: str,
        benchmark_percentile_column: str,
    ) -> float:
        """
        Rescales the percent change benchmark value of an asset using the 75th
        percentile of the benchmark group.

        Parameters:
        membership (pd.Series): a row of the memberships dataset.

        Returns:
        float: the re-scaled value of the percent change benchmark value.
        """
        max_value = np.minimum(
            self.lfl_largest_maximum, membership[benchmark_percentile_column]
        )
        max_value = np.maximum(self.lfl_smallest_maximum, max_value)

        membership[benchmark_value_column] = (
            membership[benchmark_value_column] / max_value
        )

        if membership[benchmark_value_column] < 0:
            return 0

        if membership[benchmark_value_column] > 1:
            return 1

        return membership[benchmark_value_column]

    def calculate_en1_lfl_benchmark(
        self, asset_data: pd.DataFrame, memberships: pd.DataFrame, control: str
    ) -> pd.DataFrame:
        """
        Calculates the benchmark values and metrics for the Like-For-Like
        percent change for indicator EN1.

        Parameters:
        asset_data (pd.DataFrame): DataFrame containing the asset data and energy columns.
        memberships (pd.DataFrame): DataFrame containing the pairs asset/benchmark group.
        control (str): a string indicating whether the metric to compute is based on
            landlord controlled (lc) or tenant conrolled (tc) areas.

        Returns:
        pd.DataFrame: the memberships DataFrame with the added benchmark values
            and metrics per asset.
        """
        if control not in ["lc", "tc"]:
            raise ValueError(
                "calculate_en1_coverage_benchmark: control should be one of 'lc' or 'tc'."
            )

        if control == "lc":
            memberships = self.calculate_percent_change_benchmark(
                asset_data,
                memberships,
                ec.en_lfl_percent_change_lc,
                mc.bench_en_lfl_percent_change_lc,
                mc.percentile_75_en_lfl_percent_change_lc,
                mc.mean_en_lfl_percent_change_lc,
                is_lfl=True,
            )
        elif control == "tc":
            memberships = self.calculate_percent_change_benchmark(
                asset_data,
                memberships,
                ec.en_lfl_percent_change_tc,
                mc.bench_en_lfl_percent_change_tc,
                mc.percentile_75_en_lfl_percent_change_tc,
                mc.mean_en_lfl_percent_change_tc,
                is_lfl=True,
            )

        return memberships

    def calculate_wt1_lfl_benchmark(
        self, asset_data: pd.DataFrame, memberships: pd.DataFrame, control: str
    ) -> pd.DataFrame:
        """
        Calculates the benchmark values and metrics for the Like-For-Like
        percent change for indicator WT1.

        Parameters:
        asset_data (pd.DataFrame): DataFrame containing the asset data and water columns.
        memberships (pd.DataFrame): DataFrame containing the pairs asset/benchmark group.
        control (str): a string indicating whether the metric to compute is based on
            landlord controlled (lc) or tenant conrolled (tc) areas.

        Returns:
        pd.DataFrame: the memberships DataFrame with the added benchmark values
            and metrics per asset.
        """
        if control not in ["lc", "tc"]:
            raise ValueError(
                "calculate_en1_coverage_benchmark: control should be one of 'lc' or 'tc'."
            )

        if control == "lc":
            memberships = self.calculate_percent_change_benchmark(
                asset_data,
                memberships,
                wc.wat_lfl_percent_change_lc,
                mc.bench_wat_lfl_percent_change_lc,
                mc.percentile_75_wat_lfl_percent_change_lc,
                mc.mean_wat_lfl_percent_change_lc,
                is_lfl=True,
            )
        elif control == "tc":
            memberships = self.calculate_percent_change_benchmark(
                asset_data,
                memberships,
                wc.wat_lfl_percent_change_tc,
                mc.bench_wat_lfl_percent_change_tc,
                mc.percentile_75_wat_lfl_percent_change_tc,
                mc.mean_wat_lfl_percent_change_tc,
                is_lfl=True,
            )

        return memberships

    def calculate_gh1_lfl_benchmark(
        self, asset_data: pd.DataFrame, memberships: pd.DataFrame, scope: str
    ) -> pd.DataFrame:
        """
        Calculates the benchmark values and metrics for the Like-For-Like
        percent change for indicator GH1.

        Parameters:
        asset_data (pd.DataFrame): DataFrame containing the asset data and GHG columns.
        memberships (pd.DataFrame): DataFrame containing the pairs asset/benchmark group.
        scope (str): a string indicating whether the metric to compute is based on
            scopes 1 and 2 (s12) or scope 3 (s3) emissions.

        Returns:
        pd.DataFrame: the memberships DataFrame with the added benchmark values
            and metrics per asset.
        """
        if scope not in ["s12", "s3"]:
            raise ValueError(
                "calculate_gh1_coverage_benchmark: scope should be one of 's12' or 's3'."
            )

        if scope == "s12":
            memberships = self.calculate_percent_change_benchmark(
                asset_data,
                memberships,
                gc.ghg_lfl_percent_change_s12,
                mc.bench_ghg_lfl_percent_change_s12,
                mc.percentile_75_ghg_lfl_percent_change_s12,
                mc.mean_ghg_lfl_percent_change_s12,
                is_lfl=True,
            )
        elif scope == "s3":
            memberships = self.calculate_percent_change_benchmark(
                asset_data,
                memberships,
                gc.ghg_lfl_percent_change_s3,
                mc.bench_ghg_lfl_percent_change_s3,
                mc.percentile_75_ghg_lfl_percent_change_s3,
                mc.mean_ghg_lfl_percent_change_s3,
                is_lfl=True,
            )

        return memberships

    def calculate_en1_renewable_benchmark(
        self, asset_data: pd.DataFrame, memberships: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Calculates the benchmark values and metrics for the renewable energy
        generation percent change for indicator EN1.

        Parameters:
        asset_data (pd.DataFrame): DataFrame containing the asset data and energy columns.
        memberships (pd.DataFrame): DataFrame containing the pairs asset/benchmark group.

        Returns:
        pd.DataFrame: the memberships DataFrame with the added benchmark values
            and metrics per asset.
        """
        memberships = self.calculate_percent_change_benchmark(
            asset_data,
            memberships,
            ec.en_ren_percent_change,
            mc.bench_en_ren_percent_change,
            mc.percentile_75_en_ren_percent_change,
            mc.mean_en_ren_percent_change,
        )

        return memberships

    def calculate_wt1_recycled_benchmark(
        self, asset_data: pd.DataFrame, memberships: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Calculates the benchmark values and metrics for the recycled/reused
        water percent change for indicator WT1.

        Parameters:
        asset_data (pd.DataFrame): DataFrame containing the asset data and water columns.
        memberships (pd.DataFrame): DataFrame containing the pairs asset/benchmark group.

        Returns:
        pd.DataFrame: the memberships DataFrame with the added benchmark values
            and metrics per asset.
        """
        memberships = self.calculate_percent_change_benchmark(
            asset_data,
            memberships,
            wc.wat_rec_percent_change,
            mc.bench_wat_rec_percent_change,
            mc.percentile_75_wat_rec_percent_change,
            mc.mean_wat_rec_percent_change,
        )

        return memberships
