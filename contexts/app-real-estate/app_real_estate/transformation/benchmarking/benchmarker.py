import pandas as pd
from app_real_estate.transformation.benchmarking.create_benchmark_groups import (
    BenchmarkGroupCreator,
)
from app_real_estate.transformation.benchmarking.calculate_benchmark_metrics import (
    BenchmarkMetricCalculator,
)
from app_real_estate.transformation.benchmarking.calculate_percent_change_benchmark_metric import (
    PercentChangeMetricBenchmarkCalculator,
)
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.ghg_columns as gc
import app_real_estate.constants.column_names.waste_columns as wsc
import app_real_estate.constants.column_names.water_columns as wc
import app_real_estate.constants.column_names.building_certification_columns as bc


class Benchmarker:
    """
    RunBenchmarkingPipeline defines the benchmarking pipeline functions for each
    indicator.

    The benchmarking pipeline involves defining the metrics to benchmark, creating
    the benchmark groups and calculating the benchmark metrics.
    Each function outputs two dictionaries that contain respectively the benchmark
    groups DataFrames and the group memberships DataFrames for each metric.

    Usage example for EN1:
        data_preparator = RunDataPreparation()
        asset_data, asset_data_cy, is_positive_ren, is_not_outlier = (
            self.data_preparator.prepare_data_en1(asset_data)
        )
        benchmarker = RunBenchmarkingPipeline()
        groups_dict, memberships_dict = benchmarker.benchmark_metrics_en1(asset_data_cy, is_positive_ren, is_not_outlier)

    Note: This class requires preprocessed data as input.
    """

    def __init__(self) -> None:
        self.benchmark_group_creator = BenchmarkGroupCreator()
        self.benchmark_metric_creator = BenchmarkMetricCalculator()
        self.p_change_benchmark_creator = PercentChangeMetricBenchmarkCalculator()

    def benchmark_metrics_en1(
        self,
        asset_data_cy: pd.DataFrame,
        descriptions: pd.DataFrame,
    ) -> tuple[dict[str, pd.DataFrame], dict[str, pd.DataFrame]]:
        """
        Runs the benchmarking pipeline for EN1.

        Parameters:
        asset_data_cy (pd.DataFrame): the asset data with current year
            operational data and metric calculations.
        descriptions: (DataFrame) which contains the following information:
        is_positive_ren (list[bool]): a boolean list describing whether assets
            are eligible for renewable energy benchmarking.
        is_not_lfl_outlier (list[bool]): a boolean list describing whether assets
            are outliers or not.
        is_rejected_lfl_outlier (list[bool]): a boolean list describing whether assets
            are rejected outliers.
        is_intensity_benchmarkable (list[bool]): a boolean list describing whether
            assets' intensity metric is benchmarkable (coverage 100% and not an outlier).
        is_rejected_int_outlier (list[bool]): a boolean list describing whether
            the assets' intensity metric is a rejected outlier.

        Returns:
        tuple[dict[str, pd.DataFrame], dict[str, pd.DataFrame]]:
            a tuple containing the dictionary containing the benchmark groups
            for EN1 and the dictionary containing the benchmark group
            memberships for EN1.
        """

        is_positive_ren = descriptions["is_positive_ren"].tolist()
        is_not_lfl_outlier = descriptions["is_not_lfl_outlier"].tolist()
        is_rejected_lfl_outlier = descriptions["is_rejected_lfl_outlier"].tolist()
        is_intensity_benchmarkable = descriptions["is_intensity_benchmarkable"].tolist()
        is_rejected_int_outlier = descriptions["is_rejected_int_outlier"].tolist()

        # Define the metric to benchmark for EN1.
        en_1_benchmarked_metrics = [
            ec.en_area_time_cov_p_lc,
            ec.en_area_time_cov_p_tc,
            ec.en_lfl_percent_change_lc,
            ec.en_lfl_percent_change_tc,
            ec.en_ren_percent_change,
            ec.en_efficiency_int_kwh_m2,
        ]

        # Create benchmark groups for each metric.
        groups_memberships_dfs = [
            self.benchmark_group_creator.create_benchmark_groups(asset_data_cy, metric)
            for metric in en_1_benchmarked_metrics[:2]
        ]
        # For LFL, hard outliers should be completely removed from the benchmarking
        # because they are not scored.
        groups_memberships_dfs += [
            self.benchmark_group_creator.create_benchmark_groups(
                asset_data_cy,
                metric,
                is_benchmarkable=is_not_lfl_outlier,
                is_excluded=is_rejected_lfl_outlier,
            )
            for metric in en_1_benchmarked_metrics[2:4]
        ]
        groups_memberships_dfs.append(
            self.benchmark_group_creator.create_benchmark_groups(
                asset_data_cy,
                ec.en_ren_percent_change,
                is_benchmarkable=is_positive_ren,
            )
        )

        groups_memberships_dfs.append(
            self.benchmark_group_creator.create_benchmark_groups(
                asset_data_cy,
                ec.en_efficiency_int_kwh_m2,
                is_benchmarkable=is_intensity_benchmarkable,
                is_excluded=is_rejected_int_outlier,
            )
        )

        groups_dfs = [group for (group, _) in groups_memberships_dfs]
        memberships_dfs = [memberships for (_, memberships) in groups_memberships_dfs]

        # Calculate benchmark metrics and values for each metric.
        memberships_dfs[0] = (
            self.benchmark_metric_creator.calculate_en1_coverage_benchmark(
                asset_data_cy, memberships_dfs[0], "lc"
            )
        )
        memberships_dfs[1] = (
            self.benchmark_metric_creator.calculate_en1_coverage_benchmark(
                asset_data_cy, memberships_dfs[1], "tc"
            )
        )

        memberships_dfs[2] = (
            self.p_change_benchmark_creator.calculate_en1_lfl_benchmark(
                asset_data_cy, memberships_dfs[2], "lc"
            )
        )
        memberships_dfs[3] = (
            self.p_change_benchmark_creator.calculate_en1_lfl_benchmark(
                asset_data_cy, memberships_dfs[3], "tc"
            )
        )
        memberships_dfs[4] = (
            self.p_change_benchmark_creator.calculate_en1_renewable_benchmark(
                asset_data_cy, memberships_dfs[4]
            )
        )

        memberships_dfs[5] = (
            self.benchmark_metric_creator.calculate_energy_efficiency_benchmark(
                asset_data_cy, memberships_dfs[5]
            )
        )

        groups_dict = dict(zip(en_1_benchmarked_metrics, groups_dfs))
        memberships_dict = dict(zip(en_1_benchmarked_metrics, memberships_dfs))

        return groups_dict, memberships_dict

    def benchmark_metrics_gh1(
        self,
        asset_data_cy: pd.DataFrame,
        descriptions: pd.DataFrame,
    ) -> tuple[dict[str, pd.DataFrame], dict[str, pd.DataFrame]]:
        """
        Runs the benchmarking pipeline for GH1.

        Parameters:
        asset_data_cy (pd.DataFrame): the asset data with current year
            operational data and metric calculations.
        descriptions (DataFrame): A dataframe which contains:
        is_not_outlier (list[bool]): a boolean list describing whether assets
            are the dictionary containing the benchmark group memberships.
        is_rejected_outlier (list[bool]): a boolean list describing whether assets
            are rejected outliers.

        Returns:
        tuple[dict[str, pd.DataFrame], dict[str, pd.DataFrame]]:
            a tuple containing the dictionary containing the benchmark groups
            for GH1 and the the dictionary containing the benchmark group
            memberships for GH1.
        """

        is_not_outlier = descriptions["is_not_outlier"].tolist()
        is_rejected_lfl_outlier = descriptions["is_rejected_lfl_outlier"].tolist()
        is_intensity_benchmarkable = descriptions["is_intensity_benchmarkable"].tolist()
        is_rejected_int_outlier = descriptions["is_rejected_int_outlier"].tolist()

        # Define the metric to benchmark for GH1.
        gh_1_benchmarked_metrics = [
            gc.ghg_area_time_cov_p_s12,
            gc.ghg_area_time_cov_p_s3,
            gc.ghg_lfl_percent_change_s12,
            gc.ghg_lfl_percent_change_s3,
            gc.ghg_scored_int_ton_m2,
        ]

        # Create benchmark groups for each metric.
        groups_memberships_dfs = [
            self.benchmark_group_creator.create_benchmark_groups(asset_data_cy, metric)
            for metric in gh_1_benchmarked_metrics[:2]
        ]
        # For LFL, hard outliers should be completely removed from the benchmarking
        # because they are not scored.
        groups_memberships_dfs += [
            self.benchmark_group_creator.create_benchmark_groups(
                asset_data_cy,
                metric,
                is_benchmarkable=is_not_outlier,
                is_excluded=is_rejected_lfl_outlier,
            )
            for metric in gh_1_benchmarked_metrics[2:4]
        ]

        groups_memberships_dfs.append(
            self.benchmark_group_creator.create_benchmark_groups(
                asset_data_cy,
                gc.ghg_scored_int_ton_m2,
                is_benchmarkable=is_intensity_benchmarkable,
                is_excluded=is_rejected_int_outlier,
            )
        )

        groups_dfs = [group for (group, _) in groups_memberships_dfs]
        memberships_dfs = [memberships for (_, memberships) in groups_memberships_dfs]

        # Calculate benchmark metrics and values for each metric.
        memberships_dfs[0] = (
            self.benchmark_metric_creator.calculate_gh1_coverage_benchmark(
                asset_data_cy, memberships_dfs[0], "s12"
            )
        )
        memberships_dfs[1] = (
            self.benchmark_metric_creator.calculate_gh1_coverage_benchmark(
                asset_data_cy, memberships_dfs[1], "s3"
            )
        )

        memberships_dfs[2] = (
            self.p_change_benchmark_creator.calculate_gh1_lfl_benchmark(
                asset_data_cy, memberships_dfs[2], "s12"
            )
        )
        memberships_dfs[3] = (
            self.p_change_benchmark_creator.calculate_gh1_lfl_benchmark(
                asset_data_cy, memberships_dfs[3], "s3"
            )
        )

        groups_dict = dict(zip(gh_1_benchmarked_metrics, groups_dfs))
        memberships_dict = dict(zip(gh_1_benchmarked_metrics, memberships_dfs))

        return groups_dict, memberships_dict

    def benchmark_metrics_ws1(
        self,
        asset_data_cy: pd.DataFrame,
    ) -> tuple[dict[str, pd.DataFrame], dict[str, pd.DataFrame]]:
        """
        Runs the benchmarking pipeline for WS1.

        Parameters:
        asset_data_cy (pd.DataFrame): the asset data with current year
            operational data and metric calculations.

        Returns:
        tuple[dict[str, pd.DataFrame], dict[str, pd.DataFrame]]:
            a tuple containing the dictionary containing the benchmark groups
            for WS1 and the dictionary containing the benchmark group
            memberships for WS1.
        """
        # Define the metric to benchmark for WS1.
        ws_1_benchmarked_metrics = [
            wsc.was_area_cov_p_lc,
            wsc.was_area_cov_p_tc,
            wsc.was_pabs_div,
        ]

        # Create benchmark groups for each metric.
        groups_memberships_dfs = [
            self.benchmark_group_creator.create_benchmark_groups(asset_data_cy, metric)
            for metric in ws_1_benchmarked_metrics[:3]
        ]

        groups_dfs = [group for (group, _) in groups_memberships_dfs]
        memberships_dfs = [memberships for (_, memberships) in groups_memberships_dfs]

        # Calculate benchmark metrics and values for each metric.
        memberships_dfs[0] = (
            self.benchmark_metric_creator.calculate_ws1_coverage_benchmark(
                asset_data_cy, memberships_dfs[0], "lc"
            )
        )
        memberships_dfs[1] = (
            self.benchmark_metric_creator.calculate_ws1_coverage_benchmark(
                asset_data_cy, memberships_dfs[1], "tc"
            )
        )
        memberships_dfs[2] = (
            self.benchmark_metric_creator.calculate_diverted_waste_coverage_benchmark(
                asset_data_cy, memberships_dfs[2]
            )
        )

        groups_dict = dict(zip(ws_1_benchmarked_metrics, groups_dfs))
        memberships_dict = dict(zip(ws_1_benchmarked_metrics, memberships_dfs))

        return groups_dict, memberships_dict

    def benchmark_metrics_wt1(
        self,
        asset_data_cy: pd.DataFrame,
        descriptions: pd.DataFrame,
    ) -> tuple[dict[str, pd.DataFrame], dict[str, pd.DataFrame]]:
        """
        Runs the benchmarking pipeline for WT1.

        Parameters:
        asset_data_cy (pd.DataFrame): the asset data with current year
            operational data and metric calculations.
        descriptions (DataFrame): A dataframe which contains:
        is_positive_rec (list[bool]): a boolean list describing whether assets
            are eligible for recycled water benchmarking.
        is_not_outlier (list[bool]): a boolean list describing whether assets
            are the dictionary containing the benchmark group memberships.
        is_rejected_outlier (list[bool]): a boolean list describing whether assets
            are rejected outliers.

        Returns:
        tuple[dict[str, pd.DataFrame], dict[str, pd.DataFrame]]:
            a tuple containing the dictionary containing the benchmark groups
            for WT1 and the the dictionary containing the benchmark group
            memberships for WT1.
        """

        is_positive_rec = descriptions["is_positive_rec"].tolist()
        is_not_outlier = descriptions["is_not_outlier"].tolist()
        is_rejected_lfl_outlier = descriptions["is_rejected_lfl_outlier"].tolist()
        is_intensity_benchmarkable = descriptions["is_intensity_benchmarkable"].tolist()
        is_rejected_int_outlier = descriptions["is_rejected_int_outlier"].tolist()

        # Define the metric to benchmark for WT1.
        wt_1_benchmarked_metrics = [
            wc.wat_area_time_cov_p_lc,
            wc.wat_area_time_cov_p_tc,
            wc.wat_lfl_percent_change_lc,
            wc.wat_lfl_percent_change_tc,
            wc.wat_rec_percent_change,
            wc.wat_scored_int_dm3_m2,
        ]

        # Create benchmark groups for each metric.
        groups_memberships_dfs = [
            self.benchmark_group_creator.create_benchmark_groups(asset_data_cy, metric)
            for metric in wt_1_benchmarked_metrics[:2]
        ]
        # For LFL, hard outliers should be completely removed from the benchmarking
        # because they are not scored.
        groups_memberships_dfs += [
            self.benchmark_group_creator.create_benchmark_groups(
                asset_data_cy,
                metric,
                is_benchmarkable=is_not_outlier,
                is_excluded=is_rejected_lfl_outlier,
            )
            for metric in wt_1_benchmarked_metrics[2:4]
        ]
        groups_memberships_dfs.append(
            self.benchmark_group_creator.create_benchmark_groups(
                asset_data_cy,
                wc.wat_rec_percent_change,
                is_benchmarkable=is_positive_rec,
            )
        )

        groups_memberships_dfs.append(
            self.benchmark_group_creator.create_benchmark_groups(
                asset_data_cy,
                wc.wat_scored_int_dm3_m2,
                is_benchmarkable=is_intensity_benchmarkable,
                is_excluded=is_rejected_int_outlier,
            )
        )

        groups_dfs = [group for (group, _) in groups_memberships_dfs]
        memberships_dfs = [memberships for (_, memberships) in groups_memberships_dfs]

        # Calculate benchmark metrics and values for each metric.
        memberships_dfs[0] = (
            self.benchmark_metric_creator.calculate_wt1_coverage_benchmark(
                asset_data_cy, memberships_dfs[0], "lc"
            )
        )
        memberships_dfs[1] = (
            self.benchmark_metric_creator.calculate_wt1_coverage_benchmark(
                asset_data_cy, memberships_dfs[1], "tc"
            )
        )

        memberships_dfs[2] = (
            self.p_change_benchmark_creator.calculate_wt1_lfl_benchmark(
                asset_data_cy, memberships_dfs[2], "lc"
            )
        )
        memberships_dfs[3] = (
            self.p_change_benchmark_creator.calculate_wt1_lfl_benchmark(
                asset_data_cy, memberships_dfs[3], "tc"
            )
        )
        memberships_dfs[4] = (
            self.p_change_benchmark_creator.calculate_wt1_recycled_benchmark(
                asset_data_cy, memberships_dfs[4]
            )
        )

        groups_dict = dict(zip(wt_1_benchmarked_metrics, groups_dfs))
        memberships_dict = dict(zip(wt_1_benchmarked_metrics, memberships_dfs))

        return groups_dict, memberships_dict

    def benchmark_metrics_bc1(
        self, certification_data: pd.DataFrame
    ) -> tuple[dict[str, pd.DataFrame], dict[str, pd.DataFrame]]:
        """
        Runs the entire scoring pipeline for indicators BC1.1 and BC1.2.

        Parameters:
        certification_data (pd.DataFrame): the certifications data with metric calculations.

        Returns:
        tuple[dict[str, pd.DataFrame], dict[str, pd.DataFrame]]:
            a tuple containing the dictionary containing the benchmark groups
            for BC1.1 or BC1.2 and the the dictionary containing the benchmark group
            memberships for BC1.1 or BC1.2.
        """
        # Create benchmark groups for each metric.
        bc_1_benchmarked_metrics = [
            bc.scoring_coverage,
            bc.coverage,
        ]

        # Create benchmark groups for each metric.
        groups_memberships_dfs = [
            self.benchmark_group_creator.create_benchmark_groups(
                certification_data,
                metric,
                unique_id_col=bc.building_data_certifications_scoring_id,
            )
            for metric in bc_1_benchmarked_metrics[:2]
        ]

        groups_dfs = [group for (group, _) in groups_memberships_dfs]
        memberships_dfs = [memberships for (_, memberships) in groups_memberships_dfs]

        # Calculate benchmark metrics and values for each metric.
        memberships_dfs[0] = (
            self.benchmark_metric_creator.calculate_certification_coverage_benchmark(
                certification_data, memberships_dfs[0]
            )
        )
        memberships_dfs[1] = (
            self.benchmark_metric_creator.calculate_certification_coverage_benchmark(
                certification_data, memberships_dfs[1], for_scoring=False
            )
        )

        groups_dict = dict(zip(bc_1_benchmarked_metrics, groups_dfs))
        memberships_dict = dict(zip(bc_1_benchmarked_metrics, memberships_dfs))

        return groups_dict, memberships_dict

    def benchmark_metrics_bc2(
        self,
        asset_data_cy: pd.DataFrame,
    ) -> tuple[dict[str, pd.DataFrame], dict[str, pd.DataFrame]]:
        """
        Runs the entire scoring pipeline for indicators BC2.

        Parameters:
        asset_data_cy (pd.DataFrame): the asset data with current year
            operational data and metric calculations.

        Returns:
        tuple[dict[str, pd.DataFrame], dict[str, pd.DataFrame]]:
            a tuple containing the dictionary containing the benchmark groups
            for BC2 and the the dictionary containing the benchmark group
            memberships for BC2.
        """
        # Create benchmark groups for each metric.
        groups_bc2, memberships_bc2 = (
            self.benchmark_group_creator.create_benchmark_groups(
                asset_data_cy,
                ac.rating_coverage,
            )
        )

        # Calculate benchmark metrics and values for each metric.
        memberships_bc2 = (
            self.benchmark_metric_creator.calculate_energy_rating_coverage_benchmark(
                asset_data_cy, memberships_bc2
            )
        )

        groups_dict = dict({ac.rating_coverage: groups_bc2})
        memberships_dict = dict({ac.rating_coverage: memberships_bc2})

        return groups_dict, memberships_dict
