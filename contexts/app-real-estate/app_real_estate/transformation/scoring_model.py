"""
Scoring Model for Real Estate Analytics

This module defines the ScoringModel class, which orchestrates the scoring pipeline
for various indicators in real estate analytics. It handles data preparation,
benchmarking, asset scoring, and score aggregation for different types of indicators
including energy (EN1), greenhouse gas (GH1), waste (WS1), water (WT1), and building
certifications (BC1.1, BC1.2, BC2).

The ScoringModel class provides a unified interface for running complete scoring
pipelines for each indicator, managing the flow of data through various stages of
calculation and analysis.

Classes:
    ScoringModel: Manages the scoring pipeline for multiple real estate indicators.

Note:
    This model relies on several other components (DataPreparer, Benchmarker,
    IndicatorScorer, IndicatorAggregator) to perform specific tasks within the
    scoring pipeline.

See Also:
    DataPreparer: Prepares data for scoring calculations.
    Benchmarker: Performs benchmarking calculations.
    IndicatorScorer: Calculates scores for individual indicators.
    IndicatorAggregator: Aggregates scores across different levels.
"""

from typing import Callable, Optional
import pandas as pd
import numpy as np
from app_real_estate.models.aggregated_score import AggregatedScore
from app_real_estate.models.score_aggregation_util_models.bc1_1_scored_data_aggregated_level import (
    BC1_1ScoredData_AggregatedLevel,
)
from app_real_estate.models.score_aggregation_util_models.bc1_2_scored_data_aggregated_level import (
    BC1_2ScoredData_AggregatedLevel,
)
from app_real_estate.transformation.aggregation.indicator.bc1_1.bc1_1_score_aggregator_property_subtype_response_id import (
    BC1_1ScoreAggregator_PropertySubtype_ResponseId,
)
from app_real_estate.transformation.aggregation.indicator.bc1_1.bc1_1_score_aggregator_response_id import (
    BC1_1ScoreAggregator_ResponseId,
)
from app_real_estate.transformation.aggregation.indicator.bc1_2.bc1_2_score_aggregator_property_subtype_response_id import (
    BC1_2ScoreAggregator_PropertySubtype_ResponseId,
)
from app_real_estate.transformation.aggregation.indicator.bc1_2.bc1_2_score_aggregator_response_id import (
    BC1_2ScoreAggregator_ResponseId,
)
from app_real_estate.transformation.metric_calculation.data_preparer import (
    DataPreparer,
)
from app_real_estate.transformation.benchmarking.benchmarker import (
    Benchmarker,
)
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.building_certification_columns as bc
from app_real_estate.transformation.scoring.indicator_scorer import IndicatorScorer
from app_real_estate.transformation.scoring.indicator_aggregator import (
    IndicatorAggregator,
)
from app_real_estate.transformation.aggregation.validator.validator import (
    Validator,
)


class ScoringModel:
    """
    ScoringModel defines and executes the scoring pipeline functions for each indicator.

    This class manages the entire scoring process, including data preparation,
    benchmarking, asset scoring, and score aggregation. It provides methods to score
    various indicators such as EN1 (Energy), GH1 (Greenhouse Gas), WS1 (Waste),
    WT1 (Water), BC1.1 and BC1.2 (Building Certifications), and BC2 (Energy Ratings).

    Attributes:
        data_preparer (DataPreparer): Handles data preparation for each indicator.
        benchmarker (Benchmarker): Performs benchmarking calculations.
        indicator_scorer (IndicatorScorer): Calculates scores for individual indicators.
        indicator_aggregator (IndicatorAggregator): Aggregates scores across different levels.

    Methods:
        score_en1: Scores the EN1 (Energy) indicator.
        score_gh1: Scores the GH1 (Greenhouse Gas) indicator.
        score_ws1: Scores the WS1 (Waste) indicator.
        score_wt1: Scores the WT1 (Water) indicator.
        score_bc1_1_or_2: Scores either BC1.1 or BC1.2 (Building Certifications) indicators.
        score_bc1: Combines scores from BC1.1 and BC1.2.
        score_bc2: Scores the BC2 (Energy Ratings) indicator.

    Usage example:
        scoring_model = ScoringModel()
        scored_data_en, groups_dict_en, memberships_dict_en, aggregated_score_en = scoring_model.score_en1(asset_data, ashrae_thresholds, r1_table)
    """

    def __init__(self, report_type: str = "re") -> None:
        """
        Initialize ScoringModel with a specific report type.

        Args:
            report_type: The report type ('re' for Real Estate, 'res' for Residential)
        """
        self.report_type = report_type
        self.data_preparer = DataPreparer()
        self.benchmarker = Benchmarker()

        self.indicator_scorer = IndicatorScorer(report_type)
        self.indicator_aggregator = IndicatorAggregator(report_type)

    # TODO: move this function to a util class
    @staticmethod
    def _merge_filtered_scores_with_data(
        filtered_data: pd.DataFrame,
        data: pd.DataFrame,
        id_column: str | list[str],
    ) -> pd.DataFrame:
        """
        Merge a filtered DataFrame with scores back with its original unfiltered DataFrame.

        Args:
            filtered_data (pd.DataFrame): The filtered and scored data.
            data (pd.DataFrame): The original unfiltered data.
            id_column (str | list[str]): The column(s) on which to merge the two DataFrames.

        Returns:
            pd.DataFrame: The merged data containing original and scored information.
        """
        calculated_columns = filtered_data.columns.difference(data.columns).tolist()
        if isinstance(id_column, str):
            id_column = [id_column]

        Validator.validate_missing_column(data, id_column)
        Validator.validate_missing_column(filtered_data, id_column)

        scored_data = data.merge(
            filtered_data[calculated_columns + id_column],
            how="left",
            on=id_column,
        )

        return scored_data

    def _score_utility_indicator(
        self,
        asset_data: pd.DataFrame,
        prepare_data_func: Callable[
            ...,
            dict[str, pd.DataFrame],
        ],
        benchmark_metrics_func: Callable[
            ..., tuple[dict[str, pd.DataFrame], dict[str, pd.DataFrame]]
        ],
        calculate_asset_score_func: Callable[
            [pd.DataFrame, dict[str, pd.DataFrame]], pd.DataFrame
        ],
        weather_stations: Optional[pd.DataFrame] = None,
        ashrae_thresholds: Optional[pd.DataFrame] = None,
    ) -> tuple[pd.DataFrame, dict[str, pd.DataFrame], dict[str, pd.DataFrame]]:
        """
        Run the entire scoring pipeline for utility indicators (EN1, GH1, WT1 and WS1).

        This method orchestrates the scoring process for utility-based indicators,
        including data preparation, benchmarking, and score calculation.

        Args:
            asset_data (pd.DataFrame): The asset DataFrame with both data years.
            prepare_data_func (Callable): The data preparation function.
            benchmark_metrics_func (Callable): The metrics benchmarking function.
            calculate_asset_score_func (Callable): The asset score calculation function.
            weather_stations (pd.DataFrame): the lookup snapshot from the `asset_portal_ashrae_weather_stations` table.
            ashrae_thresholds (pd.DataFrame): the lookup snapshot from the `asset_portal_ashrae_energy_use_intensity_thresholds` table.

        Returns:
            tuple[pd.DataFrame, dict[str, pd.DataFrame], dict[str, pd.DataFrame]]:
                A tuple containing:
                - The asset data with calculations and scores
                - A dictionary containing the benchmark groups
                - A dictionary containing the benchmark group memberships
        """
        # Calculate asset metrics and potential filters
        if prepare_data_func == self.data_preparer.prepare_data_en1:
            data_prep_result = prepare_data_func(
                asset_data, weather_stations, ashrae_thresholds
            )
        else:
            data_prep_result = prepare_data_func(asset_data)

        asset_data = data_prep_result["asset_data"]
        asset_data_cy = data_prep_result["asset_data_cy"]
        additional_params = (
            [data_prep_result["descriptions"]]
            if "descriptions" in data_prep_result
            else []
        )

        # Benchmark metrics
        groups_dict, memberships_dict = benchmark_metrics_func(
            asset_data_cy, *additional_params
        )

        # Compute asset scores
        asset_data_cy = calculate_asset_score_func(asset_data_cy, memberships_dict)

        # Merge the scored data back in the complete asset data with both years
        scored_data = self._merge_filtered_scores_with_data(
            asset_data_cy, asset_data, ac.portfolio_asset_id
        )

        return (
            scored_data,
            groups_dict,
            memberships_dict,
        )

    def score_en1(
        self,
        asset_data: pd.DataFrame,
        weather_stations: pd.DataFrame,
        ashrae_thresholds: pd.DataFrame,
        r1_table: pd.DataFrame,
    ) -> tuple[
        pd.DataFrame,
        dict[str, pd.DataFrame],
        dict[str, pd.DataFrame],
        AggregatedScore,
    ]:
        """
        Runs the entire scoring pipeline for EN1.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame with both data years.
        weather_stations (pd.DataFrame): the lookup snapshot from the `asset_portal_ashrae_weather_stations` table.
        ashrae_thresholds (pd.DataFrame): the lookup snapshot from the `asset_portal_ashrae_energy_use_intensity_thresholds` table.
        r1_table (pd.DataFrame): the input R1 table from the survey data.

        Returns:
        tuple[pd.DataFrame, dict[str, pd.DataFrame], dict[str, pd.DataFrame]]:
            a tuple containing the asset data with the EN1 calculations and scores,
            the dictionary containing the benchmark groups for EN1 and the
            the dictionary containing the benchmark group memberships for EN1.
        """
        scored_data_en, groups_dict_en, memberships_dict_en = (
            self._score_utility_indicator(
                asset_data,
                self.data_preparer.prepare_data_en1,
                self.benchmarker.benchmark_metrics_en1,
                self.indicator_scorer.score_en1,
                weather_stations,
                ashrae_thresholds,
            )
        )

        aggregated_score_en = self.indicator_aggregator.aggregate_score_en1(
            scored_data_en=scored_data_en, r1_table=r1_table
        )

        return (
            scored_data_en,
            groups_dict_en,
            memberships_dict_en,
            aggregated_score_en,
        )

    def score_gh1(
        self,
        asset_data: pd.DataFrame,
        r1_table: pd.DataFrame,
    ) -> tuple[
        pd.DataFrame,
        dict[str, pd.DataFrame],
        dict[str, pd.DataFrame],
        AggregatedScore,
    ]:
        """
        Runs the entire scoring pipeline for GH1.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame with both data years.

        Returns:
        tuple[pd.DataFrame, dict[str, pd.DataFrame], dict[str, pd.DataFrame]]:
            a tuple containing the asset data with the GH1 calculations and scores,
            the dictionary containing the benchmark groups for GH1 and the
            the dictionary containing the benchmark group memberships for GH1.
        """
        scored_data_gh, groups_dict_gh, memberships_dict_gh = (
            self._score_utility_indicator(
                asset_data,
                self.data_preparer.prepare_data_gh1,
                self.benchmarker.benchmark_metrics_gh1,
                self.indicator_scorer.score_gh1,
            )
        )

        aggregated_score_gh = self.indicator_aggregator.aggregate_score_gh1(
            scored_data_gh, r1_table=r1_table
        )

        return (
            scored_data_gh,
            groups_dict_gh,
            memberships_dict_gh,
            aggregated_score_gh,
        )

    def score_ws1(
        self,
        asset_data: pd.DataFrame,
        r1_table: pd.DataFrame,
    ) -> tuple[
        pd.DataFrame,
        dict[str, pd.DataFrame],
        dict[str, pd.DataFrame],
        AggregatedScore,
    ]:
        """
        Runs the entire scoring pipeline for WS1.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame with both data years.

        Returns:
        tuple[pd.DataFrame, dict[str, pd.DataFrame], dict[str, pd.DataFrame]]:
            a tuple containing the asset data with the WS1 calculations and scores,
            the dictionary containing the benchmark groups for WS1 and the
            the dictionary containing the benchmark group memberships for WS1.
        """
        scored_data_ws, groups_dict_ws, memberships_dict_ws = (
            self._score_utility_indicator(
                asset_data,
                self.data_preparer.prepare_data_ws1,
                self.benchmarker.benchmark_metrics_ws1,
                self.indicator_scorer.score_ws1,
            )
        )

        aggregated_score_ws = self.indicator_aggregator.aggregate_score_ws1(
            scored_data_ws, r1_table=r1_table
        )

        return (
            scored_data_ws,
            groups_dict_ws,
            memberships_dict_ws,
            aggregated_score_ws,
        )

    def score_wt1(
        self,
        asset_data: pd.DataFrame,
        r1_table: pd.DataFrame,
    ) -> tuple[
        pd.DataFrame,
        dict[str, pd.DataFrame],
        dict[str, pd.DataFrame],
        AggregatedScore,
    ]:
        """
        Runs the entire scoring pipeline for WT1.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame with both data years.

        Returns:
        tuple[pd.DataFrame, dict[str, pd.DataFrame], dict[str, pd.DataFrame]]:
            a tuple containing the asset data with the WT1 calculations and scores,
            the dictionary containing the benchmark groups for WT1 and the
            the dictionary containing the benchmark group memberships for WT1.
        """
        scored_data_wt, groups_dict_wt, memberships_dict_wt = (
            self._score_utility_indicator(
                asset_data,
                self.data_preparer.prepare_data_wt1,
                self.benchmarker.benchmark_metrics_wt1,
                self.indicator_scorer.score_wt1,
            )
        )

        aggregated_score_wt = self.indicator_aggregator.aggregate_score_wt1(
            scored_data_wt, r1_table=r1_table
        )

        return (
            scored_data_wt,
            groups_dict_wt,
            memberships_dict_wt,
            aggregated_score_wt,
        )

    def score_bc1_1_or_2(
        self,
        asset_data: pd.DataFrame,
        certification_data: pd.DataFrame,
        certification_table: pd.DataFrame,
        r1_table: pd.DataFrame,
        indicator: str = "BC1.1",
    ) -> tuple[
        pd.DataFrame,
        pd.DataFrame,
        dict[str, pd.DataFrame],
        dict[str, pd.DataFrame],
        AggregatedScore,
    ]:
        """
        Runs the entire scoring pipeline for indicators BC1.1 and BC1.2.

        Parameters:
        asset_data (pd.DataFrame): the asset to score.s
        certification_data (pd.DataFrame): the assets' certifications DataFrame.
        certification_table (pd.DataFrame): the certification information.
        indicator (str): a string describing for which indicator to prepare the
            data.

        Returns:
        tuple[pd.DataFrame, dict[str, pd.DataFrame], dict[str, pd.DataFrame]]:
            a tuple containing the asset data with the BC1.1 and BC1.2 calculations and scores,
            the certifications data with the BC1.1 and BC1.2 calculations and scores,
            the dictionary containing the benchmark groups for BC1, the
            the dictionary containing the benchmark group memberships for BC1 and
            the aggregated scores.
        """
        # Check indicator value
        if indicator not in ["BC1.1", "BC1.2"]:
            raise ValueError(
                "score_bc1: valid indicator names for BC1 scoring are 'BC1.1' and 'BC1.2'."
            )

        # Calculate asset metrics and filters
        asset_data, certification_data, is_operational, is_cy_op = (
            self.data_preparer.prepare_data_bc1_asset_level(
                asset_data, certification_data, certification_table
            )
        )

        is_valid_type = (
            ~np.array(is_operational) if indicator == "BC1.1" else is_operational
        )
        filtered_certifications = certification_data[is_valid_type]

        # Create the groups and benchmark the metrics
        groups_dict, memberships_dict = self.benchmarker.benchmark_metrics_bc1(
            filtered_certifications
        )

        # Get current year operational assets for scoring
        asset_data_cy = asset_data[is_cy_op]

        # Compute certification and asset scores
        asset_score_func = (
            self.indicator_scorer.score_bc1_1
            if indicator == "BC1.1"
            else self.indicator_scorer.score_bc1_2
        )

        filtered_certifications, asset_data_cy = asset_score_func(
            filtered_certifications, asset_data_cy, memberships_dict
        )

        # Merge the scored certifications back with the rest
        scored_certifications = self._merge_filtered_scores_with_data(
            filtered_certifications,
            certification_data,
            bc.building_data_certifications_scoring_id,
        )

        # Merge the scored data back in the complete asset data with both years
        scored_assets = self._merge_filtered_scores_with_data(
            asset_data_cy, asset_data, ac.portfolio_asset_id
        )

        aggregated_score_bc1_1_or_2 = (
            self.indicator_aggregator.aggregate_score_bc1_1(
                scored_assets, r1_table=r1_table
            )
            if indicator == "BC1.1"
            else self.indicator_aggregator.aggregate_score_bc1_2(
                scored_assets, r1_table=r1_table
            )
        )

        return (
            scored_assets,
            scored_certifications,
            groups_dict,
            memberships_dict,
            aggregated_score_bc1_1_or_2,
        )

    def score_bc1(
        self,
        scored_data_bc1_1: AggregatedScore,
        scored_data_bc1_2: AggregatedScore,
        r1_table: pd.DataFrame,
    ) -> AggregatedScore:
        """
        Combine scores from BC1.1 and BC1.2 indicators.

        This method aggregates the scores from BC1.1 and BC1.2 indicators,
        which represent different aspects of building certifications.

        Args:
            scored_data_bc1_1 (AggregatedScore): Aggregated scores for BC1.1.
            scored_data_bc1_2 (AggregatedScore): Aggregated scores for BC1.2.
            r1_table (pd.DataFrame): Reference table for R1 data.

        Returns:
            AggregatedScore: Combined and aggregated scores for BC1.
        """

        aggregated_score = AggregatedScore(
            portfolio_level=pd.merge(
                scored_data_bc1_1.portfolio_level,
                scored_data_bc1_2.portfolio_level,
                on=[ac.response_id, ac.data_year],
                how="left",
            ),
            propertysubtype_country_responseid=pd.merge(
                scored_data_bc1_1.propertysubtype_country_responseid,
                scored_data_bc1_2.propertysubtype_country_responseid,
                on=[
                    ac.property_type_code,
                    ac.country,
                    ac.response_id,
                    ac.data_year,
                ],
                how="left",
            ),
            propertysubtype_responseid=pd.merge(
                scored_data_bc1_1.propertysubtype_responseid,
                scored_data_bc1_2.propertysubtype_responseid,
                on=[ac.property_type_code, ac.response_id, ac.data_year],
                how="left",
            ),
        )
        return aggregated_score

    def score_bc2(
        self,
        asset_data: pd.DataFrame,
        energy_ratings_data: pd.DataFrame,
        r1_table: pd.DataFrame,
    ) -> tuple[
        pd.DataFrame,
        pd.DataFrame,
        dict[str, pd.DataFrame],
        dict[str, pd.DataFrame],
        AggregatedScore,
    ]:
        """
        Runs the entire scoring pipeline for BC2.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame with both data years.
        energy_ratings_data (pd.DataFrame): the energy ratings DataFrame.

        Returns:
        tuple[pd.DataFrame, pd.DataFrame, dict[str, pd.DataFrame], dict[str, pd.DataFrame]]:
            a tuple containing the asset data with the BC2 calculations and scores,
            the energy ratings data with the BC2 calculations and scores,
            the dictionary containing the benchmark groups for BC2,
            the dictionary containing the benchmark group memberships for BC2 and
            the aggregated scores.
        """
        # Import data and calculate asset metrics
        asset_data, energy_ratings_data, is_cy_op = self.data_preparer.prepare_data_bc2(
            asset_data, energy_ratings_data
        )

        asset_data_cy = asset_data[is_cy_op]

        # Compute benchmark groups and metrics
        groups_dict, memberships_dict = self.benchmarker.benchmark_metrics_bc2(
            asset_data_cy
        )

        # Compute asset scores
        asset_data_cy = self.indicator_scorer.score_bc2(asset_data_cy, memberships_dict)
        scored_data = self._merge_filtered_scores_with_data(
            asset_data_cy, asset_data, ac.portfolio_asset_id
        )

        aggregated_score = self.indicator_aggregator.aggregate_score_bc2(
            scored_data, r1_table=r1_table
        )

        return (
            scored_data,
            energy_ratings_data,
            groups_dict,
            memberships_dict,
            aggregated_score,
        )

    def score_bc1_1_or_2_at_aggregated_level(
        self,
        asset_data: pd.DataFrame,
        certification_data: pd.DataFrame,
        certification_table: pd.DataFrame,
        r1_table: pd.DataFrame,
        indicator: str = "BC1.1",
    ) -> tuple[
        pd.DataFrame,
        dict[str, pd.DataFrame],
        dict[str, pd.DataFrame],
        pd.DataFrame,
        pd.DataFrame,
        pd.DataFrame,
    ]:
        """
        Runs the entire scoring pipeline for indicators BC1.1 and BC1.2.

        Parameters:
        asset_data (pd.DataFrame): the asset to score.s
        certification_data (pd.DataFrame): the assets' certifications DataFrame.
        certification_table (pd.DataFrame): the certification information.
        indicator (str): a string describing for which indicator to prepare the
            data.

        Returns:
        tuple[pd.DataFrame, dict[str, pd.DataFrame], dict[str, pd.DataFrame], pd.DataFrame, pd.DataFrame, pd.DataFrame]:
            a tuple containing the certifications data with the BC1.1 and BC1.2 calculations,
            the dictionary containing the benchmark groups for BC1, the
            the dictionary containing the benchmark group memberships for BC1 and
            the aggregated scores.
        """
        # Check indicator value
        if indicator not in ["BC1.1", "BC1.2"]:
            raise ValueError(
                "score_bc1: valid indicator names for BC1 scoring are 'BC1.1' and 'BC1.2'."
            )

        # Calculate asset metrics and filters
        aggregated_certification_data, certification_data = (
            self.data_preparer.prepare_data_bc1_aggregated_level(
                asset_data, certification_data, certification_table, r1_table
            )
        )

        if any(~aggregated_certification_data[bc.indicator].isin(["BC1.1", "BC1.2"])):
            raise ValueError(
                "Value of the column 'indicator' in the certification data should be one of 'BC1.1', 'BC1.2'."
            )

        is_valid_type = aggregated_certification_data[bc.indicator] == indicator
        filtered_certifications = aggregated_certification_data.loc[is_valid_type]

        # Create the groups and benchmark the metrics
        groups_dict, memberships_dict = self.benchmarker.benchmark_metrics_bc1(
            filtered_certifications
        )

        # Compute certification and asset scores
        asset_score_func = (
            self.indicator_scorer.score_bc1_1_aggregated
            if indicator == "BC1.1"
            else self.indicator_scorer.score_bc1_2_aggregated
        )

        filtered_certifications = asset_score_func(
            filtered_certifications, memberships_dict
        )

        # Rename coverage
        coverage_column_name = (
            bc.bc1_1_coverage_scoring
            if indicator == "BC1.1"
            else bc.bc1_2_coverage_scoring
        )
        filtered_certifications.rename(
            columns={"scoring_coverage": coverage_column_name}, inplace=True
        )

        # Aggregate to the property subtype level and then the portfolio level
        scored_aggregated_certifications = filtered_certifications.set_index(
            [ac.response_id, ac.country, ac.property_type_code, ac.data_year]
        )
        portfolio_level_scores = (
            BC1_1ScoreAggregator_ResponseId(
                scored_aggregated_certifications,
                r1_table=r1_table,
                is_input_aggregated=True,
                report_type=self.report_type,
            ).process()
            if indicator == "BC1.1"
            else BC1_2ScoreAggregator_ResponseId(
                scored_aggregated_certifications,
                r1_table=r1_table,
                is_input_aggregated=True,
                report_type=self.report_type,
            ).process()
        )

        prt_scores = (
            BC1_1ScoreAggregator_PropertySubtype_ResponseId(
                scored_aggregated_certifications,
                r1_table,
                data_model=BC1_1ScoredData_AggregatedLevel,
                report_type=self.report_type,
            ).process()
            if indicator == "BC1.1"
            else BC1_2ScoreAggregator_PropertySubtype_ResponseId(
                scored_aggregated_certifications,
                r1_table,
                data_model=BC1_2ScoredData_AggregatedLevel,
                report_type=self.report_type,
            ).process()
        )

        return (
            certification_data,
            groups_dict,
            memberships_dict,
            scored_aggregated_certifications,
            prt_scores,
            portfolio_level_scores,
        )
