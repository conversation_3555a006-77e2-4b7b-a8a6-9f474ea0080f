import pandas as pd
import numpy as np
from app_real_estate.constants.helper_enumerators import Indicator
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.building_certification_columns as bc
import app_real_estate.constants.column_names.export_columns as export_columns
from app_real_estate.transformation.aggregation.processor.iprocessor import (
    IProcessor,
)


class IndicatorDataFormatter(IProcessor):
    """
    The class `IndicatorDataFormatter` provides functions to merge and format
    the data of all levels after scoring and score aggregation.
    """

    # TODO: Add other subscores/metrics for each indicator
    indicator_columns = {
        Indicator.EN_1: [
            sc.score_en1,
            sc.score_en1_fraction,
            sc.score_en1_percent,
            sc.score_en1_max,
            sc.score_en1_en_area_time_cov_p_lc,
            sc.score_en1_en_area_time_cov_p_lc_absolute,
            sc.score_en1_en_area_time_cov_p_lc_percent,
            sc.score_en1_en_area_time_cov_p_lc_max,
            sc.score_en1_en_area_time_cov_p_tc,
            sc.score_en1_en_area_time_cov_p_tc_absolute,
            sc.score_en1_en_area_time_cov_p_tc_percent,
            sc.score_en1_en_area_time_cov_p_tc_max,
            sc.score_en1_en_area_time_cov_p,
            sc.score_en1_en_area_time_cov_p_absolute,
            sc.score_en1_en_area_time_cov_p_percent,
            sc.score_en1_en_area_time_cov_p_max,
            sc.score_en1_lfl_availability,
            sc.score_en1_lfl_availability_absolute,
            sc.score_en1_lfl_availability_percent,
            sc.score_en1_lfl_availability_max,
            sc.score_en1_en_lfl_percent_change_lc,
            sc.score_en1_en_lfl_percent_change_lc_absolute,
            sc.score_en1_en_lfl_percent_change_lc_percent,
            sc.score_en1_en_lfl_percent_change_lc_max,
            sc.score_en1_en_lfl_percent_change_tc,
            sc.score_en1_en_lfl_percent_change_tc_absolute,
            sc.score_en1_en_lfl_percent_change_tc_percent,
            sc.score_en1_en_lfl_percent_change_tc_max,
            sc.score_en1_en_lfl_percent_change,
            sc.score_en1_en_lfl_percent_change_absolute,
            sc.score_en1_en_lfl_percent_change_percent,
            sc.score_en1_en_lfl_percent_change_max,
            sc.score_en1_en_ren_ons,
            sc.score_en1_en_ren_ons_absolute,
            sc.score_en1_en_ren_ons_percent,
            sc.score_en1_en_ren_ons_max,
            sc.score_en1_en_ren_ofs,
            sc.score_en1_en_ren_ofs_absolute,
            sc.score_en1_en_ren_ofs_percent,
            sc.score_en1_en_ren_ofs_max,
            sc.score_en1_en_ren_availability,
            sc.score_en1_en_ren_percent_change,
            sc.score_en1_en_ren_percent_change_absolute,
            sc.score_en1_en_ren_percent_change_percent,
            sc.score_en1_en_ren_percent_change_max,
            sc.score_en1_en_ren_performance,
            sc.score_en1_en_ren_performance_absolute,
            sc.score_en1_en_ren_performance_percent,
            sc.score_en1_en_ren_performance_max,
            sc.score_en1_energy_efficiency,
            sc.score_en1_energy_efficiency_absolute,
            sc.score_en1_energy_efficiency_max,
            sc.score_en1_energy_efficiency_percent,
            sc.score_en1_energy_performance,
            sc.score_en1_energy_performance_absolute,
            sc.score_en1_energy_performance_max,
            sc.score_en1_energy_performance_percent,
            sc.score_en1_en_ren,
            sc.score_en1_en_ren_percent,
            sc.score_en1_en_ren_absolute,
            sc.score_en1_en_ren_max,
            sc.score_en1_en_lfl,
            sc.score_en1_en_lfl_percent,
            sc.score_en1_en_lfl_absolute,
            sc.score_en1_en_lfl_max,
        ],
        Indicator.GH_1: [
            sc.score_gh1,
            sc.score_gh1_fraction,
            sc.score_gh1_percent,
            sc.score_gh1_max,
            sc.score_gh1_ghg_area_time_cov_p_s12,
            sc.score_gh1_ghg_area_time_cov_p_s12_absolute,
            sc.score_gh1_ghg_area_time_cov_p_s12_percent,
            sc.score_gh1_ghg_area_time_cov_p_s12_max,
            sc.score_gh1_ghg_area_time_cov_p_s3,
            sc.score_gh1_ghg_area_time_cov_p_s3_absolute,
            sc.score_gh1_ghg_area_time_cov_p_s3_percent,
            sc.score_gh1_ghg_area_time_cov_p_s3_max,
            sc.score_gh1_ghg_area_time_cov_p,
            sc.score_gh1_ghg_area_time_cov_p_absolute,
            sc.score_gh1_ghg_area_time_cov_p_percent,
            sc.score_gh1_ghg_area_time_cov_p_max,
            sc.score_gh1_ghg_lfl_percent_change_s12,
            sc.score_gh1_ghg_lfl_percent_change_s12_absolute,
            sc.score_gh1_ghg_lfl_percent_change_s12_percent,
            sc.score_gh1_ghg_lfl_percent_change_s12_max,
            sc.score_gh1_ghg_lfl_percent_change_s3,
            sc.score_gh1_ghg_lfl_percent_change_s3_absolute,
            sc.score_gh1_ghg_lfl_percent_change_s3_percent,
            sc.score_gh1_ghg_lfl_percent_change_s3_max,
            sc.score_gh1_ghg_lfl_percent_change,
            sc.score_gh1_ghg_lfl_percent_change_absolute,
            sc.score_gh1_ghg_lfl_percent_change_percent,
            sc.score_gh1_ghg_lfl_percent_change_max,
        ],
        Indicator.WS_1: [
            sc.score_ws1,
            sc.score_ws1_fraction,
            sc.score_ws1_percent,
            sc.score_ws1_max,
            sc.score_ws1_was_area_cov_p,
            sc.score_ws1_was_area_cov_p_lc,
            sc.score_ws1_was_area_cov_p_tc,
            sc.score_ws1_was_area_cov_p_absolute,
            sc.score_ws1_was_area_cov_p_percent,
            sc.score_ws1_was_area_cov_p_max,
            sc.score_ws1_was_diverted_percent,
            sc.score_ws1_was_diverted_percent_absolute,
            sc.score_ws1_was_diverted_percent_percent,
            sc.score_ws1_was_diverted_percent_max,
        ],
        Indicator.WT_1: [
            sc.score_wt1,
            sc.score_wt1_fraction,
            sc.score_wt1_percent,
            sc.score_wt1_max,
            sc.score_wt1_wat_area_time_cov_p_lc,
            sc.score_wt1_wat_area_time_cov_p_lc_absolute,
            sc.score_wt1_wat_area_time_cov_p_lc_percent,
            sc.score_wt1_wat_area_time_cov_p_lc_max,
            sc.score_wt1_wat_area_time_cov_p_tc,
            sc.score_wt1_wat_area_time_cov_p_tc_absolute,
            sc.score_wt1_wat_area_time_cov_p_tc_percent,
            sc.score_wt1_wat_area_time_cov_p_tc_max,
            sc.score_wt1_wat_area_time_cov_p,
            sc.score_wt1_wat_area_time_cov_p_absolute,
            sc.score_wt1_wat_area_time_cov_p_percent,
            sc.score_wt1_wat_area_time_cov_p_max,
            sc.score_wt1_wat_lfl_percent_change_lc,
            sc.score_wt1_wat_lfl_percent_change_lc_absolute,
            sc.score_wt1_wat_lfl_percent_change_lc_percent,
            sc.score_wt1_wat_lfl_percent_change_lc_max,
            sc.score_wt1_wat_lfl_percent_change_tc,
            sc.score_wt1_wat_lfl_percent_change_tc_absolute,
            sc.score_wt1_wat_lfl_percent_change_tc_percent,
            sc.score_wt1_wat_lfl_percent_change_tc_max,
            sc.score_wt1_wat_lfl_percent_change,
            sc.score_wt1_wat_lfl_percent_change_absolute,
            sc.score_wt1_wat_lfl_percent_change_percent,
            sc.score_wt1_wat_lfl_percent_change_max,
            sc.score_wt1_wat_rec_ons,
            sc.score_wt1_wat_rec_ons_absolute,
            sc.score_wt1_wat_rec_ons_percent,
            sc.score_wt1_wat_rec_ons_max,
            sc.score_wt1_wat_rec_percent_change,
            sc.score_wt1_wat_rec_percent_change_absolute,
            sc.score_wt1_wat_rec_percent_change_percent,
            sc.score_wt1_wat_rec_percent_change_max,
            sc.score_wt1_wat_rec_performance,
            sc.score_wt1_wat_rec_performance_absolute,
            sc.score_wt1_wat_rec_performance_percent,
            sc.score_wt1_wat_rec_performance_max,
            sc.score_wt1_wat_rec,
            sc.score_wt1_wat_rec_absolute,
            sc.score_wt1_wat_rec_percent,
            sc.score_wt1_wat_rec_max,
        ],
        Indicator.BC_1_1: [
            sc.score_bc1_1,
            sc.score_bc1_1_fraction,
            sc.score_bc1_1_percent,
            sc.score_bc1_1_max,
            sc.score_bc1_1_coverage,
        ],
        Indicator.BC_1_2: [
            sc.score_bc1_2,
            sc.score_bc1_2_fraction,
            sc.score_bc1_2_percent,
            sc.score_bc1_2_max,
            sc.score_bc1_2_coverage,
        ],
        Indicator.BC_1: [
            bc.bc1_1_coverage_scoring,
            bc.bc1_1_coverage,
            sc.score_bc1,
            sc.score_bc1_fraction,
            sc.score_bc1_percent,
            sc.score_bc1_max,
        ],
        Indicator.BC_2: [
            bc.bc1_2_coverage_scoring,
            bc.bc1_2_coverage,
            sc.score_bc2,
            sc.score_bc2_fraction,
            sc.score_bc2_percent,
            sc.score_bc2_max,
            sc.score_bc2_coverage,
        ],
    }

    def __init__(
        self,
        indicator_data_dict: dict[Indicator, pd.DataFrame],
        merge_by_columns: list[str],
        rename: bool,
        asset_data: pd.DataFrame = None,
    ) -> None:
        super().__init__()
        self.indicator_data_dict = indicator_data_dict
        self.merge_by_columns = merge_by_columns + [ac.data_year]
        self.rename = rename
        self.asset_data = asset_data

    def get_added_columns(self):
        return None

    def validate(self) -> None:
        """
        Check the homogeneity of the number of rows and unique response IDs in the
        scored dataframes.

        Parameters:
        indicator_data_dict (dict[Indicator, pd.DataFrame]): dictionary containing
            the scored data for each indicator.

        Raises:
        ValueError if not all input DataFrames have the same number of rows or
            unique response IDs.
        """
        # Check that all DataFrames have all the assets
        current_year: int = list(self.indicator_data_dict.values())[0].data_year.max()
        row_number = [
            sum(data.reset_index().data_year == current_year)
            for data in list(self.indicator_data_dict.values())
        ]
        response_ids_check_cols = self.merge_by_columns.copy()
        response_ids_check_cols.remove(ac.data_year)
        unique_response_ids_number = [
            data.groupby(response_ids_check_cols).ngroups
            for data in list(self.indicator_data_dict.values())
        ]
        if len(np.unique(row_number)) > 1:
            raise ValueError("Scored dataframes have different number of rows.")

        if len(np.unique(unique_response_ids_number)) > 1:
            raise ValueError(
                "Scored dataframes have different number of unique ID columns."
            )

    def process(self) -> pd.DataFrame:
        """
        Validate and merge the scored dataframes for each
        indicator, then rename the columns to fit the R model response data.

        Parameters:
        indicator_data_dict (dict[Indicator, pd.DataFrame]): dictionary containing
            the scored data for each indicator.

        Returns:
        pd.DataFrame: the merged scored dataframes.
        """
        self.validate()

        # Initialise the output dataframe
        data = self.asset_data
        if data is None:
            data = list(self.indicator_data_dict.values())[0][
                self.merge_by_columns + [sc.pgav]
            ]

        # Merge the scored dataframes only keeping relevant columns
        for indicator in list(self.indicator_data_dict.keys()):
            if indicator not in list(self.indicator_data_dict.keys()):
                continue

            # If we are merging asset data we want to keep all previous columns
            # and add the new ones for each indicator. If not, we take the columns
            # from the `indicator_columns` attribute.
            if self.asset_data is None:
                columns_to_keep = (
                    self.merge_by_columns + self.indicator_columns[indicator]
                )
                # TODO: Ultimately we want to validate that all the columns in the
                # dict are in the frames so we'll have to remove the next statement.
                columns_to_keep = [
                    col
                    for col in columns_to_keep
                    if col in self.indicator_data_dict[indicator].columns
                ]
            else:
                new_columns = self.indicator_data_dict[indicator].columns
                new_columns = [col for col in new_columns if col not in data.columns]
                columns_to_keep = self.merge_by_columns + new_columns

            data = data.merge(
                self.indicator_data_dict[indicator][columns_to_keep],
                on=self.merge_by_columns,
                how="left",
            )

        # Rename the scores to fit the R model
        if self.rename:
            data.rename(columns=export_columns.score_columns_rename_dict, inplace=True)

        return data
