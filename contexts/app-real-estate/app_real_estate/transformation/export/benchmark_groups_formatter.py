import pandas as pd
import app_real_estate.constants.column_names.benchmark_group_memberships_columns as mc
import app_real_estate.constants.column_names.benchmark_groups_columns as gc
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.building_certification_columns as bc


class BenchmarkGroupsFormatter:
    """
    The class `BenchmarkGroupsFormatter` provides functions to merge and format
    the data of a given set of groups and memberships.
    """

    def __init__(
        self,
        groups_dict: dict[str, pd.DataFrame],
        memberships_dict: dict[str, pd.DataFrame],
        by_metric: bool = True,
        is_asset: bool = True,
    ) -> None:
        super().__init__()
        self.groups_dict = groups_dict
        self.memberships_dict = memberships_dict
        self._new_column = "metric" if by_metric else "indicator"
        self._id_columns = ["metric"] if by_metric else ["metric", "indicator"]
        self._row_id_col = (
            ac.portfolio_asset_id
            if is_asset
            else bc.building_data_certifications_scoring_id
        )

    def process(self):
        # `separator` can be either a metric or an indicator
        for separator in list(self.groups_dict.keys()):
            self.groups_dict[separator] = self.groups_dict[separator].assign(
                **{self._new_column: separator}
            )
            self.memberships_dict[separator] = self.memberships_dict[separator].assign(
                **{self._new_column: separator}
            )

        indicator_groups = pd.concat(list(self.groups_dict.values()))
        indicator_memberships = pd.concat(list(self.memberships_dict.values()))

        # Now that all groups are together some group ids might be the same, let's replace them.
        # First generate the new group IDs
        indicator_groups["new_group_id"] = indicator_groups.groupby(
            [gc.id] + self._id_columns
        ).ngroup()
        indicator_memberships = indicator_memberships.merge(
            indicator_groups[["new_group_id", "id"] + self._id_columns],
            left_on=[mc.group_id] + self._id_columns,
            right_on=[gc.id] + self._id_columns,
            how="left",
            suffixes=(None, "_old"),
        )

        # Replace the group IDs with the new ones
        indicator_memberships[mc.group_id] = indicator_memberships["new_group_id"]
        indicator_groups[gc.id] = indicator_groups["new_group_id"]

        # Drop the extra column
        indicator_groups.drop("new_group_id", inplace=True, axis=1)
        indicator_memberships.drop(["new_group_id", "id_old"], inplace=True, axis=1)

        # Reset memberships ID
        indicator_memberships[mc.id] = indicator_memberships.groupby(
            [self._row_id_col, mc.group_id] + self._id_columns
        ).ngroup()

        # Reset groups index
        indicator_groups.reset_index(drop=True, inplace=True)

        return indicator_groups, indicator_memberships
