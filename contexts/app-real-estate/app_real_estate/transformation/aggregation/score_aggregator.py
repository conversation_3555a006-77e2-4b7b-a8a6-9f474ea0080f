"""
Asset Level Score Aggregation for Real Estate Analytics

This module defines the ScoreAggregatorAssetLevel class, which is responsible for
aggregating asset-level data in real estate analytics. It performs weighted data
aggregation, calculates indicator scores, and optionally merges PGAV (Potential
Gross Asset Value) data.

The class is designed to be flexible and can be customized through various
parameters passed during instantiation.

Classes:
    ScoreAggregatorAssetLevel: Manages the aggregation of asset-level scored data.

Dependencies:
    - pandas: For data manipulation and analysis.
    - pandantic: For data model validation.
    - Various custom modules for specific processing steps.

Note:
    This class is intended to be used as a base class for more specific score
    aggregators, although it can be instantiated directly if needed.
"""

import pandas as pd
from pandantic import BaseModel
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
from app_real_estate.models.input_models.r1_table_model import R1TableModel
from app_real_estate.transformation.scoring.common_indicator_score_utils import (
    CommonIndicatorScoreUtils,
)
from app_real_estate.transformation.aggregation.processor.add_area_ownership_weight_factor import (
    AddAreaOwnershipWeightFactor,
)
from app_real_estate.transformation.aggregation.processors.weighted_mean import (
    WeightedMean,
)
from app_real_estate.transformation.aggregation.processor.add_fractional_max_percent_score import (
    Add_Fractional_Max_Percent_Score,
)
from app_real_estate.transformation.aggregation.merger.r1_table_merger import (
    R1TableMerger,
)


class ScoreAggregator:
    """
    Aggregates data for scoring in real estate analytics.

    This class is responsible for aggregating data, calculating
    weighted means, merging PGAV data, and producing final scores. It's designed
    to be flexible and customizable through its initialization parameters.

    Attributes:
        _data_model (BaseModel): The data model used for parsing the input data.
        _scale_list (list[str]): Columns to scale during aggregation.
        _agg_dict (dict): Aggregation recipe.
        _subscore_weights (dict): Weights for subscores in final score calculation.
        _agg_keys (list[str]): Keys to group by during aggregation.
        _score_columns (str): Column name for the final score.
        _data (pd.DataFrame): The input data parsed into the data model.
        _max_score (float): Maximum possible score.
        _r1_table (pd.DataFrame): Optional R1 table for PGAV merging.
        _is_input_aggregated (bool): Whether the input data is aggregated.

    Methods:
        aggregate_weighted_data: Performs weighted aggregation of data.
        merge_pgav: Merges PGAV data from R1 table.
        process: Executes the full aggregation and scoring process.
    """

    def __init__(
        self,
        data_model: BaseModel,
        scale_list: list[str],
        agg_dict: dict,
        subscore_weights: dict,
        agg_keys: list[str],
        score_column: str,
        data: pd.DataFrame,
        max_score: float,
        r1_table: pd.DataFrame,
        is_input_aggregated: bool,
    ):
        self._data_model = data_model
        self._scale_list = scale_list
        self._agg_dict = agg_dict
        self._subscore_weights = subscore_weights
        self._agg_keys = agg_keys
        self._score_column = score_column
        self._data = data
        if len(self._agg_keys) > 1:
            self._data = self._data_model.parse_df(data.reset_index())
        self._max_score = max_score
        self._r1_table = r1_table
        self._is_input_aggregated = is_input_aggregated

    @property
    def data_model(self):
        """Get the data model."""
        return self._data_model

    @data_model.setter
    def data_model(self, data_model: BaseModel):
        """Set the data model."""
        self._data_model = data_model

    @property
    def scale_list(self):
        return self._scale_list

    @scale_list.setter
    def scale_list(self, scale_list: list[str]):
        self._scale_list = scale_list

    @property
    def agg_dict(self):
        return self._agg_dict

    @agg_dict.setter
    def agg_dict(self, agg_dict: dict):
        self._agg_dict = agg_dict

    @property
    def subscore_weights(self):
        return self._subscore_weights

    @subscore_weights.setter
    def subscore_weights(self, subscore_weights: dict):
        self._subscore_weights = subscore_weights

    @property
    def agg_keys(self):
        return self._agg_keys

    @agg_keys.setter
    def agg_keys(self, agg_keys: list):
        self._agg_keys = agg_keys

    def aggregate_weighted_data(self) -> pd.DataFrame:
        """
        Perform weighted aggregation of the data.

        Returns:
            pd.DataFrame: Aggregated data with weighted means.
        """
        if not self._is_input_aggregated:
            self._data = AddAreaOwnershipWeightFactor(self._data).process()

        weight_column = sc.pgav if self._is_input_aggregated else sc.asset_size_owned_m2
        self._data = WeightedMean(
            data=self._data,
            weight_column=weight_column,
            cols_to_scale=self._scale_list,
            group_by=self._agg_keys + [ac.data_year],
            agg_dict=self._agg_dict,
            ignore_na=True,
        ).process()
        return self._data

    def add_pgav(self) -> pd.DataFrame:
        """
        Use the R1 table to add the PGAV at the corresponding level.
        """
        if len(self._agg_keys) == 2:
            self._r1_table = (
                self._r1_table.groupby(["RESPONSE_ID", "PRT_TYPE"])[
                    R1TableModel.R_1_TBL_PGAV
                ]
                .agg("sum")
                .reset_index()
            )

        self._data = R1TableMerger(
            r1_data=self._r1_table,
            data=self._data,
            merge_on_keys=self._agg_keys,
        ).merge()
        self._data = self._data.reset_index()
        self._data = self._data.set_index(self._agg_keys + [ac.data_year])

        return self._data

    def set_missing_scores_to_zero(self) -> pd.DataFrame:
        self._data.fillna(value=0, inplace=True)
        return self._data

    def process(self) -> pd.DataFrame:
        """
        Execute the full aggregation and scoring process.

        This method performs weighted data aggregation, calculates the indicator
        score, adds fractional scores, and optionally merges PGAV data.

        Returns:
            pd.DataFrame: Processed and scored data.
        """
        self._data = self.aggregate_weighted_data()
        self._data = CommonIndicatorScoreUtils.calculate_indicator_score(
            data=self._data,
            subscore_weights=self._subscore_weights,
            score_column=self._score_column,
        )
        self._data = Add_Fractional_Max_Percent_Score(
            data=self._data,
            score_column=self._score_column,
            max_score=self._max_score,
        ).process()

        if not self._is_input_aggregated and len(self._agg_keys) > 1:
            self._data = self.add_pgav()
        self._data = self.set_missing_scores_to_zero()

        if self._data.empty:
            ValueError("Error: The dataframe is empty.")

        return self._data
