"""
Metric Aggregation Pipeline for Real Estate Analytics

This module defines the MetricAggregationPipeline class, which orchestrates
the process of calculating and aggregating various metrics for real estate
assets across different levels (asset, property-country, property, and portfolio).

The pipeline performs a series of calculations on asset data, including
vacancy rates, asset sizes, recycled water percentages, renewable energy
percentages, and intensity metrics for various utilities. It then aggregates
these metrics to higher levels and merges the results with original scores.

Classes:
    MetricAggregationPipeline: Manages the metric calculation and aggregation process.

Dependencies:
    - AssetMetricAggregator: For aggregating metrics across different levels.
    - AssetCalculations: For performing various calculations on asset data.
    - AssetFilters: For filtering asset data.
    - ScoringModel: For merging filtered scores with data.

Note:
    This pipeline is a crucial component in processing and analyzing real estate
    asset data, providing a comprehensive view of various performance metrics
    across different organizational levels.
"""

import pandas as pd
from app_real_estate.transformation.aggregation.asset_metric_aggregator import (
    AssetMetricAggregator,
)
from app_real_estate.transformation.aggregation.certification_metric_aggregator import (
    CertificationMetricAggregator,
)
from app_real_estate.transformation.aggregation.energy_rating_metric_aggregator import (
    EnergyRatingMetricAggregator,
)
from app_real_estate.transformation.metric_calculation.asset_characteristics_calculation import (
    AssetCharacteristicsCalculations,
)
from app_real_estate.transformation.metric_calculation.consumption_calculations import (
    ConsumptionCalculations,
)
from app_real_estate.transformation.metric_calculation.coverage_calculations import (
    CoverageCalculations,
)
from app_real_estate.transformation.metric_calculation.lfl_calculations import (
    LFLCalculations,
)
from app_real_estate.transformation.metric_calculation.intensity_calculations import (
    IntensityCalculations,
)
from app_real_estate.transformation.metric_calculation.sustainable_utility_calculations import (
    SustainableUtilityCalculations,
)
from app_real_estate.transformation.metric_calculation.asset_filters import (
    AssetFilters,
)
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
from app_real_estate.transformation.scoring_model import ScoringModel
import app_real_estate.constants.common_aggregation_keys as agg_keys


class MetricAggregationPipeline:
    """
    Manages the process of calculating and aggregating metrics for real estate assets.

    This class provides a static method to execute the entire metric aggregation
    pipeline, including various calculations on asset data and aggregations to
    different organizational levels.

    Methods:
        process: Execute the complete metric aggregation pipeline.
    """

    @staticmethod
    def process_asset_data(
        scored_asset_data: pd.DataFrame,
        scored_portfolio: pd.DataFrame,
        r1_table: pd.DataFrame,
    ):
        """
        Execute the asset metric aggregation pipeline.

        This method calculates missing asset data metrics, aggregates them to various
        levels, and then merges these results back with the original scores.

        Args:
            scored_asset_data (pd.DataFrame): Asset-level scored data.
            scored_portfolio (pd.DataFrame): Portfolio level scored data.
            r1_table (pd.DataFrame): the R1 table from the survey.

        Returns:
            tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame, pd.DataFrame]:
                Updated DataFrames for asset, property-country, property, and portfolio levels.
        """
        scored_asset_data = (
            ConsumptionCalculations().add_consumed_renewable_energy_consumption(
                scored_asset_data
            )
        )
        scored_asset_data = SustainableUtilityCalculations().add_previous_year_sustainable_utility_rates(
            scored_asset_data
        )
        scored_asset_data = ConsumptionCalculations().add_previous_year_consumption(
            scored_asset_data
        )
        scored_asset_data = (
            SustainableUtilityCalculations().add_previous_year_sustainable_consumption(
                scored_asset_data
            )
        )
        scored_asset_data = (
            ConsumptionCalculations.add_energy_consumption_per_energy_type(
                scored_asset_data
            )
        )
        scored_asset_data = ConsumptionCalculations.add_net_ghg_emissions(
            scored_asset_data
        )

        iscyop = AssetFilters.filter_current_year_operational_assets(scored_asset_data)
        scored_asset_data_cy = scored_asset_data[iscyop]

        scored_asset_data = (
            IntensityCalculations().add_vacancy_rate_for_intensity_assets(
                scored_asset_data
            )
        )
        scored_asset_data = IntensityCalculations().add_asset_size_for_intensity_assets(
            scored_asset_data
        )
        scored_asset_data_cy = (
            IntensityCalculations().add_intensity_columns_without_outliers(
                scored_asset_data_cy
            )
        )
        scored_asset_data_cy = LFLCalculations().add_lfl_aggregation_abs_change(
            scored_asset_data_cy
        )
        scored_asset_data_cy = (
            CoverageCalculations().add_owned_area_time_weight_per_control(
                scored_asset_data_cy
            )
        )
        scored_asset_data_cy = (
            CoverageCalculations().add_cross_control_area_time_weights(
                scored_asset_data_cy
            )
        )
        scored_asset_data_cy = CoverageCalculations().add_coverage_weighted_per_control(
            scored_asset_data_cy
        )
        scored_asset_data_cy = (
            LFLCalculations().add_lfl_aggregation_abs_change_per_control(
                scored_asset_data_cy
            )
        )
        scored_asset_data_cy = IntensityCalculations().add_int_eligibility(
            scored_asset_data_cy
        )
        scored_asset_data = LFLCalculations().add_lfl_eligibility(scored_asset_data)
        scored_asset_data = LFLCalculations().add_lfl_area(scored_asset_data)
        scored_asset_data = AssetCharacteristicsCalculations.add_asset_size_owned(
            scored_asset_data
        )
        scored_asset_data = ConsumptionCalculations.add_mwh_energy_consumption(
            scored_asset_data
        )
        scored_asset_data = ConsumptionCalculations.add_ghg_consumption_per_scope(
            scored_asset_data
        )

        scored_asset_data = (
            AssetCharacteristicsCalculations.add_asset_ownership_fraction(
                scored_asset_data
            )
        )
        scored_asset_data = LFLCalculations().add_lfl_aggregation_abs_cons(
            scored_asset_data
        )
        scored_asset_data = AssetCharacteristicsCalculations().add_owned_area_weight(
            scored_asset_data
        )

        scored_asset_data = ScoringModel._merge_filtered_scores_with_data(
            scored_asset_data_cy, scored_asset_data, ac.portfolio_asset_id
        )

        scored_metrics_sector_ctr = AssetMetricAggregator(
            asset_metric_data=scored_asset_data,
            agg_keys=agg_keys.propertysector_country_responseid,
            r1_table=r1_table,
            add_benchmarks=True,
        ).aggregate()

        scored_metrics_portfolio = (
            AssetMetricAggregator(
                asset_metric_data=scored_asset_data,
                agg_keys=agg_keys.portfolio_level,
            )
            .aggregate()
            .reset_index()
        )

        # TODO: Create common model to validate the column before renaming it

        scored_portfolio = scored_portfolio.rename(
            columns={"RESPONSE_ID": "response_id", "COUNTRY": "country"}
        )
        scored_portfolio = ScoringModel._merge_filtered_scores_with_data(
            scored_metrics_portfolio,
            scored_portfolio,
            [ac.response_id, ac.data_year],
        )

        return scored_asset_data, scored_metrics_sector_ctr, scored_portfolio

    @staticmethod
    def aggregate_certifications_data(
        r1_table: pd.DataFrame,
        certification_data: pd.DataFrame,
        property_type_sector_mapping: pd.DataFrame,
    ) -> tuple[
        pd.DataFrame,
        pd.DataFrame,
        pd.DataFrame,
        pd.DataFrame,
        pd.DataFrame,
        pd.DataFrame,
    ]:
        """
        Execute the certification metric aggregation pipeline.

        This method aggregates the metrics to various levels, and then merges these results back with the original scores.

        Args:
        certification_data (pd.DataFrame): the assets' building certifications.
        r1_table (pd.DataFrame): the R1 table containing asset counts.

        Returns:
        tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame, pd.DataFrame, pd.DataFrame, pd.DataFrame]:
            Certification metrics aggregated at various levels for reporting purposes.
        """
        cert_metrics_brand_scheme, cert_metrics_brand, cert_metrics_total = (
            CertificationMetricAggregator(
                r1_table=r1_table,
                certification_data=certification_data,
                property_type_sector_mapping=property_type_sector_mapping,
            ).aggregate()
        )
        (
            cert_metrics_brand_scheme_portfolio,
            cert_metrics_brand_portfolio,
            cert_metrics_total_portfolio,
        ) = CertificationMetricAggregator(
            r1_table=r1_table,
            certification_data=certification_data,
            portfolio_level=True,
        ).aggregate()

        return (
            cert_metrics_brand_scheme,
            cert_metrics_brand,
            cert_metrics_total,
            cert_metrics_brand_scheme_portfolio,
            cert_metrics_brand_portfolio,
            cert_metrics_total_portfolio,
        )

    @staticmethod
    def aggregate_ratings_data(
        energy_ratings_data: pd.DataFrame,
        r1_table: pd.DataFrame,
        property_type_sector_mapping: pd.DataFrame,
    ) -> tuple[
        pd.DataFrame,
        pd.DataFrame,
        pd.DataFrame,
        pd.DataFrame,
    ]:
        """
        Execute the certification metric aggregation pipeline.

        This method aggregates the metrics to various levels, and then merges these results back with the original scores.

        Args:
        energy_ratings_data (pd.DataFrame): the assets' energy ratings.
        r1_table (pd.DataFrame): the R1 table containing asset counts.

        Returns:
        tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame, pd.DataFrame]:
            Certification metrics aggregated at various levels for reporting purposes.
        """
        er_metrics_brand, er_metrics_total = EnergyRatingMetricAggregator(
            energy_ratings_data=energy_ratings_data,
            r1_table=r1_table,
            property_type_sector_mapping=property_type_sector_mapping,
        ).aggregate()
        (
            er_metrics_brand_portfolio,
            er_metrics_total_portfolio,
        ) = EnergyRatingMetricAggregator(
            energy_ratings_data=energy_ratings_data,
            r1_table=r1_table,
            portfolio_level=True,
        ).aggregate()

        return (
            er_metrics_brand,
            er_metrics_total,
            er_metrics_brand_portfolio,
            er_metrics_total_portfolio,
        )
