import numpy as np

from app_real_estate.models.input_models.r1_table_model import R1TableModel
import pandas as pd
import logging

logger = logging.getLogger(__name__)


class CertificationAggregationUtils:
    def __init__(
        self,
        merge_columns: list[str],
        r1_table: pd.DataFrame,
        property_type_sector_mapping: pd.DataFrame | None = None,
    ):
        if "PRT_SECTOR" in merge_columns and property_type_sector_mapping is None:
            raise ValueError(
                "property_type_sector_mapping is required if not used on portfolio_level"
            )
        self._merge_columns = merge_columns
        self._r1_table = r1_table
        self._id_col = ["RESPONSE_ID"]
        self.property_type_sector_mapping = property_type_sector_mapping

    def add_certified_area_percent(
        self, certifications: pd.DataFrame, level: list[str]
    ) -> pd.DataFrame:
        """
        Calculate the percentage of total floor area in the bucket that
        is covered by a building certification.
        This is done by summing the `PCOV` values in the BC1.1/BC1.2 or BC2 table
        and capping the result at 100%.
        See asset-aggregation for more info on these values.

        Parameters:
            certifications (pd.DataFrame): dataframe containing the certifications or energy ratings per asset.
            level (list[str]): list of columns to group by.

        Returns:
            pd.DataFrame: dataframe containing the aggregated PCOV from the BC1.1/BC1.2 or BC2 table.
        """
        # Aggregate the owned covered floor area
        agg_certs = (
            certifications.groupby(self._id_col + level)
            .agg({"COV": lambda x: x.sum(min_count=1)})
            .reset_index()
        )

        # If the aggregation is at the sector/country level we need to aggregate
        # the R1 table to have the total floor area per sector/country.
        if self._merge_columns and "PRT_SECTOR" not in self._r1_table.columns:
            self._r1_table = self._r1_table.merge(
                self.property_type_sector_mapping,
                on=["PRT_TYPE"],
                how="left",
            )
            if self._r1_table["PRT_SECTOR"].isna().any():
                unknown_types = self._r1_table.loc[
                    self._r1_table["PRT_SECTOR"].isna(), "PRT_TYPE"
                ].drop_duplicates()
                logger.warning(
                    f"Property sector not found for some property types: {unknown_types}"
                )
            self._r1_table = (
                self._r1_table.groupby(self._id_col + self._merge_columns)
                .agg({R1TableModel.R_1_TBL_AREA: "sum"})
                .reset_index()
            )

        # Get the total owned area per bucket/portfolio
        bucket_total_area = (
            self._r1_table.groupby(self._id_col + self._merge_columns)[
                [R1TableModel.R_1_TBL_AREA]
            ]
            .sum()
            .reset_index()
        )

        if len(level) == len(["TYPE"] + self._merge_columns):
            # When aggregating to total level, ensure all options are included
            # Cross join with types to ensure we get all combinations
            bucket_total_area = bucket_total_area.merge(
                agg_certs["TYPE"].drop_duplicates(), how="cross"
            )
            agg_certs = agg_certs.merge(
                bucket_total_area, on=self._id_col + level, how="outer"
            )
            agg_certs["COV"] = agg_certs["COV"].fillna(0)
        else:
            agg_certs = agg_certs.merge(
                bucket_total_area, on=self._id_col + self._merge_columns, how="left"
            )

        # Add rounding to prevent the value to be slightly higher than 100.
        agg_certs["PCOV"] = np.minimum(
            agg_certs["COV"] / agg_certs[R1TableModel.R_1_TBL_AREA] * 100, 100
        )
        agg_certs.drop(["COV", R1TableModel.R_1_TBL_AREA], axis=1, inplace=True)

        return agg_certs

    @staticmethod
    def aggregate_certification_or_rating_data_metric(
        certification_or_rating_data: pd.DataFrame,
        group_by: list[str],
        agg_dict: dict,
        output_metric_names: list[str],
    ) -> pd.DataFrame:
        """
        Aggregate the metrics from the assets' certification data into a dataframe based
        on the aggregation recipe given with `agg_dict`.

        Parameters:
            certification_or_rating_data (pd.DataFrame): the assets' certification or energy ratings data.
            group_by (list[str]): list of columns to aggregate on.
            agg_dict (dict): the aggregation recipe to use.
            output_metric_names (list[str]): list of columns to rename the metrics as.

        Returns:
            pd.DataFrame: dataframe containing the aggregated metrics.
        """
        aggregated_certs = (
            certification_or_rating_data.groupby(group_by).agg(agg_dict).reset_index()
        )
        aggregated_certs = aggregated_certs.rename(
            columns=dict(zip(list(agg_dict.keys()), output_metric_names))
        )

        return aggregated_certs[group_by + output_metric_names]

    def add_benchmark_certified_area_percentage(
        self, aggregated_certs: pd.DataFrame, level: list[str]
    ) -> pd.DataFrame:
        """
        Calculate the benchmark average of the certified area percentage.

        Parameters:
            aggregated_certs (pd.DataFrame): the aggregated certification/ratings metrics.
            level (list[str]): list of columns to group by.

        Returns:
            pd.DataFrame: dataframe containing the benchmark average.
        """
        aggregated_certs["BENCH_PCOV"] = aggregated_certs.groupby(level)[
            ["PCOV"]
        ].transform("mean")
        return aggregated_certs
