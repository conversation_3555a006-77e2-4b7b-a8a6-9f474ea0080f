from pandantic.basemodel import PandanticBaseModel

from app_real_estate.constants.aggregation_recipes.en1_score_aggregation_recipe import (
    EN1_SCORE_AGGREGATION_RECIPE_PORTFOLIO_LEVEL,
    EN1_SCORE_AGGREGATION_SCALE_ASSET_LEVEL,
)
from app_real_estate.constants.indicator_subscore_weights import (
    indicator_weights,
)
from app_real_estate.transformation.aggregation.score_aggregator import (
    ScoreAggregator,
)
import app_real_estate.constants.column_names.score_columns as sc
from app_real_estate.constants.common_aggregation_keys import (
    propertysubtype_responseid,
)
import pandas as pd

from app_real_estate.transformation.aggregation.indicator.en1.en1_score_aggregator_property_subtype_country_response_id import (
    EN1ScoreAggregator_PropertySubtype_Country_ResponseId,
)
from app_real_estate.transformation.aggregation.processor.add_fractional_max_percent_score import (
    Add_Fractional_Max_Percent_Score,
)


class EN1ScoreAggregator_PropertySubtype_ResponseId(ScoreAggregator):
    """Class For Asset Level Aggregation on property subtype and response id"""

    _agg_keys = propertysubtype_responseid
    _asset_level_aggregator = EN1ScoreAggregator_PropertySubtype_Country_ResponseId
    _agg_dict = EN1_SCORE_AGGREGATION_RECIPE_PORTFOLIO_LEVEL
    _scale_list = EN1_SCORE_AGGREGATION_SCALE_ASSET_LEVEL
    _score_column = sc.score_en1

    def __init__(
        self, data: pd.DataFrame, r1_table: pd.DataFrame, report_type: str = "re"
    ):
        self.report_type = report_type
        self.report_type_score_weights = indicator_weights(report_type)
        _subscore_weights = self.report_type_score_weights.EN1_SUBSCORE_WEIGHTS
        _max_score = self.report_type_score_weights.EN1_MAX_SCORE

        asset_level_aggregated_data = self._asset_level_aggregator(
            data.copy(), r1_table=r1_table, report_type=report_type
        ).process()
        super().__init__(
            data=asset_level_aggregated_data,
            data_model=PandanticBaseModel(),
            scale_list=self._scale_list,
            agg_dict=self._agg_dict,
            subscore_weights=_subscore_weights,
            agg_keys=self._agg_keys,
            score_column=self._score_column,
            max_score=_max_score,
            r1_table=r1_table,
            is_input_aggregated=True,
        )

    def process(self):
        self._data = super().process()

        self._data = Add_Fractional_Max_Percent_Score(
            data=self._data,
            score_column=sc.score_en1_energy_efficiency,
            max_score=self.report_type_score_weights.EN1_EFFICIENCY_MAX_SCORE,
            is_fractional=True,
        ).process()

        self._data = Add_Fractional_Max_Percent_Score(
            data=self._data,
            score_column=sc.score_en1_en_area_time_cov_p,
            max_score=self.report_type_score_weights.EN1_COV_MAX_SCORE,
            is_fractional=True,
        ).process()

        self._data = Add_Fractional_Max_Percent_Score(
            data=self._data,
            score_column=sc.score_en1_en_lfl,
            max_score=self.report_type_score_weights.EN1_LFL_MAX_SCORE,
            is_fractional=True,
        ).process()

        self._data = Add_Fractional_Max_Percent_Score(
            data=self._data,
            score_column=sc.score_en1_en_ren,
            max_score=self.report_type_score_weights.EN1_REN_MAX_SCORE,
            is_fractional=True,
        ).process()

        self._data = Add_Fractional_Max_Percent_Score(
            data=self._data,
            score_column=sc.score_en1_energy_performance,
            max_score=self.report_type_score_weights.EN1_PERF_MAX_SCORE,
            is_fractional=True,
        ).process()

        return self._data
