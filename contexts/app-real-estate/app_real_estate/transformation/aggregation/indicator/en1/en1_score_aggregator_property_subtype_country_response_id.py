import pandas as pd
from app_real_estate.constants.common_aggregation_keys import (
    propertysubtype_country_responseid,
)
from app_real_estate.constants.aggregation_recipes.en1_score_aggregation_recipe import (
    EN1_SCORE_AGGREGATION_SCALE_ASSET_LEVEL,
    EN1_SCORE_AGGREGATION_RECIPE_ASSET_LEVEL,
)
from app_real_estate.constants.indicator_subscore_weights import (
    indicator_weights,
)
from app_real_estate.models.score_aggregation_util_models.en1_scored_data import (
    EN1ScoredData,
)
from app_real_estate.transformation.aggregation.score_aggregator import (
    ScoreAggregator,
)
import app_real_estate.constants.column_names.score_columns as sc
from app_real_estate.transformation.aggregation.processor.add_fractional_max_percent_score import (
    Add_Fractional_Max_Percent_Score,
)


class EN1ScoreAggregator_PropertySubtype_Country_ResponseId(ScoreAggregator):
    """
    Class For Asset Level Aggregation on property subtype, country and response id
    """

    _data_model = EN1ScoredData
    _agg_keys = propertysubtype_country_responseid
    _scale_list = EN1_SCORE_AGGREGATION_SCALE_ASSET_LEVEL
    _agg_dict = EN1_SCORE_AGGREGATION_RECIPE_ASSET_LEVEL
    _score_column = sc.score_en1

    def __init__(
        self,
        data: pd.DataFrame,
        r1_table: pd.DataFrame,
        report_type: str = "re",
    ):
        self.report_type = report_type
        self.report_type_score_weights = indicator_weights(report_type)
        _subscore_weights = self.report_type_score_weights.EN1_SUBSCORE_WEIGHTS
        _max_score = self.report_type_score_weights.EN1_MAX_SCORE

        super().__init__(
            data_model=self._data_model,
            scale_list=self._scale_list,
            agg_dict=self._agg_dict,
            subscore_weights=_subscore_weights,
            score_column=self._score_column,
            agg_keys=self._agg_keys,
            data=data,
            max_score=_max_score,
            r1_table=r1_table,
            is_input_aggregated=False,
        )

    def add_ren_score(self) -> pd.DataFrame:
        absolute_availability_scores = (
            self._data[sc.score_en1_en_ren_availability]
            * self.report_type_score_weights.EN1_REN_AVAILABILITY_MAX_SCORE
        )
        absolute_perf_scores = (
            self._data[sc.score_en1_en_ren_performance]
            * self.report_type_score_weights.EN1_REN_PERF_MAX_SCORE
        )

        self._data[sc.score_en1_en_ren] = absolute_perf_scores.add(
            absolute_availability_scores, fill_value=0
        )
        self._data[sc.score_en1_en_ren] = self._data[sc.score_en1_en_ren].div(
            self.report_type_score_weights.EN1_REN_MAX_SCORE
        )

        return self._data

    def process(self) -> pd.DataFrame:
        self._data = self.add_ren_score()
        self._data = super().process()

        self._data = Add_Fractional_Max_Percent_Score(
            data=self._data,
            score_column=sc.score_en1_energy_efficiency,
            max_score=self.report_type_score_weights.EN1_EFFICIENCY_MAX_SCORE,
            is_fractional=True,
        ).process()

        self._data = Add_Fractional_Max_Percent_Score(
            data=self._data,
            score_column=sc.score_en1_en_area_time_cov_p,
            max_score=self.report_type_score_weights.EN1_COV_MAX_SCORE,
            is_fractional=True,
        ).process()

        self._data = Add_Fractional_Max_Percent_Score(
            data=self._data,
            score_column=sc.score_en1_en_lfl,
            max_score=self.report_type_score_weights.EN1_LFL_MAX_SCORE,
            is_fractional=True,
        ).process()

        self._data = Add_Fractional_Max_Percent_Score(
            data=self._data,
            score_column=sc.score_en1_en_ren,
            max_score=self.report_type_score_weights.EN1_REN_MAX_SCORE,
            is_fractional=True,
        ).process()

        self._data = Add_Fractional_Max_Percent_Score(
            data=self._data,
            score_column=sc.score_en1_energy_performance,
            max_score=self.report_type_score_weights.EN1_PERF_MAX_SCORE,
            is_fractional=True,
        ).process()

        return self._data
