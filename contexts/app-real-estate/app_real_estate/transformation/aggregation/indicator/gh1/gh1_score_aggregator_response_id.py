import pandas as pd
from pandantic.basemodel import PandanticBaseModel
import app_real_estate.constants.column_names.score_columns as sc
from app_real_estate.transformation.aggregation.score_aggregator import (
    ScoreAggregator,
)
from app_real_estate.transformation.aggregation.indicator.gh1.gh1_score_aggregator_property_subtype_country_response_id import (
    GH1ScoreAggregator_PropertySubtype_Country_ResponseId,
)
from app_real_estate.constants.aggregation_recipes.gh1_score_aggregation_recipe import (
    GH1_SCORE_AGGREGATION_RECIPE_PORTFOLIO_LEVEL,
)
from app_real_estate.constants.indicator_subscore_weights import (
    indicator_weights,
)
from app_real_estate.transformation.aggregation.processor.add_fractional_max_percent_score import (
    Add_Fractional_Max_Percent_Score,
)
from app_real_estate.constants.common_aggregation_keys import portfolio_level


class GH1ScoreAggregator_ResponseId(ScoreAggregator):
    """
    Aggregates GH1 scores at the portfolio level.
    """

    _agg_keys = portfolio_level
    _asset_level_aggregator = GH1ScoreAggregator_PropertySubtype_Country_ResponseId
    _agg_dict = GH1_SCORE_AGGREGATION_RECIPE_PORTFOLIO_LEVEL
    _score_column = sc.score_gh1

    def __init__(
        self,
        data: pd.DataFrame,
        r1_table: pd.DataFrame,
        is_input_aggregated: bool = False,
        report_type: str = "re",
    ):
        self.report_type = report_type
        self.report_type_score_weights = indicator_weights(report_type)
        _subscore_weights = self.report_type_score_weights.GH1_SUBSCORE_WEIGHTS
        _max_score = self.report_type_score_weights.GH1_MAX_SCORE

        if not is_input_aggregated:
            asset_level_aggregated_data = self._asset_level_aggregator(
                data.copy(), r1_table=r1_table, report_type=report_type
            ).process()
        else:
            asset_level_aggregated_data = data
        super().__init__(
            data_model=PandanticBaseModel(),
            data=asset_level_aggregated_data.copy(),
            scale_list=list(self._agg_dict.keys()),
            agg_keys=self._agg_keys,
            agg_dict=self._agg_dict,
            subscore_weights=_subscore_weights,
            score_column=self._score_column,
            max_score=_max_score,
            r1_table=r1_table,
            is_input_aggregated=True,
        )

    def process(self) -> pd.DataFrame:
        self._data = super().process()

        self._data = Add_Fractional_Max_Percent_Score(
            data=self._data,
            score_column=sc.score_gh1_ghg_area_time_cov_p,
            max_score=self.report_type_score_weights.GH1_COV_MAX_SCORE,
            is_fractional=True,
        ).process()

        self._data = Add_Fractional_Max_Percent_Score(
            data=self._data,
            score_column=sc.score_gh1_ghg_lfl_percent_change,
            max_score=self.report_type_score_weights.GH1_LFL_MAX_SCORE,
            is_fractional=True,
        ).process()

        return self._data
