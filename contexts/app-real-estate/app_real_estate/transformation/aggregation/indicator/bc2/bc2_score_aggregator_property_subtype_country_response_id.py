import pandas as pd

from app_real_estate.constants.aggregation_recipes.bc2_score_aggregation_recipe import (
    BC2_SCORE_AGGREGATION_RECIPE_ASSET_LEVEL,
    BC2_SCORE_AGGREGATION_SCALE_ASSET_LEVEL,
)
from app_real_estate.constants.common_aggregation_keys import (
    propertysubtype_country_responseid,
)
from app_real_estate.constants.indicator_subscore_weights import (
    indicator_weights,
)
from app_real_estate.models.score_aggregation_util_models.bc2_scored_data import (
    BC2ScoredData,
)
from app_real_estate.transformation.aggregation.score_aggregator import (
    ScoreAggregator,
)
import app_real_estate.constants.column_names.score_columns as sc


class BC2ScoreAggregator_PropertySubtype_Country_ResponseId(ScoreAggregator):
    """
    Aggregates BC2 scores at the property subtype, country, and response ID level.
    """

    _data_model = BC2ScoredData
    _agg_keys = propertysubtype_country_responseid
    _scale_list = BC2_SCORE_AGGREGATION_SCALE_ASSET_LEVEL
    _agg_dict = BC2_SCORE_AGGREGATION_RECIPE_ASSET_LEVEL
    _score_column = sc.score_bc2

    def __init__(
        self,
        data: pd.DataFrame,
        r1_table: pd.DataFrame,
        report_type: str = "re",
    ):
        self.report_type = report_type
        report_type_score_weights = indicator_weights(report_type)
        _subscore_weights = report_type_score_weights.BC2_SUBSCORE_WEIGHTS
        _max_score = report_type_score_weights.BC2_MAX_SCORE

        super().__init__(
            data_model=self._data_model,
            scale_list=self._scale_list,
            agg_dict=self._agg_dict,
            subscore_weights=_subscore_weights,
            score_column=self._score_column,
            agg_keys=self._agg_keys,
            data=data,
            max_score=_max_score,
            r1_table=r1_table,
            is_input_aggregated=False,
        )

    def process(self):
        self._data = super().process()
        return self._data
