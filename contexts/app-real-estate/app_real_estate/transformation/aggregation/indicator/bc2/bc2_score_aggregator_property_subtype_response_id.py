import pandas as pd
from pandantic.basemodel import PandanticBaseModel

from app_real_estate.constants.aggregation_recipes.bc2_score_aggregation_recipe import (
    BC2_SCORE_AGGREGATION_RECIPE_PORTFOLIO_LEVEL,
    BC2_SCORE_AGGREGATION_SCALE_ASSET_LEVEL,
)
from app_real_estate.constants.common_aggregation_keys import (
    propertysubtype_responseid,
)
from app_real_estate.constants.indicator_subscore_weights import (
    indicator_weights,
)
from app_real_estate.transformation.aggregation.score_aggregator import (
    ScoreAggregator,
)
import app_real_estate.constants.column_names.score_columns as sc
from app_real_estate.transformation.aggregation.indicator.bc2.bc2_score_aggregator_property_subtype_country_response_id import (
    BC2ScoreAggregator_PropertySubtype_Country_ResponseId,
)


class BC2ScoreAggregator_PropertySubtype_ResponseId(ScoreAggregator):
    """
    Aggregates BC2 scores at the property subtype and response ID level.
    """

    _agg_keys = propertysubtype_responseid
    _asset_level_aggregator = BC2ScoreAggregator_PropertySubtype_Country_ResponseId
    _agg_dict = BC2_SCORE_AGGREGATION_RECIPE_PORTFOLIO_LEVEL
    _scale_list = BC2_SCORE_AGGREGATION_SCALE_ASSET_LEVEL
    _score_column = sc.score_bc2

    def __init__(
        self,
        data: pd.DataFrame,
        r1_table: pd.DataFrame,
        report_type: str = "re",
    ):
        self.report_type = report_type
        report_type_score_weights = indicator_weights(report_type)
        _subscore_weights = report_type_score_weights.BC2_SUBSCORE_WEIGHTS
        _max_score = report_type_score_weights.BC2_MAX_SCORE

        asset_level_aggregated_data = self._asset_level_aggregator(
            data.copy(), r1_table=r1_table, report_type=report_type
        ).process()
        super().__init__(
            data=asset_level_aggregated_data,
            data_model=PandanticBaseModel(),
            scale_list=self._scale_list,
            agg_dict=self._agg_dict,
            subscore_weights=_subscore_weights,
            agg_keys=self._agg_keys,
            score_column=self._score_column,
            max_score=_max_score,
            r1_table=r1_table,
            is_input_aggregated=True,
        )

    def process(self):
        self._data = super().process()
        return self._data
