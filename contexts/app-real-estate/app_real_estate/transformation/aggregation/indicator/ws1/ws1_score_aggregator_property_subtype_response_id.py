import pandas as pd
from pandantic.basemodel import PandanticBaseModel

from app_real_estate.constants.indicator_subscore_weights import (
    indicator_weights,
)
from app_real_estate.constants.aggregation_recipes.ws1_score_aggregation_recipe import (
    WS1_SCORE_AGGREGATION_RECIPE_PORTFOLIO_LEVEL,
    WS1_SCORE_AGGREGATION_SCALE_ASSET_LEVEL,
)
from app_real_estate.transformation.aggregation.score_aggregator import (
    ScoreAggregator,
)
import app_real_estate.constants.column_names.score_columns as sc
from app_real_estate.constants.common_aggregation_keys import (
    propertysubtype_responseid,
)
from app_real_estate.transformation.aggregation.indicator.ws1.ws1_score_aggregator_property_subtype_country_response_id import (
    WS1ScoreAggregator_PropertySubtype_Country_ResponseId,
)
from app_real_estate.transformation.aggregation.processor.add_fractional_max_percent_score import (
    Add_Fractional_Max_Percent_Score,
)


class WS1ScoreAggregator_PropertySubtype_ResponseId(ScoreAggregator):
    """Aggregator for WS1 subscores at the property subtype and response ID level."""

    _agg_keys = propertysubtype_responseid
    _asset_level_aggregator = WS1ScoreAggregator_PropertySubtype_Country_ResponseId
    _agg_dict = WS1_SCORE_AGGREGATION_RECIPE_PORTFOLIO_LEVEL
    _scale_list = WS1_SCORE_AGGREGATION_SCALE_ASSET_LEVEL
    _score_column = sc.score_ws1

    def __init__(
        self, data: pd.DataFrame, r1_table: pd.DataFrame, report_type: str = "re"
    ):
        self.report_type = report_type
        self.report_type_score_weights = indicator_weights(report_type)
        _subscore_weights = self.report_type_score_weights.WS1_SUBSCORE_WEIGHTS
        _max_score = self.report_type_score_weights.WS1_MAX_SCORE

        asset_level_aggregated_data = self._asset_level_aggregator(
            data.copy(), r1_table=r1_table, report_type=report_type
        ).process()
        super().__init__(
            data=asset_level_aggregated_data,
            data_model=PandanticBaseModel(),
            scale_list=self._scale_list,
            agg_dict=self._agg_dict,
            subscore_weights=_subscore_weights,
            agg_keys=self._agg_keys,
            score_column=self._score_column,
            max_score=_max_score,
            r1_table=r1_table,
            is_input_aggregated=True,
        )

    def process(self):
        self._data = super().process()

        self._data = Add_Fractional_Max_Percent_Score(
            data=self._data,
            score_column=sc.score_ws1_was_area_cov_p,
            max_score=self.report_type_score_weights.WS1_COV_MAX_SCORE,
            is_fractional=True,
        ).process()

        self._data = Add_Fractional_Max_Percent_Score(
            data=self._data,
            score_column=sc.score_ws1_was_diverted_percent,
            max_score=self.report_type_score_weights.WS1_DIV_MAX_SCORE,
            is_fractional=True,
        ).process()

        return self._data
