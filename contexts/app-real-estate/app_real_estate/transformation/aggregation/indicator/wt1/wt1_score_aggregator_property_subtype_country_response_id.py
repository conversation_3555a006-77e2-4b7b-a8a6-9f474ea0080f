import pandas as pd
import app_real_estate.constants.column_names.score_columns as sc
from app_real_estate.constants.indicator_subscore_weights import (
    indicator_weights,
)
from app_real_estate.constants.aggregation_recipes.wt1_score_aggregation_recipe import (
    WT1_SCORE_AGGREGATION_SCALE_ASSET_LEVEL,
    WT1_SCORE_AGGREGATION_RECIPE_ASSET_LEVEL,
)
from app_real_estate.models.score_aggregation_util_models.wt1_scored_data import (
    WT1ScoredData,
)
from app_real_estate.transformation.aggregation.score_aggregator import (
    ScoreAggregator,
)

from app_real_estate.constants.common_aggregation_keys import (
    propertysubtype_country_responseid,
)
from app_real_estate.transformation.aggregation.processor.add_fractional_max_percent_score import (
    Add_Fractional_Max_Percent_Score,
)


class WT1ScoreAggregator_PropertySubtype_Country_ResponseId(ScoreAggregator):
    """
    Aggregation for WT1 score at the property subtype, country, and response id level.
    """

    _data_model = WT1ScoredData
    _agg_keys = propertysubtype_country_responseid
    _scale_list = WT1_SCORE_AGGREGATION_SCALE_ASSET_LEVEL
    _agg_dict = WT1_SCORE_AGGREGATION_RECIPE_ASSET_LEVEL
    _score_column = sc.score_wt1

    def __init__(
        self,
        data: pd.DataFrame,
        r1_table: pd.DataFrame,
        report_type: str = "re",
    ):
        self.report_type = report_type
        self.report_type_score_weights = indicator_weights(report_type)
        _subscore_weights = self.report_type_score_weights.WT1_SUBSCORE_WEIGHTS
        _max_score = self.report_type_score_weights.WT1_MAX_SCORE

        super().__init__(
            data_model=self._data_model,
            scale_list=self._scale_list,
            agg_dict=self._agg_dict,
            subscore_weights=_subscore_weights,
            score_column=self._score_column,
            agg_keys=self._agg_keys,
            data=data,
            max_score=_max_score,
            r1_table=r1_table,
            is_input_aggregated=False,
        )

    def add_rec_score(self) -> pd.DataFrame:
        absolute_onsite_scores = (
            self._data[sc.score_wt1_wat_rec_ons]
            * self.report_type_score_weights.WT1_REC_ONS_MAX_SCORE
        )
        absolute_perf_scores = (
            self._data[sc.score_wt1_wat_rec_performance]
            * self.report_type_score_weights.WT1_REC_PERF_MAX_SCORE
        )

        self._data[sc.score_wt1_wat_rec] = absolute_perf_scores.add(
            absolute_onsite_scores, fill_value=0
        )
        self._data[sc.score_wt1_wat_rec] = self._data[sc.score_wt1_wat_rec].div(
            self.report_type_score_weights.WT1_REC_MAX_SCORE
        )

        return self._data

    def process(self) -> pd.DataFrame:
        self._data = self.add_rec_score()
        self._data = super().process()

        self._data = Add_Fractional_Max_Percent_Score(
            data=self._data,
            score_column=sc.score_wt1_wat_area_time_cov_p,
            max_score=self.report_type_score_weights.WT1_COV_MAX_SCORE,
            is_fractional=True,
        ).process()

        self._data = Add_Fractional_Max_Percent_Score(
            data=self._data,
            score_column=sc.score_wt1_wat_lfl_percent_change,
            max_score=self.report_type_score_weights.WT1_LFL_MAX_SCORE,
            is_fractional=True,
        ).process()

        self._data = Add_Fractional_Max_Percent_Score(
            data=self._data,
            score_column=sc.score_wt1_wat_rec,
            max_score=self.report_type_score_weights.WT1_REC_MAX_SCORE,
            is_fractional=True,
        ).process()

        return self._data
