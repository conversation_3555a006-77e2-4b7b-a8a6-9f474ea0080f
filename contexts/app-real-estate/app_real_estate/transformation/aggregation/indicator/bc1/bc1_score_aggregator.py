import pandas as pd
from app_real_estate.models.score_aggregation_util_models.bc1_scored_data import (
    BC1ScoredData,
)
import app_real_estate.constants.column_names.score_columns as sc
from app_real_estate.transformation.scoring.common_indicator_score_utils import (
    CommonIndicatorScoreUtils,
)
from app_real_estate.constants.indicator_subscore_weights import (
    indicator_weights,
)


class BC1ScoreAggregator:
    """
    Aggregate BC1.1 and BC1.2 scores to get BC1 score
    """

    _data_model = BC1ScoredData
    _score_column = sc.score_bc1

    def __init__(self, data: pd.DataFrame, report_type: str = "re"):
        """
        Initialize BC1ScoreAggregator with data and report type.

        Args:
            data: The input DataFrame with BC1.1 and BC1.2 scores
            report_type: The report type ('re' for Real Estate, 'res' for Residential)
        """
        self.report_type = report_type
        report_type_score_weights = indicator_weights(report_type)
        self._subscore_weights = report_type_score_weights.BC1_SUBSCORE_WEIGHTS

        self._data = self._data_model.parse_df(
            dataframe=data,
            verbose=True,
        )

    def process(self) -> pd.DataFrame:
        return CommonIndicatorScoreUtils.calculate_indicator_score(
            data=self._data,
            subscore_weights=self._subscore_weights,
            score_column=self._score_column,
        )
