import pandas as pd
from pandantic.basemodel import PandanticBaseModel

import app_real_estate.constants.column_names.score_columns as sc
from app_real_estate.transformation.aggregation.score_aggregator import (
    ScoreAggregator,
)
from app_real_estate.transformation.aggregation.indicator.bc1_1.bc1_1_score_aggregator_property_subtype_country_response_id import (
    BC1_1ScoreAggregator_PropertySubtype_Country_ResponseId,
)
from app_real_estate.constants.aggregation_recipes.bc1_1_score_aggregation_recipe import (
    BC1_1_SCORE_AGGREGATION_RECIPE_PORTFOLIO_LEVEL,
)
from app_real_estate.constants.indicator_subscore_weights import (
    indicator_weights,
)
from app_real_estate.constants.common_aggregation_keys import portfolio_level


class BC1_1ScoreAggregator_ResponseId(ScoreAggregator):
    """
    Aggregates BC1.1 scores at the portfolio level.

    Attributes:
        _asset_level_aggregator (ScoreAggregator_AssetLevel): The asset level aggregator.
        _agg_dict: The aggregation dictionary.
        _subscore_weights: The subscore weights.
        _score_column: The score column.
    """

    _asset_level_aggregator = BC1_1ScoreAggregator_PropertySubtype_Country_ResponseId
    _agg_dict = BC1_1_SCORE_AGGREGATION_RECIPE_PORTFOLIO_LEVEL
    _score_column = sc.score_bc1_1

    def __init__(
        self,
        data: pd.DataFrame,
        r1_table: pd.DataFrame,
        report_type: str,
        is_input_aggregated: bool = False,
    ):
        self.report_type = report_type
        report_type_score_weights = indicator_weights(report_type)
        _subscore_weights = report_type_score_weights.BC1_1_SUBSCORE_WEIGHTS
        _max_score = report_type_score_weights.BC1_1_MAX_SCORE

        if not is_input_aggregated:
            asset_level_aggregated_data = self._asset_level_aggregator(
                data.copy(), r1_table=r1_table, report_type=report_type
            ).process()
        else:
            asset_level_aggregated_data = data.copy()
        super().__init__(
            data_model=PandanticBaseModel(),
            scale_list=list(self._agg_dict.keys()),
            agg_dict=self._agg_dict,
            subscore_weights=_subscore_weights,
            score_column=self._score_column,
            agg_keys=portfolio_level,
            data=asset_level_aggregated_data,
            max_score=_max_score,
            r1_table=r1_table,
            is_input_aggregated=True,
        )

    def process(self):
        self._data = super().process()
        return self._data
