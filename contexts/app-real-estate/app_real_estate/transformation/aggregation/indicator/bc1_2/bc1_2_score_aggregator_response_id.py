import pandas as pd
from pandantic.basemodel import PandanticBaseModel

import app_real_estate.constants.column_names.score_columns as sc
from app_real_estate.transformation.aggregation.score_aggregator import (
    ScoreAggregator,
)
from app_real_estate.transformation.aggregation.indicator.bc1_2.bc1_2_score_aggregator_property_subtype_country_responseid import (
    BC1_2ScoreAggregator_PropertySubtype_Country_ResponseId,
)
from app_real_estate.constants.aggregation_recipes.bc1_2_score_aggregation_recipe import (
    BC1_2_SCORE_AGGREGATION_RECIPE_PORTFOLIO_LEVEL,
)
from app_real_estate.constants.indicator_subscore_weights import (
    indicator_weights,
)
from app_real_estate.constants.common_aggregation_keys import portfolio_level


class BC1_2ScoreAggregator_ResponseId(ScoreAggregator):
    """
    Aggregates BC1.2 scores at the portfolio level.
    """

    _agg_keys = portfolio_level
    _asset_level_aggregator = BC1_2ScoreAggregator_PropertySubtype_Country_ResponseId
    _agg_dict = BC1_2_SCORE_AGGREGATION_RECIPE_PORTFOLIO_LEVEL
    _score_column = sc.score_bc1_2

    def __init__(
        self,
        data: pd.DataFrame,
        r1_table: pd.DataFrame,
        report_type: str,
        is_input_aggregated: bool = False,
    ):
        self.report_type = report_type
        report_type_score_weights = indicator_weights(report_type)
        _subscore_weights = report_type_score_weights.BC1_2_SUBSCORE_WEIGHTS
        _max_score = report_type_score_weights.BC1_2_MAX_SCORE

        if not is_input_aggregated:
            asset_level_aggregated_data = self._asset_level_aggregator(
                data.copy(), r1_table=r1_table, report_type=report_type
            ).process()
        else:
            asset_level_aggregated_data = data.copy()
        super().__init__(
            data_model=PandanticBaseModel(),
            data=asset_level_aggregated_data.copy(),
            scale_list=list(self._agg_dict.keys()),
            agg_keys=self._agg_keys,
            agg_dict=self._agg_dict,
            subscore_weights=_subscore_weights,
            score_column=self._score_column,
            max_score=_max_score,
            r1_table=r1_table,
            is_input_aggregated=True,
        )

    def process(self):
        self._data = super().process()
        return self._data
