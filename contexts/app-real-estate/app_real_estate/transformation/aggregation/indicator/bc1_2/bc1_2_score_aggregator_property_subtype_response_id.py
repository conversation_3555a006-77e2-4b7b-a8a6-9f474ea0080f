import pandas as pd

from app_real_estate.constants.aggregation_recipes.bc1_2_score_aggregation_recipe import (
    BC1_2_SCORE_AGGREGATION_RECIPE_PORTFOLIO_LEVEL,
    BC1_2_SCORE_AGGREGATION_RECIPE_ASSET_LEVEL,
    BC1_2_SCORE_AGGREGATION_SCALE_ASSET_LEVEL,
)
from app_real_estate.constants.common_aggregation_keys import (
    propertysubtype_responseid,
)
from app_real_estate.constants.indicator_subscore_weights import (
    indicator_weights,
)
from app_real_estate.models.score_aggregation_util_models.bc1_2_scored_data import (
    BC1_2ScoredData,
)
from app_real_estate.models.score_aggregation_util_models.bc1_2_scored_data_aggregated_level import (
    BC1_2ScoredData_AggregatedLevel,
)
from app_real_estate.transformation.aggregation.score_aggregator import (
    ScoreAggregator,
)
import app_real_estate.constants.column_names.score_columns as sc


class BC1_2ScoreAggregator_PropertySubtype_ResponseId(ScoreAggregator):
    """
    Aggregates BC1.2 scores at the property subtype and response ID level.
    """

    _agg_keys = propertysubtype_responseid
    _scale_list = BC1_2_SCORE_AGGREGATION_SCALE_ASSET_LEVEL
    _agg_dict = BC1_2_SCORE_AGGREGATION_RECIPE_ASSET_LEVEL
    _score_column = sc.score_bc1_2

    def __init__(
        self,
        data: pd.DataFrame,
        r1_table: pd.DataFrame,
        data_model: type[
            BC1_2ScoredData | BC1_2ScoredData_AggregatedLevel
        ] = BC1_2ScoredData,
        report_type: str = "re",
    ):
        self.report_type = report_type
        report_type_score_weights = indicator_weights(report_type)
        _subscore_weights = report_type_score_weights.BC1_2_SUBSCORE_WEIGHTS
        _max_score = report_type_score_weights.BC1_2_MAX_SCORE

        is_input_aggregated = not data_model is BC1_2ScoredData
        if is_input_aggregated:
            self._agg_dict = BC1_2_SCORE_AGGREGATION_RECIPE_PORTFOLIO_LEVEL
            self._scale_list = list(
                BC1_2_SCORE_AGGREGATION_RECIPE_PORTFOLIO_LEVEL.keys()
            )
        super().__init__(
            data=data.copy(),
            data_model=data_model,
            scale_list=self._scale_list,
            agg_dict=self._agg_dict,
            subscore_weights=_subscore_weights,
            agg_keys=self._agg_keys,
            score_column=self._score_column,
            max_score=_max_score,
            r1_table=r1_table,
            is_input_aggregated=is_input_aggregated,
        )

    def process(self):
        self._data = super().process()
        return self._data
