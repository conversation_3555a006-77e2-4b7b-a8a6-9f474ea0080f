import pandas as pd


class Validator:
    @staticmethod
    def validate_missing_column(data: pd.DataFrame, required_columns: list[str]):
        """Ensures required columns are present. Raises an error if any required columns are missing.

        Args:
            data (pd.DataFrame): The DataFrame to validate.
            required_columns (list): A list of column names that must be present in the DataFrame.
        """
        missing_columns = [
            col for col in required_columns if not data.columns.isin([col]).any()
        ]
        if missing_columns:
            raise ValueError(f"Missing required columns: {', '.join(missing_columns)}")

    @staticmethod
    def validate_exception_column(data: pd.DataFrame, exclude_columns: list[str]):
        """Ensures certain columns are absent. Raises an error if any of the exception columns are present.

        Args:
            data (pd.DataFrame): The DataFrame to validate.
            exclude_columns (list): A list of column names that must not be present in the DataFrame.
        """
        exception_columns = [
            col for col in exclude_columns if data.columns.isin([col]).any()
        ]
        if exception_columns:
            raise ValueError(
                f"these columns should not be in data: {', '.join(exception_columns)}"
            )
