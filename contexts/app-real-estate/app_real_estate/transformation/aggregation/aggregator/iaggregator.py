import pandas as pd
from abc import ABCMeta, abstractmethod


class IAggregator(metaclass=ABCMeta):
    @abstractmethod
    def validate(self, data: pd.DataFrame) -> pd.DataFrame:
        """validation of data"""
        raise NotImplementedError("Subclass must implement this method")

    @abstractmethod
    def aggregate(self, data: pd.DataFrame) -> pd.DataFrame:
        """Aggregate the data"""
        raise NotImplementedError("Subclass must implement this method")
