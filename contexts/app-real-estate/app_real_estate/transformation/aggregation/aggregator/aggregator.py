import pandas as pd
from app_real_estate.transformation.aggregation.validator.validator import (
    Validator,
)
from app_real_estate.transformation.aggregation.aggregator.iaggregator import (
    IAggregator,
)


class Aggregator(IAggregator):
    _index = None

    def __init__(self, data: pd.DataFrame, _groupby_params: list[str], agg_dict: dict):
        """
        Initialize an Aggregator instance.

        This constructor sets up the Aggregator with the necessary data and parameters
        for performing aggregation operations on a pandas DataFrame.

        Parameters:
        -----------
        data : pd.DataFrame
            The input DataFrame on which aggregation will be performed.

        groupby_params : dict
            A dictionary specifying the parameters for the groupby operation.
            It should have one of the following forms:
            - {'level': list[int]} for grouping by index levels
            - {'by': list[str]} for grouping by column names

        agg_dict : dict
            A dictionary specifying the aggregation functions to apply to each column.
            Keys are column names, and values are aggregation functions or lists of functions.
            Example: {'column1': 'sum', 'column2': ['mean', 'max']}
        """
        self._data = data
        self._groupby_params = _groupby_params
        self._agg_dict = agg_dict

    def validate(self):
        Validator.validate_missing_column(self._data, list(self._agg_dict.keys()))

    def aggregate(self):
        self.validate()
        aggregated = self._data.groupby(self._groupby_params).agg(self._agg_dict)

        return aggregated
