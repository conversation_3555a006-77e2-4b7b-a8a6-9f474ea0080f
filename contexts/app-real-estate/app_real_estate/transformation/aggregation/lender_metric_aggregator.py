import pandas as pd
import numpy as np


from app_real_estate.transformation.aggregation.processor.add_area_ownership_weight_factor import (
    AddAreaOwnershipWeightFactor,
)

from app_real_estate.transformation.aggregation.processors.weighted_mean import (
    WeightedMean,
)

from app_real_estate.constants.aggregation_recipes.lender_asset_metric_aggregation_recipe import (
    LENDER_ASSET_METRIC_AGGREGATION_RECIPE,
    LENDER_COLUMNS_SCALED_PER_AREA_OWNERSHIP,
    LENDER_COLUMNS_TO_WEIGHT_SUM_PER_OWNERSHIP,
)

import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.energy_columns as ec
from app_real_estate.transformation.metric_calculation.lender_coverage_calcualtions import (
    LenderCoverageCalculations,
)


class LenderAssetMetricAggregator:
    """
    `LenderAssetMetricAggregator` aggregate the asset-level metrics
    Aggregate metrics at multiple levels.
    """

    def __init__(
        self,
        asset_metric_data: pd.DataFrame,
        agg_keys: list[str],
        # r1_table: pd.DataFrame = None,
        add_benchmarks: bool = False,
        asset_metric_agg_recipe=LENDER_ASSET_METRIC_AGGREGATION_RECIPE.copy(),
        weighted_mean_area_list=LENDER_COLUMNS_SCALED_PER_AREA_OWNERSHIP,
        weighted_sum_ownership_list=LENDER_COLUMNS_TO_WEIGHT_SUM_PER_OWNERSHIP,
    ):
        self._data = asset_metric_data.copy()
        self._agg_keys = agg_keys + [ac.data_year]
        # self._r1_table = r1_table
        self.aggregation_recipe = asset_metric_agg_recipe
        self._weighted_mean_list_area = weighted_mean_area_list
        self._consumption_cols = weighted_sum_ownership_list
        self._weight_column = sc.asset_size_owned_m2

    def aggregate_weighted_data(self) -> pd.DataFrame:
        """
        Aggregate the data using the specified recipe. It also performs a weighted
        mean on the variables that require it.
        """
        self._data = AddAreaOwnershipWeightFactor(self._data).process()
        self._data["asset_count"] = 1
        self._data = LenderCoverageCalculations.add_coverage_weighted_lender(self._data)
        self._data = WeightedMean(
            data=self._data,
            weight_column=self._weight_column,
            cols_to_scale=self._weighted_mean_list_area,
            group_by=self._agg_keys,
            agg_dict=self.aggregation_recipe,
        ).process()
        return self._data

    def cap_renewable_energy_rates(self) -> pd.DataFrame:
        result = self._data.copy()
        cols_to_cap = [ec.en_ren_rate, ec.en_ren_rate_ly]
        cols_to_cap = [c for c in cols_to_cap if c in result.columns]

        if not cols_to_cap:
            return self._data
        result[cols_to_cap] = np.minimum(result[cols_to_cap], 100)

        return result

    def fill_missing_values(self) -> pd.DataFrame:
        result = self._data.copy()
        cols_to_fill = [
            ec.en_ren_rate,
            ec.en_ren_rate_ly,
        ]
        cols_to_fill = [c for c in cols_to_fill if c in result.columns]
        if not cols_to_fill:
            return self._data
        result[cols_to_fill] = result[cols_to_fill].fillna(value=0)

        return result

    def aggregate(self) -> pd.DataFrame:
        """
        Aggregate the metrics with the specified configuration.
        This is the main function of the class.
        """

        self._data = self.aggregate_weighted_data()
        self._data = self.cap_renewable_energy_rates()
        self._data = self.fill_missing_values()

        return self._data
