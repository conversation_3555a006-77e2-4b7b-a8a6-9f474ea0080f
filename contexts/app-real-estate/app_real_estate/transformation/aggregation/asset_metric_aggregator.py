import pandas as pd
import numpy as np

from app_real_estate.models.input_models.r1_table_model import R1TableModel
from app_real_estate.transformation.metric_calculation.coverage_calculations import (
    CoverageCalculations,
)

from app_real_estate.transformation.aggregation.processor.add_area_ownership_weight_factor import (
    AddAreaOwnershipWeightFactor,
)

from app_real_estate.constants.aggregation_recipes.asset_metric_aggregation_recipe import (
    ASSET_METRIC_AGGREGATION_RECIPE,
    COLUMNS_SCALED_PER_AREA_OWNERSHIP,
    COLUMNS_TO_WEIGHT_SUM_PER_OWNERSHIP,
    COLUMNS_NORMALISED_DIFFERENT_WEIGHTS,
    BENCHMARK_COLUMNS_MEAN,
)
from app_real_estate.transformation.aggregation.processor.weight_normaliser import (
    WeightNormaliser,
)
from app_real_estate.transformation.aggregation.processors.weighted_mean import (
    WeightedMean,
)
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.water_columns as wc
import app_real_estate.constants.column_names.ghg_columns as gc
import app_real_estate.constants.column_names.waste_columns as wsc
import app_real_estate.constants.common_aggregation_keys as agg_keys


class AssetMetricAggregator:
    """
    `AssetMetricAggregator` provides a framework to aggregate the asset-level metrics
    and data. It enables aggregation at multiple levels, on a specific list of variables
    with different methods of aggregation.
    The `add_benchmarks` (as well as the `indicator_groups` and `indicator_memberships`)
    allow to aggregate the metrics used as benchmarks in the Benchmark reports and
    to fetch the benchmark metrics calculated for scoring.
    All the aggregation recipes used are defined in `app_real_estate/constants/asset_metric_aggregation_recipe.py`.
    """

    def __init__(
        self,
        asset_metric_data: pd.DataFrame,
        agg_keys: list[str],
        r1_table: pd.DataFrame = None,
        add_benchmarks: bool = False,
        asset_metric_agg_recipe=ASSET_METRIC_AGGREGATION_RECIPE.copy(),
        weighted_mean_area_list=COLUMNS_SCALED_PER_AREA_OWNERSHIP,
        weighted_sum_ownership_list=COLUMNS_TO_WEIGHT_SUM_PER_OWNERSHIP,
        weighted_mean_other_frame=COLUMNS_NORMALISED_DIFFERENT_WEIGHTS,
        benchmark_mean_list=BENCHMARK_COLUMNS_MEAN,
    ):
        self._data = asset_metric_data.copy()
        self._agg_keys = agg_keys + [ac.data_year]
        self._r1_table = r1_table
        self._benchmark_metrics_mean = benchmark_mean_list if add_benchmarks else None
        self.aggregation_recipe = asset_metric_agg_recipe
        self._weighted_mean_list_area = weighted_mean_area_list
        self._normalised_cols_frame = weighted_mean_other_frame

        # The columns to weight by ownership should only be weighted from asset-level
        # not when already aggregated (we don't want to weight them twice).
        self._consumption_cols = weighted_sum_ownership_list
        self._weight_column = sc.asset_size_owned_m2

    def aggregate_weighted_data(self) -> pd.DataFrame:
        """
        Aggregate the data using the specified recipe. It also performs a weighted
        mean on the variables that require it.
        """
        self._data = AddAreaOwnershipWeightFactor(self._data).process()
        self._data["asset_count"] = 1

        self._data = CoverageCalculations.add_coverage_weighted(self._data)

        # WeightedMean will not only perform the weighted mean on the `cols_to_scale`
        # but it will also aggregate the other columns specified in `agg_dict`.
        self._data = WeightedMean(
            data=self._data,
            weight_column=self._weight_column,
            cols_to_scale=self._weighted_mean_list_area,
            group_by=self._agg_keys,
            agg_dict=self.aggregation_recipe,
            ignore_na=True,
            cols_to_sum_only=self._consumption_cols,
        ).process()

        # This is for covs and LFL percent changes, the weighted mean is different
        self._data = WeightNormaliser(
            data=self._data,
            weight_column=list(self._normalised_cols_frame["normaliser_column"]),
            cols_to_normalise=list(self._normalised_cols_frame["base_column"]),
            normalised_cols_names=list(self._normalised_cols_frame["new_column"]),
            coefficient=list(self._normalised_cols_frame["coefficient"]),
        ).process()
        return self._data

    def add_benchmark_metrics(self):
        """
        Add the "reporting" benchmark metrics based on aggregated metrics.
        """
        self._data = self._data.copy()
        self._data[list(self._benchmark_metrics_mean.values())] = self._data.groupby(
            [ac.country, ac.property_sector, ac.data_year]
        )[list(self._benchmark_metrics_mean.keys())].transform("mean")

        return self._data

    def cap_renewable_energy_rates(self) -> pd.DataFrame:
        result = self._data.copy()
        cols_to_cap = [ec.en_ren_rate, ec.en_ren_rate_ly]
        cols_to_cap = [c for c in cols_to_cap if c in result.columns]

        if not cols_to_cap:
            return self._data
        result[cols_to_cap] = np.minimum(result[cols_to_cap], 100)

        return result

    def fill_missing_values(self) -> pd.DataFrame:
        result = self._data.copy()
        cols_to_fill = [
            ec.en_ren_rate,
            ec.en_ren_rate_ly,
            wc.wat_rec_rate,
            wc.wat_rec_rate_ly,
            wsc.was_pabs_div,
            wsc.was_pabs_div_ly,
            ec.en_lfl_area_p_lc,
            ec.en_lfl_area_p_tc,
            wc.wat_lfl_area_p_lc,
            wc.wat_lfl_area_p_tc,
            gc.ghg_lfl_area_p_s12,
            gc.ghg_lfl_area_p_s3,
        ]
        cols_to_fill = [c for c in cols_to_fill if c in result.columns]
        if not cols_to_fill:
            return self._data
        result[cols_to_fill] = result[cols_to_fill].fillna(value=0)

        return result

    def aggregate_pgav(
        self, r1_table: pd.DataFrame, asset_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Aggregate the PGAV from the R1 table (country / subtype level) to the
        country / sector level.
        Parameters:
            r1_table (pd.DataFrame): the R1 table from the survey.
            asset_data (pd.DataFrame): the asset data to get the sector info from.

        Returns:
            pd.DataFrame: a dataframe with the aggregated PGAV.
        """
        # First merge the sector information to the R1 table
        sector_info = asset_data[
            [ac.property_sector, ac.property_type_code]
        ].drop_duplicates()
        r1_table = r1_table.merge(
            sector_info,
            left_on=R1TableModel.PRT_TYPE,
            right_on=ac.property_type_code,
            how="left",
        )

        # Then, aggregate the PGAV along response ID, country and sector
        agg_data = (
            r1_table.groupby(
                [R1TableModel.RESPONSE_ID, R1TableModel.COUNTRY, ac.property_sector]
            )[R1TableModel.R_1_TBL_PGAV]
            .sum()
            .reset_index()
        )

        if any(round(agg_data[R1TableModel.R_1_TBL_PGAV], 4) > 100):
            raise ValueError("The PGAV cannot be greater than 100.")

        agg_data.rename(
            columns={
                R1TableModel.RESPONSE_ID: ac.response_id,
                R1TableModel.COUNTRY: ac.country,
                R1TableModel.R_1_TBL_PGAV: sc.pgav,
            },
            inplace=True,
        )

        return agg_data

    def aggregate(self):
        """
        Aggregate the metrics with the specified configuration.
        This is the main function of the class.
        """
        asset_data = self._data

        self._data = self.aggregate_weighted_data()

        self._data = self.cap_renewable_energy_rates()

        self._data = self.fill_missing_values()

        if not self._benchmark_metrics_mean is None:
            self._data = self.add_benchmark_metrics()

        if (
            self._agg_keys
            == agg_keys.propertysector_country_responseid + [ac.data_year]
            and self._r1_table is not None
        ):
            self._data = self._data.reset_index().merge(
                self.aggregate_pgav(self._r1_table, asset_data),
                on=agg_keys.propertysector_country_responseid,
                how="left",
            )

        return self._data
