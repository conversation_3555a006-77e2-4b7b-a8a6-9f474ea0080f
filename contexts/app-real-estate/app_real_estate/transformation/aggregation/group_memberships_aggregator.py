import pandas as pd
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.benchmark_groups_columns as gc
import app_real_estate.constants.column_names.building_certification_columns as bc
import app_real_estate.constants.column_names.benchmark_group_memberships_columns as mc


class GroupMembershipsAggregator:
    """
    The `GroupMembershipsAggregator` is used to aggregate the scoring benchmark
    group memberships at the property subtype and country level.
    It takes the different possible property subtype and country buckets
    from the asset data and maps it with the unique active scoring benchmark group
    for this bucket for each metric and indicator.
    """

    def __init__(
        self,
        data_with_loc_prt: pd.DataFrame,
        memberships: pd.DataFrame,
        groups: pd.DataFrame,
        is_asset: bool = True,
        benchmark_metrics: list[str] = None,
    ) -> None:
        self._data = data_with_loc_prt
        self._memberships = memberships
        self._groups = groups
        self._id_col = (
            ac.portfolio_asset_id
            if is_asset
            else bc.building_data_certifications_scoring_id
        )
        self._benchmark_metrics = benchmark_metrics

    def process(self) -> pd.DataFrame:
        countries = self._data[[ac.country, ac.country_name]].drop_duplicates()
        property_types = self._data[
            [ac.property_subtype, ac.property_type_code]
        ].drop_duplicates()
        buckets = self._data[
            [self._id_col, ac.property_subtype, ac.country]
        ].drop_duplicates()

        # Get bucket country and property subtype to map in the reports.
        self._memberships = self._memberships.merge(
            buckets[[self._id_col, ac.property_subtype, ac.country]],
            on=self._id_col,
            how="left",
        )

        # Group memberships to get the unique active group per metric / country / subtype.
        result = (
            self._memberships[self._memberships[mc.is_active]]
            .groupby([ac.country, ac.property_subtype, "metric", "indicator"])[
                mc.group_id
            ]
            .agg(lambda x: x.unique()[0])
            .reset_index()
        )

        result = self.add_group_asset_count(result)

        # Get groups characteristics.
        self._groups = self._groups[[gc.id, gc.location, gc.property_type]]
        self._groups.rename(columns={gc.id: mc.group_id}, inplace=True)
        result = result.merge(self._groups, on=mc.group_id, how="left")

        # Add country name and property type code
        result = result.merge(countries, on=ac.country, how="left")
        is_location_country = result[ac.country] == result[gc.location]
        result.loc[is_location_country, gc.location] = result[ac.country_name]
        result.drop(ac.country_name, inplace=True, axis=1)
        result = result.merge(property_types, on=ac.property_subtype, how="left")

        result.rename(
            columns={
                gc.location: "benchmark_group_location",
                gc.property_type: "benchmark_group_prt_type",
            },
            inplace=True,
        )

        if self._benchmark_metrics is not None:
            result = self.get_benchmark_metric(
                result, self._memberships, self._benchmark_metrics
            )

        return result

    def add_group_asset_count(self, result: pd.DataFrame) -> pd.DataFrame:
        memberships = self._memberships.copy()
        result = result.copy()

        if self._id_col == ac.portfolio_asset_id:
            memberships["asset_count"] = 1
        else:
            memberships = memberships.merge(
                self._data[[self._id_col, bc.unique_asset_count]],
                on=self._id_col,
                how="left",
            )
            memberships = memberships.rename(
                columns={bc.unique_asset_count: "asset_count"}
            )

        group_asset_count = (
            memberships.groupby([mc.group_id])["asset_count"].agg("sum").reset_index()
        )
        result = result.merge(group_asset_count, on=mc.group_id, how="left")
        return result

    @staticmethod
    def get_benchmark_metric(
        aggregated_groups: pd.DataFrame, memberships: pd.DataFrame, metrics: list[str]
    ) -> pd.DataFrame:
        benchmark_metrics = memberships.loc[(memberships[mc.is_active])][
            [mc.group_id] + metrics
        ].drop_duplicates()
        aggregated_groups_with_metrics = aggregated_groups.merge(
            benchmark_metrics, on=mc.group_id, how="left"
        )
        return aggregated_groups_with_metrics
