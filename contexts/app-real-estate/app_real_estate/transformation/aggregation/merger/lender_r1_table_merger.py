import pandas as pd
from app_real_estate.transformation.aggregation.merger.imerger import <PERSON>Merger
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
from app_real_estate.models.input_models.r1_table_model import R1TableModel


class LenderR1TableMerger(IMerger):
    """
    Merges and validates data against the R1 table for lender asset aggregation.
    Filters out records that don't have corresponding entries in the R1 table.
    """

    def __init__(
        self, r1_data: pd.DataFrame, data: pd.DataFrame, data_type_name: str = "data"
    ):
        """
        Initialize the R1 table merger.

        Args:
            r1_data: R1 table data with columns RESPONSE_ID, COUNTRY, PRT_TYPE, R_1_TBL_AREA
            data: Data to validate against R1 table
            data_type_name: Name for logging purposes (e.g., "certification", "energy_ratings")
        """
        self.r1_data = r1_data
        self.data = data
        self.data_type_name = data_type_name
        self.merge_on_keys = [ac.response_id, ac.country, ac.property_type_code]
        self.r1_columns_mapping = {
            R1TableModel.RESPONSE_ID: ac.response_id,
            R1TableModel.COUNTRY: ac.country,
            R1TableModel.PRT_TYPE: ac.property_type_code,
        }

    def validate(self):
        """
        Validate that the required columns exist in both datasets.
        """
        # Check R1 data has required columns
        required_r1_columns = list(self.r1_columns_mapping.keys()) + [
            R1TableModel.R_1_TBL_AREA
        ]
        missing_r1_columns = [
            col for col in required_r1_columns if col not in self.r1_data.columns
        ]
        if missing_r1_columns:
            raise ValueError(f"R1 data missing required columns: {missing_r1_columns}")

        # Check data has required merge keys
        missing_data_columns = [
            col for col in self.merge_on_keys if col not in self.data.columns
        ]
        if missing_data_columns:
            raise ValueError(
                f"Data missing required merge columns: {missing_data_columns}"
            )

    def merge(self) -> pd.DataFrame:
        """
        Merge data with R1 table and filter out unmatched records.

        Returns:
            DataFrame with only records that have corresponding R1 table entries
        """
        self.validate()

        # Prepare R1 data with renamed columns for merging
        r1_for_merge = self.r1_data[
            list(self.r1_columns_mapping.keys()) + [R1TableModel.R_1_TBL_AREA]
        ].rename(columns=self.r1_columns_mapping)

        # Merge with R1 table
        merged_data = self.data.merge(
            r1_for_merge, on=self.merge_on_keys, how="left", indicator=True
        )

        # Identify and log unmatched records
        no_r1_match = merged_data[merged_data["_merge"] == "left_only"]
        print(
            f"Rows that won't match with R1 table ({self.data_type_name}): {len(no_r1_match)}"
        )

        if len(no_r1_match) > 0:
            print(f"Sample of unmatched records ({self.data_type_name}):")
            print(no_r1_match[self.merge_on_keys].head())

        # Filter to keep only matched records and clean up
        validated_data = merged_data[merged_data["_merge"] == "both"].drop(
            columns=[R1TableModel.R_1_TBL_AREA, "_merge"]
        )

        print(f"After R1 filtering ({self.data_type_name}): {len(validated_data)} rows")

        # Convert response_id to int64 to ensure consistent data types
        validated_data = validated_data.copy()
        validated_data[ac.response_id] = validated_data[ac.response_id].astype("int64")

        return validated_data
