import pandas as pd
from app_real_estate.transformation.aggregation.merger.imerger import IMerger
import app_real_estate.constants.column_names.asset_characteristics_columns as ac


class LenderAssetDataMerger(IMerger):
    """
    Merges data with asset level data for lender asset aggregation.
    """

    def __init__(
        self, asset_data: pd.DataFrame, data: pd.DataFrame, data_type_name: str = "data"
    ):
        """
        Initialize the asset data merger.

        Args:
            asset_data: Asset level data
            data: Data to merge with asset data
            data_type_name: Name for logging purposes
        """
        self.asset_data = asset_data
        self.data = data
        self.data_type_name = data_type_name
        self.merge_on_keys = [ac.portfolio_asset_id]
        self.asset_columns = [
            ac.portfolio_asset_id,
            ac.asset_ownership,
            ac.asset_size_m2,
            ac.data_year,
            ac.property_sector,
            ac.property_type_code,
            ac.country,
        ]

    def validate(self):
        """
        Validate that the required columns exist in both datasets.
        """
        # Check asset data has required columns
        missing_asset_columns = [
            col for col in self.asset_columns if col not in self.asset_data.columns
        ]
        if missing_asset_columns:
            raise ValueError(
                f"Asset data missing required columns: {missing_asset_columns}"
            )

        # Check data has merge key
        if self.merge_on_keys[0] not in self.data.columns:
            raise ValueError(f"Data missing merge column: {self.merge_on_keys[0]}")

    def merge(self) -> pd.DataFrame:
        """
        Merge data with asset level data.

        Returns:
            DataFrame merged with asset level data
        """
        self.validate()

        # Merge with asset level data
        merged_data = pd.merge(
            self.data,
            self.asset_data[self.asset_columns],
            on=self.merge_on_keys[0],
            how="left",
        )

        return merged_data
