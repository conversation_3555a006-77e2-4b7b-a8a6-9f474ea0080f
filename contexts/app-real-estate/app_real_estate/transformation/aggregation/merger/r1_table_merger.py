from app_real_estate.models.input_models.r1_table_model import R1TableModel
from app_real_estate.transformation.aggregation.merger.imerger import IMerger
import pandas as pd
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
from typing import List
from app_real_estate.transformation.aggregation.validator.validator import (
    Validator,
)


class R1TableMerger(IMerger):
    def __init__(
        self,
        r1_data: pd.DataFrame,
        data: pd.DataFrame,
        merge_on_keys: List[str],
    ):
        self._r1_data = r1_data
        self._data = data
        self._merge_on_keys = merge_on_keys

    def validate(self):
        if self._data.empty or self._r1_data.empty:
            raise ValueError("Data is empty")

    def rename(self):
        self._r1_data = self._r1_data.rename(
            columns={
                R1TableModel.RESPONSE_ID: ac.response_id,
                R1TableModel.PRT_TYPE: ac.property_type_code,
                R1TableModel.COUNTRY: ac.country,
                R1TableModel.R_1_TBL_PGAV: sc.pgav,
            }
        )

    def merge(self):
        self.validate()
        self.rename()

        if isinstance(self._data.index, pd.MultiIndex):
            self._data = self._data.reset_index()

        Validator.validate_missing_column(self._data, self._merge_on_keys)

        self._data = self._data.merge(self._r1_data, on=self._merge_on_keys, how="left")
        self._data = self._data.set_index(self._merge_on_keys)

        return self._data
