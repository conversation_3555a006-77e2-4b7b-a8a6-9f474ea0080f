import pandas as pd
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
from app_real_estate.transformation.aggregation.processors.iprocessors import (
    IProcessors,
)
from app_real_estate.transformation.aggregation.aggregator.aggregator import (
    Aggregator,
)
from app_real_estate.transformation.aggregation.processor.weight_normaliser import (
    WeightNormaliser,
)
from app_real_estate.transformation.aggregation.processor.scale_with_weight_factor import (
    ScaleWithWeightFactor,
)


class WeightedMean(IProcessors):
    """
    Calculate the weighted mean of a set of columns with weight factor.
    This processor only weights and normalises the columns in `cols_to_scale`.
    Others columns can be aggregated if they are mentioned in `agg_dict`.
    """

    def __init__(
        self,
        data: pd.DataFrame,
        weight_column: str,
        cols_to_scale: list[str],
        group_by: list[str],
        agg_dict: dict,
        ignore_na: bool = False,
        cols_to_sum_only: list[str] = None,
    ):
        self._data = data
        self._weight_column = weight_column
        self._cols_to_scale = cols_to_scale
        if cols_to_sum_only:
            self._cols_to_normalise = [
                c for c in cols_to_scale if c not in cols_to_sum_only
            ]
        else:
            self._cols_to_normalise = cols_to_scale
        self._cols_to_sum = cols_to_sum_only
        self._group_by = group_by
        self._agg_dict = agg_dict.copy()
        self.ignore_na = ignore_na

    def process(self):
        self._data = ScaleWithWeightFactor(
            self._data, self._weight_column, self._cols_to_scale
        ).process()

        # TODO: Replace the following hack by an actual implementation
        # The cols to sum need to be weighted by ownership
        if self._cols_to_sum:
            self._data = ScaleWithWeightFactor(
                self._data, ac.asset_ownership_fraction, self._cols_to_sum
            ).process()

        weight_columns = self._weight_column
        # If we want to ignore the weights for assets with NAs in `_cols_to_scale`,
        # the solution is to create new weight columns with NAs at the same rows.
        if self.ignore_na:
            weight_columns = [s + self._weight_column for s in self._cols_to_scale]
            for i in range(len(weight_columns)):
                self._data = self._data.copy()
                self._data.loc[
                    ~self._data[self._cols_to_scale[i]].isna(),
                    weight_columns[i],
                ] = self._data[self._weight_column]

            # Update the aggregation recipes to aggregate those new weight columns
            self._agg_dict.update(
                dict(
                    zip(
                        weight_columns,
                        [lambda x: x.sum(min_count=1)] * len(weight_columns),
                    )
                )
            )

        self._data = Aggregator(self._data, self._group_by, self._agg_dict).aggregate()
        self._data = WeightNormaliser(
            data=self._data,
            weight_column=weight_columns,
            cols_to_normalise=self._cols_to_normalise,
        ).process()

        # If we created new weight columns for aggregation, let's remove them.
        if self.ignore_na:
            self._data.drop(weight_columns, axis=1, inplace=True)

        return self._data
