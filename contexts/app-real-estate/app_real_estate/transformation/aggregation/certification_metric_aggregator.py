import pandas as pd
import numpy as np

import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.building_certification_columns as bc
from app_real_estate.models.output_models.building_certification_metrics_models import (
    BC_ResponseID_Brand_Scheme_Model,
    BC_ResponseID_Brand_Model,
    BC_ResponseID_Model,
    BC_ResponseID_PropertySector_Country_Brand_Scheme_Model,
    BC_ResponseID_PropertySector_Country_Brand_Model,
    BC_ResponseID_PropertySector_Country_Model,
)
from app_real_estate.transformation.aggregation.certification_aggregation_utils import (
    CertificationAggregationUtils,
)


class CertificationMetricAggregator:
    """
    `CertificationMetricAggregator` provides a series of functions to aggregate the metrics
    related to certifications. It enables aggregation at multiple levels using different data sources:
        - the certification data
        - the R1 table from the survey

    More documentation is provided here: https://app.clickup.com/24594449/v/dc/qej0h-8012/qej0h-33772
    """

    def __init__(
        self,
        certification_data: pd.DataFrame,
        r1_table: pd.DataFrame,
        portfolio_level: bool = False,
        property_type_sector_mapping: pd.DataFrame | None = None,
    ):
        if not portfolio_level and property_type_sector_mapping is None:
            raise ValueError(
                "property_type_sector_mapping is required if not used on portfolio_level"
            )
        self._certification_data = certification_data
        self._r1_table = r1_table
        self._property_type_sector_mapping = property_type_sector_mapping
        self._merge_columns = [] if portfolio_level else ["COUNTRY", "PRT_SECTOR"]

        self._id_col = ["RESPONSE_ID"]

        # Whether it's at the portfolio or property subtype <> country level,
        # the BC tables in the Benchmark Reports contain 3 levels of
        # aggregation defined below.
        self._level_1 = ["TYPE"] + self._merge_columns + ["BRAND", "SCHEME_LEVEL", "ID"]
        self._level_2 = ["TYPE"] + self._merge_columns + ["BRAND"]
        self._level_3 = ["TYPE"] + self._merge_columns

        # Set up an instance of the class giving helper functions for aggregation
        self._aggregator_utils = CertificationAggregationUtils(
            self._merge_columns, self._r1_table, self._property_type_sector_mapping
        )

        if portfolio_level:
            self._level1_schema = BC_ResponseID_Brand_Scheme_Model.to_schema()
            self._level2_schema = BC_ResponseID_Brand_Model.to_schema()
            self._level3_schema = BC_ResponseID_Model.to_schema()
        else:
            self._level1_schema = (
                BC_ResponseID_PropertySector_Country_Brand_Scheme_Model.to_schema()
            )
            self._level2_schema = (
                BC_ResponseID_PropertySector_Country_Brand_Model.to_schema()
            )
            self._level3_schema = BC_ResponseID_PropertySector_Country_Model.to_schema()

        # Prepare certifications data
        self._certification_data = self.prepare_certification_data(
            self._certification_data
        )

    def aggregate(self) -> tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        Prepare the certifications data metrics, then aggregate at various levels.
        Add the benchmark metrics if the level is not portfolio.

        Returns:
            tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]: the three dataframes
            representing the three levels of aggregation, scheme/outcome level,
            brand-level and "total" level.
        """
        certs_scheme_level = self.aggregate_certification_metrics_per_level(
            self._level_1
        )
        certs_brand_level = self.aggregate_certification_metrics_per_level(
            self._level_2
        )
        certs_total_level = self.aggregate_certification_metrics_per_level(
            self._level_3
        )
        self.validate_aggregation_outputs(
            certs_scheme_level, certs_brand_level, certs_total_level
        )

        return (
            certs_scheme_level,
            certs_brand_level,
            certs_total_level,
        )

    @staticmethod
    def prepare_certification_data(certification_data: pd.DataFrame) -> pd.DataFrame:
        """
        Prepare certification data for aggregation. Renames variables,
        filters the mock certifications and adds the scheme/level information.

        Parameters:
            certification_data (pd.DataFrame): the yearly certification data

        Returns:
            pd.DataFrame: the cleaned certification data.
        """
        if certification_data.empty:
            raise ValueError("Empty certification data.")

        # Filter out the 'fake' certifications created for benchmarking.
        certification_data = certification_data[
            certification_data[bc.scoring_coverage] > 0
        ].copy()

        certification_data[ac.asset_size_owned_m2] = (
            certification_data[bc.asset_size_m2]
            * certification_data[bc.asset_ownership]
            / 100
        )

        # Create a column combining the names of the scheme and outcomes for display purposes.
        certification_data["SCHEME_LEVEL"] = np.where(
            certification_data[bc.outcome].isna(),
            certification_data[bc.scheme],
            certification_data[bc.scheme] + " | " + certification_data[bc.outcome],
        )

        # Rename the variables so they fit the upper-case format expected.
        certification_data.rename(
            columns={
                bc.response_id: "RESPONSE_ID",
                bc.brand: "BRAND",
                bc.scheme: "SCHEME",
                bc.outcome: "LEVEL",
                bc.certification_id: "ID",
                ac.property_sector: "PRT_SECTOR",
                ac.property_type_code: "PRT_TYPE",
                ac.country: "COUNTRY",
                bc.type: "TYPE",
                bc.owned_covered_floor_area: "COV",
            },
            inplace=True,
        )
        certification_data["TYPE"] = np.where(
            certification_data["TYPE"] == "operational", "OPR", "NC"
        )
        certification_data["ID"] = certification_data["ID"].astype(int)

        return certification_data[
            [
                "RESPONSE_ID",
                "TYPE",
                "COUNTRY",
                "PRT_SECTOR",
                "PRT_TYPE",
                "ID",
                "BRAND",
                "SCHEME",
                "LEVEL",
                "SCHEME_LEVEL",
                bc.portfolio_asset_id,
                "COV",
                ac.asset_size_owned_m2,
            ]
        ]

    def aggregate_certification_metrics_per_level(
        self, level: list[str]
    ) -> pd.DataFrame:
        """
        Aggregate the certification metrics from various sources at the
        given level. The function calculates then merges the different
        metrics one by one.
        Parameters:
            level (list[str]): list of columns to group by.

        Returns:
            pd.DataFrame: aggregated certification metrics dataframe.
        """
        # Initialise the output with the percent of floor area certified
        aggregated_certs = self._aggregator_utils.add_certified_area_percent(
            self._certification_data, level
        )

        # Add the certified asset count to the output
        aggregated_certs = aggregated_certs.merge(
            self.add_certified_asset_count(self._certification_data, level),
            on=self._id_col + level,
            how="left",
        )
        aggregated_certs["CERTIFIED_AST_COUNT"] = (
            aggregated_certs["CERTIFIED_AST_COUNT"].fillna(0).astype(int)
        )

        # If we are computing the totals at the sector/country level, we can add the benchmark coverage.
        if level == self._level_3 and self._merge_columns:
            aggregated_certs = aggregated_certs.merge(
                self._aggregator_utils.add_benchmark_certified_area_percentage(
                    aggregated_certs, level
                ),
            )

        return aggregated_certs

    def add_certified_asset_count(
        self,
        certification_data: pd.DataFrame,
        level: list[str],
    ) -> pd.DataFrame:
        """
        Calculate the number of assets in the bucket that have at least
        one building certification.

        Parameters:
            certification_data (pd.DataFrame): the table of assets' building certifications.
            level (list[str]): list of columns to group by.

        Returns:
            pd.DataFrame: dataframe containing the number of certified assets.
        """
        agg_dict = {bc.portfolio_asset_id: lambda x: len(np.unique(x))}
        groupby = self._id_col + level
        output_metric_names = ["CERTIFIED_AST_COUNT"]
        return (
            CertificationAggregationUtils.aggregate_certification_or_rating_data_metric(
                certification_data, groupby, agg_dict, output_metric_names
            )
        )

    def validate_aggregation_outputs(
        self,
        level1_output: pd.DataFrame,
        level2_output: pd.DataFrame,
        level3_output: pd.DataFrame,
    ) -> None:
        """
        Validate the outputs of the `aggregate` function based on the column_names determined during construction.
        Parameters:
            level1_output (pd.DataFrame): the brand/scheme level output of the aggregation process.
            level2_output (pd.DataFrame): the brand level output of the aggregation process.
            level3_output (pd.DataFrame): the "total"-level output of the aggregation process.
        """
        self._level1_schema.validate(level1_output)
        self._level2_schema.validate(level2_output)
        self._level3_schema.validate(level3_output)
