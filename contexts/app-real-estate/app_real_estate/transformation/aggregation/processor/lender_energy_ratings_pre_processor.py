import pandas as pd
from app_real_estate.transformation.aggregation.processor.iprocessor import IProcessor
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.building_certification_columns as bc
from app_real_estate.transformation.metric_calculation.common_operations import (
    CommonOperations,
)


class LenderEnergyRatingsPreProcessor(IProcessor):
    """
    Preprocessor for energy ratings data in lender asset aggregation.

    This processor handles:
    - Survey year filtering
    - Asset data merging via injected merger
    - Coverage calculation (using size column or calculated values)
    - Data filtering
    - R1 table validation via injected merger
    """

    def __init__(
        self,
        energy_ratings_data: pd.DataFrame,
        asset_level_data: pd.DataFrame,
        r1_table: pd.DataFrame,
        survey_year: int,
        asset_merger_class=None,
        r1_merger_class=None,
    ):
        """
        Initialize the energy ratings preprocessor with dependency injection.

        Args:
            energy_ratings_data: Raw energy ratings data
            asset_level_data: Asset level data for merging
            r1_table: R1 table for validation
            survey_year: Survey year to filter by
            asset_merger_class: Class for asset data merging (injected dependency)
            r1_merger_class: Class for R1 table validation (injected dependency)
        """
        self.energy_ratings_data = energy_ratings_data
        self.asset_level_data = asset_level_data
        self.r1_table = r1_table
        self.survey_year = survey_year

        # Use injected classes or default imports
        if asset_merger_class is None:
            from app_real_estate.transformation.aggregation.merger.lender_asset_data_merger import (
                LenderAssetDataMerger,
            )

            self.asset_merger_class = LenderAssetDataMerger
        else:
            self.asset_merger_class = asset_merger_class

        if r1_merger_class is None:
            from app_real_estate.transformation.aggregation.merger.lender_r1_table_merger import (
                LenderR1TableMerger,
            )

            self.r1_merger_class = LenderR1TableMerger
        else:
            self.r1_merger_class = r1_merger_class

    def validate(self) -> pd.DataFrame:
        """
        Validate and prepare energy ratings data for processing.

        Returns:
            Validated DataFrame ready for processing

        Raises:
            ValueError: If required columns are missing or no data found
        """
        # Check required columns exist
        required_columns = [ac.survey_year, ac.portfolio_asset_id]
        missing_columns = [
            col
            for col in required_columns
            if col not in self.energy_ratings_data.columns
        ]
        if missing_columns:
            raise ValueError(
                f"Energy ratings data missing required columns: {missing_columns}"
            )

        # Filter by survey year
        data_cy = self.energy_ratings_data[
            self.energy_ratings_data[ac.survey_year] == self.survey_year
        ].copy()

        if len(data_cy) == 0:
            raise ValueError(
                f"No energy ratings data found for survey year {self.survey_year}"
            )

        # Energy ratings don't have a separate year column to filter on
        return data_cy

    def process(self) -> pd.DataFrame:
        """
        Execute the full energy ratings preprocessing pipeline.

        Returns:
            Fully processed energy ratings data ready for aggregation
        """
        # Validate and prepare data
        validated_data = self.validate()

        # Create and use asset merger
        asset_merger = self.asset_merger_class(
            self.asset_level_data, validated_data, "energy_ratings"
        )
        merged_data = asset_merger.merge()

        merged_data = CommonOperations.rename_covered_floor_area_column(merged_data)
        merged_data = CommonOperations.add_covered_floor_area_m2(
            merged_data, self.asset_level_data
        )

        # Filter out rows with NaN values in required columns
        required_cols = [
            bc.covered_floor_area_m2,
            ac.property_sector,
            ac.property_type_code,
            ac.country,
        ]

        print(f"Before filtering (energy_ratings): {len(merged_data)} rows")
        merged_data = merged_data.dropna(subset=required_cols)
        print(f"After filtering (energy_ratings): {len(merged_data)} rows")

        # Create and use R1 merger for validation
        r1_merger = self.r1_merger_class(self.r1_table, merged_data, "energy_ratings")
        validated_data = r1_merger.merge()

        return validated_data
