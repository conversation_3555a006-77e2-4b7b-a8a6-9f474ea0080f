import pandas as pd
import numpy as np
from app_real_estate.transformation.aggregation.processor.iprocessor import (
    IProcessor,
)
import app_real_estate.constants.column_names.asset_characteristics_columns as asc
from pandantic import BaseModel


class MetricScorePreProcessor(IProcessor):
    """pre processor that is used to weight the score and metric data by the (%ownership x floor area),
    this class should not be instantiated directly, new column asset_size_owned_m2 is added to the dataframe
    """

    _data: pd.DataFrame = None
    _base_data_model: BaseModel = None
    _scale_metric_score_cols: list[str]

    asset_size_owned_m2 = "asset_size_owned_m2"
    """new column added to the dataframe"""

    @property
    def data(self):
        return self._data

    @data.setter
    def data(self, data: pd.DataFrame):
        self._data = data

    @property
    def base_data_model(self):
        return self._base_data_model

    @base_data_model.setter
    def base_data_model(self, base_data_model: BaseModel):
        self._base_data_model = base_data_model

    @property
    def scale_metric_score_cols(self):
        return self._scale_metric_score_cols

    @scale_metric_score_cols.setter
    def scale_metric_score_cols(self, cols: list[str]):
        self._scale_metric_score_cols = cols

    def column_validation(self) -> None:
        if not (all([c in self.data.columns for c in self.scale_metric_score_cols])):
            raise ValueError(f"Column {self.scale_metric_score_cols} not found in data")

    def add_area_ownership_weight_factor(self) -> pd.DataFrame:
        """
        Scale asset metrics by the fraction of ownership.
        where asset_size_owned_m2 = (%Ownership x floor area) column is introduced

        Raises:
            ValueError: If any of the columns in `cols` are not found in the data.
        """

        tmp_data = self.data.copy()
        fraction_ownership = tmp_data[asc.asset_ownership].values / 100
        weight = fraction_ownership * tmp_data[asc.asset_size_m2].values
        tmp_data[self.asset_size_owned_m2] = weight
        return tmp_data

    def floor_weight_metric_score(
        self, area_ownership_weight_factor_data: pd.DataFrame
    ) -> pd.DataFrame:
        area_ownership_weight_factor_data[self.scale_metric_score_cols] = (
            area_ownership_weight_factor_data[self.scale_metric_score_cols]
            * area_ownership_weight_factor_data[self.asset_size_owned_m2].values[
                :, np.newaxis
            ]
        )

        return area_ownership_weight_factor_data

    def preprocess(self) -> pd.DataFrame:
        # check if data is not none and base_data_model is not none
        # we can also create factory interface but lets save that for later
        if any(
            v is None
            for v in [
                self.data,
                self.base_data_model,
                self.scale_metric_score_cols,
            ]
        ):
            raise AttributeError(
                "data, base_data_model, scale_metric_score_cols must be set"
            )

        self.data = self.base_data_model.parse_df(dataframe=self.data, errors="raise")
        self.column_validation()
        area_ownership_weight_factor_data = self.add_area_ownership_weight_factor()
        return self.floor_weight_metric_score(area_ownership_weight_factor_data)
