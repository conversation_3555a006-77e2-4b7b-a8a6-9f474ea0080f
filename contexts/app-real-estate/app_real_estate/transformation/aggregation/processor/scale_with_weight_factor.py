import pandas as pd
from app_real_estate.transformation.aggregation.processor.iprocessor import (
    IProcessor,
)
from app_real_estate.transformation.aggregation.validator.validator import (
    Validator,
)
from typing import List
import numpy as np


class ScaleWithWeightFactor(IProcessor):
    """Scale the data by the floor weight factor"""

    def __init__(
        self, data: pd.DataFrame, weight_factor: str, cols_to_scale: List[str]
    ):
        self._data = data
        self._weight_factor = weight_factor  # name of the column
        self._cols_to_scale = cols_to_scale

    @property
    def get_added_columns(self):
        return None

    def remove_weight_factor_from_cols_to_scale(self):
        self._cols_to_scale = [
            col for col in self._cols_to_scale if col != self._weight_factor
        ]

    def validate(self):
        Validator.validate_missing_column(
            self._data, self._cols_to_scale + [self._weight_factor]
        )

    def scale_with_weight_factor(self):
        self._data[self._cols_to_scale] = (
            self._data[self._cols_to_scale].values
            * self._data[self._weight_factor].values[:, np.newaxis]
        )

    def process(self):
        self.remove_weight_factor_from_cols_to_scale()
        self.validate()
        self.scale_with_weight_factor()
        return self._data
