"""
Interface for Data Processors in Real Estate Analytics

This module defines the IProcessor abstract base class, which serves as an interface
for various data processing operations in real estate analytics. It provides a
common structure for data processors, ensuring consistency across different
implementations.

Classes:
    IProcessor: Abstract base class for data processors.

The IProcessor class defines three methods that all subclasses must implement:
    - get_added_columns: Specifies which columns are added by the processor.
    - validate: Performs data validation.
    - process: Executes the main processing logic.

Note:
    This class uses NotImplementedError to enforce method implementation in subclasses,
    which will raise an error at runtime if a method is not properly overridden.
"""

import pandas as pd
from typing import Protocol


class IProcessor(Protocol):
    """
    Abstract base class for data processors in real estate analytics.

    This class defines the interface for data processors, specifying methods
    that all processors should implement. It ensures a consistent structure
    across different types of data processors.

    Methods:
        validate: Performs data validation.
        process: Executes the main processing logic.

    All methods raise NotImplementedError and must be implemented by subclasses.
    """

    def validate(self) -> pd.DataFrame:
        """
        Perform validation on the data.

        This method should implement any necessary checks or validations
        on the input data before processing.

        Returns:
            pd.DataFrame: The validated DataFrame.

        Raises:
            NotImplementedError: If the subclass does not implement this method.
        """
        raise NotImplementedError("Subclass must implement this method")

    def process(self) -> pd.DataFrame:
        """
        Execute the main processing logic.

        This method should contain the core functionality of the processor,
        applying transformations or calculations to the data.

        Returns:
            pd.DataFrame: The processed DataFrame.

        Raises:
            NotImplementedError: If the subclass does not implement this method.
        """
        raise NotImplementedError("Subclass must implement this method")
