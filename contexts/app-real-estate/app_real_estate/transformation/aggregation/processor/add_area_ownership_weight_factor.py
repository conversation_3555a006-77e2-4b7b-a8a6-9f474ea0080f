import pandas as pd
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.asset_characteristics_columns as asc
from app_real_estate.transformation.aggregation.processor.iprocessor import (
    IProcessor,
)
from app_real_estate.transformation.aggregation.validator.validator import (
    Validator,
)


class AddAreaOwnershipWeightFactor(IProcessor):
    """
    The AddAreaOwnershipWeightFactor class is designed to add a new column,
    sc.asset_size_owned_m2, to a given DataFrame. This new column is calculated
    based on the ownership fraction and the size of the asset in square meters.
    """

    column_added = sc.asset_size_owned_m2

    def __init__(self, data: pd.DataFrame):
        self._data = data

    def get_added_columns(self):
        return self.column_added

    def add_area_ownership_weight_factor(self):
        """
        Calculates the ownership fraction by dividing the ownership percentage by 100
        Computes the weight by multiplying the ownership fraction by the asset size in square meters
        """

        fraction_ownership = self._data[asc.asset_ownership].div(100)
        weight = fraction_ownership * self._data[asc.asset_size_m2].values
        self._data[self.column_added] = weight
        return self._data

    def validate(self):
        Validator.validate_missing_column(
            self._data, [asc.asset_ownership, asc.asset_size_m2]
        )

    def process(self) -> pd.DataFrame:
        self.validate()
        return self.add_area_ownership_weight_factor()
