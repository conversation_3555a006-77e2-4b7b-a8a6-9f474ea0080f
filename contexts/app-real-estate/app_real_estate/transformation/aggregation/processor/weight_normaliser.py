import pandas as pd
from app_real_estate.transformation.aggregation.processor.iprocessor import (
    IProcessor,
)
from app_real_estate.transformation.aggregation.validator.validator import (
    Validator,
)


class WeightNormaliser(IProcessor):
    """
    Normalise columns by dividing them by weight column,
    Update the values of _cols_to_normalise with the normalised values.
    The parameter `weight_column` can be a list of columns. In this case, it should
    be of same size as the `cols_to_normalise` and each element of the latter will
    be normalised by the corresponding element of the former.
    """

    def __init__(
        self,
        data: pd.DataFrame,
        weight_column: str | list[str],
        cols_to_normalise: list[str],
        normalised_cols_names: list[str] = None,
        coefficient: list[int] = None,
    ):
        self._data = data
        self._weight_column = weight_column
        self._cols_to_normalise = cols_to_normalise
        self._normalised_cols_names = (
            cols_to_normalise
            if normalised_cols_names is None
            else normalised_cols_names
        )
        self._coefficient = coefficient

    @property
    def get_added_columns(self):
        return None

    def validate(self):
        weight_columns = (
            [self._weight_column]
            if isinstance(self._weight_column, str)
            else self._weight_column
        )
        # Validate that the columns are in the data,
        # we make sure to remove `None` columns from `weight_columns`.
        Validator.validate_missing_column(
            self._data,
            self._cols_to_normalise
            + [col for col in weight_columns if not col is None],
        )
        if len(weight_columns) > 1 and len(self._cols_to_normalise) != len(
            weight_columns
        ):
            raise ValueError(
                "If a list of weight columns is specified, it should be the same size as `cols_to_normalise`."
            )

    def normalise(self):
        coeff = 1 if not self._coefficient else self._coefficient
        if isinstance(self._weight_column, str):
            self._data[self._normalised_cols_names] = (
                self._data[self._cols_to_normalise]
                .div(self._data[self._weight_column], axis=0)
                .mul(coeff)
            )
        else:
            coeff = [1] * len(self._cols_to_normalise) if coeff == 1 else coeff
            for i in range(len(self._cols_to_normalise)):
                weights = (
                    self._data[self._weight_column[i]] if self._weight_column[i] else 1
                )
                self._data[self._normalised_cols_names[i]] = (
                    self._data[self._cols_to_normalise[i]]
                    .div(weights, axis=0)
                    .mul(coeff[i])
                )

    def process(self):
        self.validate()
        self.normalise()
        return self._data
