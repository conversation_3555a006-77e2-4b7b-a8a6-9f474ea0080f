import pandas as pd
from app_real_estate.transformation.aggregation.processor.iprocessor import IProcessor
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.building_certification_columns as bc
from app_real_estate.transformation.metric_calculation.building_certification_calculations import (
    BuildingCertificationsCalculations,
)
from app_real_estate.transformation.metric_calculation.common_operations import (
    CommonOperations,
)


class LenderCertificationsPreProcessor(IProcessor):
    """
    Preprocessor for certification data in lender asset aggregation.
    """

    def __init__(
        self,
        certification_data: pd.DataFrame,
        asset_level_data: pd.DataFrame,
        r1_table: pd.DataFrame,
        survey_year: int,
        asset_merger_class=None,
        r1_merger_class=None,
    ):
        """
        Initialize the certifications preprocessor with dependency injection.

        Args:
            certification_data: Raw certification data
            asset_level_data: Asset level data for merging
            r1_table: R1 table for validation
            survey_year: Survey year to filter by
            asset_merger_class: Class for asset data merging (injected dependency)
            r1_merger_class: Class for R1 table validation (injected dependency)
        """
        self.certification_data = certification_data
        self.asset_level_data = asset_level_data
        self.r1_table = r1_table
        self.survey_year = survey_year

        # Use injected classes or default imports
        if asset_merger_class is None:
            from app_real_estate.transformation.aggregation.merger.lender_asset_data_merger import (
                LenderAssetDataMerger,
            )

            self.asset_merger_class = LenderAssetDataMerger
        else:
            self.asset_merger_class = asset_merger_class

        if r1_merger_class is None:
            from app_real_estate.transformation.aggregation.merger.lender_r1_table_merger import (
                LenderR1TableMerger,
            )

            self.r1_merger_class = LenderR1TableMerger
        else:
            self.r1_merger_class = r1_merger_class

    def validate(self) -> pd.DataFrame:
        """
        Validate and prepare certification data for processing.
        """
        # Check required columns exist
        required_columns = [ac.survey_year, bc.year, ac.portfolio_asset_id]
        missing_columns = [
            col
            for col in required_columns
            if col not in self.certification_data.columns
        ]
        if missing_columns:
            raise ValueError(
                f"Certification data missing required columns: {missing_columns}"
            )

        # Filter by survey year
        data_cy = self.certification_data[
            self.certification_data[bc.survey_year] == self.survey_year
        ].copy()

        if len(data_cy) == 0:
            raise ValueError(
                f"No certification data found for survey year {self.survey_year}"
            )

        # Certification data has a 'year' column that needs filtering
        data_cy = data_cy.dropna(subset=[bc.year])

        if len(data_cy) == 0:
            raise ValueError(
                "No certification data remaining after filtering out missing years"
            )

        # Convert year to int64
        data_cy[bc.year] = data_cy[bc.year].astype("int64")

        return data_cy

    def process(self) -> pd.DataFrame:
        """
        Execute the full certification preprocessing pipeline.
        """
        # Validate and prepare data
        validated_data = self.validate()

        # Create and use asset merger
        asset_merger = self.asset_merger_class(
            self.asset_level_data, validated_data, "certification"
        )
        merged_data = asset_merger.merge()

        merged_data = CommonOperations.rename_covered_floor_area_column(merged_data)

        merged_data = BuildingCertificationsCalculations.add_owned_certified_area(
            merged_data
        )

        # Filter out rows with NaN values in required columns
        required_cols = [
            bc.owned_covered_floor_area,
            ac.property_sector,
            ac.property_type_code,
            ac.country,
        ]

        merged_data = merged_data.dropna(subset=required_cols)

        # Create and use R1 merger for validation
        r1_merger = self.r1_merger_class(self.r1_table, merged_data, "certification")
        validated_data = r1_merger.merge()

        return validated_data
