import pandas as pd
import numpy as np
from app_real_estate.transformation.aggregation.processor.iprocessor import (
    IProcessor,
)
import app_real_estate.constants.column_names.asset_characteristics_columns as asc
import app_real_estate.constants.aggregation_recipes.asset_metric_aggregation_recipe as amad
from app_real_estate.models.asset_metric_data import AssetMetricData


class AssetMetricProcessor(IProcessor):
    _scale_asset_metrics_cols = list(amad.ASSET_METRIC_AGGREGATION_RECIPE)

    def __init__(self, asset_metric_data: pd.DataFrame):
        self.asset_metric_data = AssetMetricData.parse_df(
            dataframe=asset_metric_data, errors="raise"
        )

    def column_validation(self) -> None:
        if not (
            all(
                [
                    c in self.asset_metric_data.columns
                    for c in self._scale_asset_metrics_cols
                ]
            )
        ):
            raise ValueError(
                f"Column {self._scale_asset_metrics_cols} not found in asset_metric_data"
            )

    def scale_asset_metrics(self) -> pd.DataFrame:
        """
        Scale asset metrics by the fraction of ownership.

        Raises:
            ValueError: If any of the columns in `cols` are not found in the data.
        """

        fraction_ownership = self.asset_metric_data[asc.asset_ownership].div(100)
        self.asset_metric_data[self._scale_asset_metrics_cols] = (
            self.asset_metric_data[self._scale_asset_metrics_cols].values
            * fraction_ownership[:, np.newaxis]
        )
        return self.asset_metric_data

    def preprocess(self) -> pd.DataFrame:
        self.column_validation()
        self.scale_asset_metrics()
        return self.asset_metric_data
