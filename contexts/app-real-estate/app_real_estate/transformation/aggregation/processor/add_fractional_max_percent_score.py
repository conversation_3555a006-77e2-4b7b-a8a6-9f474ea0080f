import pandas as pd
from app_real_estate.transformation.aggregation.processor.iprocessor import (
    IProcessor,
)
from app_real_estate.transformation.aggregation.validator.validator import (
    Validator,
)


class Add_Fractional_Max_Percent_Score(IProcessor):
    """
    The Add_Fractional_Max_Percent_Score processor is used to add fractional scores to the aggregated score.
    """

    _fraction = "_fraction"
    _absolute = "_absolute"
    _percent = "_percent"
    _max = "_max"

    def __init__(
        self,
        data: pd.DataFrame,
        max_score: float,
        score_column: str,
        is_fractional: bool = False,
    ):
        """
        Initialize the Add_Fractional_Max_Percent_Score processor.

        Args:
            data (pd.DataFrame): The data to process.
            max_score (float): The maximum score.
            score_column (str): The column to add the fractional score to.
        """
        self._data = data
        self._score_column = score_column
        self._max_score = max_score
        self._absolute_score_var = (
            f"{self._score_column}{self._absolute}" if is_fractional else score_column
        )
        self._fractional_score_var = (
            f"{self._score_column}{self._fraction}"
            if not is_fractional
            else score_column
        )
        self._percent_score_var = f"{self._score_column}{self._percent}"
        self._max_score_var = f"{self._score_column}{self._max}"
        self._is_fractional = is_fractional

    def get_added_columns(self):
        return [self._max_score, self._fractional_score_var]

    def validate(self) -> None:
        Validator.validate_missing_column(self._data, [self._score_column])

    def add_fractional_score(self) -> pd.DataFrame:
        """
        Add the fractional score to the aggregated score.
        """
        self._data[self._fractional_score_var] = self._data[self._score_column].div(
            self._max_score
        )
        return self._data

    def add_absolute_score(self) -> pd.DataFrame:
        """
        Add the absolute score to the aggregated score.
        """
        self._data[self._absolute_score_var] = self._data[self._score_column].mul(
            self._max_score
        )
        return self._data

    def add_percent_score(self) -> pd.DataFrame:
        """
        Add the percent score to the aggregated score.
        """
        self._data[self._percent_score_var] = self._data[
            self._fractional_score_var
        ].mul(100)
        return self._data

    def add_max_score(self) -> pd.DataFrame:
        """
        Add the max score to the aggregated score.
        """
        self._data[self._max_score_var] = self._max_score
        return self._data

    def process(self):
        self.validate()
        self._data = self.add_max_score()

        if self._is_fractional:
            self._data = self.add_absolute_score()
        else:
            self._data = self.add_fractional_score()

        self._data = self.add_percent_score()
        return self._data
