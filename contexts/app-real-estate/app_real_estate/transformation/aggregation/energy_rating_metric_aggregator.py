import pandas as pd
import numpy as np

from app_real_estate.models.output_models.energy_rating_metrics_models import (
    ER_ResponseID_PropertySector_Country_Brand_Model,
    ER_ResponseID_PropertySector_Country_Model,
    ER_ResponseID_Brand_Model,
    ER_ResponseID_Model,
)
from app_real_estate.transformation.aggregation.certification_aggregation_utils import (
    CertificationAggregationUtils,
)
import app_real_estate.constants.column_names.energy_rating_columns as erc
import app_real_estate.constants.column_names.asset_characteristics_columns as ac


class EnergyRatingMetricAggregator:
    """
    `EnergyRatingMetricAggregator` provides a series of functions to aggregate the metrics
    related to energy ratings. It enables aggregation at multiple levels using different data sources:
        - the energy ratings data
        - the R1 table from the survey

    More documentation is provided here: https://app.clickup.com/24594449/v/dc/qej0h-8012/qej0h-33772
    """

    def __init__(
        self,
        energy_ratings_data: pd.DataFrame,
        r1_table: pd.DataFrame,
        portfolio_level: bool = False,
        property_type_sector_mapping: pd.DataFrame | None = None,
    ):
        if not portfolio_level and property_type_sector_mapping is None:
            raise ValueError(
                "property_type_sector_mapping is required if not used on portfolio_level"
            )
        self._energy_rating_data = energy_ratings_data
        self._r1_table = r1_table
        self._property_type_sector_mapping = property_type_sector_mapping
        self._merge_columns = [] if portfolio_level else ["COUNTRY", "PRT_SECTOR"]

        self._id_col = ["RESPONSE_ID"]

        # Whether it's at the portfolio or property subtype <> country level,
        # the BC tables in the Benchmark Reports contain 3 levels of
        # aggregation defined below.
        self._level_1 = ["TYPE"] + self._merge_columns + ["NAME", "ID"]
        self._level_2 = ["TYPE"] + self._merge_columns

        # Set up an instance of the class giving helper functions for aggregation
        self._aggregator_utils = CertificationAggregationUtils(
            self._merge_columns, self._r1_table, self._property_type_sector_mapping
        )

        if portfolio_level:
            self._level1_schema = ER_ResponseID_Brand_Model.to_schema()
            self._level2_schema = ER_ResponseID_Model.to_schema()
        else:
            self._level1_schema = (
                ER_ResponseID_PropertySector_Country_Brand_Model.to_schema()
            )
            self._level2_schema = ER_ResponseID_PropertySector_Country_Model.to_schema()

        # Prepare certifications data
        self._energy_rating_data = self.prepare_energy_ratings_data(
            self._energy_rating_data
        )

    def aggregate(self):
        """
        Prepare the certifications data metrics, then aggregate at various levels.
        Add the benchmark metrics if the level is not portfolio.
        """
        agg_certs_brand = self.aggregate_energy_rating_metrics_per_level(self._level_1)
        agg_certs_total = self.aggregate_energy_rating_metrics_per_level(self._level_2)

        self.validate_aggregation_outputs(agg_certs_brand, agg_certs_total)

        return agg_certs_brand, agg_certs_total

    @staticmethod
    def prepare_energy_ratings_data(energy_rating_data: pd.DataFrame) -> pd.DataFrame:
        """
        Prepare energy ratings data for aggregation.
        Parameters:
            energy_rating_data (pd.DataFrame): Energy ratings yearly data

        Returns:
            pd.DataFrame: the cleaned performance energy ratings data from the survey.
        """
        if energy_rating_data.empty:
            raise ValueError("Empty energy ratings data.")

        energy_rating_data[ac.asset_size_owned_m2] = (
            energy_rating_data[erc.asset_size_m2]
            * energy_rating_data[erc.asset_ownership]
            / 100
        )
        energy_rating_data["COV"] = (
            energy_rating_data[erc.covered_floor_area_m2]
            * energy_rating_data[erc.asset_ownership]
            / 100
        )

        energy_rating_data = energy_rating_data.rename(
            columns={
                erc.response_id: "RESPONSE_ID",
                erc.energy_rating_id: "ID",
                erc.energy_rating_name: "NAME",
                ac.property_sector: "PRT_SECTOR",
                ac.property_type_code: "PRT_TYPE",
                ac.country: "COUNTRY",
            },
        )
        energy_rating_data["TYPE"] = "EN"

        return energy_rating_data[
            [
                "RESPONSE_ID",
                "TYPE",
                "COUNTRY",
                "PRT_SECTOR",
                "PRT_TYPE",
                "ID",
                "NAME",
                erc.portfolio_asset_id,
                "COV",
                ac.asset_size_owned_m2,
            ]
        ]

    def aggregate_energy_rating_metrics_per_level(
        self, level: list[str]
    ) -> pd.DataFrame:
        """
        Aggregate the energy_rating metrics from various sources at the
        given level. The function calculates then merges the different
        metrics one by one.
        Parameters:
            level (list[str]): list of columns to group by.

        Returns:
            pd.DataFrame: aggregated energy_rating metrics dataframe.
        """
        # Initialise the output with the percent of floor area certified
        aggregated_certs = self._aggregator_utils.add_certified_area_percent(
            self._energy_rating_data, level
        )

        # Add the rated asset count to the output
        aggregated_certs = aggregated_certs.merge(
            self.add_rated_asset_count(self._energy_rating_data, level),
            on=self._id_col + level,
            how="left",
        )
        aggregated_certs["RATED_AST_COUNT"] = (
            aggregated_certs["RATED_AST_COUNT"].fillna(0).astype(int)
        )

        # If we are computing the totals at the sector/country level, we can add the benchmark coverage.
        if level == self._level_2 and self._merge_columns:
            aggregated_certs = aggregated_certs.merge(
                self._aggregator_utils.add_benchmark_certified_area_percentage(
                    aggregated_certs, level
                ),
            )

        return aggregated_certs

    def add_rated_asset_count(
        self,
        energy_rating_data: pd.DataFrame,
        level: list[str],
    ) -> pd.DataFrame:
        agg_dict = {ac.portfolio_asset_id: lambda x: len(np.unique(x))}
        groupby = self._id_col + level
        output_metric_names = ["RATED_AST_COUNT"]
        return (
            CertificationAggregationUtils.aggregate_certification_or_rating_data_metric(
                energy_rating_data, groupby, agg_dict, output_metric_names
            )
        )

    def validate_aggregation_outputs(
        self,
        level1_output: pd.DataFrame,
        level2_output: pd.DataFrame,
    ) -> None:
        """
        Validate the outputs of the `aggregate` function based on the column_names determined during construction.
        Parameters:
            level1_output (pd.DataFrame): the brand/scheme level output of the aggregation process.
            level2_output (pd.DataFrame): the brand level output of the aggregation process.
        """
        self._level1_schema.validate(level1_output)
        self._level2_schema.validate(level2_output)
