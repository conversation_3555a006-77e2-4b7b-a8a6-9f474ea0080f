"""
Lender Metric Aggregation Pipeline for Lender Real Estate Analytics

This module defines the LenderMetricAggregationPipeline class, which orchestrates
the process of calculating and aggregating various metrics for real estate
assets across different levels (asset, property-country, property, and portfolio).
This pipeline is used for lender assessment.

The pipeline performs a series of calculations on asset data, including
vacancy rates, asset sizes, recycled water percentages, renewable energy
percentages, and intensity metrics for various utilities. It then aggregates
these metrics to higher levels and merges the results with original scores.

Classes:
    LenderMetricAggregationPipeline: Manages the metric calculation and aggregation process.

Dependencies:
    - LenderAssetMetricAggregator: For aggregating metrics across different levels.
    - AssetCalculations: For performing various calculations on asset data. (r1 without pgav)
    - AssetFilters: For filtering asset data.
    - ScoringModel: For merging filtered scores with data.

Note:
    This pipeline is a crucial component in processing and analyzing real estate
    asset data, providing a comprehensive view of various performance metrics
    across different organizational levels.
"""

import pandas as pd
from app_real_estate.transformation.metric_calculation.consumption_calculations import (
    ConsumptionCalculations,
)
from app_real_estate.transformation.metric_calculation.asset_filters import (
    AssetFilters,
)
from app_real_estate.transformation.metric_calculation.lender_intensity_calculations import (
    LenderIntensityCalculations,
)
from app_real_estate.transformation.metric_calculation.lender_coverage_calcualtions import (
    LenderCoverageCalculations,
)
from app_real_estate.transformation.metric_calculation.sustainable_utility_calculations import (
    SustainableUtilityCalculations,
)
from app_real_estate.constants.helper_enumerators import Utility
from app_real_estate.transformation.metric_calculation.lender_asset_characteristics_calculation import (
    LenderAssetCharacteristicsCalculations,
)
from app_real_estate.transformation.scoring_model import ScoringModel
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
from app_real_estate.transformation.aggregation.lender_metric_aggregator import (
    LenderAssetMetricAggregator,
)
from app_real_estate.transformation.aggregation.processor.add_area_ownership_weight_factor import (
    AddAreaOwnershipWeightFactor,
)
import app_real_estate.constants.common_aggregation_keys as agg_keys


class LenderMetricAggregationPipeline:
    """
    Manages the metric calculation and aggregation process for lender assessment.
    """

    @staticmethod
    def process_asset_data(
        asset_data: pd.DataFrame,
    ):
        """
        Execute the asset metric aggregation pipeline.
        """
        iscyop = AssetFilters.filter_current_year_operational_assets(asset_data)
        asset_data_cy = asset_data[iscyop]

        asset_data_cy = LenderAssetCharacteristicsCalculations().add_asset_characteristic_calculations(
            asset_data_cy
        )

        asset_data_cy = (
            SustainableUtilityCalculations.add_renewable_generation_per_area(
                asset_data_cy
            )
        )

        asset_data_cy = (
            ConsumptionCalculations.add_consumed_renewable_energy_consumption(
                asset_data_cy
            )
        )

        asset_data_cy = SustainableUtilityCalculations.add_renewable_energy_rate(
            asset_data_cy
        )

        asset_data_cy = SustainableUtilityCalculations.add_previous_year_sustainable_utility_rates_per_utility(
            asset_data_cy, Utility.Energy
        )

        asset_data_cy = ConsumptionCalculations.add_energy_consumption_per_energy_type(
            asset_data_cy
        )

        asset_data_cy = ConsumptionCalculations.add_net_ghg_emissions(asset_data_cy)

        asset_data_cy = (
            LenderIntensityCalculations().add_vacancy_rate_for_intensity_assets(
                asset_data_cy
            )
        )

        asset_data_cy = LenderIntensityCalculations().add_intensity_per_utility(
            asset_data_cy, Utility.Energy
        )

        asset_data_cy = LenderIntensityCalculations().add_intensity_per_utility(
            asset_data_cy, Utility.GHG
        )

        asset_data_cy = (
            LenderIntensityCalculations().add_asset_size_for_intensity_assets(
                asset_data_cy
            )
        )

        asset_data_cy = (
            LenderIntensityCalculations().add_intensity_columns_without_outliers(
                asset_data_cy
            )
        )

        asset_data_cy = (
            LenderCoverageCalculations().add_owned_area_time_weight_per_control(
                asset_data_cy
            )
        )

        asset_data_cy = (
            LenderCoverageCalculations().add_cross_control_area_time_weights(
                asset_data_cy
            )
        )

        asset_data_cy = LenderCoverageCalculations().add_coverage_weighted_per_control(
            asset_data_cy
        )

        asset_data_cy = LenderIntensityCalculations().add_int_eligibility(asset_data_cy)

        asset_data_cy = ConsumptionCalculations.add_mwh_energy_consumption(
            asset_data_cy
        )

        asset_data_cy = ConsumptionCalculations.add_ghg_consumption_per_scope(
            asset_data_cy
        )

        asset_data = ScoringModel._merge_filtered_scores_with_data(
            asset_data_cy, asset_data, ac.portfolio_asset_id
        )

        asset_data = AddAreaOwnershipWeightFactor(asset_data).process()

        metrics_sector_ctr = LenderAssetMetricAggregator(
            asset_metric_data=asset_data,
            agg_keys=agg_keys.propertysector_country_responseid,
            add_benchmarks=False,
        ).aggregate()

        metrics_portfolio = (
            LenderAssetMetricAggregator(
                asset_metric_data=asset_data,
                agg_keys=agg_keys.portfolio_level,
            )
            .aggregate()
            .reset_index()
        )

        return metrics_sector_ctr, metrics_portfolio, asset_data
