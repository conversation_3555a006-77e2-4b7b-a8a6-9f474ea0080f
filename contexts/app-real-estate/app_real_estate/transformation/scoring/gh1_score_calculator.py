import pandas as pd

from app_real_estate.constants.helper_enumerators import Utility
from app_real_estate.transformation.scoring.common_indicator_score_utils import (
    CommonIndicatorScoreUtils,
)
from app_real_estate.transformation.scoring.benchmark_score_utils import (
    BenchmarkScoreUtils,
)
from app_real_estate.transformation.scoring.control_based_score_utils import (
    ControlBasedScoreUtils,
)
import app_real_estate.constants.column_names.benchmark_group_memberships_columns as mc
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.ghg_columns as gc


class GH1ScoreCalculator:
    """
    GH1ScoreCalculator calculates asset scores for the GH1 (GHG) indicator.

    This class provides methods to calculate various subscores and the overall
    score for the GH1 indicator, which measures GHG emissions in real estate assets.

    Key functionalities include:
    - Calculating data coverage scores
    - Evaluating Like-For-Like (LFL) GHG emissions changes
    - Combining subscores to produce an overall GH1 indicator score

    The class uses benchmark comparisons and weighted scoring methods to evaluate
    asset performance across multiple energy-related metrics.

    Usage:
        calculator = GH1ScoreCalculator()
        scored_data = calculator.calculate_asset_score(asset_data, memberships)

    Note: This class requires preprocessed asset data and benchmark group memberships
    as inputs.
    """

    def __init__(
        self,
        asset_score_utils: CommonIndicatorScoreUtils,
        benchmark_score_utils: BenchmarkScoreUtils,
        control_score_utils: ControlBasedScoreUtils,
        subscore_weights: dict[str, float],
    ) -> None:
        self.asset_score_utils = asset_score_utils
        self.benchmark_score_utils = benchmark_score_utils
        self.control_score_utils = control_score_utils
        self.subscore_weights = subscore_weights

    def calculate_asset_coverage_score(
        self, asset_data: pd.DataFrame, memberships: dict[str, pd.DataFrame]
    ) -> pd.DataFrame:
        """
        Calculate the data coverage subscore.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        (pd.DataFrame): the input DataFrame with the added subscore.
        """
        # Compare S12/S3 coverage against benchmark
        asset_data = self.benchmark_score_utils.calculate_benchmarked_subscore(
            asset_data,
            memberships.get(gc.ghg_area_time_cov_p_s12),
            mc.bench_ghg_area_time_cov_p_s12,
            mc.mean_ghg_area_time_cov_p_s12,
            sc.score_gh1_ghg_area_time_cov_p_s12,
        )
        asset_data = self.benchmark_score_utils.calculate_benchmarked_subscore(
            asset_data,
            memberships.get(gc.ghg_area_time_cov_p_s3),
            mc.bench_ghg_area_time_cov_p_s3,
            mc.mean_ghg_area_time_cov_p_s3,
            sc.score_gh1_ghg_area_time_cov_p_s3,
        )

        # Weighted sum of S12 and S3 for coverage
        asset_data[sc.score_gh1_ghg_area_time_cov_p] = (
            self.control_score_utils.sum_control_based_subscores(
                asset_data,
                sc.score_gh1_ghg_area_time_cov_p_s12,
                sc.score_gh1_ghg_area_time_cov_p_s3,
                False,
                Utility.GHG,
            )
        )

        return asset_data

    def calculate_asset_lfl_score(
        self, asset_data: pd.DataFrame, memberships: dict[str, pd.DataFrame]
    ) -> pd.DataFrame:
        """
        Calculate the Like-For-Like subscore.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        (pd.DataFrame): the input DataFrame with the added subscore.
        """
        # Score LFL performance
        asset_data = self.benchmark_score_utils.calculate_benchmarked_subscore(
            asset_data,
            memberships.get(gc.ghg_lfl_percent_change_s12),
            mc.bench_ghg_lfl_percent_change_s12,
            mc.mean_ghg_lfl_percent_change_s12,
            sc.score_gh1_ghg_lfl_percent_change_s12,
            is_lfl=True,
        )
        asset_data = self.benchmark_score_utils.calculate_benchmarked_subscore(
            asset_data,
            memberships.get(gc.ghg_lfl_percent_change_s3),
            mc.bench_ghg_lfl_percent_change_s3,
            mc.mean_ghg_lfl_percent_change_s3,
            sc.score_gh1_ghg_lfl_percent_change_s3,
            is_lfl=True,
        )
        # TODO: find better way to handle rejected outliers
        asset_data = self.asset_score_utils.set_lfl_rejected_outlier_scores_to_zero(
            asset_data,
            sc.score_gh1_ghg_lfl_percent_change_s12,
            gc.ghg_lfl_percent_change_s12,
            gc.ghg_lfl_outlier_status,
        )
        asset_data = self.asset_score_utils.set_lfl_rejected_outlier_scores_to_zero(
            asset_data,
            sc.score_gh1_ghg_lfl_percent_change_s3,
            gc.ghg_lfl_percent_change_s3,
            gc.ghg_lfl_outlier_status,
        )

        # Weighted sum of S12 and S3 for LFL
        asset_data[sc.score_gh1_ghg_lfl_percent_change] = (
            self.control_score_utils.sum_control_based_subscores(
                asset_data,
                sc.score_gh1_ghg_lfl_percent_change_s12,
                sc.score_gh1_ghg_lfl_percent_change_s3,
                True,
                Utility.GHG,
            )
        )

        return asset_data

    def calculate_asset_score(
        self, asset_data: pd.DataFrame, memberships: dict[str, pd.DataFrame]
    ) -> pd.DataFrame:
        """
        Calculate the asset scores and subscores.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        (pd.DataFrame): the input DataFrame with the added score and subscores.
        """
        required_columns = [
            gc.ghg_area_time_cov_p_s12,
            gc.ghg_area_time_cov_p_s12,
            gc.ghg_area_time_weight_s12,
            gc.ghg_area_time_weight_s3,
            gc.ghg_lfl_p,
            gc.ghg_lfl_outlier_status,
            gc.ghg_lfl_percent_change_s12,
            gc.ghg_lfl_percent_change_s3,
        ]

        return self.asset_score_utils.calculate_asset_score(
            asset_data,
            memberships,
            required_columns,
            [sc.score_gh1_ghg_area_time_cov_p],
            sc.score_gh1,
            self.subscore_weights,
            [
                self.calculate_asset_coverage_score,
                self.calculate_asset_lfl_score,
            ],
        )
