import logging
from typing import Callable

import numpy as np
import pandas as pd
from app_real_estate.transformation.metric_calculation.asset_filters import (
    AssetFilters as af,
)


class CommonIndicatorScoreUtils:
    """
    CommonIndicatorScoreUtils provides helper functions for each <INDICATOR>ScoreCalculator classes.

    This class provides a common function to run all of the indicator subscoring
    methods that lead to asset scores. It also provides an asset indicator score
    calculation method that performs a weighted sum on the provided subscores.

    Usage:
        asset_scoring_utils = CommonIndicatorScoreUtils()
        en1_score_calculator = EN1ScoreCalculator(asset_scoring_utils, benchmark_scoring_utils, subscore_weights.EN1_SUBSCORE_WEIGHTS)
        scored_data = calculator.calculate_asset_score(asset_data, memberships)

    Note: This class requires preprocessed asset data and benchmark group memberships
    as inputs.
    """

    @staticmethod
    def calculate_indicator_score(
        data: pd.DataFrame,
        subscore_weights: dict[str, float],
        score_column: str,
    ) -> pd.DataFrame:
        """
        Calculate the indicator score of each asset by adding the subscores.

        Parameters:
        data (pd.DataFrame): the DataFrame containing the scores.
        subscore_weights (dict[str, float]): a dictionary describing the
            different sub-scores and their weights in the indicator score.
        score_column (str): a string describing the name of the column
            containing the indicator score.

        Returns:
        (pd.DataFrame): the input DataFrame with the added score.
        """
        subscores_columns = list(subscore_weights.keys())
        subscores_weights = list(subscore_weights.values())

        # Weighted sum of the sub-score to get the indicator score.
        # We don't use 'dot' because it doesn't handle missing values properly.
        subscores = data[subscores_columns]
        subscores = subscores.mul(subscores_weights, axis=1)
        data[score_column] = subscores.sum(axis=1)

        return data

    def calculate_asset_score(
        self,
        asset_data: pd.DataFrame,
        memberships: dict[str, pd.DataFrame],
        required_columns: list[str],
        required_scores: list[str],
        indicator_score_column: str,
        subscore_weights: dict[str, float],
        subscore_func_list: list[
            Callable[[pd.DataFrame, dict[str, pd.DataFrame]], pd.DataFrame]
        ],
    ) -> pd.DataFrame:
        """
        Calculate the asset scores and subscores.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.
        required_colums (list[str]): a list of required metrics for scoring.
        required_scores (list[str]): a list of scores that cannot be missing.
        indicator_score_column (str): the indicator score variable name.
        subscore_weights (dict[str, float]): a dictionary giving the max score
            for each subscore.
        subscore_func_list (list[Callable]): a list of functions each calculating
            a subscore for the indicator.

        Returns:
        (pd.DataFrame): the input DataFrame with the added score and subscores.
        """
        # Set up logging
        logger = logging.getLogger(__name__)
        indicator = indicator_score_column[6:].upper()

        # Validate input data
        if asset_data.empty:
            logger.warning("Input asset_data is empty. Returning empty DataFrame.")
            return pd.DataFrame()

        missing_columns = [
            col for col in required_columns if col not in asset_data.columns
        ]
        if missing_columns:
            logger.error(f"Missing required columns in asset_data: {missing_columns}")
            raise ValueError(
                f"Missing required columns in asset_data: {missing_columns}"
            )

        try:
            # Calculate sub-scores
            for subscore_func in subscore_func_list:
                asset_data = subscore_func(asset_data, memberships)

            # Calculate indicator score
            asset_data = self.calculate_indicator_score(
                asset_data, subscore_weights, indicator_score_column
            )

        except Exception as e:
            logger.error(
                f"Error occurred during {indicator} asset score calculation: {str(e)}"
            )
            raise

        # Check for any missing scores or subscores
        for score_variable in required_scores:
            missing_scores = asset_data[score_variable].isna().sum()
            if missing_scores > 0:
                logger.error(
                    f"{missing_scores} assets have missing {indicator} scores for variable {score_variable}."
                )
                raise ValueError(
                    f"{missing_scores} assets have missing {indicator} scores for variable {score_variable}. Operational performance assets should not have missing scores."
                )

        return asset_data

    @staticmethod
    def set_lfl_rejected_outlier_scores_to_zero(
        asset_data: pd.DataFrame,
        score_column: str,
        lfl_p_column: str,
        outlier_column: str,
    ) -> pd.DataFrame:
        """
        Assign a score of 0 to rejected outliers that are eligible to LFL scoring.

        Parameters:
            asset_data (pd.DataFrame): the current year asset DataFrame.
            score_column (str): the column name containing the score to change.
            lfl_p_column (str): the column name containing the LFL percent change.
            outlier_column (str): the column name containing the LFL outlier status.

        Returns:
            (pd.DataFrame): the input DataFrame with the changed score.
        """
        is_rejected_outlier = af.filter_outliers(
            asset_data, outlier_column, ["rejected"]
        )
        is_lfl_eligible = ~asset_data[lfl_p_column].isna()
        should_get_zero = np.logical_and(is_rejected_outlier, is_lfl_eligible)
        asset_data.loc[should_get_zero, score_column] = 0

        return asset_data
