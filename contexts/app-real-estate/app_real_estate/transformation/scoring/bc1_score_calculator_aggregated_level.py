import pandas as pd

from app_real_estate.transformation.scoring.common_indicator_score_utils import (
    CommonIndicatorScoreUtils,
)
from app_real_estate.transformation.scoring.benchmark_score_utils import (
    BenchmarkScoreUtils,
)
import app_real_estate.constants.column_names.benchmark_group_memberships_columns as mc
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.building_certification_columns as bc


class BC1ScoreCalculator_AggregatedLevel:
    """
    Class responsible for calculating the certification scores at the property subtype and country level for each indicator.
    """

    def __init__(
        self,
        indicator: str,
        indicator_score_utils: CommonIndicatorScoreUtils,
        benchmark_score_utils: BenchmarkScoreUtils,
        subscore_weights: dict[str, float],
        max_score: float,
    ) -> None:
        if indicator not in ["BC1.1", "BC1.2"]:
            raise ValueError(
                "BC1ScoreCalculator initialisation: indicator should be one of 'BC1.1' and 'BC1.2'."
            )
        self.indicator = indicator
        self.indicator_score_utils = indicator_score_utils
        self.benchmark_score_utils = benchmark_score_utils
        self.subscore_weights = subscore_weights
        self.max_score = max_score

        if indicator == "BC1.1":
            self.coverage_score_column = sc.score_bc1_1_coverage
            self.score_column = sc.score_bc1_1
        elif indicator == "BC1.2":
            self.coverage_score_column = sc.score_bc1_2_coverage
            self.score_column = sc.score_bc1_2

    def calculate_certification_coverage_score(
        self,
        certification_data: pd.DataFrame,
        memberships: dict[str, pd.DataFrame],
    ) -> pd.DataFrame:
        """
        Calculate the certifications coverage score.

        Parameters:
        certification_data (pd.DataFrame): the aggregated certifications DataFrame
            containing only certifications of the type "design", "contruction"
            or "interior".
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        pd.DataFrame: the input DataFrame with the added subscore.
        """
        # Benchmark the coverage at the certification level.
        certification_data = self.benchmark_score_utils.calculate_benchmarked_subscore(
            certification_data,
            memberships.get(bc.scoring_coverage),
            mc.bench_certification_coverage,
            mc.mean_certification_coverage,
            self.coverage_score_column,
        )

        return certification_data

    def calculate_score(
        self,
        certification_data: pd.DataFrame,
        memberships: dict[str, pd.DataFrame],
    ) -> pd.DataFrame:
        """
        Calculate the asset scores and subscores.

        Parameters:
        certification_data (pd.DataFrame): the aggregated certifications DataFrame
            containing only certifications of the type "design", "contruction"
            or "interior" for BC1.1 or "operational" for BC1.2.
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        pd.DataFrame: the input DataFrames with the added score and subscore.
        """
        data_indicator = certification_data[bc.indicator].unique()
        if self.indicator == "BC1.1" and data_indicator != ["BC1.1"]:
            raise ValueError(
                "BC1ScoreCalculator.calculate_asset_score: BC1.1 scoring only applies to non-operational certifications."
            )

        if self.indicator == "BC1.2" and data_indicator != ["BC1.2"]:
            raise ValueError(
                "BC1ScoreCalculator.calculate_asset_score: BC1.2 scoring only applies to operational certifications."
            )

        # Score certification coverage and calculate certification score
        certification_data = self.calculate_certification_coverage_score(
            certification_data, memberships
        )

        # Compute indicator score (absolute, fractional, max and percent)
        fractional_score_col = f"{self.score_column}_fraction"
        percent_score_col = f"{self.score_column}_percent"
        max_score_col = f"{self.score_column}_max"

        certification_data = self.indicator_score_utils.calculate_indicator_score(
            certification_data, self.subscore_weights, self.score_column
        )
        certification_data[fractional_score_col] = (
            certification_data[self.score_column] / self.max_score
        )
        certification_data[percent_score_col] = (
            certification_data[fractional_score_col] * 100
        )
        certification_data[max_score_col] = self.max_score

        return certification_data
