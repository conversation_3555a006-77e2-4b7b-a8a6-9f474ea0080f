import pandas as pd
import numpy as np

from app_real_estate.constants.helper_enumerators import Utility
from app_real_estate.transformation.scoring.common_indicator_score_utils import (
    CommonIndicatorScoreUtils,
)
from app_real_estate.transformation.scoring.benchmark_score_utils import (
    BenchmarkScoreUtils,
)
from app_real_estate.transformation.scoring.control_based_score_utils import (
    ControlBasedScoreUtils,
)
import app_real_estate.constants.column_names.benchmark_group_memberships_columns as mc
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.water_columns as wc


class WT1ScoreCalculator:
    """
    WT1ScoreCalculator calculates asset scores for the WT1 (Water) indicator.

    This class provides methods to calculate various subscores and the overall
    score for the WT1 indicator, which measures water consumption and water
    recycling in real estate assets.

    Key functionalities include:
    - Calculating data coverage scores
    - Evaluating Like-For-Like (LFL) water consumption changes
    - Assessing water recycling and re-usage
    - Combining subscores to produce an overall WT1 indicator score

    The class uses benchmark comparisons and weighted scoring methods to evaluate
    asset performance across multiple water-related metrics.

    Usage:
        calculator = WT1ScoreCalculator()
        scored_data = calculator.calculate_asset_score(asset_data, memberships)

    Note: This class requires preprocessed asset data and benchmark group memberships
    as inputs.
    """

    def __init__(
        self,
        asset_score_utils: CommonIndicatorScoreUtils,
        benchmark_score_utils: BenchmarkScoreUtils,
        control_score_utils: ControlBasedScoreUtils,
        subscore_weights: dict[str, float],
    ) -> None:
        self.asset_score_utils = asset_score_utils
        self.benchmark_score_utils = benchmark_score_utils
        self.control_score_utils = control_score_utils
        self.subscore_weights = subscore_weights

    def calculate_asset_coverage_score(
        self, asset_data: pd.DataFrame, memberships: dict[str, pd.DataFrame]
    ) -> pd.DataFrame:
        """
        Calculate the data coverage subscore.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        (pd.DataFrame): the input DataFrame with the added subscore.
        """
        # Compare LC/TC coverage against benchmark
        asset_data = self.benchmark_score_utils.calculate_benchmarked_subscore(
            asset_data,
            memberships.get(wc.wat_area_time_cov_p_lc),
            mc.bench_wat_area_time_cov_p_lc,
            mc.mean_wat_area_time_cov_p_lc,
            sc.score_wt1_wat_area_time_cov_p_lc,
        )
        asset_data = self.benchmark_score_utils.calculate_benchmarked_subscore(
            asset_data,
            memberships.get(wc.wat_area_time_cov_p_tc),
            mc.bench_wat_area_time_cov_p_tc,
            mc.mean_wat_area_time_cov_p_tc,
            sc.score_wt1_wat_area_time_cov_p_tc,
        )

        # Weighted sum of LC and TC for coverage
        asset_data[sc.score_wt1_wat_area_time_cov_p] = (
            self.control_score_utils.sum_control_based_subscores(
                asset_data,
                sc.score_wt1_wat_area_time_cov_p_lc,
                sc.score_wt1_wat_area_time_cov_p_tc,
                False,
                Utility.Water,
            )
        )

        return asset_data

    def calculate_asset_lfl_score(
        self, asset_data: pd.DataFrame, memberships: dict[str, pd.DataFrame]
    ) -> pd.DataFrame:
        """
        Calculate the Like-For-Like subscore.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        (pd.DataFrame): the input DataFrame with the added subscore.
        """
        # Score LFL performance
        asset_data = self.benchmark_score_utils.calculate_benchmarked_subscore(
            asset_data,
            memberships.get(wc.wat_lfl_percent_change_lc),
            mc.bench_wat_lfl_percent_change_lc,
            mc.mean_wat_lfl_percent_change_lc,
            sc.score_wt1_wat_lfl_percent_change_lc,
            is_lfl=True,
        )
        asset_data = self.benchmark_score_utils.calculate_benchmarked_subscore(
            asset_data,
            memberships.get(wc.wat_lfl_percent_change_tc),
            mc.bench_wat_lfl_percent_change_tc,
            mc.mean_wat_lfl_percent_change_tc,
            sc.score_wt1_wat_lfl_percent_change_tc,
            is_lfl=True,
        )
        # TODO: find better way to handle rejected outliers
        asset_data = self.asset_score_utils.set_lfl_rejected_outlier_scores_to_zero(
            asset_data,
            sc.score_wt1_wat_lfl_percent_change_lc,
            wc.wat_lfl_percent_change_lc,
            wc.wat_lfl_outlier_status,
        )
        asset_data = self.asset_score_utils.set_lfl_rejected_outlier_scores_to_zero(
            asset_data,
            sc.score_wt1_wat_lfl_percent_change_tc,
            wc.wat_lfl_percent_change_tc,
            wc.wat_lfl_outlier_status,
        )

        # Weighted sum of LC and TC for LFL
        asset_data[sc.score_wt1_wat_lfl_percent_change] = (
            self.control_score_utils.sum_control_based_subscores(
                asset_data,
                sc.score_wt1_wat_lfl_percent_change_lc,
                sc.score_wt1_wat_lfl_percent_change_tc,
                True,
                Utility.Water,
            )
        )

        return asset_data

    def calculate_asset_recycled_performance_score(
        self, asset_data: pd.DataFrame, memberships: dict[str, pd.DataFrame]
    ) -> pd.DataFrame:
        """
        Calculate the recycled/reused water performance subscore.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        pd.DataFrame: the input DataFrame with the added subscore.
        """
        asset_data = self.benchmark_score_utils.calculate_asset_performance_score(
            asset_data,
            memberships,
            wc.wat_rec_rate,
            wc.wat_rec_percent_change,
            mc.bench_wat_rec_percent_change,
            mc.mean_wat_rec_percent_change,
            sc.score_wt1_wat_rec_percent_change,
            sc.score_wt1_wat_rec_performance,
        )

        return asset_data

    def calculate_asset_recycled_score(
        self, asset_data: pd.DataFrame, memberships: dict[str, pd.DataFrame]
    ) -> pd.DataFrame:
        """
        Calculate the recycled water subscore.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        (pd.DataFrame): the input DataFrame with the added subscore.
        """
        # Score the recycled water on-site availability
        has_onsite_recycled = (
            ~asset_data[wc.wat_rec_ons].isna() & asset_data[wc.wat_rec_ons] != 0
        )

        asset_data[sc.score_wt1_wat_rec_ons] = np.where(has_onsite_recycled, 1, 0)

        # Score the recycled water performance
        asset_data = self.calculate_asset_recycled_performance_score(
            asset_data, memberships
        )

        return asset_data

    def calculate_asset_score(
        self, asset_data: pd.DataFrame, memberships: dict[str, pd.DataFrame]
    ) -> pd.DataFrame:
        """
        Calculate the asset scores and subscores.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        (pd.DataFrame): the input DataFrame with the added score and subscores.
        """
        required_columns = [
            wc.wat_area_time_cov_p_lc,
            wc.wat_area_time_cov_p_tc,
            wc.wat_area_time_weight_lc,
            wc.wat_area_time_weight_tc,
            wc.wat_lfl_p,
            wc.wat_lfl_outlier_status,
            wc.wat_lfl_percent_change_lc,
            wc.wat_lfl_percent_change_tc,
            wc.wat_rec_ons,
            wc.wat_rec_percent_change,
            wc.wat_rec_rate,
        ]

        required_scores = [
            sc.score_wt1_wat_area_time_cov_p,
            sc.score_wt1_wat_rec_ons,
            sc.score_wt1_wat_rec_percent_change,
            sc.score_wt1_wat_rec_performance,
        ]

        return self.asset_score_utils.calculate_asset_score(
            asset_data,
            memberships,
            required_columns,
            required_scores,
            sc.score_wt1,
            self.subscore_weights,
            [
                self.calculate_asset_coverage_score,
                self.calculate_asset_lfl_score,
                self.calculate_asset_recycled_score,
            ],
        )
