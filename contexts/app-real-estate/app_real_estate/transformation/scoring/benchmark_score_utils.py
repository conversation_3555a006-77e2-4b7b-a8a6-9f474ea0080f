import numpy as np
import pandas as pd
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.building_certification_columns as bc
import app_real_estate.constants.column_names.benchmark_group_memberships_columns as mc

BENCHMARK_METRIC_LOWER_THRESHOLD = 0.1
BENCHMARK_METRIC_UPPER_THRESHOLD = 0.5


class BenchmarkScoreUtils:
    """
    BenchmarkScoreUtils provides helper functions for <INDICATOR>ScoreCalculator classes.

    This class provides functions to compare the metrics to their benchmark and
    create a score out of this comparison. It also defines functions to score
    performance metrics that use both the benchmark-ed improvement score and the
    percent change metric itself (like the renewable energy and recycled water
    scores).

    Usage:
        benchmark_scoring_utils = BenchmarkScoreUtils()
        en1_score_calculator = EN1ScoreCalculator(asset_scoring_utils, benchmark_scoring_utils, control_weight_utils, subscore_weights.EN1_SUBSCORE_WEIGHTS)
        scored_data = calculator.calculate_asset_score(asset_data, memberships)
    """

    @staticmethod
    def score_on_curve(x: float, x_gives_half: float, tol: float = 1e-5) -> float:
        """
        Scores the input metric relatively to its group benchmark metric.

        Parameters:
        x (np.ndarray): Input values to score
        x_gives_half (np.ndarray): Values that should give a score of 0.5
        tol (float): Tolerance for boundary conditions

        Returns:
        np.ndarray: the scores
        """
        # Check conditions
        valid_x = np.logical_or(np.isnan(x), np.logical_and(x >= -tol, x <= 1 + tol))
        valid_x_gives_half = np.logical_and(x_gives_half > 0, x_gives_half < 0.5)

        if not valid_x or not valid_x_gives_half:
            raise ValueError("score_on_curve: Invalid input values detected.")

        # Clamp x values to [0, 1]
        x = np.clip(x, 0, 1)

        curve_par = (1 - 2 * x_gives_half) / x_gives_half**2

        return np.log(curve_par * x + 1) / np.log(curve_par + 1)

    def compare_metric_to_benchmark(
        self,
        data: pd.DataFrame,
        memberships: pd.DataFrame,
        benchmark_value_column: str,
        benchmark_metric_column: str,
        subscore_column: str,
    ) -> pd.DataFrame:
        """
        Calculates the score on curve of a given metric based on its benchmark.

        Parameters:
        data (pd.DataFrame): the DataFrame containing the benchmarked metric.
        memberships (pd.DataFrame): the DataFrame containing the pairs asset
            (or certification) / benchmark group.
        benchmark_value_column (str): column containing the benchmark value in
            the membership data.
        benchmark_metric_column (str): column containing the benchmark metric
            in the membership data.
        subscore_column (str): column containing the subscores for this metric.

        Returns:
        pd.DataFrame: the data with added subscore based on the metric benchmark.
        """
        # If the benchmark metric is inferior to the lowest accepted value,
        # set it to this threshold.
        is_benchmark_too_low = (
            memberships[benchmark_metric_column] < BENCHMARK_METRIC_LOWER_THRESHOLD
        )
        memberships.loc[is_benchmark_too_low, benchmark_metric_column] = (
            BENCHMARK_METRIC_LOWER_THRESHOLD
        )

        # If the benchmark metric is inferior to the highest accepted value,
        # score the metric against it.
        is_benchmark_metric_valid = (
            memberships[benchmark_metric_column] < BENCHMARK_METRIC_UPPER_THRESHOLD
        )
        if any(is_benchmark_metric_valid):
            score_on_curve_vectorized = np.vectorize(self.score_on_curve)
            memberships.loc[is_benchmark_metric_valid, subscore_column] = (
                score_on_curve_vectorized(
                    memberships.loc[is_benchmark_metric_valid, benchmark_value_column],
                    memberships.loc[is_benchmark_metric_valid, benchmark_metric_column],
                )
            )

        # Else, the score is equal to the metric value.
        memberships.loc[~is_benchmark_metric_valid, subscore_column] = memberships[
            ~is_benchmark_metric_valid
        ][benchmark_value_column]

        # Merge the scores back in the data.
        unique_id_column = (
            bc.building_data_certifications_scoring_id
            if benchmark_value_column == mc.bench_certification_coverage
            else ac.portfolio_asset_id
        )
        data = data.merge(
            memberships[[unique_id_column, subscore_column]],
            how="left",
            on=unique_id_column,
        )

        return data

    def calculate_benchmarked_subscore(
        self,
        data: pd.DataFrame,
        memberships: pd.DataFrame,
        benchmark_value_column: str,
        benchmark_metric_column: str,
        subscore_column: str,
        is_lfl: bool = False,
    ) -> pd.DataFrame:
        """
        Calculate a subscore of an indicator based on a benchmarked metric.

        Parameters:
        data (pd.DataFrame): the DataFrame containing the benchmarked metric.
        memberships (pd.DataFrame): the DataFrame containing the pairs asset
            (or certification) / benchmark group.
        metric_column (str): column containing the metric to score.
        benchmark_value_column (str): column containing the benchmark value in
            the membership data.
        benchmark_metric_column (str): column containing the benchmark metric
            in the membership data.
        subscore_column (str): column containing the subscores for this metric.

        Returns:
        (pd.DataFrame): the input DataFrame with the added subscores.
        """
        # Since we're comparing the metrics to their benchmark, we want to
        # select the active benchmark.
        memberships = memberships[memberships[mc.is_active]].copy()

        # Compare metric value to benchmark
        data = self.compare_metric_to_benchmark(
            data,
            memberships,
            benchmark_value_column,
            benchmark_metric_column,
            subscore_column,
        )

        # Metrics close to 0 and 1 receive respectively 0 and full points.
        # The unique ID column differs whether we are dealing with certifications or asset data.
        unique_id_column = (
            bc.building_data_certifications_scoring_id
            if benchmark_value_column == mc.bench_certification_coverage
            else ac.portfolio_asset_id
        )
        benchmark_values = data.merge(memberships, how="left", on=unique_id_column)[
            benchmark_value_column
        ]
        has_small_enough_value = round(benchmark_values, 4) == 0
        has_big_enough_value = round(benchmark_values, 4) == 1
        has_no_value = data[subscore_column].isna()
        has_small_or_no_value = np.logical_or(has_small_enough_value, has_no_value)

        if is_lfl:
            data.loc[has_small_enough_value, subscore_column] = 0
        else:
            data.loc[has_small_or_no_value, subscore_column] = 0
        data.loc[has_big_enough_value, subscore_column] = 1

        return data

    @staticmethod
    def calculate_percent_change_performance_score(
        improvement_score: float, metric_value: float
    ) -> float:
        """
        Calculates the sub-score based on the benchmark-ed improvement score and
        the value of a given metric. This is used for the renewable energy
        performance score in EN1 and the recycled/reused water score in WT1.

        Parameters:
        improvement_score (float): the benchmark-ed sub-score of the percent change.
        metric_value (float): the metric value (expressed as a percentage).

        Returns:
        float: the score for the given metric using the formula detailed
            in the scoring document.
        """
        score = (100 + metric_value) / 200 * metric_value / 100 + (
            100 - metric_value
        ) / 200 * improvement_score
        score = np.minimum(score, 1)

        return score if not np.isnan(score) else 0

    def calculate_asset_performance_score(
        self,
        asset_data: pd.DataFrame,
        memberships: dict[str, pd.DataFrame],
        consumption_rate_column: str,
        percent_change_column: str,
        benchmark_value_column: str,
        benchmark_metric_column: str,
        improvement_score_column: str,
        performance_score_column: str,
    ) -> pd.DataFrame:
        """
        Calculate the performance subscore based on a benchmark-ed percent change
        metric and the consumption percentage.
        This is used for renewable energy generation and recycled / reused water
        subscores.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.
        consumption_rate_column (str): the consumption value as a percentage of
            the corresponding utility consumption.
        percent_change_column (str): the absolute percent change between current
            and previous year.
        benchmark_value_column (str): the benchmark value for the percent change.
        benchmark_metric_column (str): the benchmark metric for the percent change.
        improvement_score_column (str): the score of the benchmark-ed percent
            change metric.
        performance_score_column (str): the resulting performance score.

        Returns:
        pd.DataFrame: the input DataFrame with the added subscore.
        """
        # Calculate the "improvement" score (as named in the scoring document)
        asset_data = self.calculate_benchmarked_subscore(
            asset_data,
            memberships.get(percent_change_column),
            benchmark_value_column,
            benchmark_metric_column,
            improvement_score_column,
        )

        # Calculate the renewable energy sub-score using improvement score and percentage of renewable
        calculate_renewable_performance = np.vectorize(
            self.calculate_percent_change_performance_score
        )
        asset_data[performance_score_column] = calculate_renewable_performance(
            asset_data[improvement_score_column],
            asset_data[consumption_rate_column],
        )

        return asset_data
