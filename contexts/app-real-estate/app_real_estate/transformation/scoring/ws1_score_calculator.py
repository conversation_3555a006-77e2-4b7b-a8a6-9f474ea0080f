import pandas as pd

from app_real_estate.constants.helper_enumerators import Utility
from app_real_estate.transformation.scoring.common_indicator_score_utils import (
    CommonIndicatorScoreUtils,
)
from app_real_estate.transformation.scoring.benchmark_score_utils import (
    BenchmarkScoreUtils,
)
import app_real_estate.constants.column_names.benchmark_group_memberships_columns as mc
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.waste_columns as wsc
from app_real_estate.transformation.scoring.control_based_score_utils import (
    ControlBasedScoreUtils,
)


class WS1ScoreCalculator:
    """
    WS1ScoreCalculator calculates asset scores for the WS1 (Waste) indicator.

    This class provides methods to calculate various subscores and the overall
    score for the WS1 indicator, which measures waste consumption and waste
    diversion in real estate assets.

    Key functionalities include:
    - Calculating data coverage scores
    - Assessing waste diversion
    - Combining subscores to produce an overall WS1 indicator score

    The class uses benchmark comparisons and weighted scoring methods to evaluate
    asset performance across multiple waste-related metrics.

    Usage:
        calculator = WS1ScoreCalculator()
        scored_data = calculator.calculate_asset_score(asset_data, memberships)

    Note: This class requires preprocessed asset data and benchmark group memberships
    as inputs.
    """

    def __init__(
        self,
        asset_score_utils: CommonIndicatorScoreUtils,
        benchmark_score_utils: BenchmarkScoreUtils,
        control_score_utils: ControlBasedScoreUtils,
        subscore_weights: dict[str, float],
    ) -> None:
        self.asset_score_utils = asset_score_utils
        self.benchmark_score_utils = benchmark_score_utils
        self.control_score_utils = control_score_utils
        self.subscore_weights = subscore_weights

    def calculate_asset_coverage_score(
        self, asset_data: pd.DataFrame, memberships: dict[str, pd.DataFrame]
    ) -> pd.DataFrame:
        """
        Calculate the data coverage subscore.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        (pd.DataFrame): the input DataFrame with the added subscore.
        """
        asset_data = self.benchmark_score_utils.calculate_benchmarked_subscore(
            asset_data,
            memberships.get(wsc.was_area_cov_p_lc),
            mc.bench_was_area_cov_p_lc,
            mc.mean_was_area_cov_p_lc,
            sc.score_ws1_was_area_cov_p_lc,
        )
        asset_data = self.benchmark_score_utils.calculate_benchmarked_subscore(
            asset_data,
            memberships.get(wsc.was_area_cov_p_tc),
            mc.bench_was_area_cov_p_tc,
            mc.mean_was_area_cov_p_tc,
            sc.score_ws1_was_area_cov_p_tc,
        )

        # Weighted sum of LC and TC for coverage
        asset_data[sc.score_ws1_was_area_cov_p] = (
            self.control_score_utils.sum_control_based_subscores(
                asset_data,
                sc.score_ws1_was_area_cov_p_lc,
                sc.score_ws1_was_area_cov_p_tc,
                False,
                Utility.Waste,
            )
        )

        return asset_data

    def calculate_asset_diverted_waste_score(
        self, asset_data: pd.DataFrame, memberships: dict[str, pd.DataFrame]
    ) -> pd.DataFrame:
        """
        Calculate the diverted waste subscore.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        (pd.DataFrame): the input DataFrame with the added subscore.
        """
        # Score the recycled waste performance
        asset_data = self.benchmark_score_utils.calculate_benchmarked_subscore(
            asset_data,
            memberships.get(wsc.was_pabs_div),
            mc.bench_was_diverted_percent,
            mc.mean_was_diverted_percent,
            sc.score_ws1_was_diverted_percent,
        )

        return asset_data

    def calculate_asset_score(
        self, asset_data: pd.DataFrame, memberships: dict[str, pd.DataFrame]
    ) -> pd.DataFrame:
        """
        Calculate the asset scores and subscores.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        (pd.DataFrame): the input DataFrame with the added score and subscores.
        """
        required_columns = [
            wsc.was_area_cov_p_lc,
            wsc.was_area_cov_p_tc,
            wsc.was_pabs_div,
        ]

        return self.asset_score_utils.calculate_asset_score(
            asset_data,
            memberships,
            required_columns,
            [sc.score_ws1_was_area_cov_p, sc.score_ws1_was_diverted_percent],
            sc.score_ws1,
            self.subscore_weights,
            [
                self.calculate_asset_coverage_score,
                self.calculate_asset_diverted_waste_score,
            ],
        )
