import pandas as pd
from app_real_estate.transformation.scoring.common_indicator_score_utils import (
    CommonIndicatorScoreUtils,
)
from app_real_estate.transformation.scoring.benchmark_score_utils import (
    BenchmarkScoreUtils,
)
import app_real_estate.constants.column_names.benchmark_group_memberships_columns as mc
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.asset_characteristics_columns as ac


class BC2ScoreCalculator:
    """
    Class responsible for calculating the asset scores for each indicator.
    """

    def __init__(
        self,
        asset_score_utils: CommonIndicatorScoreUtils,
        benchmark_score_utils: BenchmarkScoreUtils,
        subscore_weights: dict[str, float],
    ) -> None:
        self.asset_score_utils = asset_score_utils
        self.benchmark_score_utils = benchmark_score_utils
        self.subscore_weights = subscore_weights

    def calculate_asset_rating_coverage_score(
        self, asset_data: pd.DataFrame, memberships: dict[str, pd.DataFrame]
    ) -> pd.DataFrame:
        """
        Calculate the energy ratings coverage sub-score.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        (pd.DataFrame): the input DataFrame with the added subscore.
        """
        asset_data = self.benchmark_score_utils.calculate_benchmarked_subscore(
            asset_data,
            memberships.get(ac.rating_coverage),
            mc.bench_energy_rating_coverage,
            mc.mean_energy_rating_coverage,
            sc.score_bc2_coverage,
        )

        return asset_data

    def calculate_asset_score(
        self, asset_data: pd.DataFrame, memberships: dict[str, pd.DataFrame]
    ) -> pd.DataFrame:
        """
        Calculate the asset scores and subscores.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        (pd.DataFrame): the input DataFrame with the added score and subscores.
        """
        # Score data coverage
        asset_data = self.calculate_asset_rating_coverage_score(asset_data, memberships)

        # Calculate indicator score
        asset_data = self.asset_score_utils.calculate_indicator_score(
            asset_data, self.subscore_weights, sc.score_bc2
        )

        return asset_data
