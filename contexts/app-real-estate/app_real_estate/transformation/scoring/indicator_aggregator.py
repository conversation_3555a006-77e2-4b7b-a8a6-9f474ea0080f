import pandas as pd
from app_real_estate.models.aggregated_score import AggregatedScore
from app_real_estate.models.score_aggregation_util_models.bc1_1_scored_data import (
    BC1_1ScoredData,
)
from app_real_estate.models.score_aggregation_util_models.bc1_1_scored_data_aggregated_level import (
    BC1_1ScoredData_AggregatedLevel,
)
from app_real_estate.models.score_aggregation_util_models.bc1_2_scored_data import (
    BC1_2ScoredData,
)
from app_real_estate.models.score_aggregation_util_models.bc1_2_scored_data_aggregated_level import (
    BC1_2ScoredData_AggregatedLevel,
)
from app_real_estate.transformation.aggregation.indicator.en1.en1_score_aggregator_response_id import (
    EN1ScoreAggregator_ResponseId,
)
from app_real_estate.transformation.aggregation.indicator.en1.en1_score_aggregator_property_subtype_country_response_id import (
    EN1ScoreAggregator_PropertySubtype_Country_ResponseId,
)
from app_real_estate.transformation.aggregation.indicator.en1.en1_score_aggregator_property_subtype_response_id import (
    EN1ScoreAggregator_PropertySubtype_ResponseId,
)

from app_real_estate.transformation.aggregation.indicator.gh1.gh1_score_aggregator_response_id import (
    GH1ScoreAggregator_ResponseId,
)
from app_real_estate.transformation.aggregation.indicator.gh1.gh1_score_aggregator_property_subtype_country_response_id import (
    GH1ScoreAggregator_PropertySubtype_Country_ResponseId,
)
from app_real_estate.transformation.aggregation.indicator.gh1.gh1_score_aggregator_property_subtype_response_id import (
    GH1ScoreAggregator_PropertySubtype_ResponseId,
)

from app_real_estate.transformation.aggregation.indicator.ws1.ws1_score_aggregator_response_id import (
    WS1ScoreAggregator_ResponseId,
)
from app_real_estate.transformation.aggregation.indicator.ws1.ws1_score_aggregator_property_subtype_country_response_id import (
    WS1ScoreAggregator_PropertySubtype_Country_ResponseId,
)
from app_real_estate.transformation.aggregation.indicator.ws1.ws1_score_aggregator_property_subtype_response_id import (
    WS1ScoreAggregator_PropertySubtype_ResponseId,
)

from app_real_estate.transformation.aggregation.indicator.wt1.wt1_score_aggregator_response_id import (
    WT1ScoreAggregator_ResponseId,
)
from app_real_estate.transformation.aggregation.indicator.wt1.wt1_score_aggregator_property_subtype_country_response_id import (
    WT1ScoreAggregator_PropertySubtype_Country_ResponseId,
)
from app_real_estate.transformation.aggregation.indicator.wt1.wt1_score_aggregator_property_subtype_response_id import (
    WT1ScoreAggregator_PropertySubtype_ResponseId,
)

from app_real_estate.transformation.aggregation.indicator.bc1_1.bc1_1_score_aggregator_response_id import (
    BC1_1ScoreAggregator_ResponseId,
)
from app_real_estate.transformation.aggregation.indicator.bc1_1.bc1_1_score_aggregator_property_subtype_country_response_id import (
    BC1_1ScoreAggregator_PropertySubtype_Country_ResponseId,
)
from app_real_estate.transformation.aggregation.indicator.bc1_1.bc1_1_score_aggregator_property_subtype_response_id import (
    BC1_1ScoreAggregator_PropertySubtype_ResponseId,
)

from app_real_estate.transformation.aggregation.indicator.bc1_2.bc1_2_score_aggregator_response_id import (
    BC1_2ScoreAggregator_ResponseId,
)
from app_real_estate.transformation.aggregation.indicator.bc1_2.bc1_2_score_aggregator_property_subtype_country_responseid import (
    BC1_2ScoreAggregator_PropertySubtype_Country_ResponseId,
)
from app_real_estate.transformation.aggregation.indicator.bc1_2.bc1_2_score_aggregator_property_subtype_response_id import (
    BC1_2ScoreAggregator_PropertySubtype_ResponseId,
)
from app_real_estate.transformation.aggregation.indicator.bc2.bc2_score_aggregator_response_id import (
    BC2ScoreAggregator_ResponseId,
)
from app_real_estate.transformation.aggregation.indicator.bc2.bc2_score_aggregator_property_subtype_country_response_id import (
    BC2ScoreAggregator_PropertySubtype_Country_ResponseId,
)
from app_real_estate.transformation.aggregation.indicator.bc2.bc2_score_aggregator_property_subtype_response_id import (
    BC2ScoreAggregator_PropertySubtype_ResponseId,
)


class IndicatorAggregator:

    def __init__(self, report_type: str = "re") -> None:
        """
        Initialize IndicatorAggregator with a specific report type.

        Args:
            report_type: The report type ('re' for Real Estate, 'res' for Residential)
        """
        self.report_type = report_type

    _en1_portfolio_level_aggregator = EN1ScoreAggregator_ResponseId
    _en1_propertysubtype_country_responseid_aggregator = (
        EN1ScoreAggregator_PropertySubtype_Country_ResponseId
    )
    _en1_propertysubtype_responseid_aggregator = (
        EN1ScoreAggregator_PropertySubtype_ResponseId
    )

    _gh1_portfolio_level_aggregator = GH1ScoreAggregator_ResponseId
    _gh1_propertysubtype_country_responseid_aggregator = (
        GH1ScoreAggregator_PropertySubtype_Country_ResponseId
    )
    _gh1_propertysubtype_responseid_aggregator = (
        GH1ScoreAggregator_PropertySubtype_ResponseId
    )

    _ws1_portfolio_level_aggregator = WS1ScoreAggregator_ResponseId
    _ws1_propertysubtype_country_responseid_aggregator = (
        WS1ScoreAggregator_PropertySubtype_Country_ResponseId
    )
    _ws1_propertysubtype_responseid_aggregator = (
        WS1ScoreAggregator_PropertySubtype_ResponseId
    )

    _wt1_portfolio_level_aggregator = WT1ScoreAggregator_ResponseId
    _wt1_propertysubtype_country_responseid_aggregator = (
        WT1ScoreAggregator_PropertySubtype_Country_ResponseId
    )
    _wt1_propertysubtype_responseid_aggregator = (
        WT1ScoreAggregator_PropertySubtype_ResponseId
    )

    _bc1_1_portfolio_level_aggregator = BC1_1ScoreAggregator_ResponseId
    _bc1_1_propertysubtype_country_responseid_aggregator = (
        BC1_1ScoreAggregator_PropertySubtype_Country_ResponseId
    )
    _bc1_1_propertysubtype_responseid_aggregator = (
        BC1_1ScoreAggregator_PropertySubtype_ResponseId
    )

    _bc1_2_portfolio_level_aggregator = BC1_2ScoreAggregator_ResponseId
    _bc1_2_propertysubtype_country_responseid_aggregator = (
        BC1_2ScoreAggregator_PropertySubtype_Country_ResponseId
    )
    _bc1_2_propertysubtype_responseid_aggregator = (
        BC1_2ScoreAggregator_PropertySubtype_ResponseId
    )

    _bc2_portfolio_level_aggregator = BC2ScoreAggregator_ResponseId
    _bc2_propertysubtype_country_responseid_aggregator = (
        BC2ScoreAggregator_PropertySubtype_Country_ResponseId
    )
    _bc2_propertysubtype_responseid_aggregator = (
        BC2ScoreAggregator_PropertySubtype_ResponseId
    )

    def aggregate_score_en1(
        self, scored_data_en: pd.DataFrame, r1_table: pd.DataFrame
    ) -> AggregatedScore:
        aggregated_en1 = AggregatedScore(
            portfolio_level=self._en1_portfolio_level_aggregator(
                scored_data_en, r1_table, report_type=self.report_type
            ).process(),
            propertysubtype_country_responseid=self._en1_propertysubtype_country_responseid_aggregator(
                scored_data_en, r1_table, report_type=self.report_type
            ).process(),
            propertysubtype_responseid=self._en1_propertysubtype_responseid_aggregator(
                scored_data_en, r1_table, report_type=self.report_type
            ).process(),
        )
        return aggregated_en1

    def aggregate_score_gh1(
        self, scored_data_gh: pd.DataFrame, r1_table: pd.DataFrame
    ) -> AggregatedScore:
        aggregated_gh1 = AggregatedScore(
            portfolio_level=self._gh1_portfolio_level_aggregator(
                scored_data_gh, r1_table, report_type=self.report_type
            ).process(),
            propertysubtype_country_responseid=self._gh1_propertysubtype_country_responseid_aggregator(
                scored_data_gh, r1_table, report_type=self.report_type
            ).process(),
            propertysubtype_responseid=self._gh1_propertysubtype_responseid_aggregator(
                scored_data_gh, r1_table, report_type=self.report_type
            ).process(),
        )
        return aggregated_gh1

    def aggregate_score_ws1(
        self, scored_data_ws: pd.DataFrame, r1_table: pd.DataFrame
    ) -> AggregatedScore:
        aggregated_ws1 = AggregatedScore(
            portfolio_level=self._ws1_portfolio_level_aggregator(
                scored_data_ws, r1_table, report_type=self.report_type
            ).process(),
            propertysubtype_country_responseid=self._ws1_propertysubtype_country_responseid_aggregator(
                scored_data_ws, r1_table, report_type=self.report_type
            ).process(),
            propertysubtype_responseid=self._ws1_propertysubtype_responseid_aggregator(
                scored_data_ws, r1_table, report_type=self.report_type
            ).process(),
        )
        return aggregated_ws1

    def aggregate_score_wt1(
        self, scored_data_wt: pd.DataFrame, r1_table: pd.DataFrame
    ) -> AggregatedScore:
        aggregated_wt1 = AggregatedScore(
            portfolio_level=self._wt1_portfolio_level_aggregator(
                scored_data_wt, r1_table, report_type=self.report_type
            ).process(),
            propertysubtype_country_responseid=self._wt1_propertysubtype_country_responseid_aggregator(
                scored_data_wt, r1_table, report_type=self.report_type
            ).process(),
            propertysubtype_responseid=self._wt1_propertysubtype_responseid_aggregator(
                scored_data_wt, r1_table, report_type=self.report_type
            ).process(),
        )
        return aggregated_wt1

    def aggregate_score_bc1_1(
        self,
        scored_data_bc1_1: pd.DataFrame,
        r1_table: pd.DataFrame,
        is_input_aggregated: bool = False,
    ) -> AggregatedScore:
        data_model: type[BC1_1ScoredData_AggregatedLevel | BC1_1ScoredData] = (
            BC1_1ScoredData_AggregatedLevel if is_input_aggregated else BC1_1ScoredData
        )
        aggregated_bc1_1 = AggregatedScore(
            portfolio_level=self._bc1_1_portfolio_level_aggregator(
                scored_data_bc1_1, r1_table, report_type=self.report_type
            ).process(),
            propertysubtype_country_responseid=self._bc1_1_propertysubtype_country_responseid_aggregator(
                scored_data_bc1_1, r1_table, report_type=self.report_type
            ).process(),
            propertysubtype_responseid=self._bc1_1_propertysubtype_responseid_aggregator(
                scored_data_bc1_1,
                r1_table,
                data_model=data_model,
                report_type=self.report_type,
            ).process(),
        )
        return aggregated_bc1_1

    def aggregate_score_bc1_2(
        self,
        scored_data_bc1_2: pd.DataFrame,
        r1_table: pd.DataFrame,
        is_input_aggregated: bool = False,
    ) -> AggregatedScore:
        data_model: type[BC1_2ScoredData_AggregatedLevel | BC1_2ScoredData] = (
            BC1_2ScoredData_AggregatedLevel if is_input_aggregated else BC1_2ScoredData
        )
        aggregated_bc1_2 = AggregatedScore(
            portfolio_level=self._bc1_2_portfolio_level_aggregator(
                scored_data_bc1_2, r1_table, report_type=self.report_type
            ).process(),
            propertysubtype_country_responseid=self._bc1_2_propertysubtype_country_responseid_aggregator(
                scored_data_bc1_2, r1_table, report_type=self.report_type
            ).process(),
            propertysubtype_responseid=self._bc1_2_propertysubtype_responseid_aggregator(
                scored_data_bc1_2,
                r1_table,
                data_model=data_model,
                report_type=self.report_type,
            ).process(),
        )
        return aggregated_bc1_2

    def aggregate_score_bc2(
        self, scored_data_bc2: pd.DataFrame, r1_table: pd.DataFrame
    ) -> AggregatedScore:
        aggregated_bc2 = AggregatedScore(
            portfolio_level=self._bc2_portfolio_level_aggregator(
                scored_data_bc2, r1_table, report_type=self.report_type
            ).process(),
            propertysubtype_country_responseid=self._bc2_propertysubtype_country_responseid_aggregator(
                scored_data_bc2, r1_table, report_type=self.report_type
            ).process(),
            propertysubtype_responseid=self._bc2_propertysubtype_responseid_aggregator(
                scored_data_bc2, r1_table, report_type=self.report_type
            ).process(),
        )
        return aggregated_bc2
