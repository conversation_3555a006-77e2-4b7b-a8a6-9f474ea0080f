import pandas as pd
import numpy as np
from app_real_estate.transformation.scoring.common_indicator_score_utils import (
    CommonIndicatorScoreUtils,
)
from app_real_estate.transformation.scoring.benchmark_score_utils import (
    BenchmarkScoreUtils,
)
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.benchmark_group_memberships_columns as mc
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.building_certification_columns as bc


class BC1AssetScoreCalculator:
    """
    Class responsible for calculating the asset scores for each indicator.
    """

    def __init__(
        self,
        indicator: str,
        asset_score_utils: CommonIndicatorScoreUtils,
        benchmark_score_utils: BenchmarkScoreUtils,
        subscore_weights: dict[str, float],
    ) -> None:
        if indicator not in ["BC1.1", "BC1.2"]:
            raise ValueError(
                "BC1ScoreCalculator initialisation: indicator should be one of 'BC1.1' and 'BC1.2'."
            )
        self.indicator = indicator
        self.asset_score_utils = asset_score_utils
        self.benchmark_score_utils = benchmark_score_utils
        self.subscore_weights = subscore_weights

        if indicator == "BC1.1":
            self.certification_coverage_score_column = (
                sc.certification_score_bc1_1_coverage
            )
            self.certification_score_column = sc.certification_score_bc1_1
            self.coverage_score_column = sc.score_bc1_1_coverage
            self.score_column = sc.score_bc1_1
        elif indicator == "BC1.2":
            self.certification_coverage_score_column = (
                sc.certification_score_bc1_2_coverage
            )
            self.certification_score_column = sc.certification_score_bc1_2
            self.coverage_score_column = sc.score_bc1_2_coverage
            self.score_column = sc.score_bc1_2

    def calculate_certification_coverage_score(
        self,
        certification_data: pd.DataFrame,
        memberships: dict[str, pd.DataFrame],
    ) -> pd.DataFrame:
        """
        Calculate the certifications coverage score.

        Parameters:
        certification_data (pd.DataFrame): the building certifications DataFrame
            containing only certifications of the type "design", "contruction"
            or "interior".
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        pd.DataFrame: the input DataFrame with the added subscore.
        """
        # Benchmark the coverage at the certification level.
        certification_data = self.benchmark_score_utils.calculate_benchmarked_subscore(
            certification_data,
            memberships.get(bc.scoring_coverage),
            mc.bench_certification_coverage,
            mc.mean_certification_coverage,
            self.certification_coverage_score_column,
        )

        # Weight the score with the validation status and the time factor.
        certification_data[self.certification_score_column] = certification_data[
            [
                self.certification_coverage_score_column,
                bc.time_factor_weight,
                bc.validation_status,
            ]
        ].prod(axis=1)

        return certification_data

    def calculate_asset_coverage_score(
        self,
        asset_data: pd.DataFrame,
        certification_data: pd.DataFrame,
    ) -> pd.DataFrame:
        """
        Calculate the asset building certification coverage score.
        Essentially, it aggregates the certification coverage scores at the asset
        level.

        Parameters:
        asset_data (pd.DataFrame): the assets to score.
        certification_data (pd.DataFrame): the assets' building certifications.

        Returns:
        pd.DataFrame: the asset data with the aggregated coverage score.
        """
        # Sum certification fractional scores and cap the sum at 1.
        asset_scores = (
            certification_data.groupby(ac.portfolio_asset_id)[
                self.certification_score_column
            ]
            .agg([(self.coverage_score_column, lambda s: np.minimum(sum(s), 1))])
            .reset_index()
        )

        # Merge the data back in the asset data
        asset_data = asset_data.merge(
            asset_scores, how="left", on=ac.portfolio_asset_id
        )

        return asset_data

    def calculate_asset_score(
        self,
        certification_data: pd.DataFrame,
        asset_data: pd.DataFrame,
        memberships: dict[str, pd.DataFrame],
    ) -> tuple[pd.DataFrame, pd.DataFrame]:
        """
        Calculate the asset scores and subscores.

        Parameters:
        certification_data (pd.DataFrame): the building certifications DataFrame
            containing only certifications of the type "design", "contruction"
            or "interior" for BC1.1 or "operational" for BC1.2.
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        tuple[pd.DataFrame, pd.DataFrame]: the input DataFrames with the added
            score and subscores.
        """
        certification_types = certification_data[bc.type].unique()
        if self.indicator == "BC1.1" and "operational" in certification_types:
            raise ValueError(
                "BC1ScoreCalculator.calculate_asset_score: BC1.1 scoring only applies to non-operational certifications."
            )

        if self.indicator == "BC1.2" and certification_types != ["operational"]:
            raise ValueError(
                "BC1ScoreCalculator.calculate_asset_score: BC1.2 scoring only applies to operational certifications."
            )

        # Score certification coverage and calculate certification score
        certification_data = self.calculate_certification_coverage_score(
            certification_data, memberships
        )

        # Aggregate score to the asset level
        asset_data = self.calculate_asset_coverage_score(asset_data, certification_data)

        # Calculate indicator score
        asset_data = self.asset_score_utils.calculate_indicator_score(
            asset_data, self.subscore_weights, self.score_column
        )

        return certification_data, asset_data
