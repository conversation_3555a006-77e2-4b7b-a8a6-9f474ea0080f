import pandas as pd

from app_real_estate.transformation.scoring.bc1_score_calculator_aggregated_level import (
    BC1ScoreCalculator_AggregatedLevel,
)
from app_real_estate.transformation.scoring.common_indicator_score_utils import (
    CommonIndicatorScoreUtils,
)
from app_real_estate.transformation.scoring.benchmark_score_utils import (
    BenchmarkScoreUtils,
)
from app_real_estate.transformation.scoring.control_based_score_utils import (
    ControlBasedScoreUtils,
)
from app_real_estate.transformation.scoring.en1_score_calculator import (
    EN1ScoreCalculator,
)
from app_real_estate.transformation.scoring.gh1_score_calculator import (
    GH1ScoreCalculator,
)
from app_real_estate.transformation.scoring.ws1_score_calculator import (
    WS1ScoreCalculator,
)
from app_real_estate.transformation.scoring.wt1_score_calculator import (
    WT1ScoreCalculator,
)
from app_real_estate.transformation.scoring.bc1_asset_score_calculator import (
    BC1AssetScoreCalculator,
)
from app_real_estate.transformation.scoring.bc2_score_calculator import (
    BC2ScoreCalculator,
)
from app_real_estate.constants.indicator_subscore_weights import (
    indicator_weights,
    BaseIndicatorWeights,
)


class IndicatorScorer:
    """
    IndicatorScorer is a wrapper class similar to the Benchmarker and DataPreparer
    classes. Its goal is to define functions launching the scoring pipelines for each
    indicator separately.
    This scoring pipeline includes asset scoring, metric aggregation and score
    aggregation to the different levels (property subtype/country, property subtype
    and portfolio level).
    """

    def __init__(self, report_type: str = "re") -> None:
        """
        Initialize IndicatorScorer with a specific report type.

        Args:
            report_type: The report type ('re' for Real Estate, 'res' for Residential)
        """
        self.report_type = report_type
        self.report_type_score_weights: BaseIndicatorWeights = indicator_weights(
            report_type
        )

        asset_score_utils = CommonIndicatorScoreUtils()
        benchmark_score_utils = BenchmarkScoreUtils()
        control_score_utils = ControlBasedScoreUtils()
        self.en1_score_calculator = EN1ScoreCalculator(
            asset_score_utils,
            benchmark_score_utils,
            control_score_utils,
            self.report_type_score_weights.EN1_SUBSCORE_WEIGHTS,
        )
        self.gh1_score_calculator = GH1ScoreCalculator(
            asset_score_utils,
            benchmark_score_utils,
            control_score_utils,
            self.report_type_score_weights.GH1_SUBSCORE_WEIGHTS,
        )
        self.ws1_score_calculator = WS1ScoreCalculator(
            asset_score_utils,
            benchmark_score_utils,
            control_score_utils,
            self.report_type_score_weights.WS1_SUBSCORE_WEIGHTS,
        )
        self.wt1_score_calculator = WT1ScoreCalculator(
            asset_score_utils,
            benchmark_score_utils,
            control_score_utils,
            self.report_type_score_weights.WT1_SUBSCORE_WEIGHTS,
        )
        self.bc1_1_score_calculator = BC1AssetScoreCalculator(
            "BC1.1",
            asset_score_utils,
            benchmark_score_utils,
            self.report_type_score_weights.BC1_1_SUBSCORE_WEIGHTS,
        )
        self.bc1_2_score_calculator = BC1AssetScoreCalculator(
            "BC1.2",
            asset_score_utils,
            benchmark_score_utils,
            self.report_type_score_weights.BC1_2_SUBSCORE_WEIGHTS,
        )
        self.bc2_score_calculator = BC2ScoreCalculator(
            asset_score_utils,
            benchmark_score_utils,
            self.report_type_score_weights.BC2_SUBSCORE_WEIGHTS,
        )
        self.bc1_1_aggregated_score_calculator = BC1ScoreCalculator_AggregatedLevel(
            "BC1.1",
            asset_score_utils,
            benchmark_score_utils,
            self.report_type_score_weights.BC1_1_SUBSCORE_WEIGHTS,
            self.report_type_score_weights.BC1_1_MAX_SCORE,
        )
        self.bc1_2_aggregated_score_calculator = BC1ScoreCalculator_AggregatedLevel(
            "BC1.2",
            asset_score_utils,
            benchmark_score_utils,
            self.report_type_score_weights.BC1_2_SUBSCORE_WEIGHTS,
            self.report_type_score_weights.BC1_2_MAX_SCORE,
        )

    # TODO: Add metric and score aggregation to each indicator and change docstring.
    def score_en1(
        self, asset_data: pd.DataFrame, memberships: dict[str, pd.DataFrame]
    ) -> pd.DataFrame:
        """
        Calculate the scores and subscores for indicator EN1.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        (pd.DataFrame): the input DataFrame with the added score and subscores.
        """
        return self.en1_score_calculator.calculate_asset_score(asset_data, memberships)

    def score_gh1(
        self, asset_data: pd.DataFrame, memberships: dict[str, pd.DataFrame]
    ) -> pd.DataFrame:
        """
        Calculate the scores and subscores for indicator GH1.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        (pd.DataFrame): the input DataFrame with the added score and subscores.
        """
        return self.gh1_score_calculator.calculate_asset_score(asset_data, memberships)

    def score_ws1(
        self, asset_data: pd.DataFrame, memberships: dict[str, pd.DataFrame]
    ) -> pd.DataFrame:
        """
        Calculate the scores and subscores for indicator WS1.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        (pd.DataFrame): the input DataFrame with the added score and subscores.
        """
        return self.ws1_score_calculator.calculate_asset_score(asset_data, memberships)

    def score_wt1(
        self, asset_data: pd.DataFrame, memberships: dict[str, pd.DataFrame]
    ) -> pd.DataFrame:
        """
        Calculate the scores and subscores for indicator WT1.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        (pd.DataFrame): the input DataFrame with the added score and subscores.
        """
        return self.wt1_score_calculator.calculate_asset_score(asset_data, memberships)

    def score_bc1_1(
        self,
        certification_data: pd.DataFrame,
        asset_data: pd.DataFrame,
        memberships: dict[str, pd.DataFrame],
    ) -> tuple[pd.DataFrame, pd.DataFrame]:
        """
        Calculate the scores and subscores for indicator BC1.1.

        Parameters:
        certification_data (pd.DataFrame): the building certifications associated
            to the scored assets.
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        tuple[pd.DataFrame, pd.DataFrame]: the input DataFrames with the added
            score and subscores.
        """
        return self.bc1_1_score_calculator.calculate_asset_score(
            certification_data, asset_data, memberships
        )

    def score_bc1_2(
        self,
        certification_data: pd.DataFrame,
        asset_data: pd.DataFrame,
        memberships: dict[str, pd.DataFrame],
    ) -> tuple[pd.DataFrame, pd.DataFrame]:
        """
        Calculate the scores and subscores for indicator BC1.2.

        Parameters:
        certification_data (pd.DataFrame): the building certifications associated
            to the scored assets.
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        tuple[pd.DataFrame, pd.DataFrame]: the input DataFrames with the added
            score and subscores.
        """
        return self.bc1_2_score_calculator.calculate_asset_score(
            certification_data, asset_data, memberships
        )

    def score_bc2(
        self, asset_data: pd.DataFrame, memberships: dict[str, pd.DataFrame]
    ) -> pd.DataFrame:
        """
        Calculate the scores and subscores for indicator BC2.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        (pd.DataFrame): the input DataFrame with the added score and subscores.
        """
        return self.bc2_score_calculator.calculate_asset_score(asset_data, memberships)

    def score_bc1_1_aggregated(
        self,
        certification_data: pd.DataFrame,
        memberships: dict[str, pd.DataFrame],
    ) -> pd.DataFrame:
        """
        Calculate the scores and subscores for indicator BC1.1 directly at the property subtype and country level.

        Parameters:
        certification_data (pd.DataFrame): the building certifications associated
            to the scored assets.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        pd.DataFrame: the input DataFrames with the added score and subscores.
        """
        return self.bc1_1_aggregated_score_calculator.calculate_score(
            certification_data, memberships
        )

    def score_bc1_2_aggregated(
        self,
        certification_data: pd.DataFrame,
        memberships: dict[str, pd.DataFrame],
    ) -> pd.DataFrame:
        """
        Calculate the scores and subscores for indicator BC1.2 directly at the property subtype and country level.

        Parameters:
        certification_data (pd.DataFrame): the building certifications associated
            to the scored assets.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        pd.DataFrame: the input DataFrames with the added score and subscores.
        """
        return self.bc1_2_aggregated_score_calculator.calculate_score(
            certification_data, memberships
        )
