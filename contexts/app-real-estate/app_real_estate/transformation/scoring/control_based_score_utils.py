import pandas as pd
import numpy as np
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.water_columns as wc
import app_real_estate.constants.column_names.waste_columns as wsc
import app_real_estate.constants.column_names.ghg_columns as gc
from app_real_estate.transformation.metric_calculation.asset_filters import (
    AssetFilters,
)
from app_real_estate.constants.helper_enumerators import Utility, Control, Scope


class ControlBasedScoreUtils:
    """
    ControlBasedScoreUtils provides helper functions for <INDICATOR>ScoreCalculator classes.

    This class provides functions to calculate the control weights per asset.
    In the indicators EN1, WT1, WS1 and GH1, the utility is reported in different
    categories, Landlord Controlled (LC) / Tenant Controlled (TC) for energy,
    water and waste, Scopes 1 and 2 / Scope 3 for GHG. These categories do not hold the
    same weight in the scoring and the functions defined here implement the
    methodology to calculate them using the asset data.

    Usage:
        control_weight_utils = ControlBasedScoreUtils()
        en1_score_calculator = EN1ScoreCalculator(asset_scoring_utils, benchmark_scoring_utils, control_weight_utils, subscore_weights.EN1_SUBSCORE_WEIGHTS)
        scored_data = calculator.calculate_asset_score(asset_data, memberships)
    """

    def sum_control_based_subscores(
        self,
        asset_data: pd.DataFrame,
        subscore_column1: str,
        subscore_column2: str,
        is_lfl: bool,
        utility: Utility,
    ) -> pd.Series:
        """
        Calculate the subscore by weighting the two LC and TC subscores.

        Parameters:
        asset_data (pd.DataFrame): the asset data with subscores.
        subscore_column1 (str): the LC subscore column name.
        subscore_column2 (str): the TC subscore column name.
        is_lfl (bool): indicates whether the subscores are for LFL metrics.
        utility (str): indicates for which utility to compute the weights.

        Returns:
        pd.Series: a series containing the resulting combined subscore.
        """
        if utility in [Utility.Energy, Utility.Water]:
            # If the utility is energy or water, then the metric is divided by
            # controls i.e. LC/TC.
            weights1 = self.calculate_subscore_weight_per_control(
                asset_data, Control.LC, is_lfl, utility
            )
            weights2 = self.calculate_subscore_weight_per_control(
                asset_data, Control.TC, is_lfl, utility
            )

            # Check that the weights sum is either 1 or missing.
            if any(~(weights1.add(weights2)).round(4).isin([1, 0])):
                raise ValueError("Sum of the weights should be 1.")

            if any(
                np.logical_and(
                    (weights1.add(weights2).round(4) == 0),
                    asset_data[[subscore_column1, subscore_column2]].sum(axis=1) != 0,
                )
            ):
                raise ValueError(
                    "Weight sum can only be null if both subscores are null (i.e. the asset is not scored for those metrics)."
                )

        elif utility == Utility.GHG:
            # If the utility is GHG, then the metric is divided by scopes i.e. S12/S3.
            weights1 = self.calculate_subscore_weight_per_scope(
                asset_data, Scope.S12, is_lfl
            )
            weights2 = self.calculate_subscore_weight_per_scope(
                asset_data, Scope.S3, is_lfl
            )

            # Re-scale the weights as fractions in order to use a weighted sum.
            # When weights sum up to 0, we change it to 1 to avoid a division error.
            s12_3_weight_sum = weights1.add(weights2)
            s12_3_weight_sum.loc[s12_3_weight_sum == 0] = 1

            weights1 = weights1 / s12_3_weight_sum
            weights2 = weights2 / s12_3_weight_sum

        elif utility == Utility.Waste:
            # If the utility is Waste, then the metric is divided by control but differently than Energy and Water.
            weights1 = self.calculate_waste_subscore_weight_per_control(
                asset_data, Control.LC
            )
            weights2 = self.calculate_waste_subscore_weight_per_control(
                asset_data, Control.TC
            )

            # Re-scale the weights as fractions in order to use a weighted sum.
            # When weights sum up to 0, we change it to 1 to avoid a division error.
            lc_tc_weight_sum = weights1.add(weights2)
            lc_tc_weight_sum.loc[lc_tc_weight_sum == 0] = 1

            weights1 = weights1 / lc_tc_weight_sum
            weights2 = weights2 / lc_tc_weight_sum
        else:
            raise ValueError(
                "Parameter `utility` should be one of the recorded values in the Utility Enum."
            )

        # Weighted sum between LC/TC or S12/S3 subscores and their weights.
        asset_data = asset_data.copy()
        asset_data["weighted_subscore1"] = asset_data[subscore_column1] * weights1
        asset_data["weighted_subscore2"] = asset_data[subscore_column2] * weights2
        weighted_score = asset_data[["weighted_subscore1", "weighted_subscore2"]].sum(
            min_count=1, axis=1
        )

        return weighted_score

    def calculate_subscore_weight_per_control(
        self,
        asset_data: pd.DataFrame,
        control: Control,
        is_lfl: bool,
        utility: Utility,
    ) -> pd.Series:
        """
        Calculate the subscore weights depending on `control`.

        Parameters:
        asset_data (pd.DataFrame): the assets data with the subscores.
        control (str): Indicates whether the areas (or consumption for LFL) to
            get the weight for are tenant or landlord controlled.

        Returns:
        pd.Series: the subscore weights per control.
        """
        lc_t_weight, tc_t_weight = self._get_tenant_spaces_weights(is_lfl, utility)
        whole_tenant_weight = 0 if control == Control.LC else 1
        ts_base_control_weight = lc_t_weight if control == Control.LC else tc_t_weight

        weights = pd.Series(index=asset_data.index)

        # If the building is reported as 'Whole Building', then it is either
        # entirely controlled by tenant or landlord.
        is_whole_building = AssetFilters.filter_whole_building_assets_per_utility(
            asset_data, utility
        )
        is_whole_tenant = asset_data[is_whole_building][ac.tenant_ctrl]
        weights.loc[is_whole_building] = np.where(
            is_whole_tenant, whole_tenant_weight, int(not whole_tenant_weight)
        )

        # If not, then asset reporting is 'divided' between Base Building and
        # Tenant Spaces, and it gets more complicated. We say the asset is
        # reported by subspaces.
        assets_reported_by_subspaces = asset_data[~is_whole_building]

        # Because of the shared services in the asset, there can be an overlap
        # in the control of tenant spaces. This means that for weighting purposes
        # we should consider the total tenant spaces floor area (or consumption
        # for LFL) as the sum of the tenant and landlord controlled tenant spaces
        # areas rather than the reported value in `asset_size_tenant_m2` (no
        # replacement for LFL).
        tenant_spaces_denominator = assets_reported_by_subspaces[
            [lc_t_weight, tc_t_weight]
        ].sum(axis=1)

        # Since Base Building is landlord controlled by essence, the complication
        # lies in the Tenant Spaces share. If the asset has tenant controlled
        # tenant spaces then the landlord controlled weight is:
        # 0.4 (base building) + 0.6 (tenant spaces) * the share of landlord
        # controlled tenant spaces. The tenant controlled weight is then only:
        # 0.6 (tenant spaces) * the share of tenant controlled tenant spaces.
        has_tenant_controlled_tenant_spaces = (
            ~assets_reported_by_subspaces[tc_t_weight].isin([0, np.nan])
            if is_lfl
            else assets_reported_by_subspaces[tc_t_weight] > 0
        )
        percentage_tenant_spaces_specific_control = (
            assets_reported_by_subspaces[ts_base_control_weight]
            / tenant_spaces_denominator
        )
        percentage_tenant_spaces_specific_control = (
            percentage_tenant_spaces_specific_control.fillna(0)
        )
        has_base = self._has_base_building(
            assets_reported_by_subspaces, utility, is_lfl
        )

        if control == Control.LC:
            weights.loc[~is_whole_building] = np.where(
                has_tenant_controlled_tenant_spaces,
                np.where(
                    has_base,
                    0.4 + 0.6 * percentage_tenant_spaces_specific_control,
                    percentage_tenant_spaces_specific_control,
                ),
                1,
            )
        else:
            weights.loc[~is_whole_building] = np.where(
                has_tenant_controlled_tenant_spaces,
                np.where(
                    has_base,
                    0.6 * percentage_tenant_spaces_specific_control,
                    percentage_tenant_spaces_specific_control,
                ),
                0,
            )

        # If the subscores are for LFL metrics, we want to include the outdoor
        # areas consumption in the weights. For that, we are going to rescale
        # the weights (which are indoor only) by multiplying them with the total
        # indoor consumption. Then, add the outdoor consumption for the specific
        # control. Then finally, divide by the total consumption.
        if is_lfl:
            (
                outdoor_control_weight_column,
                total_indoor_weight_column,
                total_weight_column,
            ) = self._get_lfl_control_weights(utility, control)

            weights = (
                weights.mul(asset_data[total_indoor_weight_column], fill_value=0).add(
                    asset_data[outdoor_control_weight_column], fill_value=0
                )
            ) / asset_data[total_weight_column]

        weights.fillna(0, inplace=True)

        return weights

    @staticmethod
    def calculate_subscore_weight_per_scope(
        asset_data: pd.DataFrame, scopes: Scope, is_lfl: bool
    ) -> pd.Series:
        """
        Calculate the subscore weights depending on `scopes`.

        Parameters:
        asset_data (pd.DataFrame): the assets data with the subscores.
        scopes (Scope): Indicates whether to calculate the weights on Scopes 1 and
            2 area or Scope 3 area.

        Returns:
        pd.Series: the subscore weights per scopes.
        """
        if scopes == Scope.S12:
            weight_column = gc.ghg_lfl_abs_s12_ly if is_lfl else gc.ghg_area_p_s12
        elif scopes == Scope.S3:
            weight_column = gc.ghg_lfl_abs_s3_ly if is_lfl else gc.ghg_area_p_s3
        else:
            raise ValueError(
                "Parameter `scopes` should be one of the recorded values in the Enum Scope."
            )

        weights = (asset_data[ac.asset_ownership] / 100) * (
            asset_data[weight_column] / 100
        )

        return weights.fillna(0)

    @staticmethod
    def calculate_waste_subscore_weight_per_control(
        asset_data, control: Control
    ) -> pd.Series:
        """
        Calculate the subscore weights depending on `control`.

        Parameters:
        asset_data (pd.DataFrame): the assets data with the subscores.
        control (Control): Indicates whether to calculate the weights for LC or TC.

        Returns:
        pd.Series: the subscore weights per control.
        """
        if control == Control.LC:
            area_p_column = wsc.was_area_p_lc
        elif control == Control.TC:
            area_p_column = wsc.was_area_p_tc
        else:
            raise ValueError(
                "Parameter `control` should be one of the recorded values in the Enum Control."
            )

        weights = (asset_data[ac.asset_ownership] / 100) * (
            asset_data[area_p_column] / 100
        )

        return weights.fillna(0)

    @staticmethod
    def _has_base_building(
        asset_data: pd.DataFrame, utility: Utility, is_lfl: bool
    ) -> list[bool]:
        """
        Calculates whether the assets have base building weights.

        Parameters:
        asset_data (pd.DataFrame): the assets and their characteristics.
        utility (str): the utility to check for.
        is_lfl (bool): whether to check the LFL consumption or the floor area.

        Returns:
        list[bool]: boolean list indicating whether the assets have base building weights.
        """
        if utility == Utility.Energy:
            weight_bc_col = (
                ec.en_lfl_abs_lc_bc_ly if is_lfl else ac.asset_size_common_m2
            )
            weight_bs_col = (
                ec.en_lfl_abs_lc_bs_ly if is_lfl else ac.asset_size_shared_m2
            )

        if utility == Utility.Water:
            weight_bc_col = wc.wat_lfl_abs_lc_bc_ly if is_lfl else wc.wat_tot_lc_bc_m2
            weight_bs_col = wc.wat_lfl_abs_lc_bs_ly if is_lfl else wc.wat_tot_lc_bs_m2

        return asset_data[[weight_bc_col, weight_bs_col]].sum(axis=1) > 0

    @staticmethod
    def _get_tenant_spaces_weights(is_lfl: bool, utility: Utility) -> tuple[str, str]:
        """
        Get the variable names for the tenant spaces weights based on the utility and if it's
        for LFL or coverage weighting.
        Parameters:
            is_lfl (bool): whether the LC/TC weighting is for LFL or coverage subscores.
            utility (str): the utility to check for.
        Returns:
            tuple[str, str]: variable names for the tenant spaces weights (LC and TC).
        """

        # For LFL, the base weights are not the floor areas but the previous year
        # consumption.
        parameters = pd.DataFrame(
            {
                "utility": [Utility.Energy, Utility.Water] * 2,
                "is_lfl": [True, True, False, False],
                "lc_t_weight": [
                    ec.en_lfl_abs_lc_t_ly,
                    wc.wat_lfl_abs_lc_t_ly,
                    ac.asset_size_tenant_landlord_m2,
                    wc.wat_tot_lc_t,
                ],
                "tc_t_weight": [
                    ec.en_lfl_abs_tc_t_ly,
                    wc.wat_lfl_abs_tc_t_ly,
                    ac.asset_size_tenant_tenant_m2,
                    wc.wat_tot_tc_t,
                ],
            }
        )
        parameter_line = parameters.loc[
            (parameters.utility == utility) & (parameters.is_lfl == is_lfl)
        ].iloc[0]

        return parameter_line["lc_t_weight"], parameter_line["tc_t_weight"]

    @staticmethod
    def _get_lfl_control_weights(
        utility: Utility, control: Control
    ) -> tuple[str, str, str]:
        """
        Get the variable names for the previous year LFL consumption for:
            - the outdoor area in the specific `control`
            - the total indoor area
            - the total area
        Parameters:
            utility (str): the utility to check for.
            control (str): the control to check for.
        Returns:
            tuple[str, str, str]: variable names for the previous year LFL consumption.
        """
        parameters = pd.DataFrame(
            {
                "utility": [Utility.Energy, Utility.Water] * 2,
                "control": [Control.LC] * 2 + [Control.TC] * 2,
                "outdoor_var": [
                    ec.en_lfl_abs_lc_o_ly,
                    wc.wat_lfl_abs_lc_o_ly,
                    ec.en_lfl_abs_tc_o_ly,
                    wc.wat_lfl_abs_tc_o_ly,
                ],
                "indoor_var": [ec.en_lfl_abs_in_ly, wc.wat_lfl_abs_in_ly] * 2,
                "total_var": [ec.en_lfl_abs_ly, wc.wat_lfl_abs_ly] * 2,
            }
        )

        parameter_line = parameters.loc[
            (parameters.utility == utility) & (parameters.control == control)
        ].iloc[0]

        return (
            parameter_line["outdoor_var"],
            parameter_line["indoor_var"],
            parameter_line["total_var"],
        )
