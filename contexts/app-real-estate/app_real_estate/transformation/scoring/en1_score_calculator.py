import pandas as pd
import numpy as np

from app_real_estate.constants.helper_enumerators import Utility
from app_real_estate.transformation.metric_calculation.asset_filters import (
    AssetFilters as af,
)
from app_real_estate.transformation.scoring.common_indicator_score_utils import (
    CommonIndicatorScoreUtils,
)
from app_real_estate.transformation.scoring.benchmark_score_utils import (
    BenchmarkScoreUtils,
)
from app_real_estate.transformation.scoring.control_based_score_utils import (
    ControlBasedScoreUtils,
)
import app_real_estate.constants.column_names.benchmark_group_memberships_columns as mc
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.energy_columns as ec
from app_real_estate.constants.indicator_subscore_weights import indicator_weights


class EN1ScoreCalculator:
    """
    EN1ScoreCalculator calculates asset scores for the EN1 (Energy) indicator.

    This class provides methods to calculate various subscores and the overall
    score for the EN1 indicator, which measures energy consumption and renewable
    energy usage in real estate assets.

    Key functionalities include:
    - Calculating data coverage scores
    - Evaluating Like-For-Like (LFL) energy consumption changes
    - Assessing renewable energy generation and usage
    - Combining subscores to produce an overall EN1 indicator score

    The class uses benchmark comparisons and weighted scoring methods to evaluate
    asset performance across multiple energy-related metrics.

    Usage:
        calculator = EN1ScoreCalculator()
        scored_data = calculator.calculate_asset_score(asset_data, memberships)

    Note: This class requires preprocessed asset data and benchmark group memberships
    as inputs.
    """

    def __init__(
        self,
        asset_score_utils: CommonIndicatorScoreUtils,
        benchmark_score_utils: BenchmarkScoreUtils,
        control_score_utils: ControlBasedScoreUtils,
        subscore_weights: dict[str, float],
        report_type: str = "re",
    ) -> None:
        self.asset_score_utils = asset_score_utils
        self.benchmark_score_utils = benchmark_score_utils
        self.control_score_utils = control_score_utils
        self.subscore_weights = subscore_weights
        self.report_type_score_weights = indicator_weights(report_type)

    def calculate_asset_score(
        self, asset_data: pd.DataFrame, memberships: dict[str, pd.DataFrame]
    ) -> pd.DataFrame:
        """
        Calculate the asset scores and subscores.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        (pd.DataFrame): the input DataFrame with the added score and subscores.
        """
        required_columns = [
            ec.en_area_time_cov_p_lc,
            ec.en_area_time_cov_p_tc,
            ec.en_area_time_weight_lc,
            ec.en_area_time_weight_tc,
            ec.en_lfl_p,
            ec.en_lfl_outlier_status,
            ec.en_lfl_percent_change_lc,
            ec.en_lfl_percent_change_tc,
            ec.en_ren_ons,
            ec.en_ren_ofs,
            ec.en_ren_percent_change,
            ec.en_ren_rate,
            ec.en_efficiency_int_kwh_m2,
        ]

        required_scores = [
            sc.score_en1_en_area_time_cov_p,
            sc.score_en1_en_ren_ons,
            sc.score_en1_en_ren_ofs,
            sc.score_en1_en_ren_percent_change,
            sc.score_en1_en_ren_performance,
        ]

        return self.asset_score_utils.calculate_asset_score(
            asset_data,
            memberships,
            required_columns,
            required_scores,
            sc.score_en1,
            self.subscore_weights,
            [
                self.calculate_asset_coverage_score,
                self.calculate_asset_energy_performance_score,
                self.calculate_asset_renewable_score,
            ],
        )

    def calculate_asset_coverage_score(
        self, asset_data: pd.DataFrame, memberships: dict[str, pd.DataFrame]
    ) -> pd.DataFrame:
        """
        Calculate the data coverage subscore.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        (pd.DataFrame): the input DataFrame with the added subscore.
        """
        # Compare LC/TC coverage against benchmark
        asset_data = self.benchmark_score_utils.calculate_benchmarked_subscore(
            asset_data,
            memberships.get(ec.en_area_time_cov_p_lc),
            mc.bench_en_area_time_cov_p_lc,
            mc.mean_en_area_time_cov_p_lc,
            sc.score_en1_en_area_time_cov_p_lc,
        )
        asset_data = self.benchmark_score_utils.calculate_benchmarked_subscore(
            asset_data,
            memberships.get(ec.en_area_time_cov_p_tc),
            mc.bench_en_area_time_cov_p_tc,
            mc.mean_en_area_time_cov_p_tc,
            sc.score_en1_en_area_time_cov_p_tc,
        )

        # Weighted sum of LC and TC for coverage
        asset_data[sc.score_en1_en_area_time_cov_p] = (
            self.control_score_utils.sum_control_based_subscores(
                asset_data,
                sc.score_en1_en_area_time_cov_p_lc,
                sc.score_en1_en_area_time_cov_p_tc,
                False,
                Utility.Energy,
            )
        )

        return asset_data

    def calculate_asset_energy_performance_score(
        self, asset_data: pd.DataFrame, memberships: dict[str, pd.DataFrame]
    ) -> pd.DataFrame:
        """
        Calculate the Energy data performance score by calculating LFL and Energy Efficiency scores then choosing the biggest.
        Parameters:
            asset_data (pd.DataFrame): the asset DataFrame.
            memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.
        Returns:
            (pd.DataFrame): the input DataFrame with the added subscore.
        """
        asset_data = self.calculate_asset_lfl_score(asset_data, memberships)
        asset_data = self.calculate_asset_energy_efficiency_score(asset_data)

        # Calculate the energy performance score by getting the maximum (ignoring NAs) between energy efficiency and LFL scores.
        asset_data = self._calculate_asset_energy_performance_score(asset_data)

        return asset_data

    @staticmethod
    def _calculate_asset_energy_performance_score(
        asset_data: pd.DataFrame,
    ) -> pd.DataFrame:
        """
        Sub-function to calculate the assets' energy performance score. It is separated from the main one in order
        to be unit-testable.
        Parameters:
            asset_data (pd.DataFrame): the asset DataFrame.
        Returns:
            (pd.DataFrame): the input DataFrame with the added subscore.
        """
        asset_data[sc.score_en1_energy_performance] = asset_data[
            [sc.score_en1_energy_efficiency, sc.score_en1_en_lfl]
        ].max(axis=1)

        return asset_data

    def calculate_asset_lfl_score(
        self, asset_data: pd.DataFrame, memberships: dict[str, pd.DataFrame]
    ) -> pd.DataFrame:
        """
        Calculate the Like-For-Like subscore.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        (pd.DataFrame): the input DataFrame with the added subscore.
        """
        # Score LFL data availability
        asset_data = self.calculate_asset_lfl_availability_score(asset_data)

        # Score LFL performance
        asset_data = self.benchmark_score_utils.calculate_benchmarked_subscore(
            asset_data,
            memberships.get(ec.en_lfl_percent_change_lc),
            mc.bench_en_lfl_percent_change_lc,
            mc.mean_en_lfl_percent_change_lc,
            sc.score_en1_en_lfl_percent_change_lc,
            is_lfl=True,
        )
        asset_data = self.benchmark_score_utils.calculate_benchmarked_subscore(
            asset_data,
            memberships.get(ec.en_lfl_percent_change_tc),
            mc.bench_en_lfl_percent_change_tc,
            mc.mean_en_lfl_percent_change_tc,
            sc.score_en1_en_lfl_percent_change_tc,
            is_lfl=True,
        )
        # TODO: find better way to handle rejected outliers
        asset_data = self.asset_score_utils.set_lfl_rejected_outlier_scores_to_zero(
            asset_data,
            sc.score_en1_en_lfl_percent_change_lc,
            ec.en_lfl_percent_change_lc,
            ec.en_lfl_outlier_status,
        )
        asset_data = self.asset_score_utils.set_lfl_rejected_outlier_scores_to_zero(
            asset_data,
            sc.score_en1_en_lfl_percent_change_tc,
            ec.en_lfl_percent_change_tc,
            ec.en_lfl_outlier_status,
        )

        # Weighted sum of LC and TC for LFL
        asset_data[sc.score_en1_en_lfl_percent_change] = (
            self.control_score_utils.sum_control_based_subscores(
                asset_data,
                sc.score_en1_en_lfl_percent_change_lc,
                sc.score_en1_en_lfl_percent_change_tc,
                True,
                Utility.Energy,
            )
        )

        # Combine availability and benchmarked scores to create LFL score
        asset_data = self._calculate_total_lfl_score(asset_data)

        return asset_data

    def calculate_asset_lfl_availability_score(
        self,
        asset_data: pd.DataFrame,
    ) -> pd.DataFrame:
        """
        Calculate the Like-For-Like data availability subscore.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.

        Returns:
        pd.DataFrame: the input DataFrame with the added subscore.
        """
        is_lfl_available = (~asset_data[ec.en_lfl_p].isna()).tolist()
        is_lfl_valid = af.filter_outliers(
            asset_data, ec.en_lfl_outlier_status, ["none", "accepted"]
        )
        asset_data[sc.score_en1_lfl_availability] = np.where(
            np.logical_and(is_lfl_available, is_lfl_valid), 1, np.nan
        )

        # Set eligible hard outliers' availability scores to 0.
        asset_data = self.asset_score_utils.set_lfl_rejected_outlier_scores_to_zero(
            asset_data,
            sc.score_en1_lfl_availability,
            ec.en_lfl_p,
            ec.en_lfl_outlier_status,
        )

        return asset_data

    def _calculate_total_lfl_score(self, asset_data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate the total asset LFL fractional score.

        Parameters:
            asset_data (pd.DataFrame): the asset DataFrame.

        Returns:
            (pd.DataFrame): the input DataFrame with the added subscore.
        """
        absolute_availability_scores = (
            asset_data[sc.score_en1_lfl_availability]
            * self.report_type_score_weights.EN1_LFL_AVAILABILITY_MAX_SCORE
        )
        absolute_perf_scores = (
            asset_data[sc.score_en1_en_lfl_percent_change]
            * self.report_type_score_weights.EN1_LFL_PERF_MAX_SCORE
        )
        asset_data[sc.score_en1_en_lfl] = absolute_availability_scores.add(
            absolute_perf_scores, fill_value=0
        )
        asset_data[sc.score_en1_en_lfl] = asset_data[sc.score_en1_en_lfl].div(
            self.report_type_score_weights.EN1_LFL_MAX_SCORE
        )
        return asset_data

    @staticmethod
    def calculate_asset_energy_efficiency_score(
        asset_data: pd.DataFrame,
    ) -> pd.DataFrame:
        """
        Calculate the energy efficiency subscore.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.

        Returns:
        pd.DataFrame: the input DataFrame with the added subscore.
        """
        is_intensity_below_threshold = (
            asset_data[ec.en_efficiency_int_kwh_m2]
            <= asset_data[ec.ashrae_intensity_threshold]
        )
        asset_data.loc[is_intensity_below_threshold, sc.score_en1_energy_efficiency] = 1
        asset_data.loc[
            ~is_intensity_below_threshold, sc.score_en1_energy_efficiency
        ] = np.nan

        return asset_data

    @staticmethod
    def calculate_asset_renewable_availability_score(
        asset_data: pd.DataFrame,
    ) -> pd.DataFrame:
        """
        Calculate the renewable energy generation data availability subscore.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.

        Returns:
        pd.DataFrame: the input DataFrame with the added subscore.
        """
        has_onsite_renewable = (
            ~asset_data[ec.en_ren_ons].isna() & asset_data[ec.en_ren_ons] != 0
        )
        has_offsite_renewable = (
            ~asset_data[ec.en_ren_ofs].isna() & asset_data[ec.en_ren_ofs] != 0
        )

        asset_data[sc.score_en1_en_ren_ons] = np.where(has_onsite_renewable, 1, 0)
        asset_data[sc.score_en1_en_ren_ofs] = np.where(has_offsite_renewable, 1, 0)
        asset_data[sc.score_en1_en_ren_availability] = np.minimum(
            asset_data[sc.score_en1_en_ren_ons]
            + (asset_data[sc.score_en1_en_ren_ofs] * 0.5),
            1,
        )

        return asset_data

    def calculate_asset_renewable_performance_score(
        self, asset_data: pd.DataFrame, memberships: dict[str, pd.DataFrame]
    ) -> pd.DataFrame:
        """
        Calculate the renewable energy generation performance subscore.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        pd.DataFrame: the input DataFrame with the added subscore.
        """
        asset_data = self.benchmark_score_utils.calculate_asset_performance_score(
            asset_data,
            memberships,
            ec.en_ren_rate,
            ec.en_ren_percent_change,
            mc.bench_en_ren_percent_change,
            mc.mean_en_ren_percent_change,
            sc.score_en1_en_ren_percent_change,
            sc.score_en1_en_ren_performance,
        )

        return asset_data

    def calculate_asset_renewable_score(
        self, asset_data: pd.DataFrame, memberships: dict[str, pd.DataFrame]
    ) -> pd.DataFrame:
        """
        Calculate the renewable energy subscore.

        Parameters:
        asset_data (pd.DataFrame): the asset DataFrame.
        memberships (dict[str, pd.DataFrame]): a dictionary in which for each
            metric is assigned a benchmark group memberships DataFrame.

        Returns:
        pd.DataFrame: the input DataFrame with the added subscore.
        """
        # Score the renewable energy generation availability
        asset_data = self.calculate_asset_renewable_availability_score(asset_data)

        # Score the renewable energy generation performance
        asset_data = self.calculate_asset_renewable_performance_score(
            asset_data, memberships
        )

        return asset_data
