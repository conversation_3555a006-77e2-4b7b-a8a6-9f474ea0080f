import argparse
import pandas as pd
import logging
import sys
from dotenv import load_dotenv

from app_real_estate.transformation.aggregation.lender_metric_aggregation_pipeline import (
    LenderMetricAggregationPipeline,
)
from app_real_estate.transformation.aggregation.metric_aggregation_pipeline import (
    MetricAggregationPipeline,
)
from config import GOLD_DATA_BUCKET
from data_io.data_s3_gateway import DataS3Gateway
from data_io.s3_file import S3File
from app_real_estate.models.input_models.r1_table_lender_model import R1TableLenderModel
import app_real_estate.constants.column_names.building_certification_columns as bc
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
from app_real_estate.transformation.metric_calculation.asset_filters import AssetFilters
from app_real_estate.transformation.aggregation.processor.lender_certifications_pre_processor import (
    LenderCertificationsPreProcessor,
)
from app_real_estate.transformation.aggregation.processor.lender_energy_ratings_pre_processor import (
    LenderEnergyRatingsPreProcessor,
)


def run_lender_asset_aggregation(
    survey_year: int, logger: logging.Logger, is_local: bool = False
):

    s3_gateway = DataS3Gateway()

    # Non-aggregated asset level data with additional columns
    asset_level_data = None
    r1_table = None
    certification_data = None
    energy_ratings_data = None

    if is_local:
        asset_level_data = pd.read_parquet(
            f"contexts/app-real-estate/app_real_estate/tmp/data/{survey_year}/rel/input/asset_level_data.parquet"
        )
        r1_table = pd.read_parquet(
            f"contexts/app-real-estate/app_real_estate/tmp/data/{survey_year}/rel/input/r1_table_data.parquet"
        )
        certification_data = pd.read_parquet(
            f"contexts/app-real-estate/app_real_estate/tmp/data/{survey_year}/rel/input/asset_certification_data.parquet"
        )
        energy_ratings_data = pd.read_parquet(
            f"contexts/app-real-estate/app_real_estate/tmp/data/{survey_year}/rel/input/asset_energy_rating_data.parquet"
        )
        logger.info(
            f"Loaded local asset level data from {asset_level_data.shape[0]} assets"
        )
    else:
        asset_level_data = s3_gateway.import_data(
            S3File(
                bucket_name=GOLD_DATA_BUCKET,
                base_filename=f"lender-asset-aggregation/rel/{survey_year}/input/asset_level_data",
                format="parquet",
            )
        )
        r1_table = s3_gateway.import_data(
            S3File(
                bucket_name=GOLD_DATA_BUCKET,
                base_filename=f"master/rel/{survey_year}/input/r1_table_data",
                format="parquet",
            )
        )
        certification_data = s3_gateway.import_data(
            S3File(
                bucket_name=GOLD_DATA_BUCKET,
                base_filename=f"master/rel/{survey_year}/input/asset_certification_data",
                format="parquet",
            )
        )
        energy_ratings_data = s3_gateway.import_data(
            S3File(
                bucket_name=GOLD_DATA_BUCKET,
                base_filename=f"master/rel/{survey_year}/input/asset_energy_rating_data",
                format="parquet",
            )
        )
        logger.info(f"Loaded asset level data from s3")

    r1_table = R1TableLenderModel.validate(r1_table)

    lender_metric_aggregation_pipeline = LenderMetricAggregationPipeline()
    metrics_sector_ctr, metrics_portfolio, asset_data = (
        lender_metric_aggregation_pipeline.process_asset_data(asset_level_data)
    )

    metrics_sector_ctr.to_parquet(
        f"contexts/app-real-estate/app_real_estate/tmp/data/{survey_year}/rel/output/metrics_sector_ctr.parquet"
    )
    metrics_portfolio.to_parquet(
        f"contexts/app-real-estate/app_real_estate/tmp/data/{survey_year}/rel/output/metrics_portfolio.parquet"
    )

    property_type_sector_mapping = asset_level_data[
        ["property_type_code", "property_sector"]
    ].drop_duplicates()
    property_type_sector_mapping.columns = ["PRT_TYPE", "PRT_SECTOR"]

    # forcing scoring coverage to be 1 for building certifications
    # because in lender assessment we don't score the building certifications
    certification_data[bc.scoring_coverage] = 1

    is_cy_opr = AssetFilters.filter_current_year_operational_assets(asset_level_data)
    asset_level_data_cy_opr = asset_level_data[is_cy_opr]

    certification_data_cy = certification_data[
        certification_data[ac.survey_year] == survey_year
    ]
    certification_data_cy = certification_data_cy.dropna(subset=[bc.year])
    certification_data_cy[bc.year] = certification_data_cy[bc.year].astype("int64")
    certification_data_cy[[ac.portfolio_asset_id, ac.survey_year, bc.year]].head()

    merged_certification_data = LenderCertificationsPreProcessor(
        certification_data=certification_data,
        asset_level_data=asset_level_data_cy_opr,
        r1_table=r1_table,
        survey_year=survey_year,
    ).process()

    merged_energy_ratings_data = LenderEnergyRatingsPreProcessor(
        energy_ratings_data=energy_ratings_data,
        asset_level_data=asset_level_data_cy_opr,
        r1_table=r1_table,
        survey_year=survey_year,
    ).process()

    br_cert_metrics = MetricAggregationPipeline.aggregate_certifications_data(
        r1_table=r1_table,
        certification_data=merged_certification_data,
        property_type_sector_mapping=property_type_sector_mapping,
    )

    output_dir = (
        f"contexts/app-real-estate/app_real_estate/tmp/data/{survey_year}/rel/output"
    )
    br_cert_metrics[0].to_parquet(
        f"{output_dir}/ctr_prt_responseid_brand_scheme_certification_metrics.parquet"
    )
    br_cert_metrics[1].to_parquet(
        f"{output_dir}/ctr_prt_responseid_brand_certification_metrics.parquet"
    )
    br_cert_metrics[2].to_parquet(
        f"{output_dir}/ctr_prt_responseid_certification_metrics.parquet"
    )
    br_cert_metrics[3].to_parquet(
        f"{output_dir}/portfolio_brand_scheme_certification_metrics.parquet"
    )
    br_cert_metrics[4].to_parquet(
        f"{output_dir}/portfolio_brand_certification_metrics.parquet"
    )
    br_cert_metrics[5].to_parquet(
        f"{output_dir}/portfolio_certification_metrics.parquet"
    )

    br_er_metrics = MetricAggregationPipeline.aggregate_ratings_data(
        energy_ratings_data=merged_energy_ratings_data,
        r1_table=r1_table,
        property_type_sector_mapping=property_type_sector_mapping,
    )

    br_er_metrics[0].to_parquet(
        f"{output_dir}/ctr_prt_responseid_brand_er_metrics.parquet"
    )
    br_er_metrics[1].to_parquet(f"{output_dir}/ctr_prt_responseid_er_metrics.parquet")
    br_er_metrics[2].to_parquet(f"{output_dir}/portfolio_brand_er_metrics.parquet")
    br_er_metrics[3].to_parquet(f"{output_dir}/portfolio_er_metrics.parquet")


if __name__ == "__main__":
    load_dotenv()
    parser = argparse.ArgumentParser(description="Run lender asset aggregation")
    parser.add_argument(
        "survey_year", help="the reporting year of the data to score", type=int
    )
    parser.add_argument(
        "--local",
        help="whether to run the pipeline with locally stored data",
        action="store_true",
        default=False,  # Changed from True to False
    )

    args = parser.parse_args()

    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(logging.DEBUG)
    logger.addHandler(handler)

    run_lender_asset_aggregation(
        survey_year=args.survey_year, logger=logger, is_local=args.local
    )
