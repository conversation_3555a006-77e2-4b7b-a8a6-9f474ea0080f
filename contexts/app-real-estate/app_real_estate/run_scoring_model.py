"""
Real Estate Scoring Model Pipeline

This module serves as the main pipeline for running the Real Estate scoring model.
It orchestrates the entire process of importing data, scoring various indicators,
aggregating metrics, and exporting results.

Key functionalities:
1. Data import from various sources (S3 or local files)
2. Scoring of multiple indicators (EN1, GH1, WT1, WS1, BC1.1, BC1.2, BC2)
3. Aggregation of scores at different levels (asset, property type, country, portfolio)
4. Calculation of building certification and energy rating metrics
5. Formatting and exporting of results (Parquet files, S3 buckets)

The pipeline supports various execution modes through command-line arguments,
including simulated data, debug mode, and different storage options.

Usage:
    poetry run python -m contexts.app-real-estate.app_real_estate.run_scoring_model <survey_year> [--simulated] [--save-files] [--debug] [--pdb]

This script is the core component of the Real Estate scoring system and should be
maintained with care, as it impacts the entire scoring process.
"""

import argparse
import logging
import os
import pdb  # noqa: F401
import sys
import traceback

import pandas as pd
from dotenv import load_dotenv
from tqdm import tqdm  # Import tqdm for progress bars
import app_real_estate.constants.column_names.asset_characteristics_columns as asc
import app_real_estate.constants.column_names.benchmark_group_memberships_columns as mc
from data_io.data_s3_gateway import DataS3Gateway
from data_io.s3_file import S3File
from app_real_estate.models.input_models.r1_table_model import R1TableModel
from app_real_estate.constants.helper_enumerators import Indicator
from app_real_estate.transformation.aggregation.group_memberships_aggregator import (
    GroupMembershipsAggregator,
)
from app_real_estate.transformation.aggregation.metric_aggregation_pipeline import (
    MetricAggregationPipeline,
)
from app_real_estate.transformation.export.benchmark_groups_formatter import (
    BenchmarkGroupsFormatter,
)
from app_real_estate.transformation.export.indicator_data_formatter import (
    IndicatorDataFormatter,
)
from app_real_estate.transformation.metric_calculation.asset_characteristics_calculation import (
    AssetCharacteristicsCalculations,
)
from app_real_estate.transformation.metric_calculation.asset_filters import (
    AssetFilters,
)
from app_real_estate.transformation.scoring_model import ScoringModel
from config import GOLD_DATA_BUCKET, READ_FROM_MAIN_FOLDER, BRANCH, LOCAL_BUCKET

pd.options.mode.chained_assignment = None  # default='warn'


def import_data(s3_gateway, logger, args, filename):
    survey_year = args.survey_year
    report = args.report
    s3_filename = "simulated/" + filename if args.simulated else filename

    if LOCAL_BUCKET:
        local_data_path = os.path.join(LOCAL_BUCKET, f"{s3_filename}.parquet")
    else:
        local_data_path = os.path.join(
            "contexts",
            "app-real-estate",
            "app_real_estate",
            "tmp",
            "data",
            f"{survey_year}",
            f"{s3_filename}.parquet",
        )
    data_is_local = os.path.exists(local_data_path)

    if not args.debug or not data_is_local:
        logger.info(f"Importing {s3_filename} data from S3...")

        if READ_FROM_MAIN_FOLDER:
            s3_path = f"main/{report}/{survey_year}/{s3_filename}"
        else:
            s3_path = f"{BRANCH}/{report}/{survey_year}/{s3_filename}"

        data = s3_gateway.import_data(
            S3File(
                bucket_name=GOLD_DATA_BUCKET,
                base_filename=s3_path,
                format="parquet",
            ),
        )

        if args.debug:
            logger.info("The data file was not saved locally. Saving now...")
            # Create the directory structure if it doesn't exist
            os.makedirs(os.path.dirname(local_data_path), exist_ok=True)
            data.to_parquet(str(local_data_path))
    else:
        logger.info(f"Importing {s3_filename} locally...")
        data = pd.read_parquet(str(local_data_path))

    for col in data.select_dtypes(include="int32").columns:
        # Ensure int columns are regular int and not int32 to avoid discrepancies
        data[col] = data[col].astype(int)

    return data


def handle_exception(exc_type, exc_value, exc_traceback):
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    print("".join(traceback.format_exception(exc_type, exc_value, exc_traceback)))
    pdb.post_mortem(exc_traceback)


if __name__ == "__main__":
    load_dotenv()
    # Parse script arguments
    parser = argparse.ArgumentParser(description="Run Real Estate scoring model")
    parser.add_argument(
        "report", help="the report for which to run (re, res)", type=str
    )
    parser.add_argument(
        "survey_year", help="the reporting year of the data to score", type=int
    )
    parser.add_argument(
        "--simulated",
        action="store_true",
        help="whether the data should be simulated",
    )
    parser.add_argument(
        "--save-files",
        action="store_true",
        help="whether the output DataFrames should be saved on S3",
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="whether the script should be run for debug.",
    )

    parser.add_argument(
        "--pdb",
        action="store_true",
        help="drop pdb debugger where exception happens.",
    )

    args = parser.parse_args()

    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(logging.DEBUG)
    logger.addHandler(handler)

    if args.pdb:
        sys.excepthook = handle_exception

    if args.simulated or LOCAL_BUCKET:
        args.debug = True

    s3_gateway = DataS3Gateway()

    # Define the steps of the script
    steps = [
        "Import asset data",
        "Import certification data",
        "Import energy ratings data",
        "Import weather station table snapshot",
        "Import ASHRAE thresholds snapshot",
        "Import certification table snapshot",
        "Import R1 table",
        "Import BC tables",
        "Score EN1",
        "Score GH1",
        "Score WT1",
        "Score WS1",
        "Score BC1.1",
        "Score BC1.2",
        "Score BC2",
        "Score indicators",
        "Format indicator data",
        "Asset Metric Aggregation",
        "Calculate BC reporting metrics",
        "Save files",
    ]

    with tqdm(total=len(steps), desc="Running Scoring Model") as pbar:
        # Import data
        asset_data = import_data(s3_gateway, logger, args, "input/asset_level_data")
        property_type_sector_mapping = asset_data[
            ["property_type_code", "property_sector"]
        ].drop_duplicates()
        property_type_sector_mapping.columns = ["PRT_TYPE", "PRT_SECTOR"]
        pbar.update(1)

        certification_data = import_data(
            s3_gateway, logger, args, "input/asset_certification_data"
        )
        pbar.update(1)

        energy_ratings_data = import_data(
            s3_gateway, logger, args, "input/asset_energy_rating_data"
        )
        pbar.update(1)

        weather_stations = import_data(
            s3_gateway, logger, args, "input/weather_stations"
        )
        pbar.update(1)

        ashrae_thresholds = import_data(
            s3_gateway, logger, args, "input/ashrae_thresholds"
        )
        pbar.update(1)
        certification_table = import_data(
            s3_gateway, logger, args, "input/certification_table"
        )
        pbar.update(1)

        # Import PGAV from R1 table and add it to the asset data.
        r1_table = import_data(s3_gateway, logger, args, "input/r_1_table_data")

        # Remove all the illegal rows here !
        r1_table = R1TableModel.to_schema().validate(r1_table)

        pbar.update(1)

        # Scoring the data
        logger.info("Scoring indicators...")

        is_performance_asset = AssetFilters.is_performance_component_asset(asset_data)
        asset_data = asset_data[is_performance_asset]
        asset_data = AssetCharacteristicsCalculations.add_asset_size_sqft(asset_data)
        scoring_model = ScoringModel(args.report)

        pbar.update(1)
        logger.info("EN1 scores...")
        (
            scored_data_en,
            groups_dict_en,
            memberships_dict_en,
            aggregated_scores_en,
        ) = scoring_model.score_en1(
            asset_data, weather_stations, ashrae_thresholds, r1_table
        )

        pbar.update(1)
        logger.info("GH1 scores...")
        (
            scored_data_gh,
            groups_dict_gh,
            memberships_dict_gh,
            aggregated_scores_gh,
        ) = scoring_model.score_gh1(asset_data, r1_table)

        pbar.update(1)
        logger.info("WS1 scores...")
        (
            scored_data_ws,
            groups_dict_ws,
            memberships_dict_ws,
            aggregated_scores_ws,
        ) = scoring_model.score_ws1(asset_data, r1_table)

        pbar.update(1)
        logger.info("WT1 scores...")
        (
            scored_data_wt,
            groups_dict_wt,
            memberships_dict_wt,
            aggregated_scores_wt,
        ) = scoring_model.score_wt1(asset_data, r1_table)

        pbar.update(1)
        logger.info("BC1.1 scores...")
        (
            certification_data_bc1_1,
            groups_dict_bc1_1,
            memberships_dict_bc1_1,
            prt_ctr_scores_bc1_1,
            prt_scores_bc1_1,
            portfolio_scores_bc1_1,
        ) = scoring_model.score_bc1_1_or_2_at_aggregated_level(
            asset_data, certification_data, certification_table, r1_table, "BC1.1"
        )

        pbar.update(1)
        logger.info("BC1.2 scores...")
        (
            certification_data_bc1_2,
            groups_dict_bc1_2,
            memberships_dict_bc1_2,
            prt_ctr_scores_bc1_2,
            prt_scores_bc1_2,
            portfolio_scores_bc1_2,
        ) = scoring_model.score_bc1_1_or_2_at_aggregated_level(
            asset_data, certification_data, certification_table, r1_table, "BC1.2"
        )

        pbar.update(1)
        logger.info("BC2 scores...")
        (
            scored_data_bc2,
            energy_ratings,
            groups_dict_bc2,
            memberships_dict_bc2,
            aggregated_scores_bc2,
        ) = scoring_model.score_bc2(asset_data, energy_ratings_data, r1_table)
        pbar.update(1)
        logger.info("Formatting indicator data...")
        asset_scores_dict = {
            Indicator.EN_1: scored_data_en,
            Indicator.GH_1: scored_data_gh,
            Indicator.WT_1: scored_data_wt,
            Indicator.WS_1: scored_data_ws,
            Indicator.BC_2: scored_data_bc2,
        }

        prt_ctr_scores_dict = {
            Indicator.EN_1: aggregated_scores_en.propertysubtype_country_responseid.reset_index(),
            Indicator.GH_1: aggregated_scores_gh.propertysubtype_country_responseid.reset_index(),
            Indicator.WT_1: aggregated_scores_wt.propertysubtype_country_responseid.reset_index(),
            Indicator.WS_1: aggregated_scores_ws.propertysubtype_country_responseid.reset_index(),
            Indicator.BC_1_1: prt_ctr_scores_bc1_1,
            Indicator.BC_1_2: prt_ctr_scores_bc1_2,
            Indicator.BC_2: aggregated_scores_bc2.propertysubtype_country_responseid.reset_index(),
        }

        prt_scores_dict = {
            Indicator.EN_1: aggregated_scores_en.propertysubtype_responseid.reset_index(),
            Indicator.GH_1: aggregated_scores_gh.propertysubtype_responseid.reset_index(),
            Indicator.WT_1: aggregated_scores_wt.propertysubtype_responseid.reset_index(),
            Indicator.WS_1: aggregated_scores_ws.propertysubtype_responseid.reset_index(),
            Indicator.BC_1_1: prt_scores_bc1_1,
            Indicator.BC_1_2: prt_scores_bc1_2,
            Indicator.BC_2: aggregated_scores_bc2.propertysubtype_responseid.reset_index(),
        }

        portfolio_scores_dict = {
            Indicator.EN_1: aggregated_scores_en.portfolio_level.reset_index(),
            Indicator.GH_1: aggregated_scores_gh.portfolio_level.reset_index(),
            Indicator.WT_1: aggregated_scores_wt.portfolio_level.reset_index(),
            Indicator.WS_1: aggregated_scores_ws.portfolio_level.reset_index(),
            Indicator.BC_1_1: portfolio_scores_bc1_1,
            Indicator.BC_1_2: portfolio_scores_bc1_2,
            Indicator.BC_2: aggregated_scores_bc2.portfolio_level.reset_index(),
        }

        asset_level_scores = IndicatorDataFormatter(
            asset_scores_dict,
            [asc.building_data_row_id],
            False,
            asset_data=asset_data,
        ).process()
        prt_level_scores = IndicatorDataFormatter(
            prt_scores_dict, [asc.response_id, asc.property_type_code], True
        ).process()
        prt_ctr_level_scores = IndicatorDataFormatter(
            prt_ctr_scores_dict,
            [asc.response_id, asc.country, asc.property_type_code],
            True,
        ).process()
        portfolio_level_scores = IndicatorDataFormatter(
            portfolio_scores_dict, [asc.response_id], True
        ).process()

        pbar.update(1)
        logger.info("Formatting benchmark groups...")

        groups_en, memberships_en = BenchmarkGroupsFormatter(
            groups_dict_en, memberships_dict_en, by_metric=True
        ).process()
        groups_gh, memberships_gh = BenchmarkGroupsFormatter(
            groups_dict_gh, memberships_dict_gh, by_metric=True
        ).process()
        groups_wt, memberships_wt = BenchmarkGroupsFormatter(
            groups_dict_wt, memberships_dict_wt, by_metric=True
        ).process()
        groups_ws, memberships_ws = BenchmarkGroupsFormatter(
            groups_dict_ws, memberships_dict_ws, by_metric=True
        ).process()
        groups_bc1_1, memberships_bc1_1 = BenchmarkGroupsFormatter(
            groups_dict_bc1_1, memberships_dict_bc1_1, by_metric=True, is_asset=False
        ).process()
        groups_bc1_2, memberships_bc1_2 = BenchmarkGroupsFormatter(
            groups_dict_bc1_2, memberships_dict_bc1_2, by_metric=True, is_asset=False
        ).process()
        groups_bc2, memberships_bc2 = BenchmarkGroupsFormatter(
            groups_dict_bc2, memberships_dict_bc2, by_metric=True
        ).process()

        indicator_groups_dict_utility = {
            Indicator.EN_1.value: groups_en,
            Indicator.GH_1.value: groups_gh,
            Indicator.WT_1.value: groups_wt,
            Indicator.WS_1.value: groups_ws,
            Indicator.BC_2.value: groups_bc2,
        }

        indicator_groups_dict_bc = {
            Indicator.BC_1_1.value: groups_bc1_1,
            Indicator.BC_1_2.value: groups_bc1_2,
        }
        indicator_memberships_dict_utility = {
            Indicator.EN_1.value: memberships_en,
            Indicator.GH_1.value: memberships_gh,
            Indicator.WT_1.value: memberships_wt,
            Indicator.WS_1.value: memberships_ws,
            Indicator.BC_2.value: memberships_bc2,
        }

        indicator_memberships_dict_bc = {
            Indicator.BC_1_1.value: memberships_bc1_1,
            Indicator.BC_1_2.value: memberships_bc1_2,
        }

        groups_utility, memberships_utility = BenchmarkGroupsFormatter(
            groups_dict=indicator_groups_dict_utility,
            memberships_dict=indicator_memberships_dict_utility,
            by_metric=False,
        ).process()
        groups_bc, memberships_bc = BenchmarkGroupsFormatter(
            groups_dict=indicator_groups_dict_bc,
            memberships_dict=indicator_memberships_dict_bc,
            by_metric=False,
            is_asset=False,
        ).process()

        prt_ctr_utility_scoring_groups = GroupMembershipsAggregator(
            data_with_loc_prt=asset_data,
            memberships=memberships_utility,
            groups=groups_utility,
        ).process()
        prt_ctr_bc_scoring_groups = GroupMembershipsAggregator(
            data_with_loc_prt=pd.concat(
                [prt_ctr_scores_bc1_1.reset_index(), prt_ctr_scores_bc1_2.reset_index()]
            ),
            memberships=memberships_bc,
            groups=groups_bc,
            is_asset=False,
            benchmark_metrics=[mc.mean_certification_coverage],
        ).process()

        pbar.update(1)
        logger.info("Aggregating scores...")

        (asset_level_scores, prt_sector_ctr_metrics, portfolio_level_scores) = (
            MetricAggregationPipeline.process_asset_data(
                asset_level_scores, portfolio_level_scores, r1_table
            )
        )

        br_cert_metrics = MetricAggregationPipeline.aggregate_certifications_data(
            r1_table=r1_table,
            certification_data=certification_data_bc1_1,
            property_type_sector_mapping=property_type_sector_mapping,
        )
        br_er_metrics = MetricAggregationPipeline.aggregate_ratings_data(
            r1_table=r1_table,
            energy_ratings_data=energy_ratings,
            property_type_sector_mapping=property_type_sector_mapping,
        )

        # Factorise this into a function/class
        if args.save_files:
            pbar.update(1)
            logger.info("Saving files...")
            indicators = ["en1", "gh1", "wt1", "ws1", "bc1_1", "bc1_2", "bc2"]
            # Merged frames
            outputs = {
                "asset_level_scores": asset_level_scores,
                "aggregated_scores/prt_ctr_level_scores": prt_ctr_level_scores,
                "aggregated_scores/prt_level_scores": prt_level_scores,
                "aggregated_scores/portfolio_level_scores": portfolio_level_scores,
                "prt_sector_ctr_metrics": prt_sector_ctr_metrics,
                # Calculations done on certification data are the same for BC1.1 and BC1.2
                "certification_data_with_metrics": certification_data_bc1_1,
                "energy_ratings_data_with_metrics": energy_ratings,
                "aggregated_certification_metrics/ctr_prt_responseid_brand_scheme_certification_metrics": br_cert_metrics[
                    0
                ],
                "aggregated_certification_metrics/ctr_prt_responseid_brand_certification_metrics": br_cert_metrics[
                    1
                ],
                "aggregated_certification_metrics/ctr_prt_responseid_certification_metrics": br_cert_metrics[
                    2
                ],
                "aggregated_certification_metrics/portfolio_brand_scheme_certification_metrics": br_cert_metrics[
                    3
                ],
                "aggregated_certification_metrics/portfolio_brand_certification_metrics": br_cert_metrics[
                    4
                ],
                "aggregated_certification_metrics/portfolio_certification_metrics": br_cert_metrics[
                    5
                ],
                "aggregated_energy_ratings_metrics/ctr_prt_responseid_brand_er_metrics": br_er_metrics[
                    0
                ],
                "aggregated_energy_ratings_metrics/ctr_prt_responseid_er_metrics": br_er_metrics[
                    1
                ],
                "aggregated_energy_ratings_metrics/portfolio_brand_er_metrics": br_er_metrics[
                    2
                ],
                "aggregated_energy_ratings_metrics/portfolio_er_metrics": br_er_metrics[
                    3
                ],
                "benchmark_groups/benchmark_groups_utility": groups_utility,
                "benchmark_groups/benchmark_groups_bc": groups_bc,
                "benchmark_group_memberships/benchmark_group_memberships_utility": memberships_utility,
                "benchmark_group_memberships/benchmark_group_memberships_bc": memberships_bc,
                "aggregated_benchmark_groups/prt_ctr_utility_scoring_groups": prt_ctr_utility_scoring_groups,
                "aggregated_benchmark_groups/prt_ctr_bc_scoring_groups": prt_ctr_bc_scoring_groups,
            }

            if args.debug:
                local_data_dir = (
                    LOCAL_BUCKET or "contexts/app-real-estate/app_real_estate/tmp/data"
                )
                local_path = f"{local_data_dir}/{args.survey_year}/"
                local_path = local_path + "simulated/" if args.simulated else local_path
                local_path += "output/"
                os.makedirs(local_path, exist_ok=True)
                os.makedirs(f"{local_data_dir}/aggregated_data/", exist_ok=True)
                for path_to_file, df in outputs.items():
                    filename = path_to_file.split("/")[-1]
                    df.to_parquet(
                        local_path + filename + ".parquet",
                    )
            else:
                for filename, df in outputs.items():
                    s3_gateway.export_data(
                        df,
                        S3File(
                            bucket_name=GOLD_DATA_BUCKET,
                            base_filename=f"{BRANCH}/{args.report}/{args.survey_year}/output/{filename}",
                            format="parquet",
                        ),
                    )
            pbar.update(1)
