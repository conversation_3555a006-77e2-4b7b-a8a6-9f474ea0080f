import argparse
from dotenv import load_dotenv

from app_real_estate.extraction.import_portal_tables import PortalTablesImporter
from data_io.data_postgres_gateway import DataPostgresGateway
from data_io.data_s3_gateway import DataS3Gateway
from data_io.s3_file import S3File
from config import LOCAL_BUCKET, GOLD_DATA_BUCKET, BRANCH


def get_data_to_export():
    postgres_gateway = DataPostgresGateway()
    portal_data_importer = PortalTablesImporter(postgres_gateway)
    data_to_export = {
        "certification_table": portal_data_importer.import_certification_table(),
        "weather_stations": portal_data_importer.import_weather_stations(),
        "ashrae_thresholds": portal_data_importer.import_ashrae_thresholds(),
    }
    return data_to_export


def update_files_local_bucket(local_bucket):
    data_to_export = get_data_to_export()
    for filename, table_data in data_to_export.items():
        try:
            table_data.to_parquet(f"{local_bucket}/{filename}.parquet")
        except Exception as e:
            print("Failed to export table data.")
            raise e


def update_files_s3(args):
    # Get asset data to fetch the required response IDs.
    s3_gateway = DataS3Gateway()

    data_to_export = get_data_to_export()
    for filename, table_data in data_to_export.items():
        try:
            s3_filename = f"{BRANCH}/{args.report}/{args.survey_year}/input/{filename}"
            s3_gateway.export_data(
                table_data,
                S3File(
                    bucket_name=GOLD_DATA_BUCKET,
                    base_filename=s3_filename,
                    format="parquet",
                ),
            )
        except Exception as e:
            print("Failed to export survey data to S3.")
            raise e


if __name__ == "__main__":
    load_dotenv()
    # Parse script arguments
    parser = argparse.ArgumentParser(
        description="Extract survey tables and save them for Scoring Model"
    )
    parser.add_argument(
        "report", help="the report to prepare data for (re, res)", type=str
    )
    parser.add_argument(
        "survey_year", help="the reporting year of the data to score", type=int
    )
    args = parser.parse_args()

    if LOCAL_BUCKET:
        update_files_local_bucket(LOCAL_BUCKET)
    else:
        update_files_s3(args)
