import pandas as pd
from data_io.data_postgres_gateway import DataPostgresGateway
from data_io.data_query_template_generator import DataQueryTemplateGenerator
from app_real_estate.constants.helper_enumerators import Indicator
from app_real_estate.models.input_models.r1_table_model import R1TableModel
from app_real_estate.models.input_models.bc_survey_tables_model import (
    BCSurveyTableModel as BCModel,
)


class R1TableFormatter:
    """
    Class responsible for formatting an input text_values extract containing
    R1 data and formatting for scoring model uses.
    """

    @staticmethod
    def format(r1_table: pd.DataFrame) -> pd.DataFrame:
        # Extract the metric, country and property subtype from the variable name.
        r1_table[["metric", R1TableModel.COUNTRY, R1TableModel.PRT_TYPE]] = r1_table[
            "variable"
        ].str.extract(r"R_1_TBL_(.*)_(.*)\.(.*)")

        # Remove the leftover variables that don't fit ("HAS_AGGREGATED" variables).
        r1_table = r1_table.dropna()

        # Get one column per possible metric.
        r1_table = r1_table.pivot(
            index=["response_id", R1TableModel.COUNTRY, R1TableModel.PRT_TYPE],
            columns="metric",
            values="value",
        ).reset_index()

        # Rename and convert to the correct types
        r1_table = r1_table.rename(
            columns={
                "response_id": R1TableModel.RESPONSE_ID,
                "AST": R1TableModel.R_1_TBL_AST,
                "AREA": R1TableModel.R_1_TBL_AREA,
                "PGAV": R1TableModel.R_1_TBL_PGAV,
            }
        )
        r1_table = r1_table.rename_axis(None, axis=1)
        r1_table[R1TableModel.R_1_TBL_AST] = r1_table[R1TableModel.R_1_TBL_AST].astype(
            int
        )
        r1_table[[R1TableModel.R_1_TBL_PGAV, R1TableModel.R_1_TBL_AREA]] = r1_table[
            [R1TableModel.R_1_TBL_PGAV, R1TableModel.R_1_TBL_AREA]
        ].astype(float)

        return r1_table


class BCTableFormatter:
    """
    Class responsible for formatting an input text_values extract containing
    BC data and formatting for scoring model uses.
    """

    @staticmethod
    def format(bc_table: pd.DataFrame, table_prefix: str) -> pd.DataFrame:
        # Extract the metric, country and property subtype from the variable name.
        bc_table[["metric", "rown", BCModel.COUNTRY, BCModel.PRT_TYPE]] = bc_table[
            "variable"
        ].str.extract(rf"{table_prefix}(.*)_([0-9]+)_(.*)\.(.*)")

        # Remove the leftover variables that don't fit ("HAS_AGGREGATED" variables).
        bc_table = bc_table.dropna()

        # Get one column per possible metric.
        bc_table = bc_table.pivot(
            index=[
                "response_id",
                "rown",
                BCModel.COUNTRY,
                BCModel.PRT_TYPE,
            ],
            columns="metric",
            values="value",
        ).reset_index()
        bc_table = bc_table.rename(columns={"response_id": BCModel.RESPONSE_ID})
        bc_table = bc_table.rename_axis(None, axis=1)
        bc_table = bc_table.drop(columns="rown")
        bc_table[[BCModel.AST, BCModel.ID]] = bc_table[
            [BCModel.AST, BCModel.ID]
        ].astype(int)
        bc_float_columns = [BCModel.COV, BCModel.PCOV, BCModel.PGAV]
        if BCModel.AGE in bc_table.columns:
            bc_float_columns.append(BCModel.AGE)
        bc_table[bc_float_columns] = bc_table[bc_float_columns].astype(float)

        return bc_table


class SurveyDataImporter:
    """
    Class responsible for querying and formatting the survey data necessary for
    the Real Estate scoring model pipeline.
    """

    def __init__(self, postgres_gateway: DataPostgresGateway):
        self.postgres_gateway = postgres_gateway
        self.r1_formatter = R1TableFormatter
        self.bc_formatter = BCTableFormatter

    def get_r1_table_data(self, response_ids: list[int]) -> pd.DataFrame:
        """
        Import and format the R1 table from the survey data in the portal.

        Parameters:
            response_ids (list[int]): List of the unique response IDs to query for.

        Returns:
            (pd.DataFrame): Dataframe containing the R1 table from the survey data.
        """
        # Get the text values associated to the R1 table for these responses.
        query = DataQueryTemplateGenerator.get_survey_table_text_values_query(
            response_ids, "R_1_TBL_"
        )
        r1_table = self.postgres_gateway.import_db_data(query)

        # Format the table correctly
        r1_table = self.r1_formatter.format(r1_table)

        # Validate the format.
        R1TableModel(r1_table)

        return r1_table

    def get_bc_table_data(
        self,
        response_ids: list[int],
        indicator: Indicator,
    ) -> pd.DataFrame:
        """
        Import and format the given BC table from the survey data in the portal.

        Parameters:
            response_ids (list[int]): List of the unique response IDs to query for.
            indicator (Indicator): Indicator whose table to query.

        Returns:
            (pd.DataFrame): Dataframe containing the given BC table from the survey data.
        """
        # Get the table prefix based on the indicator.
        table_prefix = {
            Indicator.BC_1_1: "BC_1.1_TBL_",
            Indicator.BC_1_2: "BC_1.2_TBL_",
            Indicator.BC_2: "BC_2_TBL_",
        }[indicator]

        # Get the text values associated to the BC table for these responses.
        query = DataQueryTemplateGenerator.get_survey_table_text_values_query(
            response_ids, table_prefix
        )

        bc_table = self.postgres_gateway.import_db_data(query)

        # Format the table correctly.
        bc_table = self.bc_formatter.format(bc_table, table_prefix)

        # Validate the format.
        BCModel(bc_table)

        return bc_table
