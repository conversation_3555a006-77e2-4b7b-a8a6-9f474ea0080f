import pandas as pd

import app_real_estate.constants.column_names.energy_columns as ec
from app_real_estate.models.input_models.ashrae_thresholds_model import (
    AshraeThresholdsModel,
)
from app_real_estate.models.input_models.weather_stations_model import (
    WeatherStationsModel,
)
from data_io.data_postgres_gateway import DataPostgresGateway
from data_io.data_query_template_generator import DataQueryTemplateGenerator


class PortalTablesImporter:
    def __init__(self, postgres_gateway: DataPostgresGateway = DataPostgresGateway()):
        self.postgres_gateway = postgres_gateway

    def import_ashrae_thresholds(self) -> pd.DataFrame:
        query = DataQueryTemplateGenerator.get_ashrae_thresholds_table_query()
        data = self.postgres_gateway.import_db_data(query)
        AshraeThresholdsModel.validate(data)
        data[ec.ashrae_intensity_threshold] = data[
            ec.ashrae_intensity_threshold
        ].astype(float)
        return data

    def import_weather_stations(self) -> pd.DataFrame:
        query = DataQueryTemplateGenerator.get_weather_stations_table_query()
        data = self.postgres_gateway.import_db_data(query)
        WeatherStationsModel.validate(data)
        return data

    def import_certification_table(self) -> pd.DataFrame:
        query = DataQueryTemplateGenerator.get_certification_table_query()
        return self.postgres_gateway.import_db_data(query)
