from enum import Enum


class Indicator(Enum):
    """
    Enumerates the performance indicators scores in this scoring model.
    """

    EN_1 = "EN_1"
    GH_1 = "GH_1"
    WS_1 = "WS_1"
    WT_1 = "WT_1"
    BC_1_1 = "BC_1.1"
    BC_1_2 = "BC_1.2"
    BC_1 = "BC_1"
    BC_2 = "BC_2"


class Control(Enum):
    LC = "LC"
    TC = "TC"


class Scope(Enum):
    S12 = "S12"
    S3 = "S3"


class Utility(Enum):
    Energy = "Energy"
    Water = "Water"
    GHG = "GHG"
    Waste = "Waste"


class OutlierStatus(Enum):
    none = "none"
    accepted = "accepted"
    rejected = "rejected"


class ClimateZone(Enum):
    _0A = "0A"
    _0B = "0B"
    _1A = "1A"
    _1B = "1B"
    _1C = "1C"
    _2A = "2A"
    _2B = "2B"
    _3A = "3A"
    _3B = "3B"
    _3C = "3C"
    _4A = "4A"
    _4B = "4B"
    _4C = "4C"
    _5A = "5A"
    _5B = "5B"
    _5C = "5C"
    _6A = "6A"
    _6B = "6B"
    _7 = "7"
    _8 = "8"
