import app_real_estate.constants.column_names.score_columns as sc


GH1_SCORE_AGGREGATION_SCALE_ASSET_LEVEL = [
    sc.score_gh1_ghg_area_time_cov_p_s12,
    sc.score_gh1_ghg_area_time_cov_p_s3,
    sc.score_gh1_ghg_area_time_cov_p,
    sc.score_gh1_ghg_lfl_percent_change_s12,
    sc.score_gh1_ghg_lfl_percent_change_s3,
    sc.score_gh1_ghg_lfl_percent_change,
]

"""
Below are the parameters we use to aggregate scored data.
This is sum because we compute weighted mean in 3 steps:
1. sum of the product of the score and the weight factor
2. sum of the weight factor
3. divide step 1 by step 2
Actual implementation is in weighted_mean.py
"""
sum = "sum"
GH1_SCORE_AGGREGATION_RECIPE_ASSET_LEVEL = {
    sc.score_gh1_ghg_area_time_cov_p_s12: sum,
    sc.score_gh1_ghg_area_time_cov_p_s3: sum,
    sc.score_gh1_ghg_area_time_cov_p: sum,
    sc.score_gh1_ghg_lfl_percent_change_s12: sum,
    sc.score_gh1_ghg_lfl_percent_change_s3: sum,
    sc.score_gh1_ghg_lfl_percent_change: sum,
    sc.asset_size_owned_m2: sum,
}

GH1_SCORE_AGGREGATION_RECIPE_PORTFOLIO_LEVEL = {
    sc.score_gh1_ghg_area_time_cov_p_s12: sum,
    sc.score_gh1_ghg_area_time_cov_p_s3: sum,
    sc.score_gh1_ghg_area_time_cov_p: sum,
    sc.score_gh1_ghg_lfl_percent_change_s12: sum,
    sc.score_gh1_ghg_lfl_percent_change_s3: sum,
    sc.score_gh1_ghg_lfl_percent_change: sum,
    sc.pgav: sum,
}
