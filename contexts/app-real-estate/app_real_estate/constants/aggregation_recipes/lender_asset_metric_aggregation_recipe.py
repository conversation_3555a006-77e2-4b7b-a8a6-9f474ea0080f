import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.ghg_columns as gc
from app_real_estate.constants.aggregation_recipes.asset_metric_aggregation_recipe import (
    sum_min_count,
)

bool_sum = lambda x: x.fillna(False).astype(int).sum()
LENDER_ASSET_METRIC_AGGREGATION_RECIPE = {
    ec.asset_vacancy_energy_intensity: sum_min_count,
    gc.asset_vacancy_ghg_intensity: sum_min_count,
    ec.asset_size_energy_intensity_m2: "sum",
    gc.asset_size_ghg_intensity_m2: "sum",
    ec.asset_size_energy_intensity_sqft: "sum",
    gc.asset_size_ghg_intensity_sqft: "sum",
    ec.energy_efficiency_area_m2: "sum",
    ec.energy_efficiency_area_sqft: "sum",
    ec.en_area_time_cov_p_lc_agg: "sum",
    ec.en_area_time_cov_p_tc_agg: "sum",
    gc.ghg_area_time_cov_p_s12_agg: "sum",
    gc.ghg_area_time_cov_p_s3_agg: "sum",
    ec.en_ren_ons_con: "sum",
    ec.en_ren_ons_exp: "sum",
    ec.en_ren_ons_tpt: "sum",
    ec.en_ren_ofs_pbl: "sum",
    ec.en_ren_ofs_pbt: "sum",
    ec.en_efficiency_int_kwh_m2_accepted: "sum",
    gc.ghg_scored_int_ton_m2_accepted: "sum",
    gc.ghg_scored_int_kg_m2_accepted: "sum",
    ec.en_efficiency_int_kwh_sqft_accepted: "sum",
    gc.ghg_scored_int_ton_sqft_accepted: "sum",
    gc.ghg_scored_int_kg_sqft_accepted: "sum",
    ec.en_int_eligible: bool_sum,
    gc.ghg_int_eligible: bool_sum,
    "asset_count": "sum",
    ec.en_area_p_lc: "sum",
    ec.en_area_p_tc: "sum",
    gc.ghg_area_p_s12: "sum",
    gc.ghg_area_p_s3: "sum",
    ac.asset_size_owned_sqft: "sum",
    ac.asset_size_owned_m2: "sum",
    ec.en_abs_mwh: "sum",
    ec.en_abs: "sum",
    ec.en_abs_f_kwh: "sum",
    ec.en_abs_e_kwh: "sum",
    ec.en_abs_d_kwh: "sum",
    ec.en_abs_f_mwh: "sum",
    ec.en_abs_e_mwh: "sum",
    ec.en_abs_d_mwh: "sum",
    gc.ghg_abs: "sum",
    gc.ghg_abs_net: "sum",
    ec.en_ren_abs_mwh: "sum",
    ec.en_ren_abs: "sum",
    ec.en_ren_abs_consumed_mwh: "sum",
    gc.ghg_abs_offset: "sum",
    ec.en_area_time_cov_p: "sum",
    gc.ghg_area_time_cov_p: "sum",
    ec.en_area_cov_p: "sum",
    gc.ghg_area_cov_p: "sum",
    ec.en_abs_nopr_ev_mwh: "sum",
    gc.ghg_abs_s1: "sum",
    gc.ghg_abs_s2_lb: "sum",
    gc.ghg_abs_s2_mb: "sum",
    gc.ghg_abs_s3: "sum",
}


LENDER_COLUMNS_SCALED_PER_AREA_OWNERSHIP = [
    ec.asset_vacancy_energy_intensity,
    gc.asset_vacancy_ghg_intensity,
    ec.en_efficiency_int_kwh_m2_accepted,
    ec.en_efficiency_int_kwh_sqft_accepted,
    ec.en_area_p_lc,
    ec.en_area_p_tc,
    gc.ghg_area_p_s12,
    gc.ghg_area_p_s3,
]

LENDER_COLUMNS_TO_WEIGHT_SUM_PER_OWNERSHIP = [
    ec.en_abs_mwh,
    ec.en_abs,
    ec.en_abs_f_kwh,
    ec.en_abs_e_kwh,
    ec.en_abs_d_kwh,
    ec.en_abs_f_mwh,
    ec.en_abs_e_mwh,
    ec.en_abs_d_mwh,
    ec.en_ren_ons_con,
    ec.en_ren_ons_exp,
    ec.en_ren_ons_tpt,
    ec.en_ren_ofs_pbl,
    ec.en_ren_ofs_pbt,
    gc.ghg_abs,
    gc.ghg_abs_net,
    ec.en_ren_abs_mwh,
    ec.en_ren_abs,
    ec.en_ren_abs_consumed_mwh,
    gc.ghg_abs_offset,
    ec.en_abs_nopr_ev_mwh,
    gc.ghg_abs_s1,
    gc.ghg_abs_s2_lb,
    gc.ghg_abs_s2_mb,
    gc.ghg_abs_s3,
]
