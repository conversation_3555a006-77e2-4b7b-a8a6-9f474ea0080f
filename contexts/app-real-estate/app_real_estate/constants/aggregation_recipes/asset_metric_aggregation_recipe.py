import pandas as pd
import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.water_columns as wc
import app_real_estate.constants.column_names.waste_columns as wsc
import app_real_estate.constants.column_names.ghg_columns as gc
import app_real_estate.constants.column_names.score_columns as sc

# To avoid having benchmarks at 0 when they should be NA,
# let's only sum if there is at least one non-NA value.
sum_min_count = lambda x: x.sum(min_count=1)
ASSET_METRIC_AGGREGATION_RECIPE = {
    ec.asset_vacancy_energy_intensity: sum_min_count,
    wc.asset_vacancy_water_intensity: sum_min_count,
    gc.asset_vacancy_ghg_intensity: sum_min_count,
    sc.asset_size_owned_m2: "sum",
    ec.asset_size_energy_intensity_m2: "sum",
    wc.asset_size_water_intensity_m2: "sum",
    gc.asset_size_ghg_intensity_m2: "sum",
    ec.asset_size_energy_intensity_sqft: "sum",
    wc.asset_size_water_intensity_sqft: "sum",
    gc.asset_size_ghg_intensity_sqft: "sum",
    ec.energy_efficiency_area_m2: "sum",
    ec.energy_efficiency_area_sqft: "sum",
    ec.en_area_time_cov_p_lc_agg: "sum",
    ec.en_area_time_cov_p_tc_agg: "sum",
    gc.ghg_area_time_cov_p_s12_agg: "sum",
    gc.ghg_area_time_cov_p_s3_agg: "sum",
    wc.wat_area_time_cov_p_lc_agg: "sum",
    wc.wat_area_time_cov_p_tc_agg: "sum",
    wsc.was_area_cov_p_lc_agg: "sum",
    wsc.was_area_cov_p_tc_agg: "sum",
    ec.en_lfl_abs_change_lc_agg: sum_min_count,
    ec.en_lfl_abs_change_tc_agg: sum_min_count,
    wc.wat_lfl_abs_change_lc_agg: sum_min_count,
    wc.wat_lfl_abs_change_tc_agg: sum_min_count,
    gc.ghg_lfl_abs_change_s12_agg: sum_min_count,
    gc.ghg_lfl_abs_change_s3_agg: sum_min_count,
    ec.en_lfl_abs_change_lc_accepted: sum_min_count,
    ec.en_lfl_abs_change_tc_accepted: sum_min_count,
    wc.wat_lfl_abs_change_lc_accepted: sum_min_count,
    wc.wat_lfl_abs_change_tc_accepted: sum_min_count,
    gc.ghg_lfl_abs_change_s12_accepted: sum_min_count,
    gc.ghg_lfl_abs_change_s3_accepted: sum_min_count,
    ec.en_lfl_abs_change_agg: sum_min_count,
    wc.wat_lfl_abs_change_agg: sum_min_count,
    gc.ghg_lfl_abs_change_agg: sum_min_count,
    ec.en_lfl_abs_change_accepted: sum_min_count,
    ec.en_lfl_abs_change_accepted_mwh: sum_min_count,
    wc.wat_lfl_abs_change_accepted: sum_min_count,
    gc.ghg_lfl_abs_change_accepted: sum_min_count,
    ec.en_ren_ons_con: "sum",
    ec.en_ren_ons_exp: "sum",
    ec.en_ren_ons_tpt: "sum",
    ec.en_ren_ofs_pbl: "sum",
    ec.en_ren_ofs_pbt: "sum",
    wc.wat_rec_ons_reu: "sum",
    wc.wat_rec_ons_cap: "sum",
    wc.wat_rec_ons_ext: "sum",
    wc.wat_rec_ofs_pur: "sum",
    wsc.was_abs_lf: "sum",
    wsc.was_abs_in: "sum",
    wsc.was_abs_ru: "sum",
    wsc.was_abs_wte: "sum",
    wsc.was_abs_rec: "sum",
    wsc.was_abs_oth: "sum",
    ec.en_efficiency_int_kwh_m2_accepted: "sum",
    wc.wat_scored_int_m3_m2_accepted: "sum",
    wc.wat_scored_int_dm3_m2_accepted: "sum",
    gc.ghg_scored_int_ton_m2_accepted: "sum",
    gc.ghg_scored_int_kg_m2_accepted: "sum",
    ec.en_efficiency_int_kwh_sqft_accepted: "sum",
    wc.wat_scored_int_m3_sqft_accepted: "sum",
    wc.wat_scored_int_dm3_sqft_accepted: "sum",
    gc.ghg_scored_int_ton_sqft_accepted: "sum",
    gc.ghg_scored_int_kg_sqft_accepted: "sum",
    ec.energy_efficiency_score_eligible: "sum",
    ec.en_int_eligible: "sum",
    wc.wat_int_eligible: "sum",
    gc.ghg_int_eligible: "sum",
    ec.en_lfl_eligible_for_aggregation: "sum",
    gc.ghg_lfl_eligible_for_aggregation: "sum",
    wc.wat_lfl_eligible_for_aggregation: "sum",
    "asset_count": "sum",
    ec.en_lfl_area_m2: "sum",
    ec.en_lfl_area_m2_lc: "sum",
    ec.en_lfl_area_m2_tc: "sum",
    gc.ghg_lfl_area_m2: "sum",
    gc.ghg_lfl_area_m2_s12: "sum",
    gc.ghg_lfl_area_m2_s3: "sum",
    wc.wat_lfl_area_m2: "sum",
    wc.wat_lfl_area_m2_lc: "sum",
    wc.wat_lfl_area_m2_tc: "sum",
    ec.en_lfl_area_sqft: "sum",
    ec.en_lfl_area_sqft_lc: "sum",
    ec.en_lfl_area_sqft_tc: "sum",
    gc.ghg_lfl_area_sqft: "sum",
    gc.ghg_lfl_area_sqft_s12: "sum",
    gc.ghg_lfl_area_sqft_s3: "sum",
    wc.wat_lfl_area_sqft: "sum",
    wc.wat_lfl_area_sqft_lc: "sum",
    wc.wat_lfl_area_sqft_tc: "sum",
    ec.en_lfl_area: "sum",
    ec.en_lfl_area_lc: "sum",
    ec.en_lfl_area_tc: "sum",
    gc.ghg_lfl_area: "sum",
    gc.ghg_lfl_area_s12: "sum",
    gc.ghg_lfl_area_s3: "sum",
    wc.wat_lfl_area: "sum",
    wc.wat_lfl_area_lc: "sum",
    wc.wat_lfl_area_tc: "sum",
    ec.en_area_p_lc: "sum",
    ec.en_area_p_tc: "sum",
    gc.ghg_area_p_s12: "sum",
    gc.ghg_area_p_s3: "sum",
    wc.wat_area_p_lc: "sum",
    wc.wat_area_p_tc: "sum",
    wsc.was_area_p_lc: "sum",
    wsc.was_area_p_tc: "sum",
    ac.asset_size_owned_sqft: "sum",
    ec.en_abs_mwh: "sum",
    ec.en_abs: "sum",
    ec.en_abs_ly: "sum",
    ec.en_abs_f_kwh: "sum",
    ec.en_abs_e_kwh: "sum",
    ec.en_abs_d_kwh: "sum",
    ec.en_abs_f_mwh: "sum",
    ec.en_abs_e_mwh: "sum",
    ec.en_abs_d_mwh: "sum",
    gc.ghg_abs: "sum",
    gc.ghg_abs_net: "sum",
    wc.wat_abs: "sum",
    wc.wat_abs_ly: "sum",
    wsc.was_abs: "sum",
    wsc.was_abs_ly: "sum",
    wsc.was_abs_haz: "sum",
    wsc.was_abs_nhaz: "sum",
    ec.en_ren_abs_mwh: "sum",
    ec.en_ren_abs: "sum",
    ec.en_ren_abs_ly: "sum",
    ec.en_ren_abs_consumed_mwh: "sum",
    gc.ghg_abs_offset: "sum",
    wc.wat_rec_abs: "sum",
    wc.wat_rec_abs_ly: "sum",
    wsc.was_abs_div: "sum",
    wsc.was_abs_div_ly: "sum",
    ec.en_area_time_cov_p: "sum",
    gc.ghg_area_time_cov_p: "sum",
    wc.wat_area_time_cov_p: "sum",
    ec.en_area_cov_p: "sum",
    gc.ghg_area_cov_p: "sum",
    wc.wat_area_cov_p: "sum",
    wsc.was_area_cov_p: "sum",
    ec.en_abs_nopr_ev_mwh: "sum",
    gc.ghg_abs_s1: "sum",
    gc.ghg_abs_s2_lb: "sum",
    gc.ghg_abs_s2_mb: "sum",
    gc.ghg_abs_s3: "sum",
    sc.en_area_time_weight_lc_owned: "sum",
    sc.en_area_time_weight_tc_owned: "sum",
    sc.ghg_area_time_weight_s12_owned: "sum",
    sc.ghg_area_time_weight_s3_owned: "sum",
    sc.wat_area_time_weight_lc_owned: "sum",
    sc.wat_area_time_weight_tc_owned: "sum",
    sc.was_area_weight_lc_owned: "sum",
    sc.was_area_weight_tc_owned: "sum",
    sc.en_area_time_weight_owned: "sum",
    sc.ghg_area_time_weight_owned: "sum",
    sc.wat_area_time_weight_owned: "sum",
    ec.en_lfl_abs_lc_ly_accepted: sum_min_count,
    ec.en_lfl_abs_lc_ly_bench: sum_min_count,
    ec.en_lfl_abs_tc_ly_accepted: sum_min_count,
    ec.en_lfl_abs_tc_ly_bench: sum_min_count,
    wc.wat_lfl_abs_lc_ly_accepted: sum_min_count,
    wc.wat_lfl_abs_lc_ly_bench: sum_min_count,
    wc.wat_lfl_abs_tc_ly_accepted: sum_min_count,
    wc.wat_lfl_abs_tc_ly_bench: sum_min_count,
    gc.ghg_lfl_abs_s12_ly_accepted: sum_min_count,
    gc.ghg_lfl_abs_s12_ly_bench: sum_min_count,
    gc.ghg_lfl_abs_s3_ly_accepted: sum_min_count,
    gc.ghg_lfl_abs_s3_ly_bench: sum_min_count,
    ec.en_lfl_abs_ly_accepted: sum_min_count,
    wc.wat_lfl_abs_ly_accepted: sum_min_count,
    gc.ghg_lfl_abs_ly_accepted: sum_min_count,
    sc.en_area_weight_lc_owned: "sum",
    sc.en_area_weight_tc_owned: "sum",
    sc.ghg_area_weight_s12_owned: "sum",
    sc.ghg_area_weight_s3_owned: "sum",
    sc.wat_area_weight_lc_owned: "sum",
    sc.wat_area_weight_tc_owned: "sum",
}

# Weighted mean by owned area
COLUMNS_SCALED_PER_AREA_OWNERSHIP = [
    ec.asset_vacancy_energy_intensity,
    wc.asset_vacancy_water_intensity,
    gc.asset_vacancy_ghg_intensity,
    ec.en_efficiency_int_kwh_m2_accepted,
    wc.wat_scored_int_m3_m2_accepted,
    wc.wat_scored_int_dm3_m2_accepted,
    gc.ghg_scored_int_ton_m2_accepted,
    gc.ghg_scored_int_kg_m2_accepted,
    ec.en_efficiency_int_kwh_sqft_accepted,
    wc.wat_scored_int_m3_sqft_accepted,
    wc.wat_scored_int_dm3_sqft_accepted,
    gc.ghg_scored_int_ton_sqft_accepted,
    gc.ghg_scored_int_kg_sqft_accepted,
    ec.en_area_p_lc,
    ec.en_area_p_tc,
    gc.ghg_area_p_s12,
    gc.ghg_area_p_s3,
    wc.wat_area_p_lc,
    wc.wat_area_p_tc,
    wsc.was_area_p_lc,
    wsc.was_area_p_tc,
]

# Special list for consumptions (need to be weighted and summed but not averaged)
COLUMNS_TO_WEIGHT_SUM_PER_OWNERSHIP = [
    ec.en_abs_mwh,
    ec.en_abs,
    ec.en_abs_ly,
    ec.en_abs_f_kwh,
    ec.en_abs_e_kwh,
    ec.en_abs_d_kwh,
    ec.en_abs_f_mwh,
    ec.en_abs_e_mwh,
    ec.en_abs_d_mwh,
    ec.en_ren_ons_con,
    ec.en_ren_ons_exp,
    ec.en_ren_ons_tpt,
    ec.en_ren_ofs_pbl,
    ec.en_ren_ofs_pbt,
    wc.wat_rec_ons_reu,
    wc.wat_rec_ons_cap,
    wc.wat_rec_ons_ext,
    wc.wat_rec_ofs_pur,
    gc.ghg_abs,
    gc.ghg_abs_net,
    wc.wat_abs,
    wc.wat_abs_ly,
    wsc.was_abs,
    wsc.was_abs_ly,
    wsc.was_abs_haz,
    wsc.was_abs_nhaz,
    ec.en_ren_abs_mwh,
    ec.en_ren_abs,
    ec.en_ren_abs_ly,
    ec.en_ren_abs_consumed_mwh,
    gc.ghg_abs_offset,
    wc.wat_rec_abs,
    wc.wat_rec_abs_ly,
    wsc.was_abs_div,
    wsc.was_abs_div_ly,
    wsc.was_abs_lf,
    wsc.was_abs_in,
    wsc.was_abs_ru,
    wsc.was_abs_wte,
    wsc.was_abs_rec,
    wsc.was_abs_oth,
    ec.en_abs_nopr_ev_mwh,
    gc.ghg_abs_s1,
    gc.ghg_abs_s2_lb,
    gc.ghg_abs_s2_mb,
    gc.ghg_abs_s3,
    ec.en_lfl_abs_lc_ly_accepted,
    ec.en_lfl_abs_tc_ly_accepted,
    wc.wat_lfl_abs_lc_ly_accepted,
    wc.wat_lfl_abs_tc_ly_accepted,
    gc.ghg_lfl_abs_s12_ly_accepted,
    gc.ghg_lfl_abs_s3_ly_accepted,
    ec.en_lfl_abs_lc_ly_bench,
    ec.en_lfl_abs_tc_ly_bench,
    wc.wat_lfl_abs_lc_ly_bench,
    wc.wat_lfl_abs_tc_ly_bench,
    gc.ghg_lfl_abs_s12_ly_bench,
    gc.ghg_lfl_abs_s3_ly_bench,
    ec.en_lfl_abs_ly_accepted,
    wc.wat_lfl_abs_ly_accepted,
    gc.ghg_lfl_abs_ly_accepted,
    ec.en_lfl_abs_change_lc_agg,
    ec.en_lfl_abs_change_tc_agg,
    wc.wat_lfl_abs_change_lc_agg,
    wc.wat_lfl_abs_change_tc_agg,
    gc.ghg_lfl_abs_change_s12_agg,
    gc.ghg_lfl_abs_change_s3_agg,
    ec.en_lfl_abs_change_lc_accepted,
    ec.en_lfl_abs_change_tc_accepted,
    wc.wat_lfl_abs_change_lc_accepted,
    wc.wat_lfl_abs_change_tc_accepted,
    gc.ghg_lfl_abs_change_s12_accepted,
    gc.ghg_lfl_abs_change_s3_accepted,
    ec.en_lfl_abs_change_agg,
    wc.wat_lfl_abs_change_agg,
    gc.ghg_lfl_abs_change_agg,
    ec.en_lfl_abs_change_accepted,
    ec.en_lfl_abs_change_accepted_mwh,
    wc.wat_lfl_abs_change_accepted,
    gc.ghg_lfl_abs_change_accepted,
]

# Weighted mean by their own weights
COLUMNS_NORMALISED_DIFFERENT_WEIGHTS = pd.DataFrame(
    [
        [
            ec.en_area_time_cov_p_lc_agg,
            ec.en_area_time_cov_p_lc_agg,
            sc.en_area_time_weight_lc_owned,
            1,
        ],
        [
            ec.en_area_time_cov_p_tc_agg,
            ec.en_area_time_cov_p_tc_agg,
            sc.en_area_time_weight_tc_owned,
            1,
        ],
        [
            gc.ghg_area_time_cov_p_s12_agg,
            gc.ghg_area_time_cov_p_s12_agg,
            sc.ghg_area_time_weight_s12_owned,
            1,
        ],
        [
            gc.ghg_area_time_cov_p_s3_agg,
            gc.ghg_area_time_cov_p_s3_agg,
            sc.ghg_area_time_weight_s3_owned,
            1,
        ],
        [
            wc.wat_area_time_cov_p_lc_agg,
            wc.wat_area_time_cov_p_lc_agg,
            sc.wat_area_time_weight_lc_owned,
            1,
        ],
        [
            wc.wat_area_time_cov_p_tc_agg,
            wc.wat_area_time_cov_p_tc_agg,
            sc.wat_area_time_weight_tc_owned,
            1,
        ],
        [
            wsc.was_area_cov_p_lc_agg,
            wsc.was_area_cov_p_lc_agg,
            sc.was_area_weight_lc_owned,
            1,
        ],
        [
            wsc.was_area_cov_p_tc_agg,
            wsc.was_area_cov_p_tc_agg,
            sc.was_area_weight_tc_owned,
            1,
        ],
        [
            ec.en_lfl_percent_change_lc_bench,
            ec.en_lfl_abs_change_lc_agg,
            ec.en_lfl_abs_lc_ly_bench,
            100,
        ],
        [
            ec.en_lfl_percent_change_tc_bench,
            ec.en_lfl_abs_change_tc_agg,
            ec.en_lfl_abs_tc_ly_bench,
            100,
        ],
        [
            wc.wat_lfl_percent_change_lc_bench,
            wc.wat_lfl_abs_change_lc_agg,
            wc.wat_lfl_abs_lc_ly_bench,
            100,
        ],
        [
            wc.wat_lfl_percent_change_tc_bench,
            wc.wat_lfl_abs_change_tc_agg,
            wc.wat_lfl_abs_tc_ly_bench,
            100,
        ],
        [
            gc.ghg_lfl_percent_change_s12_bench,
            gc.ghg_lfl_abs_change_s12_agg,
            gc.ghg_lfl_abs_s12_ly_bench,
            100,
        ],
        [
            gc.ghg_lfl_percent_change_s3_bench,
            gc.ghg_lfl_abs_change_s3_agg,
            gc.ghg_lfl_abs_s3_ly_bench,
            100,
        ],
        [
            ec.en_lfl_percent_change_lc_accepted,
            ec.en_lfl_abs_change_lc_accepted,
            ec.en_lfl_abs_lc_ly_accepted,
            100,
        ],
        [
            ec.en_lfl_percent_change_tc_accepted,
            ec.en_lfl_abs_change_tc_accepted,
            ec.en_lfl_abs_tc_ly_accepted,
            100,
        ],
        [
            wc.wat_lfl_percent_change_lc_accepted,
            wc.wat_lfl_abs_change_lc_accepted,
            wc.wat_lfl_abs_lc_ly_accepted,
            100,
        ],
        [
            wc.wat_lfl_percent_change_tc_accepted,
            wc.wat_lfl_abs_change_tc_accepted,
            wc.wat_lfl_abs_tc_ly_accepted,
            100,
        ],
        [
            gc.ghg_lfl_percent_change_s12_accepted,
            gc.ghg_lfl_abs_change_s12_accepted,
            gc.ghg_lfl_abs_s12_ly_accepted,
            100,
        ],
        [
            gc.ghg_lfl_percent_change_s3_accepted,
            gc.ghg_lfl_abs_change_s3_accepted,
            gc.ghg_lfl_abs_s3_ly_accepted,
            100,
        ],
        [ec.en_area_time_cov_p, ec.en_area_time_cov_p, sc.en_area_time_weight_owned, 1],
        [
            gc.ghg_area_time_cov_p,
            gc.ghg_area_time_cov_p,
            sc.ghg_area_time_weight_owned,
            1,
        ],
        [
            wc.wat_area_time_cov_p,
            wc.wat_area_time_cov_p,
            sc.wat_area_time_weight_owned,
            1,
        ],
        [wsc.was_area_cov_p, wsc.was_area_cov_p, sc.asset_size_owned_m2, 1],
        [
            ec.en_lfl_p_accepted,
            ec.en_lfl_abs_change_accepted,
            ec.en_lfl_abs_ly_accepted,
            100,
        ],
        [
            wc.wat_lfl_p_accepted,
            wc.wat_lfl_abs_change_accepted,
            wc.wat_lfl_abs_ly_accepted,
            100,
        ],
        [
            gc.ghg_lfl_p_accepted,
            gc.ghg_lfl_abs_change_accepted,
            gc.ghg_lfl_abs_ly_accepted,
            100,
        ],
        [
            ec.floor_area_percent_energy_intensity,
            ec.asset_size_energy_intensity_m2,
            sc.asset_size_owned_m2,
            100,
        ],
        [
            wc.floor_area_percent_water_intensity,
            wc.asset_size_water_intensity_m2,
            sc.asset_size_owned_m2,
            100,
        ],
        [
            gc.floor_area_percent_ghg_intensity,
            gc.asset_size_ghg_intensity_m2,
            sc.asset_size_owned_m2,
            100,
        ],
        [ec.en_lfl_area_p, ec.en_lfl_area_m2, sc.asset_size_owned_m2, 100],
        [ec.en_lfl_area_p_lc, ec.en_lfl_area_lc, sc.en_area_weight_lc_owned, 100],
        [ec.en_lfl_area_p_tc, ec.en_lfl_area_tc, sc.en_area_weight_tc_owned, 100],
        [gc.ghg_lfl_area_p, gc.ghg_lfl_area_m2, sc.asset_size_owned_m2, 100],
        [
            gc.ghg_lfl_area_p_s12,
            gc.ghg_lfl_area_s12,
            sc.ghg_area_weight_s12_owned,
            100,
        ],
        [gc.ghg_lfl_area_p_s3, gc.ghg_lfl_area_s3, sc.ghg_area_weight_s3_owned, 100],
        [wc.wat_lfl_area_p, wc.wat_lfl_area_m2, sc.asset_size_owned_m2, 100],
        [wc.wat_lfl_area_p_lc, wc.wat_lfl_area_lc, sc.wat_area_weight_lc_owned, 100],
        [wc.wat_lfl_area_p_tc, wc.wat_lfl_area_tc, sc.wat_area_weight_tc_owned, 100],
        [
            ec.energy_efficiency_area_p,
            ec.energy_efficiency_area_m2,
            sc.asset_size_owned_m2,
            100,
        ],
        [ec.en_ren_rate, ec.en_ren_abs, ec.en_abs, 100],
        [ec.en_ren_rate_ly, ec.en_ren_abs_ly, ec.en_abs_ly, 100],
        [wc.wat_rec_rate, wc.wat_rec_abs, wc.wat_abs, 100],
        [wc.wat_rec_rate_ly, wc.wat_rec_abs_ly, wc.wat_abs_ly, 100],
        [wsc.was_pabs_div, wsc.was_abs_div, wsc.was_abs, 100],
        [wsc.was_pabs_div_ly, wsc.was_abs_div_ly, wsc.was_abs_ly, 100],
        [wsc.was_pabs_lf, wsc.was_abs_lf, wsc.was_abs, 100],
        [wsc.was_pabs_in, wsc.was_abs_in, wsc.was_abs, 100],
        [wsc.was_pabs_ru, wsc.was_abs_ru, wsc.was_abs, 100],
        [wsc.was_pabs_wte, wsc.was_abs_wte, wsc.was_abs, 100],
        [wsc.was_pabs_rec, wsc.was_abs_rec, wsc.was_abs, 100],
        [wsc.was_pabs_oth, wsc.was_abs_oth, wsc.was_abs, 100],
        [ec.en_ren_ons_con_rate, ec.en_ren_ons_con, ec.en_ren_abs, 100],
        [ec.en_ren_ons_exp_rate, ec.en_ren_ons_exp, ec.en_ren_abs, 100],
        [ec.en_ren_ons_tpt_rate, ec.en_ren_ons_tpt, ec.en_ren_abs, 100],
        [ec.en_ren_ofs_pbl_rate, ec.en_ren_ofs_pbl, ec.en_ren_abs, 100],
        [ec.en_ren_ofs_pbt_rate, ec.en_ren_ofs_pbt, ec.en_ren_abs, 100],
        [wc.wat_rec_ons_reu_rate, wc.wat_rec_ons_reu, wc.wat_rec_abs, 100],
        [wc.wat_rec_ons_cap_rate, wc.wat_rec_ons_cap, wc.wat_rec_abs, 100],
        [wc.wat_rec_ons_ext_rate, wc.wat_rec_ons_ext, wc.wat_rec_abs, 100],
        [wc.wat_rec_ofs_pur_rate, wc.wat_rec_ofs_pur, wc.wat_rec_abs, 100],
    ],
    columns=["new_column", "base_column", "normaliser_column", "coefficient"],
)


BENCHMARK_COLUMNS_MEAN = {
    ec.en_area_time_cov_p_lc_agg: ec.en_area_time_cov_p_lc_agg_benchmark,
    ec.en_area_time_cov_p_tc_agg: ec.en_area_time_cov_p_tc_agg_benchmark,
    gc.ghg_area_time_cov_p_s12_agg: gc.ghg_area_time_cov_p_s12_agg_benchmark,
    gc.ghg_area_time_cov_p_s3_agg: gc.ghg_area_time_cov_p_s3_agg_benchmark,
    wc.wat_area_time_cov_p_lc_agg: wc.wat_area_time_cov_p_lc_agg_benchmark,
    wc.wat_area_time_cov_p_tc_agg: wc.wat_area_time_cov_p_tc_agg_benchmark,
    wsc.was_area_cov_p_lc_agg: wsc.was_area_cov_p_lc_agg_benchmark,
    wsc.was_area_cov_p_tc_agg: wsc.was_area_cov_p_tc_agg_benchmark,
    ec.en_lfl_percent_change_lc_bench: ec.en_lfl_percent_change_lc_benchmark,
    ec.en_lfl_percent_change_tc_bench: ec.en_lfl_percent_change_tc_benchmark,
    wc.wat_lfl_percent_change_lc_bench: wc.wat_lfl_percent_change_lc_benchmark,
    wc.wat_lfl_percent_change_tc_bench: wc.wat_lfl_percent_change_tc_benchmark,
    gc.ghg_lfl_percent_change_s12_bench: gc.ghg_lfl_percent_change_s12_benchmark,
    gc.ghg_lfl_percent_change_s3_bench: gc.ghg_lfl_percent_change_s3_benchmark,
    wsc.was_pabs_div: wsc.was_pabs_div_benchmark,
    ec.en_ren_rate: ec.en_ren_rate_agg_benchmark,
    ec.en_ren_rate_ly: ec.en_ren_rate_ly_agg_benchmark,
    ec.en_ren_ons_con_rate: ec.en_ren_ons_con_rate_benchmark,
    ec.en_ren_ons_exp_rate: ec.en_ren_ons_exp_rate_benchmark,
    ec.en_ren_ons_tpt_rate: ec.en_ren_ons_tpt_rate_benchmark,
    ec.en_ren_ofs_pbl_rate: ec.en_ren_ofs_pbl_rate_benchmark,
    ec.en_ren_ofs_pbt_rate: ec.en_ren_ofs_pbt_rate_benchmark,
    wc.wat_rec_rate: wc.wat_rec_rate_agg_benchmark,
    wc.wat_rec_rate_ly: wc.wat_rec_rate_ly_agg_benchmark,
    wc.wat_rec_ons_reu_rate: wc.wat_rec_ons_reu_rate_benchmark,
    wc.wat_rec_ons_cap_rate: wc.wat_rec_ons_cap_rate_benchmark,
    wc.wat_rec_ons_ext_rate: wc.wat_rec_ons_ext_rate_benchmark,
    wc.wat_rec_ofs_pur_rate: wc.wat_rec_ofs_pur_rate_benchmark,
    wsc.was_pabs_div_ly: wsc.was_pabs_div_ly_benchmark,
    wsc.was_pabs_lf: wsc.was_pabs_lf_benchmark,
    wsc.was_pabs_in: wsc.was_pabs_in_benchmark,
    wsc.was_pabs_ru: wsc.was_pabs_ru_benchmark,
    wsc.was_pabs_wte: wsc.was_pabs_wte_benchmark,
    wsc.was_pabs_rec: wsc.was_pabs_rec_benchmark,
    wsc.was_pabs_oth: wsc.was_pabs_oth_benchmark,
}
