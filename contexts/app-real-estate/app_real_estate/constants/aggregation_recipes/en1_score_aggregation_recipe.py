import app_real_estate.constants.column_names.score_columns as sc


EN1_SCORE_AGGREGATION_SCALE_ASSET_LEVEL = [
    sc.score_en1_en_area_time_cov_p_lc,
    sc.score_en1_en_area_time_cov_p_tc,
    sc.score_en1_en_area_time_cov_p,
    sc.score_en1_lfl_availability,
    sc.score_en1_en_lfl_percent_change_lc,
    sc.score_en1_en_lfl_percent_change_tc,
    sc.score_en1_en_lfl_percent_change,
    sc.score_en1_en_ren_ons,
    sc.score_en1_en_ren_ofs,
    sc.score_en1_en_ren_availability,
    sc.score_en1_en_ren_percent_change,
    sc.score_en1_en_ren_performance,
    sc.score_en1_energy_efficiency,
    sc.score_en1_en_ren,
    sc.score_en1_en_lfl,
    sc.score_en1_energy_performance,
]

"""
Below are the parameters we use to aggregate scored data.
This is sum because we compute weighted mean in 3 steps:
1. sum of the product of the score and the weight factor
2. sum of the weight factor
3. divide step 1 by step 2
Actual implementation is in weighted_mean.py
"""
sum = "sum"
EN1_SCORE_AGGREGATION_RECIPE_ASSET_LEVEL = {
    sc.score_en1_en_area_time_cov_p_lc: sum,
    sc.score_en1_en_area_time_cov_p_tc: sum,
    sc.score_en1_en_area_time_cov_p: sum,
    sc.score_en1_lfl_availability: sum,
    sc.score_en1_en_lfl_percent_change_lc: sum,
    sc.score_en1_en_lfl_percent_change_tc: sum,
    sc.score_en1_en_lfl_percent_change: sum,
    sc.score_en1_en_ren_ons: sum,
    sc.score_en1_en_ren_ofs: sum,
    sc.score_en1_en_ren_availability: sum,
    sc.score_en1_en_ren_percent_change: sum,
    sc.score_en1_en_ren_performance: sum,
    sc.score_en1_energy_efficiency: sum,
    sc.score_en1_en_ren: sum,
    sc.score_en1_en_lfl: sum,
    sc.score_en1_energy_performance: sum,
    sc.asset_size_owned_m2: sum,
}

EN1_SCORE_AGGREGATION_RECIPE_PORTFOLIO_LEVEL = {
    sc.score_en1_en_area_time_cov_p_lc: sum,
    sc.score_en1_en_area_time_cov_p_tc: sum,
    sc.score_en1_en_area_time_cov_p: sum,
    sc.score_en1_lfl_availability: sum,
    sc.score_en1_en_lfl_percent_change_lc: sum,
    sc.score_en1_en_lfl_percent_change_tc: sum,
    sc.score_en1_en_lfl_percent_change: sum,
    sc.score_en1_en_ren_ons: sum,
    sc.score_en1_en_ren_ofs: sum,
    sc.score_en1_en_ren_availability: sum,
    sc.score_en1_en_ren_percent_change: sum,
    sc.score_en1_en_ren_performance: sum,
    sc.score_en1_energy_efficiency: sum,
    sc.score_en1_en_ren: sum,
    sc.score_en1_en_lfl: sum,
    sc.score_en1_energy_performance: sum,
    sc.pgav: sum,
}
