import app_real_estate.constants.column_names.score_columns as sc


WT1_SCORE_AGGREGATION_SCALE_ASSET_LEVEL = [
    sc.score_wt1_wat_area_time_cov_p_lc,
    sc.score_wt1_wat_area_time_cov_p_tc,
    sc.score_wt1_wat_area_time_cov_p,
    sc.score_wt1_wat_lfl_percent_change_lc,
    sc.score_wt1_wat_lfl_percent_change_tc,
    sc.score_wt1_wat_lfl_percent_change,
    sc.score_wt1_wat_rec_ons,
    sc.score_wt1_wat_rec_percent_change,
    sc.score_wt1_wat_rec_performance,
    sc.score_wt1_wat_rec,
]

"""
Below are the parameters we use to aggregate scored data.
This is sum because we compute weighted mean in 3 steps:
1. sum of the product of the score and the weight factor
2. sum of the weight factor
3. divide step 1 by step 2
Actual implementation is in weighted_mean.py
"""
sum = "sum"
WT1_SCORE_AGGREGATION_RECIPE_ASSET_LEVEL = {
    sc.score_wt1_wat_area_time_cov_p_lc: sum,
    sc.score_wt1_wat_area_time_cov_p_tc: sum,
    sc.score_wt1_wat_area_time_cov_p: sum,
    sc.score_wt1_wat_lfl_percent_change_lc: sum,
    sc.score_wt1_wat_lfl_percent_change_tc: sum,
    sc.score_wt1_wat_lfl_percent_change: sum,
    sc.score_wt1_wat_rec_ons: sum,
    sc.score_wt1_wat_rec_percent_change: sum,
    sc.score_wt1_wat_rec_performance: sum,
    sc.score_wt1_wat_rec: sum,
    sc.asset_size_owned_m2: sum,
}

WT1_SCORE_AGGREGATION_RECIPE_PORTFOLIO_LEVEL = {
    sc.score_wt1_wat_area_time_cov_p_lc: sum,
    sc.score_wt1_wat_area_time_cov_p_tc: sum,
    sc.score_wt1_wat_area_time_cov_p: sum,
    sc.score_wt1_wat_lfl_percent_change_lc: sum,
    sc.score_wt1_wat_lfl_percent_change_tc: sum,
    sc.score_wt1_wat_lfl_percent_change: sum,
    sc.score_wt1_wat_rec_ons: sum,
    sc.score_wt1_wat_rec_percent_change: sum,
    sc.score_wt1_wat_rec_performance: sum,
    sc.score_wt1_wat_rec: sum,
    sc.pgav: sum,
}
