import app_real_estate.constants.column_names.score_columns as sc
from dataclasses import dataclass
from typing import Dict
from abc import ABC


@dataclass(frozen=True)
class BaseIndicatorWeights(ABC):
    """Base class for indicator weights with all common properties."""

    # EN1 weights
    EN1_MAX_SCORE: float
    EN1_COV_MAX_SCORE: float
    EN1_PERF_MAX_SCORE: float
    EN1_LFL_MAX_SCORE: float
    EN1_LFL_PERF_MAX_SCORE: float
    EN1_LFL_AVAILABILITY_MAX_SCORE: float
    EN1_EFFICIENCY_MAX_SCORE: float
    EN1_REN_MAX_SCORE: float
    EN1_REN_ONS_MAX_SCORE: float
    EN1_REN_OFS_MAX_SCORE: float
    EN1_REN_AVAILABILITY_MAX_SCORE: float
    EN1_REN_PERF_MAX_SCORE: float

    # GH1 weights
    GH1_MAX_SCORE: float
    GH1_COV_MAX_SCORE: float
    GH1_LFL_MAX_SCORE: float

    # WS1 weights
    WS1_MAX_SCORE: float
    WS1_COV_MAX_SCORE: float
    WS1_DIV_MAX_SCORE: float

    # WT1 weights
    WT1_MAX_SCORE: float
    WT1_COV_MAX_SCORE: float
    WT1_LFL_MAX_SCORE: float
    WT1_REC_MAX_SCORE: float
    WT1_REC_PERF_MAX_SCORE: float
    WT1_REC_ONS_MAX_SCORE: float

    # BC1 weights
    BC1_1_MAX_SCORE: float
    BC1_2_MAX_SCORE: float
    BC1_MAX_SCORE: float

    # BC2 weights
    BC2_MAX_SCORE: float

    @property
    def EN1_SUBSCORE_WEIGHTS(self) -> Dict[str, float]:
        """Dynamic property for EN1 subscore weights."""
        return {
            sc.score_en1_en_area_time_cov_p: self.EN1_COV_MAX_SCORE,
            sc.score_en1_energy_performance: self.EN1_PERF_MAX_SCORE,
            sc.score_en1_en_ren_availability: self.EN1_REN_AVAILABILITY_MAX_SCORE,
            sc.score_en1_en_ren_performance: self.EN1_REN_PERF_MAX_SCORE,
        }

    @property
    def GH1_SUBSCORE_WEIGHTS(self) -> Dict[str, float]:
        """Dynamic property for GH1 subscore weights."""
        return {
            sc.score_gh1_ghg_area_time_cov_p: self.GH1_COV_MAX_SCORE,
            sc.score_gh1_ghg_lfl_percent_change: self.GH1_LFL_MAX_SCORE,
        }

    @property
    def WS1_SUBSCORE_WEIGHTS(self) -> Dict[str, float]:
        """Dynamic property for WS1 subscore weights."""
        return {
            sc.score_ws1_was_area_cov_p: self.WS1_COV_MAX_SCORE,
            sc.score_ws1_was_diverted_percent: self.WS1_DIV_MAX_SCORE,
        }

    @property
    def WT1_SUBSCORE_WEIGHTS(self) -> Dict[str, float]:
        """Dynamic property for WT1 subscore weights."""
        return {
            sc.score_wt1_wat_area_time_cov_p: self.WT1_COV_MAX_SCORE,
            sc.score_wt1_wat_lfl_percent_change: self.WT1_LFL_MAX_SCORE,
            sc.score_wt1_wat_rec_ons: self.WT1_REC_ONS_MAX_SCORE,
            sc.score_wt1_wat_rec_performance: self.WT1_REC_PERF_MAX_SCORE,
        }

    @property
    def BC1_1_SUBSCORE_WEIGHTS(self) -> Dict[str, float]:
        """Dynamic property for BC1.1 subscore weights."""
        return {
            sc.score_bc1_1_coverage: self.BC1_1_MAX_SCORE,
        }

    @property
    def BC1_2_SUBSCORE_WEIGHTS(self) -> Dict[str, float]:
        """Dynamic property for BC1.2 subscore weights."""
        return {
            sc.score_bc1_2_coverage: self.BC1_2_MAX_SCORE,
        }

    @property
    def BC1_SUBSCORE_WEIGHTS(self) -> Dict[str, float]:
        """Dynamic property for BC1 subscore weights based on BC1.1 and BC1.2 scores."""
        total_bc1_components = self.BC1_1_MAX_SCORE + self.BC1_2_MAX_SCORE
        return {
            sc.score_bc1_1_fraction: (self.BC1_1_MAX_SCORE / total_bc1_components)
            * self.BC1_MAX_SCORE,
            sc.score_bc1_2_fraction: (self.BC1_2_MAX_SCORE / total_bc1_components)
            * self.BC1_MAX_SCORE,
        }

    @property
    def BC2_SUBSCORE_WEIGHTS(self) -> Dict[str, float]:
        """Dynamic property for BC2 subscore weights."""
        return {
            sc.score_bc2_coverage: self.BC2_MAX_SCORE,
        }


@dataclass(frozen=True)
class RealEstateWeights(BaseIndicatorWeights):
    """Weights for Real Estate (RE) report type."""

    # EN1 weights
    EN1_MAX_SCORE: float = 14.0
    EN1_COV_MAX_SCORE: float = 8.5
    EN1_PERF_MAX_SCORE: float = 2.5
    EN1_LFL_MAX_SCORE: float = 2.5
    EN1_LFL_PERF_MAX_SCORE: float = 2.0
    EN1_LFL_AVAILABILITY_MAX_SCORE: float = 0.5
    EN1_EFFICIENCY_MAX_SCORE: float = 2.5
    EN1_REN_MAX_SCORE: float = 3.0
    EN1_REN_ONS_MAX_SCORE: float = 1.0
    EN1_REN_OFS_MAX_SCORE: float = 0.5
    EN1_REN_AVAILABILITY_MAX_SCORE: float = 1.0
    EN1_REN_PERF_MAX_SCORE: float = 2.0

    # GH1 weights
    GH1_MAX_SCORE: float = 7.0
    GH1_COV_MAX_SCORE: float = 5.0
    GH1_LFL_MAX_SCORE: float = 2.0

    # WS1 weights
    WS1_MAX_SCORE: float = 4.0
    WS1_COV_MAX_SCORE: float = 2.0
    WS1_DIV_MAX_SCORE: float = 2.0

    # WT1 weights
    WT1_MAX_SCORE: float = 7.0
    WT1_COV_MAX_SCORE: float = 4.0
    WT1_LFL_MAX_SCORE: float = 2.0
    WT1_REC_MAX_SCORE: float = 1.0
    WT1_REC_PERF_MAX_SCORE: float = 0.75
    WT1_REC_ONS_MAX_SCORE: float = 0.25

    # BC1 weights
    BC1_1_MAX_SCORE: float = 7.0
    BC1_2_MAX_SCORE: float = 8.5
    BC1_MAX_SCORE: float = 8.5

    # BC2 weights
    BC2_MAX_SCORE: float = 2.0


@dataclass(frozen=True)
class ResidentialWeights(BaseIndicatorWeights):
    """Weights for Residential (RES) report type."""

    # EN1 weights (same as RE)
    EN1_MAX_SCORE: float = 14.0
    EN1_COV_MAX_SCORE: float = 8.5
    EN1_PERF_MAX_SCORE: float = 2.5
    EN1_LFL_MAX_SCORE: float = 2.5
    EN1_LFL_PERF_MAX_SCORE: float = 2.0
    EN1_LFL_AVAILABILITY_MAX_SCORE: float = 0.5
    EN1_EFFICIENCY_MAX_SCORE: float = 2.5
    EN1_REN_MAX_SCORE: float = 3.0
    EN1_REN_ONS_MAX_SCORE: float = 1.0
    EN1_REN_OFS_MAX_SCORE: float = 0.5
    EN1_REN_AVAILABILITY_MAX_SCORE: float = 1.0
    EN1_REN_PERF_MAX_SCORE: float = 2.0

    # GH1 weights (same as RE)
    GH1_MAX_SCORE: float = 7.0
    GH1_COV_MAX_SCORE: float = 5.0
    GH1_LFL_MAX_SCORE: float = 2.0

    # WS1 weights (same as RE)
    WS1_MAX_SCORE: float = 4.0
    WS1_COV_MAX_SCORE: float = 2.0
    WS1_DIV_MAX_SCORE: float = 2.0

    # WT1 weights (same as RE)
    WT1_MAX_SCORE: float = 7.0
    WT1_COV_MAX_SCORE: float = 4.0
    WT1_LFL_MAX_SCORE: float = 2.0
    WT1_REC_MAX_SCORE: float = 1.0
    WT1_REC_PERF_MAX_SCORE: float = 0.75
    WT1_REC_ONS_MAX_SCORE: float = 0.25

    # BC1 weights (different from RE)
    BC1_1_MAX_SCORE: float = 3.0
    BC1_2_MAX_SCORE: float = 4.5
    BC1_MAX_SCORE: float = 4.5

    # BC2 weights (same as RE)
    BC2_MAX_SCORE: float = 2.0


def indicator_weights(report_type: str = "re") -> BaseIndicatorWeights:
    """
    Factory function to create appropriate indicator weights based on report type.
    Provides a unified interface for accessing indicator weights.

    Args:
        report_type: The report type ('re' for Real Estate, 'res' for Residential)

    Returns:
        An instance of the appropriate weights class

    Raises:
        ValueError: If the report type is not supported
    """
    _WEIGHT_CLASSES = {
        "re": RealEstateWeights,
        "res": ResidentialWeights,
    }

    if report_type not in _WEIGHT_CLASSES:
        raise ValueError(
            f"Unsupported report type: {report_type}. Supported types: {list(_WEIGHT_CLASSES.keys())}"
        )

    weight_class = _WEIGHT_CLASSES[report_type]
    return weight_class()
