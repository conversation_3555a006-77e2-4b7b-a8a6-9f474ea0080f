en_days_data_avail = "en_days_data_avail"
"""Number of days that the energy data of the asset is available."""

en_max_days_data_avail = "en_max_days_data_avail"
"""Maximum number of days that the energy data of the asset was available."""

en_abs_nopr_ev = "en_abs_nopr_ev"
"""Non-operational energy consumption for EV charging stations in kWh."""

en_abs_nopr_ev_mwh = "en_abs_nopr_ev_mwh"
"""Non-operational energy consumption for EV charging stations in MWh."""

en_abs = "en_abs"
"""Energy consumption of the asset in kWh."""

en_abs_ly = "en_abs_ly"
"""Previous year energy consumption of the asset in kWh."""

en_abs_mwh = "en_abs_mwh"
"""Energy consumption of the asset in MWh."""

en_abs_in = "en_abs_in"
"""Indoor energy consumption of the asset."""

en_abs_wf = "en_abs_wf"
"""Fuel consumption in the whole building (for assets that report this way) expressed in terms of energy consumption in kwh."""

en_abs_lc_bsf = "en_abs_lc_bsf"
"""Fuel consumption of the asset in the shared services expressed in terms of energy consumption in kwh."""

en_abs_lc_bcf = "en_abs_lc_bcf"
"""Fuel consumption of the asset in the common areas expressed in terms of energy consumption in kwh."""

en_abs_lc_tf = "en_abs_lc_tf"
"""Fuel consumption of the asset in the landlord controlled tenant spaces expressed in terms of energy consumption in kwh."""

en_abs_tc_tf = "en_abs_tc_tf"
"""Fuel consumption of the asset in the tenant controlled tenant spaces expressed in terms of energy consumption in kwh."""

en_abs_lc_of = "en_abs_lc_of"
"""Fuel consumption of the asset in the landlord controlled outdoor areas expressed in terms of energy consumption in kwh."""

en_abs_tc_of = "en_abs_tc_of"
"""Fuel consumption of the asset in the tenant controlled outdoor areas expressed in terms of energy consumption in kwh."""

en_abs_wd = "en_abs_wd"
"""Energy consumed for district heating or cooling in the whole building (for assets that report this way) in kwh."""

en_abs_lc_bsd = "en_abs_lc_bsd"
"""Energy consumed for district heating or cooling in the asset's shared services in kwh."""

en_abs_lc_bcd = "en_abs_lc_bcd"
"""Energy consumed for district heating or cooling in the asset's common areas in kwh."""

en_abs_lc_td = "en_abs_lc_td"
"""Energy consumed for district heating or cooling in the asset's landlord controlled tenant spaces in kwh."""

en_abs_tc_td = "en_abs_tc_td"
"""Energy consumed for district heating or cooling in the asset's tenant controlled tenant spaces in kwh."""

en_abs_we = "en_abs_we"
"""Electricity consumed in the whole building (for assets that report this way) in kwh."""

en_abs_lc_bse = "en_abs_lc_bse"
"""Electricity consumed in the asset's shared services in kwh."""

en_abs_lc_bce = "en_abs_lc_bce"
"""Electricity consumed in the asset's common areas in kwh."""

en_abs_lc_te = "en_abs_lc_te"
"""Electricity consumed in the asset's landlord controlled tenant spaces in kwh."""

en_abs_tc_te = "en_abs_tc_te"
"""Electricity consumed in the asset's tenant controlled tenant spaces in kwh."""

en_abs_lc_oe = "en_abs_lc_oe"
"""Electricity consumed in the asset's landlord controlled outdoor areas in kwh."""

en_abs_tc_oe = "en_abs_tc_oe"
"""Electricity consumed in the asset's tenant controlled outdoor areas in kwh."""

en_abs_e_kwh = "en_abs_e_kwh"
"""Absolute consumption of electricity in kwh."""

en_abs_f_kwh = "en_abs_f_kwh"
"""Absolute consumption of fuel converted to energy in kwh."""

en_abs_d_kwh = "en_abs_d_kwh"
"""Absolute consumption of energy used for district heating and cooling in kwh."""

en_abs_e_mwh = "en_abs_e_mwh"
"""Absolute consumption of electricity in mwh."""

en_abs_f_mwh = "en_abs_f_mwh"
"""Absolute consumption of fuel converted to energy in mwh."""

en_abs_d_mwh = "en_abs_d_mwh"
"""Absolute consumption of energy used for district heating and cooling in mwh."""

en_abs_e_kwh_ly = "en_abs_e_kwh_ly"
"""Absolute previous year consumption of electricity in kwh."""

en_abs_f_kwh_ly = "en_abs_f_kwh_ly"
"""Absolute previous year consumption of fuel converted to energy in kwh."""

en_abs_d_kwh_ly = "en_abs_d_kwh_ly"
"""Absolute previous year consumption of energy used for district heating and cooling in kwh."""

en_abs_e_mwh_ly = "en_abs_e_mwh_ly"
"""Absolute previous year consumption of electricity in mwh."""

en_abs_f_mwh_ly = "en_abs_f_mwh_ly"
"""Absolute previous year consumption of fuel converted to energy in mwh."""

en_abs_d_mwh_ly = "en_abs_d_mwh_ly"
"""Absolute previous year consumption of energy used for district heating and cooling in mwh."""

en_int_kwh_m2 = "en_int_kwh_m2"
"""Energy consumption intensity in terms of kwh per square meters."""

en_area_p_lc = "en_area_p_lc"
"""Percentage of the asset's floor area controlled by landlord."""

en_area_p_tc = "en_area_p_tc"
"""Percentage of the asset's floor area controlled by tenant."""

en_area_cov_p = "en_area_cov_p"
"""Percentage of floor area covered by the reported energy data."""

en_area_cov_p_lc = "en_area_cov_p_lc"
"""Percentage of landlord controlled floor area covered by the reported energy data."""

en_area_cov_p_tc = "en_area_cov_p_tc"
"""Percentage of tenant controlled floor area covered by the reported energy data."""

en_lfl_area_cov_p_lc = "en_lfl_area_cov_p_lc"
"""Percentage of landlord controlled floor area covered by the reported energy data filtered for LFL."""

en_lfl_area_cov_p_tc = "en_lfl_area_cov_p_tc"
"""Percentage of tenant controlled floor area covered by the reported energy data filtered for LFL."""

en_time_cov_p = "en_time_cov_p"
"""Percentage of the total reporting period covered by the reported energy data."""

en_time_cov_p_lc = "en_time_cov_p_lc"
"""Percentage of the total reporting period covered by the reported energy data on landlord controlled area."""

en_time_cov_p_tc = "en_time_cov_p_tc"
"""Percentage of the total reporting period covered by the reported energy data on tenant controlled area."""

en_area_time_cov_p = "en_area_time_cov_p"
"""Percentage of area/time based coverage by the reported energy data."""

en_area_time_cov_p_lc = "en_area_time_cov_p_lc"
"""Percentage of area/time based coverage by the reported energy data on landlord controlled area."""

en_area_time_cov_p_tc = "en_area_time_cov_p_tc"
"""Percentage of area/time based coverage by the reported energy data on tenant controlled area."""

en_lfl_area_cov_p = "en_lfl_area_cov_p"
"""Percentage of area covered by the reported energy LFL data."""

en_area_weight_lc = "en_area_weight_lc"
"""Weight of the area based energy data coverage for the landlord controlled area."""

en_area_weight_tc = "en_area_weight_tc"
"""Weight of the area based energy data coverage for the tenant controlled area."""

en_area_time_weight_lc = "en_area_time_weight_lc"
"""Weight of the area/time based energy data coverage for the landlord controlled area."""

en_area_time_weight_tc = "en_area_time_weight_tc"
"""Weight of the area/time based energy data coverage for the tenant controlled area."""

en_lfl_p = "en_lfl_p"
"""Like-For-Like percent change of energy consumption. Value between [-100;Inf]."""

en_lfl_abs = "en_lfl_abs"
"""Absolute energy consumption in kWh filtered for LFL calculation."""

en_lfl_abs_mwh = "en_lfl_abs_mwh"
"""Absolute energy consumption in MWh filtered for LFL calculation."""

en_lfl_abs_change_mwh = "en_lfl_abs_change_mwh"
"""Absolute change in energy consumption from previous to current year in MWh."""

en_lfl_abs_change = "en_lfl_abs_change"
"""Absolute change in energy consumption from previous to current year in kWh."""

en_lfl_abs_change_agg = "en_lfl_abs_change_agg"
"""Absolute change in energy consumption from previous to current year in kWh filtered for benchmark calculation at the aggregated levels."""

en_lfl_abs_change_accepted = "en_lfl_abs_change_accepted"
"""Absolute change in energy consumption from previous to current year in kWh filtered for aggregation."""

en_lfl_abs_change_accepted_mwh = "en_lfl_abs_change_accepted_mwh"
"""Absolute change in energy consumption from previous to current year in MWh filtered for aggregation."""

en_lfl_abs_change_lc_agg = "en_lfl_abs_change_lc_agg"
"""Absolute change in energy consumption in landlord controlled spaces from previous to current year in kWh filtered for benchmark calculation at the aggregated levels."""

en_lfl_abs_change_lc_accepted = "en_lfl_abs_change_lc_accepted"
"""Absolute change in energy consumption in landlord controlled spaces from previous to current year in kWh filtered for aggregation."""

en_lfl_abs_change_tc_agg = "en_lfl_abs_change_tc_agg"
"""Absolute change in energy consumption in tenant controlled spaces from previous to current year in kWh filtered for benchmark calculation at the aggregated levels."""

en_lfl_abs_change_tc_accepted = "en_lfl_abs_change_tc_accepted"
"""Absolute change in energy consumption in tenant controlled spaces from previous to current year in kWh filtered for aggregation."""

en_lfl_abs_in = "en_lfl_abs_in"
"""Absolute indoor energy consumption filtered for LFL calculation."""

en_lfl_abs_lc_bsf = "en_lfl_abs_lc_bsf"
"""Absolute fuels consumption by base building shared services filtered for LFL calculation."""

en_lfl_abs_lc_bsd = "en_lfl_abs_lc_bsd"
"""Absolute energy consumption by district heating and cooling by base building shared services filtered for LFL calculation."""

en_lfl_abs_lc_bse = "en_lfl_abs_lc_bse"
"""Absolute electricity consumption by base building shared services filtered for LFL calculation."""

en_lfl_abs_lc_bcf = "en_lfl_abs_lc_bcf"
"""Absolute fuels consumption in base building common areas filtered for LFL calculation."""

en_lfl_abs_lc_bcd = "en_lfl_abs_lc_bcd"
"""Absolute energy consumption in district heating and cooling by base building common areas filtered for LFL calculation."""

en_lfl_abs_lc_bce = "en_lfl_abs_lc_bce"
"""Absolute electricity consumption in base building common areas filtered for LFL calculation."""

en_lfl_abs_lc_of = "en_lfl_abs_lc_of"
"""Absolute fuels consumption in landlord controlled outdoor / exterior / parking areas filtered for LFL calculation."""

en_lfl_abs_lc_oe = "en_lfl_abs_lc_oe"
"""Absolute electricity consumption in landlord controlled outdoor / exterior / parking areas filtered for LFL calculation."""

en_lfl_abs_lc_tf = "en_lfl_abs_lc_tf"
"""Absolute fuels consumption in landlord controlled tenant spaces filtered for LFL calculation."""

en_lfl_abs_lc_td = "en_lfl_abs_lc_td"
"""Absolute energy consumption by district heating and cooling in landlord controlled tenant spaces filtered for LFL calculation."""

en_lfl_abs_lc_te = "en_lfl_abs_lc_te"
"""Absolute electricity consumption in landlord controlled tenant spaces filtered for LFL calculation."""

en_lfl_abs_tc_tf = "en_lfl_abs_tc_tf"
"""Absolute fuels consumption in tenant controlled tenant spaces filtered for LFL calculation."""

en_lfl_abs_tc_td = "en_lfl_abs_tc_td"
"""Absolute energy consumption by district heating and cooling in tenant controlled tenant spaces filtered for LFL calculation."""

en_lfl_abs_tc_te = "en_lfl_abs_tc_te"
"""Absolute electricity consumption in tenant controlled tenant spaces filtered for LFL calculation."""

en_lfl_abs_tc_of = "en_lfl_abs_tc_of"
"""Absolute fuels consumption in tenant controlled outdoor / exterior / parking areas filtered for LFL calculation."""

en_lfl_abs_tc_oe = "en_lfl_abs_tc_oe"
"""Absolute electricity consumption in tenant controlled outdoor / exterior / parking areas filtered for LFL calculation."""

en_lfl_abs_lc = "en_lfl_abs_lc"
"""Absolute energy consumption in landlord controlled areas filtered for LFL calculation."""

en_lfl_abs_tc = "en_lfl_abs_tc"
"""Absolute energy consumption in tenant controlled areas filtered for LFL calculation."""

en_lfl_percent_change_lc = "en_lfl_percent_change_lc"
"""Percent change in absolute energy consumption in landlord controlled areas relative to the previous year (Like-For-Like)."""

en_lfl_percent_change_tc = "en_lfl_percent_change_tc"
"""Percent change in absolute energy consumption in tenant controlled areas relative to the previous year (Like-For-Like)."""

en_lfl_abs_lc_bc = "en_lfl_abs_lc_bc"
"""Absolute energy consumption in common areas filtered for LFL calculation."""

en_lfl_abs_lc_bs = "en_lfl_abs_lc_bs"
"""Absolute energy consumption in shared services filtered for LFL calculation."""

en_lfl_abs_lc_t = "en_lfl_abs_lc_t"
"""Absolute energy consumption in landlord controlled tenant spaces filtered for LFL calculation."""

en_lfl_abs_tc_t = "en_lfl_abs_tc_t"
"""Absolute energy consumption in tenant controlled tenant spaces filtered for LFL calculation."""

en_lfl_abs_lc_o = "en_lfl_abs_lc_o"
"""Absolute energy consumption in landlord controlled outdoor areas filtered for LFL calculation."""

en_lfl_abs_tc_o = "en_lfl_abs_tc_o"
"""Absolute energy consumption in tenant controlled outdoor areas filtered for LFL calculation."""

en_lfl_abs_lc_bc_ly = "en_lfl_abs_lc_bc_ly"
"""Absolute energy consumption in the previous year in common areas filtered for LFL calculation."""

en_lfl_abs_lc_bs_ly = "en_lfl_abs_lc_bs_ly"
"""Absolute energy consumption in the previous year in shared services filtered for LFL calculation."""

en_lfl_abs_lc_t_ly = "en_lfl_abs_lc_t_ly"
"""Absolute energy consumption in the previous year in landlord controlled tenant spaces filtered for LFL calculation."""

en_lfl_abs_tc_t_ly = "en_lfl_abs_tc_t_ly"
"""Absolute energy consumption in the previous year in tenant controlled tenant spaces filtered for LFL calculation."""

en_lfl_abs_lc_o_ly = "en_lfl_abs_lc_o_ly"
"""Absolute energy consumption in the previous year in landlord controlled outdoor areas filtered for LFL calculation."""

en_lfl_abs_tc_o_ly = "en_lfl_abs_tc_o_ly"
"""Absolute energy consumption in the previous year in tenant controlled outdoor areas filtered for LFL calculation."""

en_lfl_abs_ly = "en_lfl_abs_ly"
"""Absolute energy consumption in the previous year filtered for LFL calculation."""

en_lfl_abs_in_ly = "en_lfl_abs_in_ly"
"""Absolute indoor energy consumption in the previous year filtered for LFL calculation."""

en_lfl_abs_lc_ly = "en_lfl_abs_lc_ly"
"""Absolute energy consumption in the previous year in landlord controlled areas filtered for LFL calculation."""

en_lfl_abs_tc_ly = "en_lfl_abs_tc_ly"
"""Absolute energy consumption in the previous year in tenant controlled areas filtered for LFL calculation."""

en_ren_abs = "en_ren_abs"
"""Absolute amount of generated renewable energy in kWh."""

en_ren_abs_ly = "en_ren_abs_ly"
"""Absolute previous year amount of generated renewable energy in kWh."""

en_ren_abs_mwh = "en_ren_abs_mwh"
"""Absolute amount of generated renewable energy in MWh."""

en_ren_abs_consumed_mwh = "en_ren_abs_consumed_mwh"
"""Absolute amount of generated and consumed renewable energy in MWh."""

en_ren_abs_consumed_kwh = "en_ren_abs_consumed_kwh"
"""Absolute amount of generated and consumed renewable energy in kWh."""

en_ren_ons_con = "en_ren_ons_con"
"""Absolute amount of generated and consumed renewable energy on-site by landlord in kWh."""

en_ren_ons_exp = "en_ren_ons_exp"
"""Absolute amount of generated and exported renewable energy on-site by landlord in kWh."""

en_ren_ons_tpt = "en_ren_ons_tpt"
"""Absolute amount of generated and consumed renewable energy on-site by third party or tenant in kWh."""

en_ren_ons = "en_ren_ons"
"""Absolute amount of generated renewable energy on-site in kWh."""

en_ren_ofs_pbl = "en_ren_ofs_pbl"
"""Absolute amount of generated renewable energy off-site and procured by landlord in kWh."""

en_ren_ofs_pbt = "en_ren_ofs_pbt"
"""Absolute amount of generated renewable energy off-site and procured by tenant in kWh."""

en_ren_ofs = "en_ren_ofs"
"""Absolute amount of generated renewable energy off-site in kWh."""

en_ren_rate = "en_ren_rate"
"""Calculated renewable energy generation as a percentage of total energy consumption."""

en_ren_rate_ly = "en_ren_rate_ly"
"""Calculated previous year renewable energy generation as a percentage of total energy consumption."""

en_ren_ons_con_rate = "en_ren_ons_con_rate"
"""Calculated percentage of renewable energy that was generated on-site and consumed by landlord."""

en_ren_ons_exp_rate = "en_ren_ons_exp_rate"
"""Calculated percentage of renewable energy that was generated on-site and exported by landlord."""

en_ren_ons_tpt_rate = "en_ren_ons_tpt_rate"
"""Calculated percentage of renewable energy that was generated on-site and consumed by third party or tenant."""

en_ren_ofs_pbl_rate = "en_ren_ofs_pbl_rate"
"""Calculated percentage of renewable energy that was generated off-site and procured by landlord."""

en_ren_ofs_pbt_rate = "en_ren_ofs_pbt_rate"
"""Calculated percentage of renewable energy that was generated off-site and procured by tenant."""

en_ren_ons_con_mwh = "en_ren_ons_con_mwh"
"""Absolute amount of generated and consumed renewable energy on-site by landlord in MWh."""

en_ren_ons_exp_mwh = "en_ren_ons_exp_mwh"
"""Absolute amount of generated and exported renewable energy on-site by landlord in MWh."""

en_ren_ons_tpt_mwh = "en_ren_ons_tpt_mwh"
"""Absolute amount of generated and consumed renewable energy on-site by third party or tenant in MWh."""

en_ren_ofs_pbl_mwh = "en_ren_ofs_pbl_mwh"
"""Absolute amount of generated renewable energy off-site and procured by landlord in MWh."""

en_ren_ofs_pbt_mwh = "en_ren_ofs_pbt_mwh"
"""Absolute amount of generated renewable energy off-site and procured by tenant in MWh."""

en_ren_percent_change = "en_ren_percent_change"
"""Calculated field containing the percent change of renewable energy generated from the previous year to the data year. Value between [-Inf;Inf]."""

en_efficiency_int_kwh_m2 = "en_efficiency_int_kwh_m2"
"""Calculated energy intensity for assets eligible to the Energy Efficiency scoring in kwh per m2."""

en_efficiency_int_kwh_sqft = "en_efficiency_int_kwh_sqft"
"""Calculated energy intensity for assets eligible to the Energy Efficiency scoring in kwh per sqft."""

en_int_outlier_status = "en_int_outlier_status"
"""Outlier status of the asset's energy intensity value, can be either "none", "accepted" or "rejected"."""

en_lfl_outlier_status = "en_lfl_outlier_status"
"""Outlier status of the asset's energy Like-For-Like value, can be either "none", "accepted" or "rejected"."""

asset_vacancy_energy_intensity = "asset_vacancy_energy_intensity"
"""Calculated asset vacancy rate for assets eligible for energy intensity scoring (75% coverage)."""

asset_size_energy_intensity_m2 = "asset_size_energy_intensity_m2"
"""Calculated total asset floor area in square meters for assets eligible for energy intensity scoring (75% coverage)."""

asset_size_energy_intensity_sqft = "asset_size_energy_intensity_sqft"
"""Calculated total asset floor area in square feet for assets eligible for energy intensity scoring (75% coverage)."""

floor_area_percent_energy_intensity = "floor_area_percent_energy_intensity"
"""Calculated total floor area in square meters for assets eligible for energy intensity scoring (75% coverage) as a percentage of the total floor area of the group."""

en_efficiency_int_kwh_m2_accepted = "en_efficiency_int_kwh_m2_accepted"
"""The asset's energy intensity value without hard outliers in kwh/m2."""

en_efficiency_int_kwh_sqft_accepted = "en_efficiency_int_kwh_sqft_accepted"
"""The asset's energy intensity value without hard outliers in kwh/sqft."""

en_lfl_p_accepted = "en_lfl_p_accepted"
"""The asset's energy LFL percent change value filtered for aggregation."""

en_lfl_abs_accepted = "en_lfl_abs_accepted"
"""Absolute energy consumption in kWh filtered for LFL metric aggregation."""

en_lfl_abs_ly_accepted = "en_lfl_abs_ly_accepted"
"""Previous year absolute energy consumption in kWh filtered for LFL metric aggregation."""

en_lfl_abs_lc_ly_accepted = "en_lfl_abs_lc_ly_accepted"
"""Absolute energy consumption in the previous year in landlord controlled areas filtered for LFL aggregation."""

en_lfl_abs_tc_ly_accepted = "en_lfl_abs_tc_ly_accepted"
"""Absolute energy consumption in the previous year in tenant controlled areas filtered for LFL aggregation."""

en_lfl_abs_lc_ly_bench = "en_lfl_abs_lc_ly_bench"
"""Absolute energy consumption in the previous year in landlord controlled areas filtered for LFL metric reporting benchmark creation."""

en_lfl_abs_tc_ly_bench = "en_lfl_abs_tc_ly_bench"
"""Absolute energy consumption in the previous year in tenant controlled areas filtered for LFL metric reporting benchmark creation."""

en_area_time_cov_p_lc_agg = "en_area_time_cov_p_lc_agg"
"""Area-time energy data coverage weighted by the area-time weight for the landlord controlled areas."""

en_area_time_cov_p_tc_agg = "en_area_time_cov_p_tc_agg"
"""Area-time energy data coverage weighted by the area-time weight for the tenant controlled areas."""

en_lfl_percent_change_lc_bench = "en_lfl_percent_change_lc_bench"
"""Energy LFL percent change weighted by the area-time weight, filtered for aggregation and for reporting benchmark creation, for the landlord controlled areas."""

en_lfl_percent_change_tc_bench = "en_lfl_percent_change_tc_bench"
"""Energy LFL percent change weighted by the area-time weight, filtered for aggregation and for reporting benchmark creation, for the tenant controlled areas."""

en_lfl_percent_change_lc_accepted = "en_lfl_percent_change_lc_accepted"
"""Energy LFL percent change weighted by the area-time weight, filtered for aggregation, for the landlord controlled areas."""

en_lfl_percent_change_tc_accepted = "en_lfl_percent_change_tc_accepted"
"""Energy LFL percent change weighted by the area-time weight, filtered for aggregation, for the tenant controlled areas."""

en_int_eligible = "en_int_eligible"
"""Whether the asset was eligible to have its energy consumption intensity calculated."""

energy_efficiency_score_eligible = "energy_efficiency_score_eligible"
"""Whether the asset has Energy Efficiency score, meaning its energy consumption intensity is lower than the ASHRAE threshold."""

en_lfl_eligible_for_aggregation = "en_lfl_eligible_for_aggregation"
"""Whether the asset is eligible for energy LFL aggregation."""

en_lfl_area_m2 = "en_lfl_area_m2"
"""Floor area in m2 (weighted by ownership) on which the energy LFL data is reported for assets eligible for aggregation."""

en_lfl_area_m2_lc = "en_lfl_area_m2_lc"
"""Landlord controlled floor area in m2 (weighted by ownership) on which the energy LFL data is reported for assets eligible for aggregation."""

en_lfl_area_m2_tc = "en_lfl_area_m2_tc"
"""Tenant controlled floor area in m2 (weighted by ownership) on which the energy LFL data is reported for assets eligible for aggregation."""

en_lfl_area_sqft = "en_lfl_area_sqft"
"""Floor area in sqft (weighted by ownership) on which the energy LFL data is reported for assets eligible for aggregation."""

en_lfl_area_sqft_lc = "en_lfl_area_sqft_lc"
"""Landlord controlled floor area in sqft (weighted by ownership) on which the energy LFL data is reported for assets eligible for aggregation."""

en_lfl_area_sqft_tc = "en_lfl_area_sqft_tc"
"""Tenant controlled floor area in sqft (weighted by ownership) on which the energy LFL data is reported for assets eligible for aggregation."""

en_lfl_area = "en_lfl_area"
"""Floor area (weighted by ownership) on which the energy LFL data is reported for assets eligible for aggregation."""

en_lfl_area_lc = "en_lfl_area_lc"
"""Landlord controlled floor area (weighted by ownership) on which the energy LFL data is reported for assets eligible for aggregation."""

en_lfl_area_tc = "en_lfl_area_tc"
"""Tenant controlled floor area (weighted by ownership) on which the energy LFL data is reported for assets eligible for aggregation."""

en_lfl_area_p = "en_lfl_area_p"

en_lfl_area_p_lc = "en_lfl_area_p_lc"

en_lfl_area_p_tc = "en_lfl_area_p_tc"

ashrae_intensity_threshold = "ashrae_intensity_threshold"
"""Energy Use Intensity threshold determined by ASHRAE based on climate zone and property subtype."""

energy_efficiency_area_m2 = "energy_efficiency_area_m2"
"""
At the asset-level, asset size in m2 of the asset if the asset is eligible for Energy Efficiency scoring.
At the aggregated level, the total floor area in m2 of assets eligible for Energy Efficiency scoring.
"""

energy_efficiency_area_sqft = "energy_efficiency_area_sqft"
"""
At the asset-level, asset size in sqft of the asset if the asset is eligible for Energy Efficiency scoring.
At the aggregated level, the total floor area in sqft of assets eligible for Energy Efficiency scoring.
"""

energy_efficiency_area_p = "energy_efficiency_area_p"
"""
At the aggregated level, the total floor area percentage eligible for Energy Efficiency scoring
"""

"""Aggregated benchmarks of the metrics"""
en_area_time_cov_p_lc_agg_benchmark = "en_area_time_cov_p_lc_agg_benchmark"
en_area_time_cov_p_tc_agg_benchmark = "en_area_time_cov_p_tc_agg_benchmark"
en_lfl_percent_change_lc_benchmark = "en_lfl_percent_change_lc_benchmark"
en_lfl_percent_change_tc_benchmark = "en_lfl_percent_change_tc_benchmark"
en_int_kwh_m2_agg_benchmark = "en_int_kwh_m2_agg_benchmark"
en_ren_rate_agg_benchmark = "en_ren_rate_agg_benchmark"
en_ren_rate_ly_agg_benchmark = "en_ren_rate_ly_agg_benchmark"
en_ren_ons_con_rate_benchmark = "en_ren_ons_con_rate_benchmark"
en_ren_ons_exp_rate_benchmark = "en_ren_ons_exp_rate_benchmark"
en_ren_ons_tpt_rate_benchmark = "en_ren_ons_tpt_rate_benchmark"
en_ren_ofs_pbl_rate_benchmark = "en_ren_ofs_pbl_rate_benchmark"
en_ren_ofs_pbt_rate_benchmark = "en_ren_ofs_pbt_rate_benchmark"


# WIP: columns for the new framework of metric aggregation
en_lfl_abs_change_lc = "en_lfl_abs_change_lc"
en_lfl_abs_change_tc = "en_lfl_abs_change_tc"
en_lfl_asset_count = "en_lfl_asset_count"
