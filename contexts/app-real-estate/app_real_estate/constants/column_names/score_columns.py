score_en1 = "score_en1"
"""Absolute score (weighted with max indicator score) for the indicator EN1."""

score_en1_fraction = "score_en1_fraction"
"""Fractional score for the indicator EN1."""

score_en1_percent = "score_en1_percent"
"""Percentage score for the indicator EN1."""

score_en1_max = "score_en1_max"
"""Maximum possible points for the indicator EN1."""

score_en1_en_area_time_cov_p_lc = "score_en1_en_area_time_cov_p_lc"
"""Fractional sub-score for the landlord controlled data coverage in the indicator EN1."""

score_en1_en_area_time_cov_p_tc = "score_en1_en_area_time_cov_p_tc"
"""Fractional sub-score for the tenant controlled data coverage in the indicator EN1."""

score_en1_en_area_time_cov_p = "score_en1_en_area_time_cov_p"
"""Fractional sub-score for the data coverage in the indicator EN1."""

score_en1_lfl_availability = "score_en1_lfl_availability"
"""Fractional sub-score for the Like-For-Like data availability in the indicator EN1."""

score_en1_en_lfl_percent_change_lc = "score_en1_en_lfl_percent_change_lc"
"""Fractional sub-score for the landlord controlled Like-For-Like percent change in the indicator EN1."""

score_en1_en_lfl_percent_change_tc = "score_en1_en_lfl_percent_change_tc"
"""Fractional sub-score for the tenant controlled Like-For-Like percent change in the indicator EN1."""

score_en1_en_lfl_percent_change = "score_en1_en_lfl_percent_change"
"""Fractional sub-score for the Like-For-Like percent change in the indicator EN1."""

score_en1_en_lfl = "score_en1_en_lfl"
"""Fractional sub-score for Like-For-Like in the indicator EN1."""

score_en1_energy_performance = "score_en1_energy_performance"
"""Fractional sub-score for the energy performance in the indicator EN1."""

score_en1_energy_performance_absolute = "score_en1_energy_performance_absolute"
"""Absolute sub-score for the energy performance in the indicator EN1."""

score_en1_energy_performance_max = "score_en1_energy_performance_max"
"""Max points obtainable for the energy performance in the indicator EN1."""

score_en1_energy_performance_percent = "score_en1_energy_performance_percent"
"""Percentage sub-score for the energy performance in the indicator EN1."""

score_en1_en_ren_ons = "score_en1_en_ren_ons"
"""Fractional sub-score for the on-site generated renewable energy presence in the indicator EN1."""

score_en1_en_ren_ofs = "score_en1_en_ren_ofs"
"""Fractional sub-score for the off-site generated renewable energy presence in the indicator EN1."""

score_en1_en_ren_availability = "score_en1_en_ren_availability"
"""Fractional sub-score for the generated renewable energy presence in the indicator EN1."""

score_en1_en_ren_percent_change = "score_en1_en_ren_percent_change"
"""Fractional sub-score for the generated renewable energy percent change in the indicator EN1."""

score_en1_en_ren_performance = "score_en1_en_ren_performance"
"""Fractional sub-score for the renewable energy generation performance in the indicator EN1."""

score_en1_en_ren = "score_en1_en_ren"
"""Fractional sub-score for renewable energy in the indicator EN1."""

score_en1_energy_efficiency = "score_en1_energy_efficiency"
"""Fractional sub-score for the energy efficiency in the indicator EN1."""

score_en1_energy_efficiency_absolute = "score_en1_energy_efficiency_absolute"

score_en1_energy_efficiency_percent = "score_en1_energy_efficiency_percent"

score_en1_energy_efficiency_max = "score_en1_energy_efficiency_max"

score_wt1 = "score_wt1"
"""Absolute score (weighted with max indicator score) for the indicator WT1."""

score_wt1_fraction = "score_wt1_fraction"
"""Fractional score for the indicator WT1."""

score_wt1_percent = "score_wt1_percent"
"""Percentage score for the indicator WT1."""

score_wt1_max = "score_wt1_max"
"""Maximum possible points for the indicator WT1."""

score_wt1_wat_area_time_cov_p_lc = "score_wt1_wat_area_time_cov_p_lc"
"""Fractional sub-score for the landlord controlled data coverage in the indicator WT1."""

score_wt1_wat_area_time_cov_p_tc = "score_wt1_wat_area_time_cov_p_tc"
"""Fractional sub-score for the tenant controlled data coverage in the indicator WT1."""

score_wt1_wat_area_time_cov_p = "score_wt1_wat_area_time_cov_p"
"""Fractional sub-score for the data coverage in the indicator WT1."""

score_wt1_wat_lfl_percent_change_lc = "score_wt1_wat_lfl_percent_change_lc"
"""Fractional sub-score for the landlord controlled Like-For-Like percent change in the indicator WT1."""

score_wt1_wat_lfl_percent_change_tc = "score_wt1_wat_lfl_percent_change_tc"
"""Fractional sub-score for the tenant controlled Like-For-Like percent change in the indicator WT1."""

score_wt1_wat_lfl_percent_change = "score_wt1_wat_lfl_percent_change"
"""Fractional sub-score for the Like-For-Like percent change in the indicator WT1."""

score_wt1_wat_rec_ons = "score_wt1_wat_rec_ons"
"""Fractional sub-score for the on-site recycled water presence in the indicator WT1."""

score_wt1_wat_rec_percent_change = "score_wt1_wat_rec_percent_change"
"""Fractional sub-score for the recycled water percent change in the indicator WT1."""

score_wt1_wat_rec_performance = "score_wt1_wat_rec_performance"
"""Fractional sub-score for the recycled / reused water performance in the indicator WT1."""

score_wt1_wat_rec = "score_wt1_wat_rec"
"""Fractional sub-score for the recycled water in the indicator WT1."""

score_gh1 = "score_gh1"
"""Absolute score (weighted with max indicator score) for the indicator GH1."""

score_gh1_fraction = "score_gh1_fraction"
"""Fractional score for the indicator GH1."""

score_gh1_percent = "score_gh1_percent"
"""Percentage score for the indicator GH1."""

score_gh1_max = "score_gh1_max"
"""Maximum possible points for the indicator GH1."""

score_gh1_ghg_area_time_cov_p_s12 = "score_gh1_ghg_area_time_cov_p_s12"
"""Fractional sub-score for the scopes 1 and 2 data coverage in the indicator GH1."""

score_gh1_ghg_area_time_cov_p_s3 = "score_gh1_ghg_area_time_cov_p_s3"
"""Fractional sub-score for the scope 3 data coverage in the indicator GH1."""

score_gh1_ghg_area_time_cov_p = "score_gh1_ghg_area_time_cov_p"
"""Fractional sub-score for the data coverage in the indicator GH1."""

score_gh1_ghg_lfl_percent_change_s12 = "score_gh1_ghg_lfl_percent_change_s12"
"""Fractional sub-score for the scopes 1 and 2 Like-For-Like percent change in the indicator GH1."""

score_gh1_ghg_lfl_percent_change_s3 = "score_gh1_ghg_lfl_percent_change_s3"
"""Fractional sub-score for the scope 3 Like-For-Like percent change in the indicator GH1."""

score_gh1_ghg_lfl_percent_change = "score_gh1_ghg_lfl_percent_change"
"""Fractional sub-score for the Like-For-Like percent change in the indicator GH1."""

score_ws1 = "score_ws1"
"""Absolute score (weighted with max indicator score) for the indicator WS1."""

score_ws1_fraction = "score_ws1_fraction"
"""Fractional score for the indicator WS1."""

score_ws1_percent = "score_ws1_percent"
"""Percentage score for the indicator WS1."""

score_ws1_max = "score_ws1_max"
"""Maximum possible points for the indicator WS1."""

score_ws1_was_area_cov_p_lc = "score_ws1_was_area_cov_p_lc"
"""Fractional sub-score for the landlord controlled area data coverage in the indicator WS1."""

score_ws1_was_area_cov_p_tc = "score_ws1_was_area_cov_p_tc"
"""Fractional sub-score for the tenant controlled area data coverage in the indicator WS1."""

score_ws1_was_area_cov_p = "score_ws1_was_area_cov_p"
"""Fractional sub-score for the area data coverage in the indicator WS1."""

score_ws1_was_diverted_percent = "score_ws1_was_diverted_percent"
"""Fractional sub-score for the proportion of diverted waste in the indicator WS1."""

certification_score_bc1_1_coverage = "certification_score_bc1_1_coverage"
"""Fractional sub-score for the building certification coverage at the certification level for BC1.1."""

certification_score_bc1_1 = "certification_score_bc1_1"
"""Certification fractional score for BC1.1."""

score_bc1_1_coverage = "score_bc1_1_coverage"
"""Fractional sub-score for the building certification coverage at the asset level for BC1.1. Aggregation of certification_score_bc1_1."""

score_bc1_1 = "score_bc1_1"
"""Absolute score (weighted with max indicator score) for the indicator BC1.1."""

score_bc1_1_fraction = "score_bc1_1_fraction"
"""Fractional score of BC1.1"""

score_bc1_1_percent = "score_bc1_1_percent"
"""Percentage score for the indicator BC1.1."""

score_bc1_1_max = "score_bc1_1_max"
"""Maximum possible points for the indicator BC1.1."""

certification_score_bc1_2_coverage = "certification_score_bc1_2_coverage"
"""Fractional sub-score for the building certification coverage at the certification level for BC1.2."""

certification_score_bc1_2 = "certification_score_bc1_2"
"""Certification fractional score for BC1.2."""

score_bc1_2_coverage = "score_bc1_2_coverage"
"""Fractional sub-score for the building certification coverage at the asset level for BC1.2. Aggregation of certification_score_bc1_2."""

score_bc1_2 = "score_bc1_2"
"""Absolute score (weighted with max indicator score) for the indicator BC1.2."""

score_bc1_2_fraction = "score_bc1_2_fraction"
"""Fractional score of BC1.2"""

score_bc1_2_percent = "score_bc1_2_percent"
"""Percentage score for the indicator BC1.2."""

score_bc1_2_max = "score_bc1_2_max"
"""Maximum possible points for the indicator BC1.2."""

score_bc1 = "score_bc1"
"""Absolute score (weighted with max indicator score) for the indicators BC1, which is the sum of score_bc1_1 and score_bc1_2 with weight."""

score_bc1_fraction = "score_bc1_fraction"
"""Fractional score for the indicators BC1."""

score_bc1_percent = "score_bc1_percent"
"""Percentage score for the indicators BC1."""

score_bc1_max = "score_bc1_max"
"""Maximum possible points for the indicators BC1."""

score_bc2_coverage = "score_bc2_coverage"
"""Fractional sub-score for the energy ratings coverage at the asset level for BC2."""

score_bc2 = "score_bc2"
"""Absolute score (weighted with max indicator score) for the indicator BC2."""

score_bc2_fraction = "score_bc2_fraction"
"""Fractional score for the indicator BC2."""

score_bc2_percent = "score_bc2_percent"
"""Percentage score for the indicator BC2."""

score_bc2_max = "score_bc2_max"
"""Maximum possible points for the indicator BC2."""

asset_size_owned_m2 = "asset_size_owned_m2"
"""Weight factor for the floor area x % ownership for weight in the indicator EN1."""

pgav = "pgav"
"""Percentage gav(gross asset value)"""

score_en1_en_area_time_cov_p_lc_absolute = "score_en1_en_area_time_cov_p_lc_absolute"
"""Absolute score for the landlord controlled coverage sub-score."""

score_en1_en_area_time_cov_p_lc_percent = "score_en1_en_area_time_cov_p_lc_percent"
"""Percent score for the landlord controlled coverage sub-score."""

score_en1_en_area_time_cov_p_lc_max = "score_en1_en_area_time_cov_p_lc_max"
"""Max score for the landlord controlled coverage sub-score."""

score_en1_en_area_time_cov_p_tc_absolute = "score_en1_en_area_time_cov_p_tc_absolute"
"""Absolute score for the tenant controlled coverage sub-score."""

score_en1_en_area_time_cov_p_tc_percent = "score_en1_en_area_time_cov_p_tc_percent"
"""Percent score for the tenant controlled coverage sub-score."""

score_en1_en_area_time_cov_p_tc_max = "score_en1_en_area_time_cov_p_tc_max"
"""Max score for the tenant controlled coverage sub-score."""

score_en1_en_area_time_cov_p_absolute = "score_en1_en_area_time_cov_p_absolute"
"""Absolute score for the data coverage sub-score."""

score_en1_en_area_time_cov_p_percent = "score_en1_en_area_time_cov_p_percent"
"""Percent score for the data coverage sub-score."""

score_en1_en_area_time_cov_p_max = "score_en1_en_area_time_cov_p_max"
"""Max score for the data coverage sub-score."""

score_en1_lfl_availability_absolute = "score_en1_lfl_availability_absolute"
"""Absolute score for the LFL data availability sub-score."""

score_en1_lfl_availability_percent = "score_en1_lfl_availability_percent"
"""Percent score for the LFL data availability sub-score."""

score_en1_lfl_availability_max = "score_en1_lfl_availability_max"
"""Max score for the LFL data availability sub-score."""

score_en1_en_lfl_percent_change_lc_absolute = (
    "score_en1_en_lfl_percent_change_lc_absolute"
)
"""Absolute score for the LFL percent change for landlord controlled areas sub-score."""

score_en1_en_lfl_percent_change_lc_percent = (
    "score_en1_en_lfl_percent_change_lc_percent"
)
"""Percent score for the LFL percent change for landlord controlled areas sub-score."""

score_en1_en_lfl_percent_change_lc_max = "score_en1_en_lfl_percent_change_lc_max"
"""Max score for the LFL percent change for landlord controlled areas sub-score."""

score_en1_en_lfl_percent_change_tc_absolute = (
    "score_en1_en_lfl_percent_change_tc_absolute"
)
"""Absolute score for the LFL percent change for tenant controlled areas sub-score."""

score_en1_en_lfl_percent_change_tc_percent = (
    "score_en1_en_lfl_percent_change_tc_percent"
)
"""Percent score for the LFL percent change for tenant controlled areas sub-score."""

score_en1_en_lfl_percent_change_tc_max = "score_en1_en_lfl_percent_change_tc_max"
"""Max score for the LFL percent change for tenant controlled areas sub-score."""

score_en1_en_lfl_percent_change_absolute = "score_en1_en_lfl_percent_change_absolute"
"""Absolute score for the LFL percent change sub-score."""

score_en1_en_lfl_percent_change_percent = "score_en1_en_lfl_percent_change_percent"
"""Percent score for the LFL percent change sub-score."""

score_en1_en_lfl_percent_change_max = "score_en1_en_lfl_percent_change_max"
"""Max score for the LFL percent change sub-score."""

score_en1_en_lfl_absolute = "score_en1_en_lfl_absolute"
"""Absolute sub-score for LFL in EN1."""

score_en1_en_lfl_percent = "score_en1_en_lfl_percent"
"""Percent sub-score for LFL in EN1."""

score_en1_en_lfl_max = "score_en1_en_lfl_max"
"""Max score for LFL in EN1."""

score_en1_en_ren_absolute = "score_en1_en_ren_absolute"
"""Absolute sub-score for renewable energy in EN1."""

score_en1_en_ren_percent = "score_en1_en_ren_percent"
"""Percent sub-score for renewable energy in EN1."""

score_en1_en_ren_max = "score_en1_en_ren_max"
"""Max score for renewable energy in EN1."""

score_en1_en_ren_ons_absolute = "score_en1_en_ren_ons_absolute"
"""Absolute score for the on-site renewable energy data availability in indicator EN1."""

score_en1_en_ren_ons_percent = "score_en1_en_ren_ons_percent"
"""Percent score for the on-site renewable energy data availability in indicator EN1."""

score_en1_en_ren_ons_max = "score_en1_en_ren_ons_max"
"""Max score for the on-site renewable energy data availability in indicator EN1."""

score_en1_en_ren_ofs_absolute = "score_en1_en_ren_ofs_absolute"
"""Absolute score for the off-site renewable energy data availability in indicator EN1."""

score_en1_en_ren_ofs_percent = "score_en1_en_ren_ofs_percent"
"""Percent score for the off-site renewable energy data availability in indicator EN1."""

score_en1_en_ren_ofs_max = "score_en1_en_ren_ofs_max"
"""Max score for the off-site renewable energy data availability in indicator EN1."""

score_en1_en_ren_percent_change_absolute = "score_en1_en_ren_percent_change_absolute"
"""Absolute score for the renewable energy percent change sub-score in indicator EN1."""

score_en1_en_ren_percent_change_percent = "score_en1_en_ren_percent_change_percent"
"""Percent score for the renewable energy percent change sub-score in indicator EN1."""

score_en1_en_ren_percent_change_max = "score_en1_en_ren_percent_change_max"
"""Max score for the renewable energy percent change sub-score in indicator EN1."""

score_en1_en_ren_performance_absolute = "score_en1_en_ren_performance_absolute"
"""Absolute score for the renewable energy performance sub-score in indicator EN1."""

score_en1_en_ren_performance_percent = "score_en1_en_ren_performance_percent"
"""Percent score for the renewable energy performance sub-score in indicator EN1."""

score_en1_en_ren_performance_max = "score_en1_en_ren_performance_max"
"""Max score for the renewable energy performance sub-score in indicator EN1."""

score_gh1_ghg_area_time_cov_p_s12_absolute = (
    "score_gh1_ghg_area_time_cov_p_s12_absolute"
)
"""Absolute score for the scopes 1 and 2 GHG data coverage subscore in GH1."""

score_gh1_ghg_area_time_cov_p_s12_percent = "score_gh1_ghg_area_time_cov_p_s12_percent"
"""Percent score for the scopes 1 and 2 GHG data coverage subscore in GH1."""

score_gh1_ghg_area_time_cov_p_s12_max = "score_gh1_ghg_area_time_cov_p_s12_max"
"""Max score for the scopes 1 and 2 GHG data coverage subscore in GH1."""

score_gh1_ghg_area_time_cov_p_s3_absolute = "score_gh1_ghg_area_time_cov_p_s3_absolute"
"""Absolute score for the scope 3 GHG data coverage subscore in GH1."""

score_gh1_ghg_area_time_cov_p_s3_percent = "score_gh1_ghg_area_time_cov_p_s3_percent"
"""Percent score for the scope 3 GHG data coverage subscore in GH1."""

score_gh1_ghg_area_time_cov_p_s3_max = "score_gh1_ghg_area_time_cov_p_s3_max"
"""Max score for the scope 3 GHG data coverage subscore in GH1."""

score_gh1_ghg_area_time_cov_p_absolute = "score_gh1_ghg_area_time_cov_p_absolute"
"""Absolute score for the GHG data coverage subscore in GH1."""

score_gh1_ghg_area_time_cov_p_percent = "score_gh1_ghg_area_time_cov_p_percent"
"""Percent score for the GHG data coverage subscore in GH1."""

score_gh1_ghg_area_time_cov_p_max = "score_gh1_ghg_area_time_cov_p_max"
"""Max score for the GHG data coverage subscore in GH1."""

score_gh1_ghg_lfl_percent_change_s12_absolute = (
    "score_gh1_ghg_lfl_percent_change_s12_absolute"
)
"""Absolute score for the scopes 1 and 2 GHG LFL subscore in GH1."""

score_gh1_ghg_lfl_percent_change_s12_percent = (
    "score_gh1_ghg_lfl_percent_change_s12_percent"
)
"""Percent score for the scopes 1 and 2 GHG LFL subscore in GH1."""

score_gh1_ghg_lfl_percent_change_s12_max = "score_gh1_ghg_lfl_percent_change_s12_max"
"""Max score for the scopes 1 and 2 GHG LFL subscore in GH1."""

score_gh1_ghg_lfl_percent_change_s3_absolute = (
    "score_gh1_ghg_lfl_percent_change_s3_absolute"
)
"""Absolute score for the scope 3 GHG LFL subscore in GH1."""

score_gh1_ghg_lfl_percent_change_s3_percent = (
    "score_gh1_ghg_lfl_percent_change_s3_percent"
)
"""Percent score for the scope 3 GHG LFL subscore in GH1."""

score_gh1_ghg_lfl_percent_change_s3_max = "score_gh1_ghg_lfl_percent_change_s3_max"
"""Max score for the scope 3 GHG LFL subscore in GH1."""

score_gh1_ghg_lfl_percent_change_absolute = "score_gh1_ghg_lfl_percent_change_absolute"
"""Absolute score for the GHG LFL subscore in GH1."""

score_gh1_ghg_lfl_percent_change_percent = "score_gh1_ghg_lfl_percent_change_percent"
"""Percent score for the GHG LFL subscore in GH1."""

score_gh1_ghg_lfl_percent_change_max = "score_gh1_ghg_lfl_percent_change_max"
"""Max score for the GHG LFL subscore in GH1."""

score_ws1_was_area_cov_p_absolute = "score_ws1_was_area_cov_p_absolute"
"""Absolute score for the waste data coverage subscore in the indicator WS1."""

score_ws1_was_area_cov_p_percent = "score_ws1_was_area_cov_p_percent"
"""Percent score for the waste data coverage subscore in the indicator WS1."""

score_ws1_was_area_cov_p_max = "score_ws1_was_area_cov_p_max"
"""Max score for the waste data coverage subscore in the indicator WS1."""


score_ws1_was_area_cov_p_lc_absolute = "score_ws1_was_area_cov_p_lc_absolute"
"""Absolute sub-score for the landlord controlled area data coverage in the indicator WS1."""

score_ws1_was_area_cov_p_tc_absolute = "score_ws1_was_area_cov_p_tc_absolute"
"""Absolute sub-score for the tenant controlled area data coverage in the indicator WS1."""

score_ws1_was_area_cov_p_lc_percent = "score_ws1_was_area_cov_p_lc_percent"
"""Percent sub-score for the landlord controlled area data coverage in the indicator WS1."""

score_ws1_was_area_cov_p_tc_percent = "score_ws1_was_area_cov_p_tc_percent"
"""Percent sub-score for the tenant controlled area data coverage in the indicator WS1."""

score_ws1_was_area_cov_p_lc_max = "score_ws1_was_area_cov_p_lc_max"
"""Max sub-score for the landlord controlled area data coverage in the indicator WS1."""

score_ws1_was_area_cov_p_tc_max = "score_ws1_was_area_cov_p_tc_max"
"""Max sub-score for the tenant controlled area data coverage in the indicator WS1."""


score_ws1_was_diverted_percent_absolute = "score_ws1_was_diverted_percent_absolute"
"""Absolute score for the diverted waste subscore in the indicator WS1."""

score_ws1_was_diverted_percent_percent = "score_ws1_was_diverted_percent_percent"
"""Percent score for the diverted waste subscore in the indicator WS1."""

score_ws1_was_diverted_percent_max = "score_ws1_was_diverted_percent_max"
"""Max score for the diverted waste subscore in the indicator WS1."""

score_wt1_wat_area_time_cov_p_lc_absolute = "score_wt1_wat_area_time_cov_p_lc_absolute"
"""Absolute score for the water data coverage in landlord controlled areas subscore in the indicator WT1."""

score_wt1_wat_area_time_cov_p_lc_percent = "score_wt1_wat_area_time_cov_p_lc_percent"
"""Percent score for the water data coverage in landlord controlled areas subscore in the indicator WT1."""

score_wt1_wat_area_time_cov_p_lc_max = "score_wt1_wat_area_time_cov_p_lc_max"
"""Max score for the water data coverage in landlord controlled areas subscore in the indicator WT1."""

score_wt1_wat_area_time_cov_p_tc_absolute = "score_wt1_wat_area_time_cov_p_tc_absolute"
"""Absolute score for the water data coverage in tenant controlled areas subscore in the indicator WT1."""

score_wt1_wat_area_time_cov_p_tc_percent = "score_wt1_wat_area_time_cov_p_tc_percent"
"""Percent score for the water data coverage in tenant controlled areas subscore in the indicator WT1."""

score_wt1_wat_area_time_cov_p_tc_max = "score_wt1_wat_area_time_cov_p_tc_max"
"""Max score for the water data coverage in tenant controlled areas subscore in the indicator WT1."""

score_wt1_wat_area_time_cov_p_absolute = "score_wt1_wat_area_time_cov_p_absolute"
"""Absolute score for the water data coverage subscore in the indicator WT1."""

score_wt1_wat_area_time_cov_p_percent = "score_wt1_wat_area_time_cov_p_percent"
"""Percent score for the water data coverage subscore in the indicator WT1."""

score_wt1_wat_area_time_cov_p_max = "score_wt1_wat_area_time_cov_p_max"
"""Max score for the water data coverage subscore in the indicator WT1."""

score_wt1_wat_lfl_percent_change_lc_absolute = (
    "score_wt1_wat_lfl_percent_change_lc_absolute"
)
"""Absolute score for the water LFL subscore in the landlord controlled areas in the indicator WT1."""

score_wt1_wat_lfl_percent_change_lc_percent = (
    "score_wt1_wat_lfl_percent_change_lc_percent"
)
"""Percent score for the water LFL subscore in the landlord controlled areas in the indicator WT1."""

score_wt1_wat_lfl_percent_change_lc_max = "score_wt1_wat_lfl_percent_change_lc_max"
"""Max score for the water LFL subscore in the landlord controlled areas in the indicator WT1."""

score_wt1_wat_lfl_percent_change_tc_absolute = (
    "score_wt1_wat_lfl_percent_change_tc_absolute"
)
"""Absolute score for the water LFL subscore in the tenant controlled areas in the indicator WT1."""

score_wt1_wat_lfl_percent_change_tc_percent = (
    "score_wt1_wat_lfl_percent_change_tc_percent"
)
"""Percent score for the water LFL subscore in the tenant controlled areas in the indicator WT1."""

score_wt1_wat_lfl_percent_change_tc_max = "score_wt1_wat_lfl_percent_change_tc_max"
"""Max score for the water LFL subscore in the tenant controlled areas in the indicator WT1."""

score_wt1_wat_lfl_percent_change_absolute = "score_wt1_wat_lfl_percent_change_absolute"
"""Absolute score for the water LFL subscore in the indicator WT1."""

score_wt1_wat_lfl_percent_change_percent = "score_wt1_wat_lfl_percent_change_percent"
"""Percent score for the water LFL subscore in the indicator WT1."""

score_wt1_wat_lfl_percent_change_max = "score_wt1_wat_lfl_percent_change_max"
"""Max score for the water LFL subscore in the indicator WT1."""

score_wt1_wat_rec_ons_absolute = "score_wt1_wat_rec_ons_absolute"
"""Absolute score for the recycled water availability subscore in the indicator WT1."""

score_wt1_wat_rec_ons_percent = "score_wt1_wat_rec_ons_percent"
"""Percent score for the recycled water availability subscore in the indicator WT1."""

score_wt1_wat_rec_ons_max = "score_wt1_wat_rec_ons_max"
"""Max score for the recycled water availability subscore in the indicator WT1."""

score_wt1_wat_rec_percent_change_absolute = "score_wt1_wat_rec_percent_change_absolute"
"""Absolute score for the recycled water percent change subscore in the indicator WT1."""

score_wt1_wat_rec_percent_change_percent = "score_wt1_wat_rec_percent_change_percent"
"""Percent score for the recycled water percent change subscore in the indicator WT1."""

score_wt1_wat_rec_percent_change_max = "score_wt1_wat_rec_percent_change_max"
"""Max score for the recycled water percent change subscore in the indicator WT1."""

score_wt1_wat_rec_performance_absolute = "score_wt1_wat_rec_performance_absolute"
"""Absolute score for the recycled water performance subscore in the indicator WT1."""

score_wt1_wat_rec_performance_percent = "score_wt1_wat_rec_performance_percent"
"""Percent score for the recycled water performance subscore in the indicator WT1."""

score_wt1_wat_rec_performance_max = "score_wt1_wat_rec_performance_max"
"""Max score for the recycled water performance subscore in the indicator WT1."""

score_wt1_wat_rec_absolute = "score_wt1_wat_rec_absolute"
"""Absolute score for the recycled water total subscore in the indicator WT1."""

score_wt1_wat_rec_percent = "score_wt1_wat_rec_percent"
"""Percent score for the recycled water total subscore in the indicator WT1."""

score_wt1_wat_rec_max = "score_wt1_wat_rec_max"
"""Max score for the recycled water total subscore in the indicator WT1."""

score_bc1_1_coverage_absolute = "score_bc1_1_coverage_absolute"
"""Score for the coverage subscore in the indicator BC1.1 in absolute points."""

score_bc1_1_coverage_percent = "score_bc1_1_coverage_percent"
"""Percent score for the coverage subscore in the indicator BC1.1."""

score_bc1_1_coverage_max = "score_bc1_1_coverage_max"
"""Max score for the coverage subscore in the indicator BC1.1."""

score_bc1_2_coverage_absolute = "score_bc1_2_coverage_absolute"
"""Score for the coverage subscore in the indicator BC1.2 in absolute points."""

score_bc1_2_coverage_percent = "score_bc1_2_coverage_percent"
"""Percent score for the coverage subscore in the indicator BC1.2."""

score_bc1_2_coverage_max = "score_bc1_2_coverage_max"
"""Max score for the coverage subscore in the indicator BC1.2."""

score_bc2_coverage_absolute = "score_bc2_coverage_absolute"
"""Score for the coverage subscore in the indicator BC2 in absolute points."""

score_bc2_coverage_percent = "score_bc2_coverage_percent"
"""Percent score for the coverage subscore in the indicator BC2."""

score_bc2_coverage_max = "score_bc2_coverage_max"
"""Max score for the coverage subscore in the indicator BC2."""

# Weights for aggregation
en_area_time_weight_lc_owned = "en_area_time_weight_lc_owned"
en_area_time_weight_tc_owned = "en_area_time_weight_tc_owned"
ghg_area_time_weight_s12_owned = "ghg_area_time_weight_s12_owned"
ghg_area_time_weight_s3_owned = "ghg_area_time_weight_s3_owned"
wat_area_time_weight_lc_owned = "wat_area_time_weight_lc_owned"
wat_area_time_weight_tc_owned = "wat_area_time_weight_tc_owned"
was_area_weight_lc_owned = "was_area_weight_lc_owned"
was_area_weight_tc_owned = "was_area_weight_tc_owned"

en_area_time_weight_owned = "en_area_time_weight_owned"
ghg_area_time_weight_owned = "ghg_area_time_weight_owned"
wat_area_time_weight_owned = "wat_area_time_weight_owned"

en_area_weight_lc_owned = "en_area_weight_lc_owned"
en_area_weight_tc_owned = "en_area_weight_tc_owned"
ghg_area_weight_s12_owned = "ghg_area_weight_s12_owned"
ghg_area_weight_s3_owned = "ghg_area_weight_s3_owned"
wat_area_weight_lc_owned = "wat_area_weight_lc_owned"
wat_area_weight_tc_owned = "wat_area_weight_tc_owned"
