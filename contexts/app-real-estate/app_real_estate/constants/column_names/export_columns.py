import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.key_columns as kc
import app_real_estate.constants.column_names.asset_characteristics_columns as ac

RESPONSE_ID = "RESPONSE_ID"
"""Internal ID for each entity full response submission to a survey."""

PROPERTY_SUBTYPE = "PROPERTY_SUBTYPE"
"""The property subtype assigned to this group."""

COUNTRY = "COUNTRY"
"""The country assigned to this group."""

SCORE_EN_1 = "SCORE_EN_1"
"""Score for the indicator EN1 in absolute points."""

SCORE_F_EN_1 = "SCORE.F_EN_1"
"""Fractional score for the indicator EN1 in absolute points."""

SCORE_P_EN_1 = "SCORE.P_EN_1"
"""Percent score for the indicator EN1 in absolute points."""

SCORE_MAX_EN_1 = "SCORE.MAX_EN_1"
"""Max score for the indicator EN1 in absolute points."""

SCORE_ANS_EN_1_TBL_AGGR_COV_LC = "SCORE_ANS_EN_1_TBL.AGGR_COV.LC"
"""Score for the landlord controlled energy data coverage sub-score in EN1 in absolute points."""

SCORE_F_ANS_EN_1_TBL_AGGR_COV_LC = "SCORE.F_ANS_EN_1_TBL.AGGR_COV.LC"
"""Fractional score for the landlord controlled energy data coverage sub-score in EN1."""

SCORE_P_ANS_EN_1_TBL_AGGR_COV_LC = "SCORE.P_ANS_EN_1_TBL.AGGR_COV.LC"
"""Percent score for the landlord controlled energy data coverage sub-score in EN1."""

SCORE_MAX_ANS_EN_1_TBL_AGGR_COV_LC = "SCORE.MAX_ANS_EN_1_TBL.AGGR_COV.LC"
"""Max score for the landlord controlled energy data coverage sub-score in EN1."""

SCORE_ANS_EN_1_TBL_AGGR_COV_TC = "SCORE_ANS_EN_1_TBL.AGGR_COV.LC"
"""Score for the tenant controlled energy data coverage sub-score in EN1 in absolute points."""

SCORE_F_ANS_EN_1_TBL_AGGR_COV_TC = "SCORE.F_ANS_EN_1_TBL.AGGR_COV.TC"
"""Fractional score for the tenant controlled energy data coverage sub-score in EN1."""

SCORE_P_ANS_EN_1_TBL_AGGR_COV_TC = "SCORE.P_ANS_EN_1_TBL.AGGR_COV.TC"
"""Percent score for the tenant controlled energy data coverage sub-score in EN1."""

SCORE_MAX_ANS_EN_1_TBL_AGGR_COV_TC = "SCORE.MAX_ANS_EN_1_TBL.AGGR_COV.TC"
"""Max score for the tenant controlled energy data coverage sub-score in EN1."""

SCORE_ANS_EN_1_TBL_AGGR_COV = "SCORE_ANS_EN_1_TBL.AGGR_COV"
"""Score for the energy data coverage sub-score in absolute points in the indicator EN1."""

SCORE_F_ANS_EN_1_TBL_AGGR_COV = "SCORE.F_ANS_EN_1_TBL.AGGR_COV"
"""Fractional score for the energy data coverage sub-score in the indicator EN1."""

SCORE_P_ANS_EN_1_TBL_AGGR_COV = "SCORE.P_ANS_EN_1_TBL.AGGR_COV"
"""Percent score for the energy data coverage sub-score in the indicator EN1."""

SCORE_MAX_ANS_EN_1_TBL_AGGR_COV = "SCORE.MAX_ANS_EN_1_TBL.AGGR_COV"
"""Max score for the energy data coverage sub-score in the indicator EN1."""

SCORE_ANS_EN_1_TBL_AGGR_LFL_DATA = "SCORE_ANS_EN_1_TBL.AGGR_LFL.DATA"
"""Score for the LFL data availability sub-score in absolute points in the indicator EN1."""

SCORE_F_ANS_EN_1_TBL_AGGR_LFL_DATA = "SCORE.F_ANS_EN_1_TBL.AGGR_LFL.DATA"
"""Fractional score for the LFL data availability sub-score in the indicator EN1."""

SCORE_P_ANS_EN_1_TBL_AGGR_LFL_DATA = "SCORE.P_ANS_EN_1_TBL.AGGR_LFL.DATA"
"""Percent score for the LFL data availability sub-score in the indicator EN1."""

SCORE_MAX_ANS_EN_1_TBL_AGGR_LFL_DATA = "SCORE.MAX_ANS_EN_1_TBL.AGGR_LFL.DATA"
"""Max score for the LFL data availability sub-score in the indicator EN1."""

SCORE_ANS_EN_1_TBL_AGGR_LFL_C_LC = "SCORE_ANS_EN_1_TBL.AGGR_LFL.C.LC"
"""Score for the LFL percent change for landlord controlled areas sub-score in absolute points in the indicator EN1."""

SCORE_F_ANS_EN_1_TBL_AGGR_LFL_C_LC = "SCORE.F_ANS_EN_1_TBL.AGGR_LFL.C.LC"
"""Fractional score for the LFL percent change for landlord controlled areas sub-score in the indicator EN1."""

SCORE_P_ANS_EN_1_TBL_AGGR_LFL_C_LC = "SCORE.P_ANS_EN_1_TBL.AGGR_LFL.C.LC"
"""Percent score for the LFL percent change for landlord controlled areas sub-score in the indicator EN1."""

SCORE_MAX_ANS_EN_1_TBL_AGGR_LFL_C_LC = "SCORE.MAX_ANS_EN_1_TBL.AGGR_LFL.C.LC"
"""Max score for the LFL percent change for landlord controlled areas sub-score in the indicator EN1."""

SCORE_ANS_EN_1_TBL_AGGR_LFL_C_TC = "SCORE_ANS_EN_1_TBL.AGGR_LFL.C.TC"
"""Score for the LFL percent change for tenant controlled areas sub-score in absolute points in the indicator EN1."""

SCORE_F_ANS_EN_1_TBL_AGGR_LFL_C_TC = "SCORE.F_ANS_EN_1_TBL.AGGR_LFL.C.TC"
"""Fractional score for the LFL percent change for tenant controlled areas sub-score in the indicator EN1."""

SCORE_P_ANS_EN_1_TBL_AGGR_LFL_C_TC = "SCORE.P_ANS_EN_1_TBL.AGGR_LFL.C.TC"
"""Percent score for the LFL percent change for tenant controlled areas sub-score in the indicator EN1."""

SCORE_MAX_ANS_EN_1_TBL_AGGR_LFL_C_TC = "SCORE.MAX_ANS_EN_1_TBL.AGGR_LFL.C.TC"
"""Max score for the LFL percent change for tenant controlled areas sub-score in the indicator EN1."""

SCORE_ANS_EN_1_TBL_AGGR_LFL_C = "SCORE_ANS_EN_1_TBL.AGGR_LFL.C"
"""Score for the LFL percent change sub-score in absolute points in the indicator EN1."""

SCORE_F_ANS_EN_1_TBL_AGGR_LFL_C = "SCORE.F_ANS_EN_1_TBL.AGGR_LFL.C"
"""Fractional score for the LFL percent change sub-score in the indicator EN1."""

SCORE_P_ANS_EN_1_TBL_AGGR_LFL_C = "SCORE.P_ANS_EN_1_TBL.AGGR_LFL.C"
"""Percent score for the LFL percent change sub-score in the indicator EN1."""

SCORE_MAX_ANS_EN_1_TBL_AGGR_LFL_C = "SCORE.MAX_ANS_EN_1_TBL.AGGR_LFL.C"
"""Max score for the LFL percent change sub-score in the indicator EN1."""

SCORE_ANS_EN_1_TBL_REN_DATA_ONS = "SCORE_ANS_EN_1_TBL.REN_DATA.ONS"
"""Score for the on-site renewable energy data availability in indicator EN1."""

SCORE_F_ANS_EN_1_TBL_REN_DATA_ONS = "SCORE.F_ANS_EN_1_TBL.REN_DATA.ONS"
"""Fractional score for the on-site renewable energy data availability in indicator EN1."""

SCORE_P_ANS_EN_1_TBL_REN_DATA_ONS = "SCORE.P_ANS_EN_1_TBL.REN_DATA.ONS"
"""Percent score for the on-site renewable energy data availability in indicator EN1."""

SCORE_MAX_ANS_EN_1_TBL_REN_DATA_ONS = "SCORE.MAX_ANS_EN_1_TBL.REN_DATA.ONS"
"""Max score for the on-site renewable energy data availability in indicator EN1."""

SCORE_ANS_EN_1_TBL_REN_DATA_OFS = "SCORE_ANS_EN_1_TBL.REN_DATA.OFS"
"""Score for the off-site renewable energy data availability in indicator EN1."""

SCORE_F_ANS_EN_1_TBL_REN_DATA_OFS = "SCORE.F_ANS_EN_1_TBL.REN_DATA.OFS"
"""Fractional score for the off-site renewable energy data availability in indicator EN1."""

SCORE_P_ANS_EN_1_TBL_REN_DATA_OFS = "SCORE.P_ANS_EN_1_TBL.REN_DATA.OFS"
"""Percent score for the off-site renewable energy data availability in indicator EN1."""

SCORE_MAX_ANS_EN_1_TBL_REN_DATA_OFS = "SCORE.MAX_ANS_EN_1_TBL.REN_DATA.OFS"
"""Max score for the off-site renewable energy data availability in indicator EN1."""

# TODO: percentage of current renewable as a score? SCORE.F_ANS_EN_1_TBL.REN_PERF.CY

SCORE_ANS_EN_1_TBL_REN_PERF_CHANGE = "SCORE_ANS_EN_1_TBL.REN_PERF.CHANGE"
"""Score for the renewable energy percent change sub-score in indicator EN1."""

SCORE_F_ANS_EN_1_TBL_REN_PERF_CHANGE = "SCORE.F_ANS_EN_1_TBL.REN_PERF.CHANGE"
"""Fractional score for the renewable energy percent change sub-score in indicator EN1."""

SCORE_P_ANS_EN_1_TBL_REN_PERF_CHANGE = "SCORE.P_ANS_EN_1_TBL.REN_PERF.CHANGE"
"""Percent score for the renewable energy percent change sub-score in indicator EN1."""

SCORE_MAX_ANS_EN_1_TBL_REN_PERF_CHANGE = "SCORE.MAX_ANS_EN_1_TBL.REN_PERF.CHANGE"
"""Max score for the renewable energy percent change sub-score in indicator EN1."""

SCORE_ANS_EN_1_TBL_REN_PERF = "SCORE_ANS_EN_1_TBL.REN_PERF"
"""Score for the renewable energy performance sub-score in indicator EN1."""

SCORE_F_ANS_EN_1_TBL_REN_PERF = "SCORE.F_ANS_EN_1_TBL.REN_PERF"
"""Fractional score for the renewable energy performance sub-score in indicator EN1."""

SCORE_P_ANS_EN_1_TBL_REN_PERF = "SCORE.P_ANS_EN_1_TBL.REN_PERF"
"""Percent score for the renewable energy performance sub-score in indicator EN1."""

SCORE_MAX_ANS_EN_1_TBL_REN_PERF = "SCORE.MAX_ANS_EN_1_TBL.REN_PERF"
"""Max score for the renewable energy performance sub-score in indicator EN1."""

SCORE_F_ANS_EN_1_TBL_AGGR_EFF = "SCORE.F_ANS_EN_1_TBL.AGGR_EFF"
SCORE_ANS_EN_1_TBL_AGGR_EFF = "SCORE_ANS_EN_1_TBL.AGGR_EFF"
SCORE_P_ANS_EN_1_TBL_AGGR_EFF = "SCORE.P_ANS_EN_1_TBL.AGGR_EFF"
SCORE_MAX_ANS_EN_1_TBL_AGGR_EFF = "SCORE.MAX_ANS_EN_1_TBL.AGGR_EFF"

SCORE_F_ANS_EN_1_TBL_AGGR_LFL = "SCORE.F_ANS_EN_1_TBL.AGGR_LFL"
SCORE_ANS_EN_1_TBL_AGGR_LFL = "SCORE_ANS_EN_1_TBL.AGGR_LFL"
SCORE_P_ANS_EN_1_TBL_AGGR_LFL = "SCORE.P_ANS_EN_1_TBL.AGGR_LFL"
SCORE_MAX_ANS_EN_1_TBL_AGGR_LFL = "SCORE.MAX_ANS_EN_1_TBL.AGGR_LFL"

SCORE_F_ANS_EN_1_TBL_REN = "SCORE.F_ANS_EN_1_TBL.REN"
SCORE_ANS_EN_1_TBL_REN = "SCORE_ANS_EN_1_TBL.REN"
SCORE_P_ANS_EN_1_TBL_REN = "SCORE.P_ANS_EN_1_TBL.REN"
SCORE_MAX_ANS_EN_1_TBL_REN = "SCORE.MAX_ANS_EN_1_TBL.REN"

SCORE_GH_1 = "SCORE_GH_1"
"""Score for the indicator GH1 in absolute points."""

SCORE_F_GH_1 = "SCORE.F_GH_1"
"""Fractional score for the indicator GH1 in absolute points."""

SCORE_P_GH_1 = "SCORE.P_GH_1"
"""Fractional score for the indicator GH1 in absolute points."""

SCORE_MAX_GH_1 = "SCORE.MAX_GH_1"
"""Fractional score for the indicator GH1 in absolute points."""

SCORE_ANS_GH_1_TBL_AGGR_COV_S12 = "SCORE_ANS_GH_1_TBL.AGGR_COV.S12"
"""Absolute score for the scopes 1 and 2 GHG data coverage subscore in GH1."""

SCORE_F_ANS_GH_1_TBL_AGGR_COV_S12 = "SCORE.F_ANS_GH_1_TBL.AGGR_COV.S12"
"""Fractional score for the scopes 1 and 2 GHG data coverage subscore in GH1."""

SCORE_P_ANS_GH_1_TBL_AGGR_COV_S12 = "SCORE.P_ANS_GH_1_TBL.AGGR_COV.S12"
"""Percent score for the scopes 1 and 2 GHG data coverage subscore in GH1."""

SCORE_MAX_ANS_GH_1_TBL_AGGR_COV_S12 = "SCORE.MAX_ANS_GH_1_TBL.AGGR_COV.S12"
"""Max score for the scopes 1 and 2 GHG data coverage subscore in GH1."""

SCORE_ANS_GH_1_TBL_AGGR_COV_S3 = "SCORE_ANS_GH_1_TBL.AGGR_COV.S3"
"""Absolute score for the scope 3 GHG data coverage subscore in GH1."""

SCORE_F_ANS_GH_1_TBL_AGGR_COV_S3 = "SCORE.F_ANS_GH_1_TBL.AGGR_COV.S3"
"""Fractional score for the scope 3 GHG data coverage subscore in GH1."""

SCORE_P_ANS_GH_1_TBL_AGGR_COV_S3 = "SCORE.P_ANS_GH_1_TBL.AGGR_COV.S3"
"""Percent score for the scope 3 GHG data coverage subscore in GH1."""

SCORE_MAX_ANS_GH_1_TBL_AGGR_COV_S3 = "SCORE.MAX_ANS_GH_1_TBL.AGGR_COV.S3"
"""Max score for the scope 3 GHG data coverage subscore in GH1."""

SCORE_ANS_GH_1_TBL_AGGR_COV = "SCORE_ANS_GH_1_TBL.AGGR_COV"
"""Absolute score for the GHG data coverage subscore in GH1."""

SCORE_F_ANS_GH_1_TBL_AGGR_COV = "SCORE.F_ANS_GH_1_TBL.AGGR_COV"
"""Fractional score for the GHG data coverage subscore in GH1."""

SCORE_P_ANS_GH_1_TBL_AGGR_COV = "SCORE.P_ANS_GH_1_TBL.AGGR_COV"
"""Percent score for the GHG data coverage subscore in GH1."""

SCORE_MAX_ANS_GH_1_TBL_AGGR_COV = "SCORE.MAX_ANS_GH_1_TBL.AGGR_COV"
"""Max score for the GHG data coverage subscore in GH1."""

SCORE_ANS_GH_1_TBL_AGGR_LFL_S12 = "SCORE_ANS_GH_1_TBL.AGGR_LFL.S12"
"""Absolute score for the scopes 1 and 2 GHG data coverage subscore in GH1."""

SCORE_F_ANS_GH_1_TBL_AGGR_LFL_S12 = "SCORE.F_ANS_GH_1_TBL.AGGR_LFL.S12"
"""Fractional score for the scopes 1 and 2 GHG data coverage subscore in GH1."""

SCORE_P_ANS_GH_1_TBL_AGGR_LFL_S12 = "SCORE.P_ANS_GH_1_TBL.AGGR_LFL.S12"
"""Percent score for the scopes 1 and 2 GHG data coverage subscore in GH1."""

SCORE_MAX_ANS_GH_1_TBL_AGGR_LFL_S12 = "SCORE.MAX_ANS_GH_1_TBL.AGGR_LFL.S12"
"""Max score for the scopes 1 and 2 GHG LFL subscore in GH1."""

SCORE_ANS_GH_1_TBL_AGGR_LFL_S3 = "SCORE_ANS_GH_1_TBL.AGGR_LFL.S3"
"""Absolute score for the scope 3 GHG LFL subscore in GH1."""

SCORE_F_ANS_GH_1_TBL_AGGR_LFL_S3 = "SCORE.F_ANS_GH_1_TBL.AGGR_LFL.S3"
"""Fractional score for the scope 3 GHG LFL subscore in GH1."""

SCORE_P_ANS_GH_1_TBL_AGGR_LFL_S3 = "SCORE.P_ANS_GH_1_TBL.AGGR_LFL.S3"
"""Percent score for the scope 3 GHG LFL subscore in GH1."""

SCORE_MAX_ANS_GH_1_TBL_AGGR_LFL_S3 = "SCORE.MAX_ANS_GH_1_TBL.AGGR_LFL.S3"
"""Max score for the scope 3 GHG LFL subscore in GH1."""

SCORE_ANS_GH_1_TBL_AGGR_LFL = "SCORE_ANS_GH_1_TBL.AGGR_LFL"
"""Absolute score for the GHG LFL subscore in GH1."""

SCORE_F_ANS_GH_1_TBL_AGGR_LFL = "SCORE.F_ANS_GH_1_TBL.AGGR_LFL"
"""Fractional score for the GHG LFL subscore in GH1."""

SCORE_P_ANS_GH_1_TBL_AGGR_LFL = "SCORE.P_ANS_GH_1_TBL.AGGR_LFL"
"""Percent score for the GHG LFL subscore in GH1."""

SCORE_MAX_ANS_GH_1_TBL_AGGR_LFL = "SCORE.MAX_ANS_GH_1_TBL.AGGR_LFL"
"""Max score for the GHG LFL subscore in GH1."""

SCORE_WS_1 = "SCORE_WS_1"
"""Score for the indicator WS1 in absolute points."""

SCORE_F_WS_1 = "SCORE.F_WS_1"
"""Fractional score for the indicator WS1 in absolute points."""

SCORE_P_WS_1 = "SCORE.P_WS_1"
"""Fractional score for the indicator WS1 in absolute points."""

SCORE_MAX_WS_1 = "SCORE.MAX_WS_1"
"""Fractional score for the indicator WS1 in absolute points."""

SCORE_ANS_WS_1_TBL_MAIN = "SCORE_ANS_WS_1_TBL.MAIN"
"""Absolute score for the waste data coverage subscore in the indicator WS1."""

SCORE_F_ANS_WS_1_TBL_MAIN = "SCORE.F_ANS_WS_1_TBL.MAIN"
"""Fractional score for the waste data coverage subscore in the indicator WS1."""

SCORE_P_ANS_WS_1_TBL_MAIN = "SCORE.P_ANS_WS_1_TBL.MAIN"
"""Percent score for the waste data coverage subscore in the indicator WS1."""

SCORE_MAX_WS_1_COV = "SCORE.MAX_WS_1_COV"
"""Max score for the waste data coverage subscore in the indicator WS1."""

SCORE_ANS_WS_1_TBL_MAIN_LC = "SCORE_ANS_WS_1_TBL.MAIN.LC"
"""Absolute score for the landlord controlled waste data coverage subscore in the indicator WS1."""

SCORE_F_ANS_WS_1_TBL_MAIN_LC = "SCORE.F_ANS_WS_1_TBL.MAIN.LC"
"""Fractional score for the landlord controlled waste data coverage subscore in the indicator WS1."""

SCORE_P_ANS_WS_1_TBL_MAIN_LC = "SCORE.P_ANS_WS_1_TBL.MAIN.LC"
"""Percent score for the landlord controlled waste data coverage subscore in the indicator WS1."""

SCORE_MAX_WS_1_COV_LC = "SCORE.MAX_WS_1_COV.LC"
"""Max score for the landlord controlled waste data coverage subscore in the indicator WS1."""

SCORE_ANS_WS_1_TBL_MAIN_TC = "SCORE_ANS_WS_1_TBL.MAIN.TC"
"""Absolute score for the tenant controlled waste data coverage subscore in the indicator WS1."""

SCORE_F_ANS_WS_1_TBL_MAIN_TC = "SCORE.F_ANS_WS_1_TBL.MAIN.TC"
"""Fractional score for the tenant controlled waste data coverage subscore in the indicator WS1."""

SCORE_P_ANS_WS_1_TBL_MAIN_TC = "SCORE.P_ANS_WS_1_TBL.MAIN.TC"
"""Percent score for the tenant controlled waste data coverage subscore in the indicator WS1."""

SCORE_MAX_WS_1_COV_TC = "SCORE.MAX_WS_1_COV.TC"
"""Max score for the tenant controlled waste data coverage subscore in the indicator WS1."""

SCORE_ANS_WS_1_TBL_AGGR = "SCORE_ANS_WS_1_TBL.AGGR"
"""Absolute score for the diverted waste subscore in the indicator WS1."""

SCORE_F_ANS_WS_1_TBL_AGGR = "SCORE.F_ANS_WS_1_TBL.AGGR"
"""Fractional score for the diverted waste subscore in the indicator WS1."""

SCORE_P_ANS_WS_1_TBL_AGGR = "SCORE.P_ANS_WS_1_TBL.AGGR"
"""Percent score for the diverted waste subscore in the indicator WS1."""

SCORE_MAX_WS_1_DV = "SCORE.MAX_WS_1_DV"
"""Max score for the diverted waste subscore in the indicator WS1."""

SCORE_WT_1 = "SCORE_WT_1"
"""Score for the indicator WT1 in absolute points."""

SCORE_F_WT_1 = "SCORE.F_WT_1"
"""Fractional score for the indicator WT1 in absolute points."""

SCORE_P_WT_1 = "SCORE.P_WT_1"
"""Fractional score for the indicator WT1 in absolute points."""

SCORE_MAX_WT_1 = "SCORE.MAX_WT_1"
"""Fractional score for the indicator WT1 in absolute points."""

SCORE_F_ANS_WT_1_TBL_AGGR_COV_LC = "SCORE.F_ANS_WT_1_TBL.AGGR_COV.LC"
"""Fractional score for the water data coverage in landlord controlled areas subscore in the indicator WT1."""

SCORE_ANS_WT_1_TBL_AGGR_COV_LC = "SCORE_ANS_WT_1_TBL.AGGR_COV.LC"
"""Absolute score for the water data coverage in landlord controlled areas subscore in the indicator WT1."""

SCORE_P_ANS_WT_1_TBL_AGGR_COV_LC = "SCORE.P_ANS_WT_1_TBL.AGGR_COV.LC"
"""Percent score for the water data coverage in landlord controlled areas subscore in the indicator WT1."""

SCORE_MAX_ANS_WT_1_TBL_AGGR_COV_LC = "SCORE.MAX_ANS_WT_1_TBL.AGGR_COV.LC"
"""Max score for the water data coverage in landlord controlled areas subscore in the indicator WT1."""

SCORE_ANS_WT_1_TBL_AGGR_COV_TC = "SCORE_ANS_WT_1_TBL.AGGR_COV.TC"
"""Absolute score for the water data coverage in tenant controlled areas subscore in the indicator WT1."""

SCORE_F_ANS_WT_1_TBL_AGGR_COV_TC = "SCORE.F_ANS_WT_1_TBL.AGGR_COV.TC"
"""Fractional score for the water data coverage in tenant controlled areas subscore in the indicator WT1."""

SCORE_P_ANS_WT_1_TBL_AGGR_COV_TC = "SCORE.P_ANS_WT_1_TBL.AGGR_COV.TC"
"""Percent score for the water data coverage in tenant controlled areas subscore in the indicator WT1."""

SCORE_MAX_ANS_WT_1_TBL_AGGR_COV_TC = "SCORE.MAX_ANS_WT_1_TBL.AGGR_COV.TC"
"""Max score for the water data coverage in tenant controlled areas subscore in the indicator WT1."""

SCORE_ANS_WT_1_TBL_AGGR_COV = "SCORE_ANS_WT_1_TBL.AGGR_COV"
"""Absolute score for the water data coverage subscore in the indicator WT1."""

SCORE_F_ANS_WT_1_TBL_AGGR_COV = "SCORE.F_ANS_WT_1_TBL.AGGR_COV"
"""Absolute score for the water data coverage subscore in the indicator WT1."""

SCORE_P_ANS_WT_1_TBL_AGGR_COV = "SCORE.P_ANS_WT_1_TBL.AGGR_COV"
"""Percent score for the water data coverage subscore in the indicator WT1."""

SCORE_MAX_ANS_WT_1_TBL_AGGR_COV = "SCORE.MAX_ANS_WT_1_TBL.AGGR_COV"
"""Max score for the water data coverage subscore in the indicator WT1."""

SCORE_ANS_WT_1_TBL_AGGR_LFL_LC = "SCORE_ANS_WT_1_TBL.AGGR_LFL.LC"
"""Absolute score for the water LFL subscore in the landlord controlled areas in the indicator WT1."""

SCORE_F_ANS_WT_1_TBL_AGGR_LFL_LC = "SCORE.F_ANS_WT_1_TBL.AGGR_LFL.LC"
"""Fractional score for the water LFL subscore in the landlord controlled areas in the indicator WT1."""

SCORE_P_ANS_WT_1_TBL_AGGR_LFL_LC = "SCORE.P_ANS_WT_1_TBL.AGGR_LFL.LC"
"""Percent score for the water LFL subscore in the landlord controlled areas in the indicator WT1."""

SCORE_MAX_ANS_WT_1_TBL_AGGR_LFL_LC = "SCORE.MAX_ANS_WT_1_TBL.AGGR_LFL.LC"
"""Max score for the water LFL subscore in the landlord controlled areas in the indicator WT1."""

SCORE_ANS_WT_1_TBL_AGGR_LFL_TC = "SCORE_ANS_WT_1_TBL.AGGR_LFL.TC"
"""Absolute score for the water LFL subscore in the tenant controlled areas in the indicator WT1."""

SCORE_F_ANS_WT_1_TBL_AGGR_LFL_TC = "SCORE.F_ANS_WT_1_TBL.AGGR_LFL.TC"
"""Fractional score for the water LFL subscore in the tenant controlled areas in the indicator WT1."""

SCORE_P_ANS_WT_1_TBL_AGGR_LFL_TC = "SCORE.P_ANS_WT_1_TBL.AGGR_LFL.TC"
"""Percent score for the water LFL subscore in the tenant controlled areas in the indicator WT1."""

SCORE_MAX_ANS_WT_1_TBL_AGGR_LFL_TC = "SCORE.MAX_ANS_WT_1_TBL.AGGR_LFL.TC"
"""Max score for the water LFL subscore in the tenant controlled areas in the indicator WT1."""

SCORE_ANS_WT_1_TBL_AGGR_LFL = "SCORE_ANS_WT_1_TBL.AGGR_LFL"
"""Absolute score for the water LFL subscore in the indicator WT1."""

SCORE_F_ANS_WT_1_TBL_AGGR_LFL = "SCORE.F_ANS_WT_1_TBL.AGGR_LFL"
"""Fractional score for the water LFL subscore in the indicator WT1."""

SCORE_P_ANS_WT_1_TBL_AGGR_LFL = "SCORE.P_ANS_WT_1_TBL.AGGR_LFL"
"""Percent score for the water LFL subscore in the indicator WT1."""

SCORE_MAX_ANS_WT_1_TBL_AGGR_LFL = "SCORE.MAX_ANS_WT_1_TBL.AGGR_LFL"
"""Max score for the water LFL subscore in the indicator WT1."""

SCORE_ANS_WT_1_TBL_REC_DATA = "SCORE_ANS_WT_1_TBL.REC_DATA"
"""Absolute score for the recycled water availability subscore in the indicator WT1."""

SCORE_F_ANS_WT_1_TBL_REC_DATA = "SCORE.F_ANS_WT_1_TBL.REC_DATA"
"""Fractional score for the recycled water availability subscore in the indicator WT1."""

SCORE_P_ANS_WT_1_TBL_REC_DATA = "SCORE.P_ANS_WT_1_TBL.REC_DATA"
"""Percent score for the recycled water availability subscore in the indicator WT1."""

SCORE_MAX_ANS_WT_1_TBL_REC_DATA = "SCORE.MAX_ANS_WT_1_TBL.REC_DATA"
"""Max score for the recycled water availability subscore in the indicator WT1."""

SCORE_ANS_WT_1_TBL_REC_PERF_CHANGE = "SCORE_ANS_WT_1_TBL.REC_PERF.CHANGE"
"""Absolute score for the recycled water percent change subscore in the indicator WT1."""

SCORE_F_ANS_WT_1_TBL_REC_PERF_CHANGE = "SCORE.F_ANS_WT_1_TBL.REC_PERF.CHANGE"
"""Fractional score for the recycled water percent change subscore in the indicator WT1."""

SCORE_P_ANS_WT_1_TBL_REC_PERF_CHANGE = "SCORE.P_ANS_WT_1_TBL.REC_PERF.CHANGE"
"""Percent score for the recycled water percent change subscore in the indicator WT1."""

SCORE_MAX_ANS_WT_1_TBL_REC_PERF_CHANGE = "SCORE.MAX_ANS_WT_1_TBL.REC_PERF.CHANGE"
"""Max score for the recycled water percent change subscore in the indicator WT1."""

SCORE_ANS_WT_1_TBL_REC_PERF = "SCORE_ANS_WT_1_TBL.REC_PERF"
"""Absolute score for the recycled water performance subscore in the indicator WT1."""

SCORE_F_ANS_WT_1_TBL_REC_PERF = "SCORE.F_ANS_WT_1_TBL.REC_PERF"
"""Fractional score for the recycled water performance subscore in the indicator WT1."""

SCORE_P_ANS_WT_1_TBL_REC_PERF = "SCORE.P_ANS_WT_1_TBL.REC_PERF"
"""Percent score for the recycled water performance subscore in the indicator WT1."""

SCORE_MAX_ANS_WT_1_TBL_REC_PERF = "SCORE.MAX_ANS_WT_1_TBL.REC_PERF"
"""Max score for the recycled water performance subscore in the indicator WT1."""

# TODO: add the SCORE.F_ANS_WT_1_TBL.REC_PERF.CY

SCORE_ANS_WT_1_TBL_REC = "SCORE_ANS_WT_1_TBL.REC"
"""Absolute score for the recycled water total subscore in the indicator WT1."""

SCORE_F_ANS_WT_1_TBL_REC = "SCORE.F_ANS_WT_1_TBL.REC"
"""Fractional score for the recycled water total subscore in the indicator WT1."""

SCORE_P_ANS_WT_1_TBL_REC = "SCORE.P_ANS_WT_1_TBL.REC"
"""Percent score for the recycled water total subscore in the indicator WT1."""

SCORE_MAX_ANS_WT_1_TBL_REC = "SCORE.MAX_ANS_WT_1_TBL.REC"
"""Max score for the recycled water total subscore in the indicator WT1."""

SCORE_BC_1_1 = "SCORE_BC_1.1"
"""Score for the indicator BC1.1 in absolute points."""

SCORE_F_BC_1_1 = "SCORE.F_BC_1.1"
"""Fractional score for the indicator BC1.1 ."""

SCORE_P_BC_1_1 = "SCORE.P_BC_1.1"
"""Percent score for the indicator BC1.1."""

SCORE_MAX_BC_1_1 = "SCORE.MAX_BC_1.1"
"""Max score for the indicator BC1.1."""

SCORE_F_BC_1_1_COV = "SCORE.F_BC_1.1_COV"
"""Fractional score for the coverage subscore in the indicator BC1.1."""

SCORE_BC_1_1_COV = "SCORE_BC_1.1_COV"
"""Score for the coverage subscore in the indicator BC1.1 in absolute points."""

SCORE_P_BC_1_1_COV = "SCORE.P_BC_1.1_COV"
"""Percent score for the coverage subscore in the indicator BC1.1."""

SCORE_MAX_BC_1_1_COV = "SCORE.MAX_BC_1.1_COV"
"""Max score for the coverage subscore in the indicator BC1.1."""

SCORE_BC_1_2 = "SCORE_BC_1.2"
"""Score for the indicator BC1.2 in absolute points."""

SCORE_F_BC_1_2 = "SCORE.F_BC_1.2"
"""Fractional score for the indicator BC1.2."""

SCORE_P_BC_1_2 = "SCORE.P_BC_1.2"
"""Percent score for the indicator BC1.2."""

SCORE_MAX_BC_1_2 = "SCORE.MAX_BC_1.2"
"""Max score for the indicator BC1.2."""

SCORE_F_BC_1_2_COV = "SCORE.F_BC_1.2_COV"
"""Fractional score for the coverage subscore in the indicator BC1.2."""

SCORE_BC_1_2_COV = "SCORE_BC_1.2_COV"
"""Score for the coverage subscore in the indicator BC1.2 in absolute points."""

SCORE_P_BC_1_2_COV = "SCORE.P_BC_1.2_COV"
"""Percent score for the coverage subscore in the indicator BC1.2."""

SCORE_MAX_BC_1_2_COV = "SCORE.MAX_BC_1.2_COV"
"""Max score for the coverage subscore in the indicator BC1.2."""

SCORE_BC_1 = "SCORE_BC_1"
"""Score for the indicators BC1 in absolute points."""

SCORE_F_BC_1 = "SCORE.F_BC_1"
"""Fractional score for the indicators BC1."""

SCORE_P_BC_1 = "SCORE.P_BC_1"
"""Percent score for the indicators BC1."""

SCORE_MAX_BC_1 = "SCORE.MAX_BC_1"
"""Max score for the indicators BC1."""

SCORE_BC_2 = "SCORE_BC_2"
"""Score for the indicator BC2 in absolute points."""

SCORE_F_BC_2 = "SCORE.F_BC_2"
"""Fractional score for the indicator BC2."""

SCORE_P_BC_2 = "SCORE.P_BC_2"
"""Percent score for the indicator BC2."""

SCORE_MAX_BC_2 = "SCORE.MAX_BC_2"
"""Max score for the indicator BC2."""

SCORE_F_BC_2_COV = "SCORE.F_BC_2_COV"
"""Fractional score for the coverage subscore in the indicator BC2."""

SCORE_BC_2_COV = "SCORE_BC_2_COV"
"""Score for the coverage subscore in the indicator BC2 in absolute points."""

SCORE_P_BC_2_COV = "SCORE.P_BC_2_COV"
"""Percent score for the coverage subscore in the indicator BC2."""

SCORE_MAX_BC_2_COV = "SCORE.MAX_BC_2_COV"
"""Max score for the coverage subscore in the indicator BC2."""

score_columns_rename_dict = {
    kc.response_id: RESPONSE_ID,
    ac.property_subtype: PROPERTY_SUBTYPE,
    ac.country: COUNTRY,
    sc.score_en1: SCORE_EN_1,
    sc.score_en1_fraction: SCORE_F_EN_1,
    sc.score_en1_percent: SCORE_P_EN_1,
    sc.score_en1_max: SCORE_MAX_EN_1,
    sc.score_en1_en_area_time_cov_p_lc_absolute: SCORE_ANS_EN_1_TBL_AGGR_COV_LC,
    sc.score_en1_en_area_time_cov_p_lc: SCORE_F_ANS_EN_1_TBL_AGGR_COV_LC,
    sc.score_en1_en_area_time_cov_p_lc_percent: SCORE_P_ANS_EN_1_TBL_AGGR_COV_LC,
    sc.score_en1_en_area_time_cov_p_lc_max: SCORE_MAX_ANS_EN_1_TBL_AGGR_COV_LC,
    sc.score_en1_en_area_time_cov_p_tc_absolute: SCORE_ANS_EN_1_TBL_AGGR_COV_TC,
    sc.score_en1_en_area_time_cov_p_tc: SCORE_F_ANS_EN_1_TBL_AGGR_COV_TC,
    sc.score_en1_en_area_time_cov_p_tc_percent: SCORE_P_ANS_EN_1_TBL_AGGR_COV_TC,
    sc.score_en1_en_area_time_cov_p_tc_max: SCORE_MAX_ANS_EN_1_TBL_AGGR_COV_TC,
    sc.score_en1_en_area_time_cov_p_absolute: SCORE_ANS_EN_1_TBL_AGGR_COV,
    sc.score_en1_en_area_time_cov_p: SCORE_F_ANS_EN_1_TBL_AGGR_COV,
    sc.score_en1_en_area_time_cov_p_percent: SCORE_P_ANS_EN_1_TBL_AGGR_COV,
    sc.score_en1_en_area_time_cov_p_max: SCORE_MAX_ANS_EN_1_TBL_AGGR_COV,
    sc.score_en1_lfl_availability_absolute: SCORE_ANS_EN_1_TBL_AGGR_LFL_DATA,
    sc.score_en1_lfl_availability: SCORE_F_ANS_EN_1_TBL_AGGR_LFL_DATA,
    sc.score_en1_lfl_availability_percent: SCORE_P_ANS_EN_1_TBL_AGGR_LFL_DATA,
    sc.score_en1_lfl_availability_max: SCORE_MAX_ANS_EN_1_TBL_AGGR_LFL_DATA,
    sc.score_en1_en_lfl_percent_change_lc_absolute: SCORE_ANS_EN_1_TBL_AGGR_LFL_C_LC,
    sc.score_en1_en_lfl_percent_change_lc: SCORE_F_ANS_EN_1_TBL_AGGR_LFL_C_LC,
    sc.score_en1_en_lfl_percent_change_lc_percent: SCORE_P_ANS_EN_1_TBL_AGGR_LFL_C_LC,
    sc.score_en1_en_lfl_percent_change_lc_max: SCORE_MAX_ANS_EN_1_TBL_AGGR_LFL_C_LC,
    sc.score_en1_en_lfl_percent_change_tc_absolute: SCORE_ANS_EN_1_TBL_AGGR_LFL_C_TC,
    sc.score_en1_en_lfl_percent_change_tc: SCORE_F_ANS_EN_1_TBL_AGGR_LFL_C_TC,
    sc.score_en1_en_lfl_percent_change_tc_percent: SCORE_P_ANS_EN_1_TBL_AGGR_LFL_C_TC,
    sc.score_en1_en_lfl_percent_change_tc_max: SCORE_MAX_ANS_EN_1_TBL_AGGR_LFL_C_TC,
    sc.score_en1_en_lfl_percent_change_absolute: SCORE_ANS_EN_1_TBL_AGGR_LFL_C,
    sc.score_en1_en_lfl_percent_change: SCORE_F_ANS_EN_1_TBL_AGGR_LFL_C,
    sc.score_en1_en_lfl_percent_change_percent: SCORE_P_ANS_EN_1_TBL_AGGR_LFL_C,
    sc.score_en1_en_lfl_percent_change_max: SCORE_MAX_ANS_EN_1_TBL_AGGR_LFL_C,
    sc.score_en1_en_ren_ons_absolute: SCORE_ANS_EN_1_TBL_REN_DATA_ONS,
    sc.score_en1_en_ren_ons: SCORE_F_ANS_EN_1_TBL_REN_DATA_ONS,
    sc.score_en1_en_ren_ons_percent: SCORE_P_ANS_EN_1_TBL_REN_DATA_ONS,
    sc.score_en1_en_ren_ons_max: SCORE_MAX_ANS_EN_1_TBL_REN_DATA_ONS,
    sc.score_en1_en_ren_ofs_absolute: SCORE_ANS_EN_1_TBL_REN_DATA_OFS,
    sc.score_en1_en_ren_ofs: SCORE_F_ANS_EN_1_TBL_REN_DATA_OFS,
    sc.score_en1_en_ren_ofs_percent: SCORE_P_ANS_EN_1_TBL_REN_DATA_OFS,
    sc.score_en1_en_ren_ofs_max: SCORE_MAX_ANS_EN_1_TBL_REN_DATA_OFS,
    sc.score_en1_en_ren_percent_change_absolute: SCORE_ANS_EN_1_TBL_REN_PERF_CHANGE,
    sc.score_en1_en_ren_percent_change: SCORE_F_ANS_EN_1_TBL_REN_PERF_CHANGE,
    sc.score_en1_en_ren_percent_change_percent: SCORE_P_ANS_EN_1_TBL_REN_PERF_CHANGE,
    sc.score_en1_en_ren_percent_change_max: SCORE_MAX_ANS_EN_1_TBL_REN_PERF_CHANGE,
    sc.score_en1_en_ren_performance_absolute: SCORE_ANS_EN_1_TBL_REN_PERF,
    sc.score_en1_en_ren_performance: SCORE_F_ANS_EN_1_TBL_REN_PERF,
    sc.score_en1_en_ren_performance_percent: SCORE_P_ANS_EN_1_TBL_REN_PERF,
    sc.score_en1_en_ren_performance_max: SCORE_MAX_ANS_EN_1_TBL_REN_PERF,
    sc.score_en1_energy_efficiency: SCORE_F_ANS_EN_1_TBL_AGGR_EFF,
    sc.score_en1_energy_efficiency_absolute: SCORE_ANS_EN_1_TBL_AGGR_EFF,
    sc.score_en1_energy_efficiency_percent: SCORE_P_ANS_EN_1_TBL_AGGR_EFF,
    sc.score_en1_energy_efficiency_max: SCORE_MAX_ANS_EN_1_TBL_AGGR_EFF,
    sc.score_en1_en_lfl: SCORE_F_ANS_EN_1_TBL_AGGR_LFL,
    sc.score_en1_en_lfl_absolute: SCORE_ANS_EN_1_TBL_AGGR_LFL,
    sc.score_en1_en_lfl_max: SCORE_MAX_ANS_EN_1_TBL_AGGR_LFL,
    sc.score_en1_en_lfl_percent: SCORE_P_ANS_EN_1_TBL_AGGR_LFL,
    sc.score_en1_energy_performance: "SCORE.F_ANS_EN_1_TBL.AGGR_PERF",
    sc.score_en1_energy_performance_absolute: "SCORE_ANS_EN_1_TBL.AGGR_PERF",
    sc.score_en1_energy_performance_max: "SCORE.MAX_ANS_EN_1_TBL.AGGR_PERF",
    sc.score_en1_energy_performance_percent: "SCORE.P_ANS_EN_1_TBL.AGGR_PERF",
    sc.score_en1_en_ren: SCORE_F_ANS_EN_1_TBL_REN,
    sc.score_en1_en_ren_absolute: SCORE_ANS_EN_1_TBL_REN,
    sc.score_en1_en_ren_max: SCORE_MAX_ANS_EN_1_TBL_REN,
    sc.score_en1_en_ren_percent: SCORE_P_ANS_EN_1_TBL_REN,
    sc.score_gh1: SCORE_GH_1,
    sc.score_gh1_fraction: SCORE_F_GH_1,
    sc.score_gh1_percent: SCORE_P_GH_1,
    sc.score_gh1_max: SCORE_MAX_GH_1,
    sc.score_gh1_ghg_area_time_cov_p_s12: SCORE_F_ANS_GH_1_TBL_AGGR_COV_S12,
    sc.score_gh1_ghg_area_time_cov_p_s12_absolute: SCORE_ANS_GH_1_TBL_AGGR_COV_S12,
    sc.score_gh1_ghg_area_time_cov_p_s12_percent: SCORE_P_ANS_GH_1_TBL_AGGR_COV_S12,
    sc.score_gh1_ghg_area_time_cov_p_s12_max: SCORE_MAX_ANS_GH_1_TBL_AGGR_COV_S12,
    sc.score_gh1_ghg_area_time_cov_p_s3: SCORE_F_ANS_GH_1_TBL_AGGR_COV_S3,
    sc.score_gh1_ghg_area_time_cov_p_s3_absolute: SCORE_ANS_GH_1_TBL_AGGR_COV_S3,
    sc.score_gh1_ghg_area_time_cov_p_s3_percent: SCORE_P_ANS_GH_1_TBL_AGGR_COV_S3,
    sc.score_gh1_ghg_area_time_cov_p_s3_max: SCORE_MAX_ANS_GH_1_TBL_AGGR_COV_S3,
    sc.score_gh1_ghg_area_time_cov_p: SCORE_F_ANS_GH_1_TBL_AGGR_COV,
    sc.score_gh1_ghg_area_time_cov_p_absolute: SCORE_ANS_GH_1_TBL_AGGR_COV,
    sc.score_gh1_ghg_area_time_cov_p_percent: SCORE_P_ANS_GH_1_TBL_AGGR_COV,
    sc.score_gh1_ghg_area_time_cov_p_max: SCORE_MAX_ANS_GH_1_TBL_AGGR_COV,
    sc.score_gh1_ghg_lfl_percent_change_s12: SCORE_F_ANS_GH_1_TBL_AGGR_LFL_S12,
    sc.score_gh1_ghg_lfl_percent_change_s12_absolute: SCORE_ANS_GH_1_TBL_AGGR_LFL_S12,
    sc.score_gh1_ghg_lfl_percent_change_s12_percent: SCORE_P_ANS_GH_1_TBL_AGGR_LFL_S12,
    sc.score_gh1_ghg_lfl_percent_change_s12_max: SCORE_MAX_ANS_GH_1_TBL_AGGR_LFL_S12,
    sc.score_gh1_ghg_lfl_percent_change_s3: SCORE_F_ANS_GH_1_TBL_AGGR_LFL_S3,
    sc.score_gh1_ghg_lfl_percent_change_s3_absolute: SCORE_ANS_GH_1_TBL_AGGR_LFL_S3,
    sc.score_gh1_ghg_lfl_percent_change_s3_percent: SCORE_P_ANS_GH_1_TBL_AGGR_LFL_S3,
    sc.score_gh1_ghg_lfl_percent_change_s3_max: SCORE_MAX_ANS_GH_1_TBL_AGGR_LFL_S3,
    sc.score_gh1_ghg_lfl_percent_change: SCORE_F_ANS_GH_1_TBL_AGGR_LFL,
    sc.score_gh1_ghg_lfl_percent_change_absolute: SCORE_ANS_GH_1_TBL_AGGR_LFL,
    sc.score_gh1_ghg_lfl_percent_change_percent: SCORE_P_ANS_GH_1_TBL_AGGR_LFL,
    sc.score_gh1_ghg_lfl_percent_change_max: SCORE_MAX_ANS_GH_1_TBL_AGGR_LFL,
    sc.score_ws1: SCORE_WS_1,
    sc.score_ws1_fraction: SCORE_F_WS_1,
    sc.score_ws1_percent: SCORE_P_WS_1,
    sc.score_ws1_max: SCORE_MAX_WS_1,
    sc.score_ws1_was_area_cov_p: SCORE_F_ANS_WS_1_TBL_MAIN,
    sc.score_ws1_was_area_cov_p_absolute: SCORE_ANS_WS_1_TBL_MAIN,
    sc.score_ws1_was_area_cov_p_percent: SCORE_P_ANS_WS_1_TBL_MAIN,
    sc.score_ws1_was_area_cov_p_max: SCORE_MAX_WS_1_COV,
    sc.score_ws1_was_area_cov_p_lc: SCORE_F_ANS_WS_1_TBL_MAIN_LC,
    sc.score_ws1_was_area_cov_p_lc_absolute: SCORE_ANS_WS_1_TBL_MAIN_LC,
    sc.score_ws1_was_area_cov_p_lc_percent: SCORE_P_ANS_WS_1_TBL_MAIN_LC,
    sc.score_ws1_was_area_cov_p_lc_max: SCORE_MAX_WS_1_COV_LC,
    sc.score_ws1_was_area_cov_p_tc: SCORE_F_ANS_WS_1_TBL_MAIN_TC,
    sc.score_ws1_was_area_cov_p_tc_absolute: SCORE_ANS_WS_1_TBL_MAIN_TC,
    sc.score_ws1_was_area_cov_p_tc_percent: SCORE_P_ANS_WS_1_TBL_MAIN_TC,
    sc.score_ws1_was_area_cov_p_tc_max: SCORE_MAX_WS_1_COV_TC,
    sc.score_ws1_was_diverted_percent: SCORE_F_ANS_WS_1_TBL_AGGR,
    sc.score_ws1_was_diverted_percent_absolute: SCORE_ANS_WS_1_TBL_AGGR,
    sc.score_ws1_was_diverted_percent_percent: SCORE_P_ANS_WS_1_TBL_AGGR,
    sc.score_ws1_was_diverted_percent_max: SCORE_MAX_WS_1_DV,
    sc.score_wt1: SCORE_WT_1,
    sc.score_wt1_fraction: SCORE_F_WT_1,
    sc.score_wt1_percent: SCORE_P_WT_1,
    sc.score_wt1_max: SCORE_MAX_WT_1,
    sc.score_wt1_wat_area_time_cov_p_lc: SCORE_F_ANS_WT_1_TBL_AGGR_COV_LC,
    sc.score_wt1_wat_area_time_cov_p_lc_absolute: SCORE_ANS_WT_1_TBL_AGGR_COV_LC,
    sc.score_wt1_wat_area_time_cov_p_lc_percent: SCORE_P_ANS_WT_1_TBL_AGGR_COV_LC,
    sc.score_wt1_wat_area_time_cov_p_lc_max: SCORE_MAX_ANS_WT_1_TBL_AGGR_COV_LC,
    sc.score_wt1_wat_area_time_cov_p_tc: SCORE_F_ANS_WT_1_TBL_AGGR_COV_TC,
    sc.score_wt1_wat_area_time_cov_p_tc_absolute: SCORE_ANS_WT_1_TBL_AGGR_COV_TC,
    sc.score_wt1_wat_area_time_cov_p_tc_percent: SCORE_P_ANS_WT_1_TBL_AGGR_COV_TC,
    sc.score_wt1_wat_area_time_cov_p_tc_max: SCORE_MAX_ANS_WT_1_TBL_AGGR_COV_TC,
    sc.score_wt1_wat_area_time_cov_p: SCORE_F_ANS_WT_1_TBL_AGGR_COV,
    sc.score_wt1_wat_area_time_cov_p_absolute: SCORE_ANS_WT_1_TBL_AGGR_COV,
    sc.score_wt1_wat_area_time_cov_p_percent: SCORE_P_ANS_WT_1_TBL_AGGR_COV,
    sc.score_wt1_wat_area_time_cov_p_max: SCORE_MAX_ANS_WT_1_TBL_AGGR_COV,
    sc.score_wt1_wat_lfl_percent_change_lc: SCORE_F_ANS_WT_1_TBL_AGGR_LFL_LC,
    sc.score_wt1_wat_lfl_percent_change_lc_absolute: SCORE_ANS_WT_1_TBL_AGGR_LFL_LC,
    sc.score_wt1_wat_lfl_percent_change_lc_percent: SCORE_P_ANS_WT_1_TBL_AGGR_LFL_LC,
    sc.score_wt1_wat_lfl_percent_change_lc_max: SCORE_MAX_ANS_WT_1_TBL_AGGR_LFL_LC,
    sc.score_wt1_wat_lfl_percent_change_tc: SCORE_F_ANS_WT_1_TBL_AGGR_LFL_TC,
    sc.score_wt1_wat_lfl_percent_change_tc_absolute: SCORE_ANS_WT_1_TBL_AGGR_LFL_TC,
    sc.score_wt1_wat_lfl_percent_change_tc_percent: SCORE_P_ANS_WT_1_TBL_AGGR_LFL_TC,
    sc.score_wt1_wat_lfl_percent_change_tc_max: SCORE_MAX_ANS_WT_1_TBL_AGGR_LFL_TC,
    sc.score_wt1_wat_lfl_percent_change: SCORE_F_ANS_WT_1_TBL_AGGR_LFL,
    sc.score_wt1_wat_lfl_percent_change_absolute: SCORE_ANS_WT_1_TBL_AGGR_LFL,
    sc.score_wt1_wat_lfl_percent_change_percent: SCORE_P_ANS_WT_1_TBL_AGGR_LFL,
    sc.score_wt1_wat_lfl_percent_change_max: SCORE_MAX_ANS_WT_1_TBL_AGGR_LFL,
    sc.score_wt1_wat_rec_ons: SCORE_F_ANS_WT_1_TBL_REC_DATA,
    sc.score_wt1_wat_rec_ons_absolute: SCORE_ANS_WT_1_TBL_REC_DATA,
    sc.score_wt1_wat_rec_ons_percent: SCORE_P_ANS_WT_1_TBL_REC_DATA,
    sc.score_wt1_wat_rec_ons_max: SCORE_MAX_ANS_WT_1_TBL_REC_DATA,
    sc.score_wt1_wat_rec_percent_change: SCORE_F_ANS_WT_1_TBL_REC_PERF_CHANGE,
    sc.score_wt1_wat_rec_percent_change_absolute: SCORE_ANS_WT_1_TBL_REC_PERF_CHANGE,
    sc.score_wt1_wat_rec_percent_change_percent: SCORE_P_ANS_WT_1_TBL_REC_PERF_CHANGE,
    sc.score_wt1_wat_rec_percent_change_max: SCORE_MAX_ANS_WT_1_TBL_REC_PERF_CHANGE,
    sc.score_wt1_wat_rec_performance: SCORE_F_ANS_WT_1_TBL_REC_PERF,
    sc.score_wt1_wat_rec_performance_absolute: SCORE_ANS_WT_1_TBL_REC_PERF,
    sc.score_wt1_wat_rec_performance_percent: SCORE_P_ANS_WT_1_TBL_REC_PERF,
    sc.score_wt1_wat_rec_performance_max: SCORE_MAX_ANS_WT_1_TBL_REC_PERF_CHANGE,
    sc.score_wt1_wat_rec: SCORE_F_ANS_WT_1_TBL_REC,
    sc.score_wt1_wat_rec_absolute: SCORE_ANS_WT_1_TBL_REC,
    sc.score_wt1_wat_rec_percent: SCORE_P_ANS_WT_1_TBL_REC,
    sc.score_wt1_wat_rec_max: SCORE_MAX_ANS_WT_1_TBL_REC,
    sc.score_bc1_1: SCORE_BC_1_1,
    sc.score_bc1_1_fraction: SCORE_F_BC_1_1,
    sc.score_bc1_1_percent: SCORE_P_BC_1_1,
    sc.score_bc1_1_max: SCORE_MAX_BC_1_1,
    sc.score_bc1_1_coverage: SCORE_F_BC_1_1_COV,
    sc.score_bc1_1_coverage_absolute: SCORE_BC_1_1_COV,
    sc.score_bc1_1_coverage_percent: SCORE_P_BC_1_1_COV,
    sc.score_bc1_1_coverage_max: SCORE_MAX_BC_1_1_COV,
    sc.score_bc1_2: SCORE_BC_1_2,
    sc.score_bc1_2_fraction: SCORE_F_BC_1_2,
    sc.score_bc1_2_percent: SCORE_P_BC_1_2,
    sc.score_bc1_2_max: SCORE_MAX_BC_1_2,
    sc.score_bc1_2_coverage: SCORE_F_BC_1_2_COV,
    sc.score_bc1_2_coverage_absolute: SCORE_BC_1_2_COV,
    sc.score_bc1_2_coverage_percent: SCORE_P_BC_1_2_COV,
    sc.score_bc1_2_coverage_max: SCORE_MAX_BC_1_2_COV,
    sc.score_bc1: SCORE_BC_1,
    sc.score_bc1_fraction: SCORE_F_BC_1,
    sc.score_bc1_percent: SCORE_P_BC_1,
    sc.score_bc1_max: SCORE_MAX_BC_1,
    sc.score_bc2: SCORE_BC_2,
    sc.score_bc2_fraction: SCORE_F_BC_2,
    sc.score_bc2_percent: SCORE_P_BC_2,
    sc.score_bc2_max: SCORE_MAX_BC_2,
    sc.score_bc2_coverage: SCORE_F_BC_2_COV,
    sc.score_bc2_coverage_absolute: SCORE_BC_2_COV,
    sc.score_bc2_coverage_percent: SCORE_P_BC_2_COV,
    sc.score_bc2_coverage_max: SCORE_MAX_BC_2_COV,
}
