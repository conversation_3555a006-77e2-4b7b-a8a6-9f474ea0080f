id = "id"
"""Internal ID unique for each benchmark group."""

location = "location"
"""Name of the group's location."""

property_type = "property_type"
"""Name of the group's property type."""

location_granularity = "location_granularity"
"""The level of granularity for this group's location, it can span from country to global."""

property_type_granularity = "property_type_granularity"
"""The level of granularity for this group's property type, it is one of property subtype, type, sector and 'all_property_types'."""
