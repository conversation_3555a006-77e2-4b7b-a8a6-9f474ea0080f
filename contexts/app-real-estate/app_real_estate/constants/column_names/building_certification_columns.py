import app_real_estate.constants.column_names.building_certification_table_common as bc_common
import app_real_estate.constants.column_names.building_certification_energy_rating_common_columns as bc_er_common
import app_real_estate.constants.column_names.key_columns as key_columns

snapshot_id = key_columns.snapshot_id
"""Internal ID for user data saves."""

company_fund_id = key_columns.company_fund_id
"""Internal GRESB fund ID."""

portfolio_asset_id = key_columns.portfolio_asset_id
"""Internal ID for each individual asset in the GRESB Portal database ("GRESB Asset ID" in the Asset Spreadsheet)."""

survey_year = "survey_year"
"""The year this data was reported."""

response_id = key_columns.response_id
"""Internal ID for each entity full response submission to a survey."""

building_data_certifications_id = "building_data_certifications_id"
"""Internal ID for each individual building certification, unique id for each certification and asset_id combination."""

building_data_certifications_scoring_id = "building_data_certifications_scoring_id"
"""Unique ID specifically created for scoring and benchmarking. Allows us to give an idea to mocked certifications without clashing with existing/future `building_data_certifications_id`."""

brand = bc_common.brand
"""Name of the certification brand."""

scheme = bc_common.scheme
"""Name of the scheme of the certification brand."""

name = bc_common.name
"""Concatenation of the certification brand and scheme separated by '/'."""

type = "type"
"""Type of building. Should be one of the values in /scoring-models/app_real_estate/constants/building_certification_type.py. """

outcome = "outcome"
"""Outcome of the certification. The grade follows each brand's own system."""

certification_id = bc_common.certification_id
"""Internal ID for each certification name."""

covered_floor_area = bc_er_common.covered_floor_area
"""Floor area covered by the certification."""

year = "year"
"""Year the certification was delivered."""

validation_status = "validation_status"
"""The validation status of the certification (different for each certification_id)."""

age = "age"
"""Age of the certification."""

covered_floor_area_m2 = bc_er_common.covered_floor_area_m2
"""Floor area coverage by the certification in square meters."""

asset_size = "asset_size"
"""The total floor area size of the building - without outdoor/exterior areas."""

asset_size_m2 = "asset_size_m2"
"""The total floor area size of the building in square meters - without outdoor/exterior areas."""

scoring_coverage = "scoring_coverage"
"""Percentage of the total floor area weighted by time factor, validation status and ownership percentage covered by the building certification."""

coverage = "coverage"
"""Percentage of the total floor area covered by the building certification."""

time_factor_weight = "time_factor_weight"
"""Calculated field containing the weight applied to each certification score based on its age."""

scoring_status = bc_common.scoring_status
"""Scoring status of the certification."""

scoring_covered_floor_area = "scoring_covered_floor_area"
"""Covered floor area weighted by time factor, validation status and ownership. Used for scoring in the case of aggregated-level scoring."""

owned_covered_floor_area = "owned_covered_floor_area"
"""Covered floor area weighted by ownership."""

asset_ownership = "asset_ownership"
"""Percentage of ownership of the asset the certification is for."""

indicator = "indicator"
"""Indicator for which the certification should be considered during scoring."""

# Variables for the aggregated data
bc1_1_coverage_scoring = "bc1_1_coverage_scoring"
"""Aggregated coverage weighted by ownership percentage, validation status and time factor for non-operational certifications."""

bc1_2_coverage_scoring = "bc1_2_coverage_scoring"
"""Aggregated coverage weighted by ownership percentage, validation status and time factor for operational certifications."""

bc1_1_coverage = "bc1_1_coverage"
"""Aggregated coverage for non-operational certifications."""

bc1_2_coverage = "bc1_2_coverage"
"""Aggregated coverage for operational certifications."""

unique_asset_count = "unique_asset_count"
"""Number of assets in the bucket."""
