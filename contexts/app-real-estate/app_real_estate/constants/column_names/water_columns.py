wat_days_data_avail = "wat_days_data_avail"
"""Number of days that the water data of the asset is available."""

wat_max_days_data_avail = "wat_max_days_data_avail"
"""Maximum number of days that the water data of the asset was available."""

wat_abs = "wat_abs"
"""Absolute water consumption of the asset in m3."""

wat_abs_ly = "wat_abs_ly"
"""Absolute previous year water consumption of the asset in m3."""

wat_abs_in = "wat_abs_in"
"""Indoor absolute water consumption of the asset."""

wat_int_m3_m2 = "wat_int_m3_m2"
"""Water consumption intensity in terms of cube meters per square meters."""

wat_area_p_lc = "wat_area_p_lc"
"""Percentage of the asset's floor area controlled by landlord."""

wat_area_p_tc = "wat_area_p_tc"
"""Percentage of the asset's floor area controlled by tenant."""

wat_tot_w = "wat_tot_w"
"""Maximum floor area involved in the whole building reporting of the water consumption."""

wat_tot_lc_bs_m2 = "wat_tot_lc_bs_m2"
"""Maximum floor area involved in the base building shared services reporting of the water consumption."""

wat_tot_lc_bc_m2 = "wat_tot_lc_bc_m2"
"""Maximum floor area involved in the base building common areas reporting of the water consumption."""

wat_tot_lc_t = "wat_tot_lc_t"
"""Maximum floor area involved in the landlord controlled tenant spaces reporting of the water consumption."""

wat_tot_tc_t = "wat_tot_tc_t"
"""Maximum floor area involved in the tenant controlled tenant spaces reporting of the water consumption."""

wat_area_cov_p = "wat_area_cov_p"
"""Percentage of floor area covered by the reported water data."""

wat_area_cov_p_lc = "wat_area_cov_p_lc"
"""Percentage of landlord controlled floor area covered by the reported water data."""

wat_area_cov_p_tc = "wat_area_cov_p_tc"
"""Percentage of tenant controlled floor area covered by the reported water data."""

wat_lfl_area_cov_p_lc = "wat_lfl_area_cov_p_lc"
"""Percentage of landlord controlled floor area covered by the reported water data filtered for LFL."""

wat_lfl_area_cov_p_tc = "wat_lfl_area_cov_p_tc"
"""Percentage of tenant controlled floor area covered by the reported water data filtered for LFL."""

wat_time_cov_p = "wat_time_cov_p"
"""Percentage of the total reporting period covered by the reported water data."""

wat_time_cov_p_lc = "wat_time_cov_p_lc"
"""Percentage of the total reporting period covered by the reported water data on landlord controlled area."""

wat_time_cov_p_tc = "wat_time_cov_p_tc"
"""Percentage of the total reporting period covered by the reported water data on tenant controlled area."""

wat_area_time_cov_p = "wat_area_time_cov_p"
"""Percentage of area/time based coverage by the reported water data."""

wat_area_time_cov_p_lc = "wat_area_time_cov_p_lc"
"""Percentage of area/time based coverage by the reported water data on landlord controlled area."""

wat_area_time_cov_p_tc = "wat_area_time_cov_p_tc"
"""Percentage of area/time based coverage by the reported water data on tenant controlled area."""

wat_lfl_area_cov_p = "wat_lfl_area_cov_p"
"""Percentage of area covered by the reported water LFL data."""

wat_area_weight_lc = "wat_area_weight_lc"
"""Weight of the area based water data coverage for the landlord controlled area."""

wat_area_weight_tc = "wat_area_weight_tc"
"""Weight of the area based water data coverage for the tenant controlled area."""

wat_area_time_weight_lc = "wat_area_time_weight_lc"
"""Weight of the area/time based water data coverage for the landlord controlled area."""

wat_area_time_weight_tc = "wat_area_time_weight_tc"
"""Weight of the area/time based water data coverage for the tenant controlled area."""

wat_lfl_p = "wat_lfl_p"
"""Like-For-Like percent change of water consumption. Value between [-100;Inf]."""

wat_lfl_abs = "wat_lfl_abs"
"""Absolute water consumption filtered for LFL calculation."""

wat_lfl_abs_change = "wat_lfl_abs_change"
"""Absolute change in water consumption from previous to curreny year."""

wat_lfl_abs_change_agg = "wat_lfl_abs_change_agg"
"""Absolute change in water consumption from previous to current year in kWh filtered for benchmark calculation at the aggregated levels."""

wat_lfl_abs_change_accepted = "wat_lfl_abs_change_accepted"
"""Absolute change in water consumption from previous to current year in kWh filtered for aggregation."""

wat_lfl_abs_change_lc_agg = "wat_lfl_abs_change_lc_agg"
"""Absolute change in water consumption in landlord controlled spaces from previous to current year in kWh filtered for benchmark calculation at the aggregated levels."""

wat_lfl_abs_change_lc_accepted = "wat_lfl_abs_change_lc_accepted"
"""Absolute change in water consumption in landlord controlled spaces from previous to current year in kWh filtered for aggregation."""

wat_lfl_abs_change_tc_agg = "wat_lfl_abs_change_tc_agg"
"""Absolute change in water consumption in tenant controlled spaces from previous to current year in kWh filtered for benchmark calculation at the aggregated levels."""

wat_lfl_abs_change_tc_accepted = "wat_lfl_abs_change_tc_accepted"
"""Absolute change in water consumption in tenant controlled spaces from previous to current year in kWh filtered for aggregation."""

wat_lfl_abs_in = "wat_lfl_abs_in"
"""Absolute indoor water consumption filtered for LFL calculation."""

wat_lfl_abs_lc_bs = "wat_lfl_abs_lc_bs"
"""Absolute water consumption in base building shared services filtered for LFL calculation."""

wat_lfl_abs_lc_bc = "wat_lfl_abs_lc_bc"
"""Absolute water consumption in base building common areas filtered for LFL calculation."""

wat_lfl_abs_lc_t = "wat_lfl_abs_lc_t"
"""Absolute water consumption in landlord controlled tenant spaces filtered for LFL calculation."""

wat_lfl_abs_lc_o = "wat_lfl_abs_lc_o"
"""Absolute water consumption in landlord controlled outdoor / exterior / parking areas filtered for LFL calculation."""

wat_lfl_abs_tc_t = "wat_lfl_abs_tc_t"
"""Absolute water consumption in tenant controlled tenant spaces filtered for LFL calculation."""

wat_lfl_abs_tc_o = "wat_lfl_abs_tc_o"
"""Absolute water consumption in tenant controlled outdoor / exterior / parking areas filtered for LFL calculation."""

wat_lfl_abs_lc = "wat_lfl_abs_lc"
"""Absolute water consumption in landlord controlled areas filtered for LFL calculation."""

wat_lfl_abs_tc = "wat_lfl_abs_tc"
"""Absolute water consumption in tenant controlled areas filtered for LFL calculation."""

wat_lfl_percent_change_lc = "wat_lfl_percent_change_lc"
"""Percent change in absolute water consumption in landlord controlled areas relative to the previous year (Like-For-Like)."""

wat_lfl_percent_change_tc = "wat_lfl_percent_change_tc"
"""Percent change in absolute water consumption in tenant controlled areas relative to the previous year (Like-For-Like)."""

wat_lfl_abs_lc_bc_ly = "wat_lfl_abs_lc_bc_ly"
"""Absolute water consumption in the previous year in common areas filtered for LFL calculation."""

wat_lfl_abs_lc_bs_ly = "wat_lfl_abs_lc_bs_ly"
"""Absolute water consumption in the previous year in shared services filtered for LFL calculation."""

wat_lfl_abs_lc_t_ly = "wat_lfl_abs_lc_t_ly"
"""Absolute water consumption in the previous year in landlord controlled tenant spaces filtered for LFL calculation."""

wat_lfl_abs_tc_t_ly = "wat_lfl_abs_tc_t_ly"
"""Absolute water consumption in the previous year in tenant controlled tenant spaces filtered for LFL calculation."""

wat_lfl_abs_lc_o_ly = "wat_lfl_abs_lc_o_ly"
"""Absolute water consumption in the previous year in landlord controlled outdoor areas filtered for LFL calculation."""

wat_lfl_abs_tc_o_ly = "wat_lfl_abs_tc_o_ly"
"""Absolute water consumption in the previous year in tenant controlled outdoor areas filtered for LFL calculation."""

wat_lfl_abs_ly = "wat_lfl_abs_ly"
"""Absolute water consumption in the previous filtered for LFL calculation."""

wat_lfl_abs_in_ly = "wat_lfl_abs_in_ly"
"""Absolute indoor water consumption in the previous filtered for LFL calculation."""

wat_lfl_abs_lc_ly = "wat_lfl_abs_lc_ly"
"""Absolute water consumption in the previous year in landlord controlled areas filtered for LFL calculation."""

wat_lfl_abs_tc_ly = "wat_lfl_abs_tc_ly"
"""Absolute water consumption in the previous year in tenant controlled areas filtered for LFL calculation."""

wat_rec_abs = "wat_rec_abs"
"""Absolute consumption of recycled and reused water in m3."""

wat_rec_abs_ly = "wat_rec_abs_ly"
"""Absolute consumption of recycled and reused water in m3."""

wat_rec_ons_reu = "wat_rec_ons_reu"
"""Absolute consumption of reused water on-site in m3."""

wat_rec_ons_cap = "wat_rec_ons_cap"
"""Absolute consumption of captured water on-site in m3."""

wat_rec_ons_ext = "wat_rec_ons_ext"
"""Absolute consumption of extracted water on-site in m3."""

wat_rec_ons = "wat_rec_ons"
"""Absolute consumption of reused/captured/extracted water on-side in m3."""

wat_rec_ofs_pur = "wat_rec_ofs_pur"
"""Absolute consumption of purchased water off-site in m3."""

wat_rec_rate = "wat_rec_rate"
"""Calculated recycled/reused water as a percentage of total water consumption."""

wat_rec_rate_ly = "wat_rec_rate_ly"
"""Calculated previous year recycled/reused water as a percentage of total water consumption."""

wat_rec_ons_reu_rate = "wat_rec_ons_reu_rate"
"""Calculated reused water on-site as a percentage of total recycled water."""

wat_rec_ons_cap_rate = "wat_rec_ons_cap_rate"
"""Calculated captured water on-site as a percentage of total recycled water."""

wat_rec_ons_ext_rate = "wat_rec_ons_ext_rate"
"""Calculated extracted water on-site as a percentage of total recycled water."""

wat_rec_ofs_pur_rate = "wat_rec_ofs_pur_rate"
"""Calculated purchase water off-site as a percentage of total recycled water."""

wat_rec_percent_change = "wat_rec_percent_change"
"""Calculated field containing the percent change of recycled and reused water consumed from the previous year to the data year. Value between [-100;100]."""

wat_scored_int_m3_m2 = "wat_scored_int_m3_m2"
"""Calculated water intensity for assets eligible to intensity scoring in m3 per m2."""

wat_scored_int_m3_sqft = "wat_scored_int_m3_sqft"
"""Calculated water intensity for assets eligible to intensity scoring in m3 per sqft."""

wat_scored_int_dm3_m2 = "wat_scored_int_dm3_m2"
"""Calculated water intensity for assets eligible to intensity scoring in dm3 per m2."""

wat_scored_int_dm3_sqft = "wat_scored_int_dm3_sqft"
"""Calculated water intensity for assets eligible to intensity scoring in dm3 per sqft."""

wat_int_outlier_status = "wat_int_outlier_status"
"""Outlier status of the asset's water intensity value, can be either "none", "accepted" or "rejected"."""

wat_lfl_outlier_status = "wat_lfl_outlier_status"
"""Outlier status of the asset's water Like-For-Like value, can be either "none", "accepted" or "rejected"."""

asset_vacancy_water_intensity = "asset_vacancy_water_intensity"
"""Asset vacancy rate for assets eligible for water intensity scoring (75% coverage)."""

asset_size_water_intensity_m2 = "asset_size_water_intensity_m2"
"""Calculated total asset floor area in square meters for assets eligible for water intensity scoring (75% coverage)."""

asset_size_water_intensity_sqft = "asset_size_water_intensity_sqft"
"""Calculated total asset floor area in square feet for assets eligible for water intensity scoring (75% coverage)."""

floor_area_percent_water_intensity = "floor_area_percent_water_intensity"
"""Calculated total floor area in square meters for assets eligible for water intensity scoring (75% coverage) as a percentage of the total floor area of the group."""

wat_scored_int_m3_m2_accepted = "wat_scored_int_m3_m2_accepted"
"""The asset's water intensity value without hard outliers in m3/m2."""

wat_scored_int_m3_sqft_accepted = "wat_scored_int_m3_sqft_accepted"
"""The asset's water intensity value without hard outliers in m3/sqft."""

wat_scored_int_dm3_m2_accepted = "wat_scored_int_dm3_m2_accepted"
"""The asset's water intensity value without hard outliers in dm3/m2."""

wat_scored_int_dm3_sqft_accepted = "wat_scored_int_dm3_sqft_accepted"
"""The asset's water intensity value without hard outliers in dm3/sqft."""

wat_lfl_p_accepted = "wat_lfl_p_accepted"
"""The asset's water LFL percent change value without hard outliers."""

wat_lfl_abs_accepted = "wat_lfl_abs_accepted"
"""Absolute water consumption filtered for LFL metric aggregation."""

wat_lfl_abs_ly_accepted = "wat_lfl_abs_ly_accepted"
"""Previous year absolute water consumption filtered for LFL metric aggregation."""

wat_lfl_abs_lc_ly_accepted = "wat_lfl_abs_lc_ly_accepted"
"""Absolute water consumption in the previous year in landlord controlled areas filtered for LFL aggregation."""

wat_lfl_abs_tc_ly_accepted = "wat_lfl_abs_tc_ly_accepted"
"""Absolute water consumption in the previous year in tenant controlled areas filtered for LFL aggregation."""

wat_lfl_abs_lc_ly_bench = "wat_lfl_abs_lc_ly_bench"
"""Absolute water consumption in the previous year in landlord controlled areas filtered for LFL metric reporting benchmark creation."""

wat_lfl_abs_tc_ly_bench = "wat_lfl_abs_tc_ly_bench"
"""Absolute water consumption in the previous year in tenant controlled areas filtered for LFL metric reporting benchmark creation."""

wat_area_time_cov_p_lc_agg = "wat_area_time_cov_p_lc_agg"
"""Area-time water data coverage weighted by the area-time weight for the landlord controlled areas."""

wat_area_time_cov_p_tc_agg = "wat_area_time_cov_p_tc_agg"
"""Area-time water data coverage weighted by the area-time weight for the tenant controlled areas."""

wat_lfl_percent_change_lc_bench = "wat_lfl_percent_change_lc_bench"
"""Water LFL percent change weighted by the area-time weight, filtered for aggregation and for reporting benchmark creation, for the landlord controlled areas."""

wat_lfl_percent_change_tc_bench = "wat_lfl_percent_change_tc_bench"
"""Water LFL percent change weighted by the area-time weight, filtered for aggregation and for reporting benchmark creation, for the tenant controlled areas."""

wat_lfl_percent_change_lc_accepted = "wat_lfl_percent_change_lc_accepted"
"""Water LFL percent change weighted by the area-time weight, filtered for aggregation, for the landlord controlled areas."""

wat_lfl_percent_change_tc_accepted = "wat_lfl_percent_change_tc_accepted"
"""Water LFL percent change weighted by the area-time weight, filtered for aggregation, for the tenant controlled areas."""

wat_int_eligible = "wat_int_eligible"
"""Whether the asset was eligible to have its water consumption intensity calculated."""

wat_lfl_eligible_for_aggregation = "wat_lfl_eligible_for_aggregation"
"""Whether the asset is eligible for water LFL aggregation."""

wat_lfl_area_m2 = "wat_lfl_area_m2"
"""Floor area in m2 (weighted by ownership) on which the water LFL data is reported."""

wat_lfl_area_m2_lc = "wat_lfl_area_m2_lc"
"""Landlord controlled floor area in m2 (weighted by ownership) on which the water LFL data is reported."""

wat_lfl_area_m2_tc = "wat_lfl_area_m2_tc"
"""Tenant controlled floor area in m2 (weighted by ownership) on which the water LFL data is reported."""

wat_lfl_area_sqft = "wat_lfl_area_sqft"
"""Floor area in sqft (weighted by ownership) on which the water LFL data is reported."""

wat_lfl_area_sqft_lc = "wat_lfl_area_sqft_lc"
"""Landlord controlled floor area in sqft (weighted by ownership) on which the water LFL data is reported."""

wat_lfl_area_sqft_tc = "wat_lfl_area_sqft_tc"
"""Tenant controlled floor area in sqft (weighted by ownership) on which the water LFL data is reported."""

wat_lfl_area = "wat_lfl_area"
"""Floor area (weighted by ownership) on which the water LFL data is reported"""

wat_lfl_area_lc = "wat_lfl_area_lc"
"""Landlord controlled floor area (weighted by ownership) on which the water LFL data is reported"""

wat_lfl_area_tc = "wat_lfl_area_tc"
"""Tenant controlled floor area (weighted by ownership) on which the water LFL data is reported"""

wat_lfl_area_p = "wat_lfl_area_p"
wat_lfl_area_p_lc = "wat_lfl_area_p_lc"
wat_lfl_area_p_tc = "wat_lfl_area_p_tc"

"""Aggregated benchmarks of the metrics"""
wat_int_m3_m2_agg_benchmark = "wat_int_m3_m2_agg_benchmark"
wat_area_time_cov_p_lc_agg_benchmark = "wat_area_time_cov_p_lc_agg_benchmark"
wat_area_time_cov_p_tc_agg_benchmark = "wat_area_time_cov_p_tc_agg_benchmark"
wat_lfl_percent_change_lc_benchmark = "wat_lfl_percent_change_lc_benchmark"
wat_lfl_percent_change_tc_benchmark = "wat_lfl_percent_change_tc_benchmark"
wat_rec_rate_agg_benchmark = "wat_rec_rate_agg_benchmark"
wat_rec_rate_ly_agg_benchmark = "wat_rec_rate_ly_agg_benchmark"
wat_rec_ons_reu_rate_benchmark = "wat_rec_ons_reu_rate_benchmark"
wat_rec_ons_cap_rate_benchmark = "wat_rec_ons_cap_rate_benchmark"
wat_rec_ons_ext_rate_benchmark = "wat_rec_ons_ext_rate_benchmark"
wat_rec_ofs_pur_rate_benchmark = "wat_rec_ofs_pur_rate_benchmark"

# WIP: columns for the new framework of metric aggregation
wat_lfl_abs_change_lc = "wat_lfl_abs_change_lc"
wat_lfl_abs_change_tc = "wat_lfl_abs_change_tc"
wat_lfl_asset_count = "wat_lfl_asset_count"
