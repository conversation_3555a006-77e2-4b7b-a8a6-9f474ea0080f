import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.building_certification_columns as bc

id = "id"
"""Internal ID unique for each benchmark group membership."""

portfolio_asset_id = ac.portfolio_asset_id
"""The ID of the asset this membership is for. Present if the memberships were created for asset data."""

building_data_certifications_scoring_id = bc.building_data_certifications_scoring_id
"""The ID of the asset this membership is for. Present if the memberships were created for certification data."""

group_id = "group_id"
"""The ID of the benchmark group this membership is attached to."""

is_active = "is_active"
"""Flag describing whether this membership is between an asset/certification and its active group (most granular group)."""

is_benchmarkable = "is_benchmarkable"
"""Flag describing whether the row is to be included in the benchmarking process."""

bench_en_area_time_cov_p_lc = "bench_en_area_time_cov_p_lc"
"""Benchmark value for the energy area-time data coverage percentage on landlord controlled areas."""

bench_en_area_time_cov_p_tc = "bench_en_area_time_cov_p_tc"
"""Benchmark value for the energy area-time data coverage percentage on tenant controlled areas."""

bench_wat_area_time_cov_p_lc = "bench_wat_area_time_cov_p_lc"
"""Benchmark value for the water area-time data coverage percentage on landlord controlled areas."""

bench_wat_area_time_cov_p_tc = "bench_wat_area_time_cov_p_tc"
"""Benchmark value for the water area-time data coverage percentage on tenant controlled areas."""

bench_ghg_area_time_cov_p_s12 = "bench_ghg_area_time_cov_p_s12"
"""Benchmark value for the GHG area-time data coverage percentage for scopes 1 and 2."""

bench_ghg_area_time_cov_p_s3 = "bench_ghg_area_time_cov_p_s3"
"""Benchmark value for the GHG area-time data coverage percentage for scope 3."""

bench_was_area_cov_p_lc = "bench_was_area_cov_p_lc"
"""Benchmark value for the waste area data coverage percentage on landlord controlled areas."""

bench_was_area_cov_p_tc = "bench_was_area_cov_p_tc"
"""Benchmark value for the waste area data coverage percentage on tenant controlled areas."""

mean_en_area_time_cov_p_lc = "mean_en_area_time_cov_p_lc"
"""Group benchmark metric (mean) for the energy area-time data coverage percentage on landlord controlled areas."""

mean_en_area_time_cov_p_tc = "mean_en_area_time_cov_p_tc"
"""Group benchmark metric (mean) for the energy area-time data coverage percentage on tenant controlled areas."""

mean_wat_area_time_cov_p_lc = "mean_wat_area_time_cov_p_lc"
"""Group benchmark metric (mean) for the water area-time data coverage percentage on landlord controlled areas."""

mean_wat_area_time_cov_p_tc = "mean_wat_area_time_cov_p_tc"
"""Group benchmark metric (mean) for the water area-time data coverage percentage on tenant controlled areas."""

mean_ghg_area_time_cov_p_s12 = "mean_ghg_area_time_cov_p_s12"
"""Group benchmark metric (mean) for the GHG area-time data coverage percentage for scopes 1 and 2."""

mean_ghg_area_time_cov_p_s3 = "mean_ghg_area_time_cov_p_s3"
"""Group benchmark metric (mean) for the GHG area-time data coverage percentage for scope 3."""

mean_was_area_cov_p_lc = "mean_was_area_cov_p_lc"
"""Group benchmark metric (mean) for the waste area data coverage percentage on landlord controlled areas."""

mean_was_area_cov_p_tc = "mean_was_area_cov_p_tc"
"""Group benchmark metric (mean) for the waste area data coverage percentage on tenant controlled areas."""

bench_was_diverted_percent = "bench_was_diverted_percent"
"""Benchmark value for the diverted waste percentage."""

mean_was_diverted_percent = "mean_was_diverted_percent"
"""Group benchmark metric (mean) for the diverted waste percentage."""

bench_certification_coverage = "bench_certification_coverage"
"""Benchmark value for the building certification certified floor coverage."""

mean_certification_coverage = "mean_certification_coverage"
"""Group benchmark metric (mean) for the building certification certified floor coverage."""

bench_energy_rating_coverage = "bench_energy_rating_coverage"
"""Benchmark value for the energy rating certified floor coverage."""

mean_energy_rating_coverage = "mean_energy_rating_coverage"
"""Benchmark metric (mean) for the energy rating certified floor coverage."""

bench_en_efficiency_intensity = "bench_en_efficiency_intensity"
"""Benchmark value for the energy efficiency intensity."""

rank_en_efficiency_intensity = "rank_en_efficiency_intensity"
"""Benchmark metric (rank of the asset) for the energy efficiency intensity."""

percentile_10th_en_efficiency_intensity = "percentile_10th_en_efficiency_intensity"
"""10th percentile of the energy intensity values of the benchmark group."""

percentile_90th_en_efficiency_intensity = "percentile_90th_en_efficiency_intensity"
"""90th percentile of the energy intensity values of the benchmark group."""

bench_wat_intensity = "bench_wat_intensity"
"""Benchmark value for the water intensity."""

rank_wat_intensity = "rank_wat_intensity"
"""Benchmark metric (rank of the asset) for the water intensity."""

percentile_10th_wat_efficiency_intensity = "percentile_10th_wat_efficiency_intensity"
"""10th percentile of the water intensity value of the benchmark group."""

percentile_90th_wat_efficiency_intensity = "percentile_90th_wat_efficiency_intensity"
"""90th percentile of the water intensity value of the benchmark group."""

bench_ghg_intensity = "bench_ghg_intensity"
"""Benchmark value for the GHG intensity."""

rank_ghg_intensity = "rank_ghg_intensity"
"""Benchmark metric (rank of the asset) for the GHG intensity."""

percentile_10th_ghg_efficiency_intensity = "percentile_10th_ghg_efficiency_intensity"
"""10th percentile of the GHG intensity value of the benchmark group."""

percentile_90th_ghg_efficiency_intensity = "percentile_90th_ghg_efficiency_intensity"
"""90th percentile of the GHG intensity value of the benchmark group."""

bench_en_lfl_percent_change_lc = "bench_en_lfl_percent_change_lc"
"""Benchmark value for the energy Like-For-Like percent change value for landlord controlled areas."""

bench_en_lfl_percent_change_tc = "bench_en_lfl_percent_change_tc"
"""Benchmark value for the energy Like-For-Like percent change value for tenant controlled areas."""

bench_ghg_lfl_percent_change_s12 = "bench_ghg_lfl_percent_change_s12"
"""Benchmark value for the GHG Like-For-Like percent change value for scopes 1 and 2."""

bench_ghg_lfl_percent_change_s3 = "bench_ghg_lfl_percent_change_s3"
"""Benchmark value for the GHG Like-For-Like percent change value for scope 3."""

bench_wat_lfl_percent_change_lc = "bench_wat_lfl_percent_change_lc"
"""Benchmark value for the water Like-For-Like percent change value for landlord controlled areas."""

bench_wat_lfl_percent_change_tc = "bench_wat_lfl_percent_change_tc"
"""Benchmark value for the water Like-For-Like percent change value for tenant controlled areas."""

percentile_75_en_lfl_percent_change_lc = "percentile_75_en_lfl_percent_change_lc"
"""Benchmark group 75th percentile for the energy Like-For-Like percent change value for landlord controlled areas."""

percentile_75_en_lfl_percent_change_tc = "percentile_75_en_lfl_percent_change_tc"
"""Benchmark group 75th percentile for the energy Like-For-Like percent change value for tenant controlled areas."""

percentile_75_ghg_lfl_percent_change_s12 = "percentile_75_ghg_lfl_percent_change_s12"
"""Benchmark group 75th percentile for the GHG Like-For-Like percent change value for scopes 1 and 2."""

percentile_75_ghg_lfl_percent_change_s3 = "percentile_75_ghg_lfl_percent_change_s3"
"""Benchmark group 75th percentile for the GHG Like-For-Like percent change value for scope 3."""

percentile_75_wat_lfl_percent_change_lc = "percentile_75_wat_lfl_percent_change_lc"
"""Benchmark group 75th percentile for the water Like-For-Like percent change value for landlord controlled areas."""

percentile_75_wat_lfl_percent_change_tc = "percentile_75_wat_lfl_percent_change_tc"
"""Benchmark group 75th percentile for the water Like-For-Like percent change value for tenant controlled areas."""

mean_en_lfl_percent_change_lc = "mean_en_lfl_percent_change_lc"
"""Group benchmark metric (mean) for the energy Like-For-Like percent change value for landlord controlled areas."""

mean_en_lfl_percent_change_tc = "mean_en_lfl_percent_change_tc"
"""Group benchmark metric (mean) for the energy Like-For-Like percent change value for tenant controlled areas."""

mean_ghg_lfl_percent_change_s12 = "mean_ghg_lfl_percent_change_s12"
"""Group benchmark metric (mean) for the GHG Like-For-Like percent change value for scopes 1 and 2."""

mean_ghg_lfl_percent_change_s3 = "mean_ghg_lfl_percent_change_s3"
"""Group benchmark metric (mean) for the GHG Like-For-Like percent change value for scope 3."""

mean_wat_lfl_percent_change_lc = "mean_wat_lfl_percent_change_lc"
"""Group benchmark metric (mean) for the water Like-For-Like percent change value for landlord controlled areas."""

mean_wat_lfl_percent_change_tc = "mean_wat_lfl_percent_change_tc"
"""Group benchmark metric (mean) for the water Like-For-Like percent change value for tenant controlled areas."""

bench_en_ren_percent_change = "bench_en_ren_percent_change"
"""Benchmark value for the renewable energy percent change value."""

bench_wat_rec_percent_change = "bench_wat_rec_percent_change"
"""Benchmark value for the recycled/reused water percent change value."""

percentile_75_en_ren_percent_change = "percentile_75_en_ren_percent_change"
"""Benchmark group 75th percentile for the renewable energy percent change value."""

percentile_75_wat_rec_percent_change = "percentile_75_wat_rec_percent_change"
"""Benchmark group 75th percentile for the recycled/reused water percent change value."""

mean_en_ren_percent_change = "mean_en_ren_percent_change"
"""Group benchmark metric (mean) for the renewable energy percent change value."""

mean_wat_rec_percent_change = "mean_wat_rec_percent_change"
"""Group benchmark metric (mean) for the recycled/reused water percent change value."""
