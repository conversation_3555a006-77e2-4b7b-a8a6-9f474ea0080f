ghg_days_data_avail = "ghg_days_data_avail"
"""Number of days that the GHG data of the asset is available."""

ghg_max_days_data_avail = "ghg_max_days_data_avail"
"""Maximum number of days that the GHG data of the asset was available."""

ghg_abs = "ghg_abs"
"""Absolute GHG emissions of the asset."""

ghg_abs_net = "ghg_abs_net"
"""Net GHG emissions of the asset."""

ghg_abs_ly = "ghg_abs_ly"
"""Absolute previous year GHG emissions of the asset."""

ghg_abs_s1 = "ghg_abs_s1"
"""Absolute scope 1 GHG emissions of the asset."""

ghg_abs_s2_mb = "ghg_abs_s2_mb"
"""Absolute scope 2 market-based GHG emissions of the asset."""

ghg_abs_s2_lb = "ghg_abs_s2_lb"
"""Absolute scope 2 location-based GHG emissions of the asset."""

ghg_abs_s3 = "ghg_abs_s3"
"""Absolute scope 3 GHG emissions of the asset."""

ghg_abs_s1_w = "ghg_abs_s1_w"
"""Absolute scope 1 GHG emissions of the asset indoor."""

ghg_abs_s1_o = "ghg_abs_s1_o"
"""Absolute scope 1 GHG emissions of the asset oudoor."""

ghg_abs_s2_lb_w = "ghg_abs_s2_lb_w"
"""Absolute scope 2 market-based GHG emissions of the asset indoor."""

ghg_abs_s2_lb_o = "ghg_abs_s2_lb_o"
"""Absolute scope 2 market-based GHG emissions of the asset outdoor."""

ghg_abs_s2_mb_w = "ghg_abs_s2_mb_w"
"""Absolute scope 2 location-based GHG emissions of the asset indoor."""

ghg_abs_s2_mb_o = "ghg_abs_s2_mb_o"
"""Absolute scope 2 location-based GHG emissions of the asset outdoor."""

ghg_abs_s3_w = "ghg_abs_s3_w"
"""Absolute scope 3 GHG emissions of the asset indoor."""

ghg_abs_s3_o = "ghg_abs_s3_o"
"""Absolute scope 3 GHG emissions of the asset outdoor."""

ghg_abs_in = "ghg_abs_in"
"""Indoor absolute GHG emissions of the asset."""

ghg_abs_offset = "ghg_abs_offset"
"""GHG offsets purchased in tons."""

ghg_int_ton_m2 = "ghg_int_ton_m2"
"""GHG emission intensity in terms of ton per square meters."""

ghg_area_p_s12 = "ghg_area_p_s12"
"""Percentage of the asset's floor area with scopes 1 and 2 GHG emissions."""

ghg_area_p_s3 = "ghg_area_p_s3"
"""Percentage of the asset's floor area with scope 3 GHG emissions."""

ghg_area_cov_p = "ghg_area_cov_p"
"""Percentage of floor area covered by the reported GHG data."""

ghg_area_cov_p_s12 = "ghg_area_cov_p_s12"
"""Percentage of landlord controlled floor area covered by the reported GHG data."""

ghg_area_cov_p_s3 = "ghg_area_cov_p_s3"
"""Percentage of tenant controlled floor area covered by the reported GHG data."""

ghg_lfl_area_cov_p_s12 = "ghg_lfl_area_cov_p_s12"
"""Percentage of landlord controlled floor area covered by the reported GHG data filtered for LFL."""

ghg_lfl_area_cov_p_s3 = "ghg_lfl_area_cov_p_s3"
"""Percentage of tenant controlled floor area covered by the reported GHG data filtered for LFL."""

ghg_time_cov_p = "ghg_time_cov_p"
"""Percentage of the total reporting period covered by the reported GHG data."""

ghg_time_cov_p_s12 = "ghg_time_cov_p_s12"
"""Percentage of the total reporting period covered by the reported GHG data on scopes 1 and 2 emissions."""

ghg_time_cov_p_s3 = "ghg_time_cov_p_s3"
"""Percentage of the total reporting period covered by the reported GHG data on scope 3 emissions."""

ghg_area_time_cov_p = "ghg_area_time_cov_p"
"""Percentage of area/time based coverage by the reported GHG data."""

ghg_area_time_cov_p_s12 = "ghg_area_time_cov_p_s12"
"""Percentage of area/time based coverage by the reported GHG data on scopes 1 and 2 emissions."""

ghg_area_time_cov_p_s3 = "ghg_area_time_cov_p_s3"
"""Percentage of area/time based coverage by the reported GHG data on scope 3 emissions."""

ghg_lfl_area_cov_p = "ghg_lfl_area_cov_p"
"""Percentage of area covered by the reported GHG LFL data."""

ghg_area_weight_s12 = "ghg_area_weight_s12"
"""Weight of the area based GHG data coverage for the scopes 1 and 2 emissions."""

ghg_area_weight_s3 = "ghg_area_weight_s3"
"""Weight of the area based GHG data coverage for the scope 3 emissions."""

ghg_area_time_weight_s12 = "ghg_area_time_weight_s12"
"""Weight of the area/time based GHG data coverage for the scopes 1 and 2 emissions."""

ghg_area_time_weight_s3 = "ghg_area_time_weight_s3"
"""Weight of the area/time based GHG data coverage for the scope 3 emissions."""

ghg_lfl_p = "ghg_lfl_p"
"""Like-For-Like percent change of GHG emission. Value between [-100;Inf]."""

ghg_lfl_abs = "ghg_lfl_abs"
"""Absolute GHG emissions filtered for LFL calculation."""

ghg_lfl_abs_ly = "ghg_lfl_abs_ly"
"""Absolute previous year GHG emissions filtered for LFL calculation."""

ghg_lfl_abs_change = "ghg_lfl_abs_change"
"""Absolute change in GHG emissions from previous to current year."""

ghg_lfl_abs_change_agg = "ghg_lfl_abs_change_agg"
"""Absolute change in GHG emissions from previous to current year in kWh filtered for benchmark calculation at the aggregated levels."""

ghg_lfl_abs_change_accepted = "ghg_lfl_abs_change_accepted"
"""Absolute change in GHG emissions from previous to current year in kWh filtered for aggregation."""

ghg_lfl_abs_change_s12_agg = "ghg_lfl_abs_change_s12_agg"
"""Absolute change in scope 1 and 2 GHG emissions from previous to current year in kWh filtered for benchmark calculation at the aggregated levels."""

ghg_lfl_abs_change_s12_accepted = "ghg_lfl_abs_change_s12_accepted"
"""Absolute change in scope 1 and 2 GHG emissions from previous to current year in kWh filtered for aggregation."""

ghg_lfl_abs_change_s3_agg = "ghg_lfl_abs_change_s3_agg"
"""Absolute change in scope 3 GHG emissions from previous to current year in kWh filtered for benchmark calculation at the aggregated levels."""

ghg_lfl_abs_change_s3_accepted = "ghg_lfl_abs_change_s3_accepted"
"""Absolute change in scope 3 GHG emissions from previous to current year in kWh filtered for aggregation."""


ghg_lfl_abs_s1_w = "ghg_lfl_abs_s1_w"
"""Absolute scope 1 GHG emissions in the whole building filtered for LFL calculation."""

ghg_lfl_abs_s1_o = "ghg_lfl_abs_s1_o"
"""Absolute scope 1 GHG emissions in the outdoor areas filtered for LFL calculation."""

ghg_lfl_abs_s2_lb_w = "ghg_lfl_abs_s2_lb_w"
"""Absolute location based scope 2 GHG emissions in the whole building filtered for LFL calculation."""

ghg_lfl_abs_s2_lb_o = "ghg_lfl_abs_s2_lb_o"
"""Absolute location based scope 2 GHG emissions in the outdoor areas filtered for LFL calculation."""

ghg_lfl_abs_s2_mb_w = "ghg_lfl_abs_s2_mb_w"
"""Absolute market based scope 2 GHG emissions in the whole building filtered for LFL calculation."""

ghg_lfl_abs_s2_mb_o = "ghg_lfl_abs_s2_mb_o"
"""Absolute market based scope 2 GHG emissions in the outdoor areas filtered for LFL calculation."""

ghg_lfl_abs_s3_w = "ghg_lfl_abs_s3_w"
"""Absolute scope 3 GHG emissions in the whole building filtered for LFL calculation."""

ghg_lfl_abs_s3_o = "ghg_lfl_abs_s3_o"
"""Absolute scope 3 GHG emissions in the outdoor areas filtered for LFL calculation."""

ghg_lfl_abs_s12 = "ghg_lfl_abs_s12"
"""Absolute scope 1 and 2 GHG emissions filtered for LFL calculation."""

ghg_lfl_abs_s3 = "ghg_lfl_abs_s3"
"""Absolute scope 3 GHG emissions filtered for LFL calculation."""

ghg_lfl_percent_change_s12 = "ghg_lfl_percent_change_s12"
"""Percent change in absolute scope 1 and 2 GHG emissions relative to the previous year (Like-For-Like)."""

ghg_lfl_percent_change_s3 = "ghg_lfl_percent_change_s3"
"""Percent change in absolute scope 3 GHG emissions relative to the previous year (Like-For-Like)."""

ghg_lfl_abs_s12_ly = "ghg_lfl_abs_s12_ly"
"""Previous year scopes 1 and 2 GHG emissions filtered for LFL."""

ghg_lfl_abs_s3_ly = "ghg_lfl_abs_s3_ly"
"""Previous year scope 3 GHG emissions filtered for LFL."""

ghg_int_outlier_status = "ghg_int_outlier_status"
"""Outlier status of the asset's GHG intensity value, can be either "none", "accepted" or "rejected"."""

ghg_lfl_outlier_status = "ghg_lfl_outlier_status"
"""Outlier status of the asset's GHG Like-For-Like value, can be either "none", "accepted" or "rejected"."""

ghg_scored_int_ton_m2 = "ghg_scored_int_ton_m2"
"""Calculated GHG intensity for assets eligible to intensity scoring in ton per m2."""

ghg_scored_int_ton_sqft = "ghg_scored_int_ton_sqft"
"""Calculated GHG intensity for assets eligible to intensity scoring in ton per sqft."""

asset_vacancy_ghg_intensity = "asset_vacancy_ghg_intensity"
"""Asset vacancy rate for assets eligible for GHG intensity scoring (75% coverage)."""

asset_size_ghg_intensity_m2 = "asset_size_ghg_intensity_m2"
"""Calculated total asset floor area in square meters for assets eligible for GHG intensity scoring (75% coverage)."""

asset_size_ghg_intensity_sqft = "asset_size_ghg_intensity_sqft"
"""Calculated total asset floor area in square feet for assets eligible for GHG intensity scoring (75% coverage)."""

floor_area_percent_ghg_intensity = "floor_area_percent_ghg_intensity"
"""Calculated total floor area in square meters for assets eligible for GHG intensity scoring (75% coverage) as a percentage of the total floor area of the group."""

ghg_scored_int_ton_m2_accepted = "ghg_scored_int_ton_m2_accepted"
"""The asset's GHG intensity value without hard outliers in ton/m2."""

ghg_scored_int_ton_sqft_accepted = "ghg_scored_int_ton_sqft_accepted"
"""The asset's GHG intensity value without hard outliers in ton/sqft."""

ghg_scored_int_kg_m2_accepted = "ghg_scored_int_kg_m2_accepted"
"""The asset's GHG intensity value without hard outliers in kg/m2."""

ghg_scored_int_kg_sqft_accepted = "ghg_scored_int_kg_sqft_accepted"
"""The asset's GHG intensity value without hard outliers in kg/sqft."""

ghg_lfl_p_accepted = "ghg_lfl_p_accepted"
"""The asset's GHG LFL percent change value filtered for aggregation."""

ghg_lfl_abs_accepted = "ghg_lfl_abs_accepted"
"""Absolute GHG emissions filtered for LFL aggregation."""

ghg_lfl_abs_ly_accepted = "ghg_lfl_abs_ly_accepted"
"""Previous year absolute GHG emissions filtered for LFL aggregation."""

ghg_lfl_abs_s12_ly_accepted = "ghg_lfl_abs_s12_ly_accepted"
"""Absolute scopes 1 and 2 GHG emissions in the previous year filtered for LFL aggregation."""

ghg_lfl_abs_s3_ly_accepted = "ghg_lfl_abs_s3_ly_accepted"
"""Absolute scope 3 GHG emissions in the previous year filtered for LFL aggregation."""

ghg_lfl_abs_s12_ly_bench = "ghg_lfl_abs_s12_ly_bench"
"""Absolute scopes 1 and 2 GHG emissions in the previous year filtered for LFL metric reporting benchmark creation."""

ghg_lfl_abs_s3_ly_bench = "ghg_lfl_abs_s3_ly_bench"
"""Absolute scope 3 GHG emissions in the previous year filtered for LFL metric reporting benchmark creation."""

ghg_area_time_cov_p_s12_agg = "ghg_area_time_cov_p_s12_agg"
"""Area-time GHG data coverage weighted by the area-time weight for the scopes 1 and 2."""

ghg_area_time_cov_p_s3_agg = "ghg_area_time_cov_p_s3_agg"
"""Area-time GHG data coverage weighted by the area-time weight for the scope 3."""

ghg_lfl_percent_change_s12_bench = "ghg_lfl_percent_change_s12_bench"
"""GHG LFL percent change weighted by the area-time weight, filtered for reporting benchmark creation, for the scopes 1 and 2."""

ghg_lfl_percent_change_s3_bench = "ghg_lfl_percent_change_s3_bench"
"""GHG LFL percent change weighted by the area-time weight, filtered for reporting benchmark creation, for the scope 3."""

ghg_int_eligible = "ghg_int_eligible"
"""Whether the asset was eligible to have its GHG emissions intensity calculated."""

ghg_lfl_eligible_for_aggregation = "ghg_lfl_eligible_for_aggregation"
"""Whether the asset is eligible for GHG LFL aggregation."""

ghg_lfl_percent_change_s12_accepted = "ghg_lfl_percent_change_s12_accepted"
"""GHG LFL percent change weighted by the area-time weight, filtered for aggregation, for the scopes 1 and 2."""

ghg_lfl_percent_change_s3_accepted = "ghg_lfl_percent_change_s3_accepted"
"""GHG LFL percent change weighted by the area-time weight, filtered for aggregation, for the scope 3."""

ghg_lfl_area_m2 = "ghg_lfl_area_m2"
"""Floor area in m2 (weighted by ownership) on which the GHG LFL data is reported."""

ghg_lfl_area_m2_s12 = "ghg_lfl_area_m2_s12"
"""Floor area in m2 (weighted by ownership) on which the scopes 1 and 2 GHG LFL data is reported."""

ghg_lfl_area_m2_s3 = "ghg_lfl_area_m2_s3"
"""Floor area in m2 (weighted by ownership) on which the scope 3 GHG LFL data is reported."""

ghg_lfl_area_sqft = "ghg_lfl_area_sqft"
"""Floor area in sqft (weighted by ownership) on which the GHG LFL data is reported."""

ghg_lfl_area_sqft_s12 = "ghg_lfl_area_sqft_s12"
"""Floor area in sqft (weighted by ownership) on which the scopes 1 and 2 GHG LFL data is reported."""

ghg_lfl_area_sqft_s3 = "ghg_lfl_area_sqft_s3"
"""Floor area in sqft (weighted by ownership) on which the scope 3 GHG LFL data is reported."""

ghg_lfl_area = "ghg_lfl_area"
"""Floor area (weighted by ownership) on which the GHG LFL data is reported"""

ghg_lfl_area_s12 = "ghg_lfl_area_s12"
"""Floor area (weighted by ownership) on which the scopes 1 and 2 GHG LFL data is reported"""

ghg_lfl_area_s3 = "ghg_lfl_area_s3"
"""Floor area (weighted by ownership) on which the scope 3 GHG LFL data is reported"""

ghg_lfl_area_p = "ghg_lfl_area_p"
ghg_lfl_area_p_s12 = "ghg_lfl_area_p_s12"
ghg_lfl_area_p_s3 = "ghg_lfl_area_p_s3"

"""Aggregated benchmarks of the metrics"""
ghg_area_time_cov_p_s12_agg_benchmark = "ghg_area_time_cov_p_s12_agg_benchmark"
ghg_area_time_cov_p_s3_agg_benchmark = "ghg_area_time_cov_p_s3_agg_benchmark"
ghg_lfl_percent_change_s12_benchmark = "ghg_lfl_percent_change_s12_benchmark"
ghg_lfl_percent_change_s3_benchmark = "ghg_lfl_percent_change_s3_benchmark"
ghg_int_ton_m2_agg_benchmark = "ghg_int_ton_m2_agg_benchmark"

# WIP: columns for the new framework of metric aggregation
ghg_lfl_abs_change_s12 = "ghg_lfl_abs_change_s12"
ghg_lfl_abs_change_s3 = "ghg_lfl_abs_change_s3"
ghg_lfl_asset_count = "ghg_lfl_asset_count"
