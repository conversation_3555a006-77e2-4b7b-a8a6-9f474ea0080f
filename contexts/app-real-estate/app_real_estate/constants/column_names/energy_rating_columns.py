import app_real_estate.constants.column_names.building_certification_energy_rating_common_columns as bc_er_common
import app_real_estate.constants.column_names.key_columns as key_columns

asset_energy_rating_id = "asset_energy_rating_id"
"""Internal ID for each individual energy rating."""

snapshot_id = key_columns.snapshot_id
"""Internal ID for user data saves."""

response_id = key_columns.response_id
"""Internal ID for each entity full response submission to a survey."""

portfolio_asset_id = key_columns.portfolio_asset_id
"""Internal ID for each individual asset in the GRESB Portal database ("GRESB Asset ID" in the Asset Spreadsheet)."""

company_fund_id = key_columns.company_fund_id
"""Internal GRESB fund ID."""

survey_year = "survey_year"
"""The year this data was reported."""

energy_rating_id = "energy_rating_id"
"""Internal ID for each rating name."""

energy_rating_name = "energy_rating_name"
"""Name of the rating. It contains both the name of the entity issuing the rating and the grade itself."""

covered_floor_area = bc_er_common.covered_floor_area
"""Floor area covered by the rating."""

covered_floor_area_m2 = bc_er_common.covered_floor_area_m2
"""Floor area covered by the rating in square meters."""

asset_size = "asset_size"
"""The total floor area size of the building - without outdoor/exterior areas."""

asset_size_m2 = "asset_size_m2"
"""The total floor area size of the building in square meters - without outdoor/exterior areas."""

coverage = "coverage"
"""Percentage of the total floor area covered by the rating."""

asset_ownership = "asset_ownership"
"""Percentage of ownership of the asset the energy rating is for."""
