import app_real_estate.constants.column_names.building_certification_table_common as bc_common

certification_id = bc_common.certification_id
"""Certification table id, corresponding to certification_id."""

name = bc_common.name
"""Certification name, corresponding to certification name."""

brand = bc_common.brand
"""Certification brand, corresponding to certification brand."""

scheme = bc_common.scheme
"""Certification scheme, corresponding to certification scheme."""

scoring_status = bc_common.scoring_status
"""Certification table scoring status, corresponding to socring_status."""
