import app_real_estate.constants.column_names.key_columns as key_columns

building_data_row_id = "building_data_row_id"
"""Internal ID for asset data per year."""

snapshot_id = key_columns.snapshot_id
"""Internal ID for user data saves."""

company_fund_id = key_columns.company_fund_id
"""Internal GRESB fund ID."""

response_id = key_columns.response_id
"""Internal unique ID for each entity full response submission to a survey."""

portfolio_asset_id = key_columns.portfolio_asset_id
"""Internal ID for each individual asset in the GRESB Portal database ("GRESB Asset ID" in the Asset Spreadsheet)."""

asset_name = "asset_name"
"""Name of the asset as displayed to the users with access to this portfolio in the Asset Portal."""

property_type_code = "property_type_code"
"""Abbreviation of property type and subtype (e.g. "INDW" = "Industrial: Distribution Warehouse")."""

country = "country"
"""Code of the country the asset is built in."""

city = "city"
"""City the asset is built in."""

asset_ownership = "asset_ownership"
"""Percentage of the asset owned by the reporting entity."""

asset_ownership_fraction = "asset_ownership_fraction"
"""Percentage of the asset owned by the reporting entity as a fraction."""

asset_size_owned_sqft = "asset_size_owned_sqft"
"""Floor area of the asset owned by the entity in sqft."""

asset_size_owned_m2 = "asset_size_owned_m2"
"""Floor area of the asset owned by the entity in m2."""

asset_size_owned = "asset_size_owned"
"""Floor area of the asset owned by the entity in the entity unit."""

owned_entire_period = "owned_entire_period"
"""Boolean indicating whether the asset is owned through the entire reported year."""

asset_vacancy = "asset_vacancy"
"""Vacancy rate of the asset."""

owned_for_aggregation = "owned_for_aggregation"
"""Boolean indicating whether the asset counts as owned for the reporting boundaries. It is true if `owned_entire_period` is true or if the ownership period respects the set boundaries."""

standing_investment_for_aggregation = "standing_investment_for_aggregation"
"""Standing investments: real estate properties where construction work has been completed and which are owned for the purpose of leasing and producing rental income."""

ncmr_status = "ncmr_status"
"""Operational status of the asset at the beginning of the reported period. An asset is either classified as "Standing Investment", "New Construction" or "Major Renovation"."""

survey_year = "survey_year"
"""The year this data was reported."""

data_year = "data_year"
"""The year reported quantities belong to."""

asset_size = "asset_size"
"""The total floor area size of the building - without outdoor/exterior areas."""

area_unit = "area_unit"
"""Indicates whether the 'asset_size' variable is in square meters or square feet."""

country_name = "country_name"
"""Country the asset is built in."""

sub_region = "sub_region"
"""Sub-region (sub-continent) the asset is built in."""

region = "region"
"""Region (continent) the asset is built in."""

super_region = "super_region"
"""Super region the asset is built in."""

property_subtype = "property_subtype"
"""Property subtype of the asset. For example: Residential: Multi-Family: Mid-Rise Multi Family."""

property_type = "property_type"
"""Property type of the asset. For example: Residential: Multi-Family."""

property_sector = "property_sector"
"""Property sector of the asset. For example: Residential."""

asset_size_m2 = "asset_size_m2"
"""The total floor area size of the building in square meters - without outdoor/exterior areas."""

asset_size_sqft = "asset_size_sqft"
"""The total floor area size of the building in square feet - without outdoor/exterior areas."""

certification_coverage = "certification_coverage"
"""Calculated field containing the percentage of floor area covered by at least one building certification."""

certified_floor_area_m2 = "certified_floor_area_m2"
"""Calculated field containing the total floor area certified by at least one building certification."""

rating_coverage = "rating_coverage"
"""Calculated field containing the percentage of floor area covered by at least one energy rating."""

rated_floor_area_m2 = "rated_floor_area_m2"
"""Calculated field containing the total floor area certified by at least one energy rating."""

asset_gav = "asset_gav"
"""Gross asset value of the asset."""

whole_building = "whole_building"
"""Whether the asset is reported as a whole building (instead of divided into base building and tenant spaces) or not."""

tenant_ctrl = "tenant_ctrl"
"""Whether the building is entirely controlled by tenants or not. Only applicable if the `whole_building` is true, set to false if not."""

asset_size_common_m2 = "asset_size_common_m2"
"""The total floor area size of the base building common areas in square meters."""

asset_size_shared_m2 = "asset_size_shared_m2"
"""The total floor area size of the base building shared spaces in square meters."""

asset_size_tenant_m2 = "asset_size_tenant_m2"
"""The total floor area size of the tenant spaces in square meters."""

asset_size_tenant_tenant_m2 = "asset_size_tenant_tenant_m2"
"""The total floor area size of the tenant controlled tenant spaces in square meters."""

asset_size_tenant_landlord_m2 = "asset_size_tenant_landlord_m2"
"""The total floor area size of the landlord controlled tenant spaces in square meters."""

comp_perf = "comp_perf"
"""Flag indicating whether the asset was reported for the Performance component of the assessment."""

nearest_ashrae_weather_station_id = "nearest_ashrae_weather_station_id"
"""Weather station assigned to the asset based on its location."""

climate_zone = "climate_zone"
"""The climate zone the asset is built in. Determined by mapping the asset's location to the nearest weather station."""

location_columns = [country, sub_region, region, super_region, "global"]

property_type_columns = [
    property_subtype,
    property_type,
    property_sector,
]
