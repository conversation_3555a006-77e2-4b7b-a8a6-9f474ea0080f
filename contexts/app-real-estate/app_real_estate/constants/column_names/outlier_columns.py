import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.water_columns as wc
import app_real_estate.constants.column_names.waste_columns as wsc
import app_real_estate.constants.column_names.ghg_columns as gc

outlier_columns = [
    ec.en_int_outlier_status,
    ec.en_lfl_outlier_status,
    wc.wat_int_outlier_status,
    wc.wat_lfl_outlier_status,
    gc.ghg_int_outlier_status,
    gc.ghg_lfl_outlier_status,
    wsc.was_int_outlier_status,
]
