was_max_days_data_avail = "was_max_days_data_avail"
"""Maximum number of days that the waste data of the asset was available."""

was_abs = "was_abs"
"""Absolute waste production of the asset."""

was_abs_ly = "was_abs_ly"
"""Absolute previous year waste production of the asset."""

was_abs_haz = "was_abs_haz"
"""Absolute production of hazardous wastes."""

was_abs_nhaz = "was_abs_nhaz"
"""Absolute production of non-hazardous wastes."""

was_int_ton_m2 = "was_int_ton_m2"
"""Waste consumption intensity in terms of ton per square meters."""

was_area_p_lc = "was_area_p_lc"
"""Percentage of the asset's floor area controlled by landlord."""

was_area_p_tc = "was_area_p_tc"
"""Percentage of the asset's floor area controlled by tenant."""

was_area_cov_p = "was_area_cov_p"
"""Percentage of floor area covered by the reported waste data."""

was_area_cov_p_lc = "was_area_cov_p_lc"
"""Percentage of area based coverage by the reported waste data on landlord controlled areas."""

was_area_cov_p_tc = "was_area_cov_p_tc"
"""Percentage of area based coverage by the reported waste data on tenant controlled areas."""

was_area_weight_lc = "was_area_weight_lc"
"""Weight of the area based waste data coverage for the landlord controlled area."""

was_area_weight_tc = "was_area_weight_tc"
"""Weight of the area based waste data coverage for the tenant controlled area."""

was_abs_div = "was_abs_div"
"""Absolute amount of waste diverted."""

was_abs_div_ly = "was_abs_div_ly"
"""Absolute previous year amount of waste diverted."""

was_pabs_lf = "was_pabs_lf"
"""Percentage of waste disposed by landfill."""

was_pabs_in = "was_pabs_in"
"""Percentage of waste disposed by incineration."""

was_pabs_ru = "was_pabs_ru"
"""Percentage of waste disposed by reuse."""

was_pabs_wte = "was_pabs_wte"
"""Percentage of waste disposed by transformation to energy."""

was_pabs_rec = "was_pabs_rec"
"""Percentage of waste disposed by recycling."""

was_pabs_oth = "was_pabs_oth"
"""Percentage of waste disposed by other disposal route."""

was_pabs_div = "was_pabs_div"
"""Percentage of waste diverted."""

was_pabs_div_ly = "was_pabs_div_ly"
"""Percentage of waste diverted in the previous year."""

was_int_outlier_status = "was_int_outlier_status"
"""Outlier status of the asset's waste intensity value, can be either "none", "accepted" or "rejected"."""

was_int_ton_m2_agg = "was_int_ton_m2_agg"
"""The asset's waste intensity value without outliers."""

was_int_ton_m2_accepted = "was_int_ton_m2_accepted"
"""The asset's waste intensity value without hard outliers."""

was_area_cov_p_lc_agg = "was_area_cov_p_lc_agg"
"""Area waste data coverage weighted by the owned area weight for the landlord controlled areas."""

was_area_cov_p_tc_agg = "was_area_cov_p_tc_agg"
"""Area waste data coverage weighted by the owned area weight for the tenant controlled areas."""

was_abs_lf = "was_abs_lf"
"""Total waste disposed by landfill."""

was_abs_in = "was_abs_in"
"""Total waste incinerated."""

was_abs_ru = "was_abs_ru"
"""Total waste reused."""

was_abs_wte = "was_abs_wte"
"""Total waste transformed to energy."""

was_abs_rec = "was_abs_rec"
"""Total waste recycled."""

was_abs_oth = "was_abs_oth"
"""Total waste disposed by an other way."""


"""Aggregated benchmarks of the metrics"""
was_area_cov_p_lc_agg_benchmark = "was_area_cov_p_lc_agg_benchmark"
was_area_cov_p_tc_agg_benchmark = "was_area_cov_p_tc_agg_benchmark"
was_int_ton_m2_agg_benchmark = "was_int_ton_m2_agg_benchmark"
was_pabs_div_ly_benchmark = "was_pabs_div_ly_benchmark"
was_pabs_lf_benchmark = "was_pabs_lf_benchmark"
was_pabs_in_benchmark = "was_pabs_in_benchmark"
was_pabs_ru_benchmark = "was_pabs_ru_benchmark"
was_pabs_wte_benchmark = "was_pabs_wte_benchmark"
was_pabs_rec_benchmark = "was_pabs_rec_benchmark"
was_pabs_oth_benchmark = "was_pabs_oth_benchmark"
was_pabs_div_benchmark = "was_pabs_div_benchmark"
