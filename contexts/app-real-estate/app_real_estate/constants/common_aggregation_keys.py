import app_real_estate.constants.column_names.asset_characteristics_columns as asc

portfolio_level = [asc.response_id]
"""Common key used for aggregation of data to the portfolio level."""

propertysubtype_responseid = [asc.property_type_code, asc.response_id]
"""Common key used for aggregation of data to the property subtype level."""

propertysubtype_country_responseid = [
    asc.property_type_code,
    asc.country,
    asc.response_id,
]
"""Common key used for aggregation of data to the property subtype and country level."""

propertysector_country_responseid = [
    asc.property_sector,
    asc.country,
    asc.response_id,
]
"""Common key used for aggregation of data to the property sector and country level."""
