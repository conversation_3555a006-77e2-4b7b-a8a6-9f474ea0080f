import pandera as pa
from pandera.typing import Series


class ER_Metrics_Model(pa.DataFrameModel):
    RESPONSE_ID: Series[int] = pa.Field()
    TYPE: Series[str] = pa.Field(isin=["EN"])
    PCOV: Series[float] = pa.Field(le=100, ge=0)
    RATED_AST_COUNT: Series[int] = pa.Field()


class BaseERSectorCtrLevelModel(ER_Metrics_Model):
    COUNTRY: Series[str] = pa.Field()
    PRT_SECTOR: Series[str] = pa.Field()


class ER_ResponseID_PropertySector_Country_Brand_Model(BaseERSectorCtrLevelModel):
    NAME: Series[str] = pa.Field()

    class Config:
        unique = [
            "RESPONSE_ID",
            "COUNTRY",
            "PRT_SECTOR",
            "TYPE",
            "NAME",
        ]


class ER_ResponseID_PropertySector_Country_Model(BaseERSectorCtrLevelModel):
    BENCH_PCOV: Series[float] = pa.Field()

    class Config:
        unique = [
            "RESPONSE_ID",
            "COUNTRY",
            "PRT_SECTOR",
            "TYPE",
        ]


class ER_ResponseID_Brand_Model(ER_Metrics_Model):
    NAME: Series[str] = pa.Field()

    class Config:
        unique = [
            "RESPONSE_ID",
            "TYPE",
            "NAME",
        ]


class ER_ResponseID_Model(ER_Metrics_Model):
    class Config:
        unique = [
            "RESPONSE_ID",
            "TYPE",
        ]
