import pandera as pa
from pandera.typing import Series


class BC_Metrics_Model(pa.DataFrameModel):
    RESPONSE_ID: Series[int] = pa.Field()
    TYPE: Series[str] = pa.Field(isin=["NC", "OPR"])
    PCOV: Series[float] = pa.Field(le=100, ge=0)
    CERTIFIED_AST_COUNT: Series[int] = pa.Field()


class BaseBCSectorCtrLevelModel(BC_Metrics_Model):
    COUNTRY: Series[str] = pa.Field()
    PRT_SECTOR: Series[str] = pa.Field()


class BC_ResponseID_PropertySector_Country_Brand_Scheme_Model(
    BaseBCSectorCtrLevelModel
):
    BRAND: Series[str] = pa.Field()
    SCHEME_LEVEL: Series[str] = pa.Field()
    ID: Series[int] = pa.Field()

    class Config:
        unique = [
            "RESPONSE_ID",
            "COUNTRY",
            "PRT_SECTOR",
            "TYPE",
            "BRAND",
            "SCHEME_LEVEL",
            "ID",
        ]


class BC_ResponseID_PropertySector_Country_Brand_Model(BaseBCSectorCtrLevelModel):
    BRAND: Series[str] = pa.Field()

    class Config:
        unique = [
            "RESPONSE_ID",
            "COUNTRY",
            "PRT_SECTOR",
            "TYPE",
            "BRAND",
        ]


class BC_ResponseID_PropertySector_Country_Model(BaseBCSectorCtrLevelModel):
    BENCH_PCOV: Series[float] = pa.Field()

    class Config:
        unique = [
            "RESPONSE_ID",
            "COUNTRY",
            "PRT_SECTOR",
            "TYPE",
        ]


class BC_ResponseID_Brand_Scheme_Model(BC_Metrics_Model):
    BRAND: Series[str] = pa.Field()
    SCHEME_LEVEL: Series[str] = pa.Field()
    ID: Series[int] = pa.Field()

    class Config:
        unique = [
            "RESPONSE_ID",
            "TYPE",
            "BRAND",
            "SCHEME_LEVEL",
            "ID",
        ]


class BC_ResponseID_Brand_Model(BC_Metrics_Model):
    BRAND: Series[str] = pa.Field()

    class Config:
        unique = [
            "RESPONSE_ID",
            "TYPE",
            "BRAND",
        ]


class BC_ResponseID_Model(BC_Metrics_Model):
    class Config:
        unique = [
            "RESPONSE_ID",
            "TYPE",
        ]
