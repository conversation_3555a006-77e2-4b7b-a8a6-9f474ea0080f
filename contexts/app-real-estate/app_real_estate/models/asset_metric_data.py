"""
Asset Metric Data Model for Real Estate Properties

This module defines the AssetMetricData class, which represents key metrics
for real estate assets in a data pipeline. It extends the BasePropertiesDataModel
and includes specific validations for asset size and ownership.

The model is designed to ensure data integrity and consistency in real estate
analytics, particularly focusing on asset size and ownership percentage.

Classes:
    AssetMetricData: Represents metric data for a real estate asset.

Attributes:
    asset_size_m2 (float): The size of the asset in square meters.
    asset_ownership (float): The ownership percentage of the asset (0-100).

Validations:
    - Ensures asset ownership is not NaN (Not a Number).
    - Validates that asset ownership is within the range of 0 to 100.

Usage:
    This model is typically used in data processing pipelines to validate
    and structure real estate asset data before further analysis or storage.

Example:
    asset = AssetMetricData(asset_size_m2=100.5, asset_ownership=75.0)

Note:
    This model is part of a larger real estate data pipeline and should be
    used in conjunction with other models and processors in the system.

Raises:
    ValidationError: If the input data fails the defined validations.

See Also:
    BasePropertiesDataModel: The base class for property data models.
    app_real_estate.column_names.asset_characteristics_columns: Defines column aliases.
"""

import numpy as np
from pydantic import ValidationError, field_validator, Field

import app_real_estate.constants.column_names.asset_characteristics_columns as asc
from app_real_estate.models.score_aggregation_util_models.base_properties_data_model import (
    BasePropertiesDataModel,
)


class AssetMetricData(BasePropertiesDataModel):
    """
    Represents metric data for a real estate asset.

    This class extends BasePropertiesDataModel to include specific
    validations for asset size and ownership percentage.

    Attributes:
        asset_size_m2 (float): The size of the asset in square meters.
        asset_ownership (float): The ownership percentage of the asset (0-100).
    """

    asset_size_m2: float = Field(..., alias=asc.asset_size_m2)
    asset_ownership: float = Field(..., alias=asc.asset_ownership)

    @field_validator(asc.asset_ownership)
    def validate_asset_ownership_is_not_na(cls, v: float) -> float:
        """
        Validates that the asset ownership value is not NaN (Not a Number).

        Args:
            v (float): The value to be validated.

        Raises:
            ValueError: If the value is NaN.

        Returns:
            Any: The validated value if it is not NaN.
        """
        if np.isnan(v):
            raise ValueError("Asset ownership cannot be NA")
        return v

    @field_validator(asc.asset_ownership)
    def validate_asset_ownership_is_in_range(cls, v: float) -> float:
        """
        Validates that the asset ownership value is in the range 0 to 100.

        Args:
            v (float): The value to be validated.

        Raises:
            ValidationError: If the value is not in the range 0 to 100.

        Returns:
            float: The validated value if it is in the range 0 to 100.
        """
        if np.logical_or(v <= 0, v > 100):
            raise ValidationError("Asset ownership must be between 0 and 100")
        return v
