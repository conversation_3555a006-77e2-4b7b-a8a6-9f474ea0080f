"""
Base Properties Data Model for Real Estate Analytics using pandantic

This module defines the BasePropertiesDataModel class, which serves as the foundation
for all other data models in the real estate analytics system. It encapsulates common
fields that are shared across various property-related data models and leverages
pandantic for DataFrame validation.

The BasePropertiesDataModel provides a consistent structure for identifying and
categorizing real estate assets, ensuring uniformity across derived models while
allowing for efficient validation of pandas DataFrames.

Classes:
    BasePropertiesDataModel: Base model containing common fields for real estate properties.

Note:
    This base model should be inherited by other, more specific property data models
    to maintain consistency in core property attributes across the system. It uses
    pandantic to combine pydantic V2 functionality with pandas DataFrame validation.

See Also:
    app_real_estate.column_names.key_columns: Defines key column aliases.
    app_real_estate.column_names.asset_characteristics_columns: Defines column aliases for asset characteristics.
    pandantic: Package for validating pandas DataFrames using pydantic models.
    pydantic
"""

from pydantic import Field
from pandantic import BaseModel
import app_real_estate.constants.column_names.key_columns as key_columns
import app_real_estate.constants.column_names.asset_characteristics_columns as asc


class BasePropertiesDataModel(BaseModel):
    """ "
    Base data model containing common fields for real estate properties.

    This class serves as a foundation for all other property-related data models
    in the real estate analytics system. It defines core attributes that are
    common across different types of property data, ensuring consistency and
    facilitating data integration across the system. It leverages pandantic
    for efficient validation of pandas DataFrames.

    Attributes:
        response_id (int): Unique identifier for the property response.
        country (str): The country where the property is located.
        property_subtype (str): The specific subtype of the property.

    Note:
        When creating new property data models, inherit from this base class
        to automatically include these common fields and benefit from
        pandantic's DataFrame validation capabilities.
    """

    response_id: int = Field(..., alias=key_columns.response_id)
    country: str = Field(..., alias=asc.country)
    property_type_code: str = Field(..., alias=asc.property_type_code)
