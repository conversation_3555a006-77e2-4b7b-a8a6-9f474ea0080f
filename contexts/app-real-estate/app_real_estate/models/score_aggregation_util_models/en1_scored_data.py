"""
EN1 Scored Data Model for Real Estate Energy Performance

This module defines the EN1ScoredData class, which represents scored data
for the EN1 (Energy 1) standard in real estate energy performance analytics.
It extends the BasePropertiesDataModel to include specific fields and validations
for EN1 scoring.

The model serves as the expected output format for the EN1ScoreCalculator steps,
encapsulating various energy performance metrics and scores.

Classes:
    EN1ScoredData: Represents scored data for a real estate asset under EN1 standard.

Note:
    This model includes various energy-related scores and metrics, including
    like-for-like (LFL) comparisons, renewable energy data, and area-time coverage.

See Also:
    BasePropertiesDataModel: The base class for property data models.
    app_real_estate.column_names.score_columns: Defines column aliases for scoring data.
    app_real_estate.column_names.asset_characteristics_columns: Defines column aliases for asset characteristics.
"""

from pydantic import ValidationError, field_validator, Field
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.asset_characteristics_columns as asc
from app_real_estate.models.score_aggregation_util_models.base_properties_data_model import (
    BasePropertiesDataModel,
)


class EN1ScoredData(BasePropertiesDataModel):
    """
    EN1 scored data model for real estate energy performance.

    This class extends BasePropertiesDataModel to include specific fields
    related to EN1 scoring. It serves as the output format for the EN1ScoreCalculator
    steps, encompassing various energy performance metrics and scores.

    Attributes:
        asset_size_m2 (float): The size of the asset in square meters.
        asset_ownership (float): The ownership percentage of the asset.
        score_en1_en_area_time_cov_p (float): Energy area-time coverage percentage.
        score_en1_lfl_availability (int): Like-for-like data availability (0 or 1).
        score_en1_en_lfl_percent_change (float): Like-for-like energy percentage change.
        score_en1_en_ren_ons (int): On-site renewable energy score (0 or 1).
        score_en1_en_ren_ofs (int): Off-site renewable energy score (0 or 1).
        score_en1_en_ren_percent_change (float): Renewable energy percentage change.
        score_en1_en_ren_performance (float): Renewable energy performance score.
        score_en1_en_lfl_percent_change_lc (float): Like-for-like percentage change (location-based).
        score_en1_en_lfl_percent_change_tc (float): Like-for-like percentage change (time-based).
        score_en1_en_area_time_cov_p_lc (float): Energy area-time coverage percentage (location-based).
        score_en1_en_area_time_cov_p_tc (float): Energy area-time coverage percentage (time-based).
    """

    asset_size_m2: float = Field(..., alias=asc.asset_size_m2)
    asset_ownership: float = Field(..., alias=asc.asset_ownership)

    score_en1_en_area_time_cov_p: float
    score_en1_lfl_availability: float = Field(..., alias=sc.score_en1_lfl_availability)
    score_en1_en_lfl_percent_change: float = Field(
        ..., alias=sc.score_en1_en_lfl_percent_change
    )
    score_en1_en_ren_ons: int = Field(..., alias=sc.score_en1_en_ren_ons)
    score_en1_en_ren_ofs: int = Field(..., alias=sc.score_en1_en_ren_ofs)

    score_en1_en_ren_percent_change: float = Field(
        ..., alias=sc.score_en1_en_ren_percent_change
    )
    score_en1_en_ren_performance: float = Field(
        ..., alias=sc.score_en1_en_ren_performance
    )

    score_en1_en_lfl_percent_change_lc: float = Field(
        ..., alias=sc.score_en1_en_lfl_percent_change_lc
    )
    score_en1_en_lfl_percent_change_tc: float = Field(
        ..., alias=sc.score_en1_en_lfl_percent_change_tc
    )
    score_en1_en_area_time_cov_p_lc: float = Field(
        ..., alias=sc.score_en1_en_area_time_cov_p_lc
    )
    score_en1_en_area_time_cov_p_tc: float = Field(
        ..., alias=sc.score_en1_en_area_time_cov_p_tc
    )

    @field_validator(
        sc.score_en1_en_ren_ons,
        sc.score_en1_en_ren_ofs,
    )
    def score_en1_lfl_availability_validator(cls, v):
        """
        Validate that certain scores are either 0 or 1.

        Args:
            v: The value to be validated.

        Raises:
            ValidationError: If the value is not 0 or 1.

        Returns:
            int: The validated value (0 or 1).
        """
        if v not in [0, 1]:
            raise ValidationError("score must be 0 or 1")
        return v
