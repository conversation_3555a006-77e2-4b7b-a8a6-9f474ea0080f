"""
BC2 Scored Data Model for Real Estate Properties

This module defines the BC2ScoredData class, which represents scored data
for the BC2 (Building Certification 2) standard in real estate analytics.
It extends the BasePropertiesDataModel to include specific fields and validations
for BC2 scoring.

The model serves as a contract for data aggregation in the BC2 process and
is typically used as an output format for BC2-related score calculations.

Classes:
    BC2ScoredData: Represents scored data for a real estate asset under BC2 standard.

Note:
    Unlike BC1.1 and BC1.2 models, the BC2 coverage score is not optional,
    indicating that this data is expected to be available for all assessed assets.

See Also:
    BasePropertiesDataModel: The base class for property data models.
    app_real_estate.column_names.score_columns: Defines column aliases for scoring data.
    app_real_estate.column_names.asset_characteristics_columns: Defines column aliases for asset characteristics.
"""

from pydantic import Field
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.asset_characteristics_columns as asc
from app_real_estate.models.score_aggregation_util_models.base_properties_data_model import (
    BasePropertiesDataModel,
)


class BC2ScoredData(BasePropertiesDataModel):
    """
    BC2 scored data model for real estate assets.

    This class extends BasePropertiesDataModel to include specific fields
    related to BC2 scoring. It serves as a contract for data aggregation
    in the BC2 process and is typically used as an output format for
    BC2-related score calculations.

    Attributes:
        asset_size_m2 (float): The size of the asset in square meters.
        asset_ownership (float): The ownership percentage of the asset.
        score_bc2_coverage (float): The BC2 coverage score. This field is required,
            indicating that BC2 scoring data should be available for all assessed assets.
    """

    asset_size_m2: float = Field(..., alias=asc.asset_size_m2)
    asset_ownership: float = Field(..., alias=asc.asset_ownership)
    score_bc2_coverage: float = Field(..., alias=sc.score_bc2_coverage)
