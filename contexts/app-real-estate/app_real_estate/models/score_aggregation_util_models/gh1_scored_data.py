"""
GH1 Scored Data Model for Real Estate Greenhouse Gas Emissions

This module defines the GH1ScoredData class, which represents scored data
for the GH1 (Greenhouse Gas 1) standard in real estate emissions analytics.
It extends the BasePropertiesDataModel to include specific fields for GH1 scoring.

The model serves as a contract for data aggregation in the GH1 process,
encapsulating various greenhouse gas emission metrics and scores.

Classes:
    GH1ScoredData: Represents scored data for a real estate asset under GH1 standard.

Note:
    This model includes various GHG-related scores and metrics, including
    area-time coverage percentages and like-for-like (LFL) percentage changes
    for different scopes (S1, S2, S3, and combined).

See Also:
    BasePropertiesDataModel: The base class for property data models.
    app_real_estate.column_names.score_columns: Defines column aliases for scoring data.
    app_real_estate.column_names.asset_characteristics_columns: Defines column aliases for asset characteristics.
"""

from pydantic import Field
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.asset_characteristics_columns as asc
from app_real_estate.models.score_aggregation_util_models.base_properties_data_model import (
    BasePropertiesDataModel,
)


class GH1ScoredData(BasePropertiesDataModel):
    """
    GH1 scored data model for real estate greenhouse gas emissions.

    This class extends BasePropertiesDataModel to include specific fields
    related to GH1 scoring. It serves as a contract for data aggregation
    in the GH1 process, encompassing various greenhouse gas emission metrics
    and scores.

    Attributes:
        asset_size_m2 (float): The size of the asset in square meters.
        asset_ownership (float): The ownership percentage of the asset.
        score_gh1_ghg_area_time_cov_p_s12 (float): GHG area-time coverage percentage for Scope 1 & 2.
        score_gh1_ghg_area_time_cov_p_s3 (float): GHG area-time coverage percentage for Scope 3.
        score_gh1_ghg_area_time_cov_p (float): Overall GHG area-time coverage percentage.
        score_gh1_ghg_lfl_percent_change_s12 (float): Like-for-like GHG percentage change for Scope 1 & 2.
        score_gh1_ghg_lfl_percent_change_s3 (float): Like-for-like GHG percentage change for Scope 3.
        score_gh1_ghg_lfl_percent_change (float): Overall like-for-like GHG percentage change.

    Note:
        All attributes in this class should be provided when aggregating GH1 scored data.
    """

    asset_size_m2: float = Field(..., alias=asc.asset_size_m2)
    asset_ownership: float = Field(..., alias=asc.asset_ownership)

    score_gh1_ghg_area_time_cov_p_s12: float = Field(
        ..., alias=sc.score_gh1_ghg_area_time_cov_p_s12
    )
    score_gh1_ghg_area_time_cov_p_s3: float = Field(
        ..., alias=sc.score_gh1_ghg_area_time_cov_p_s3
    )
    score_gh1_ghg_area_time_cov_p: float = Field(
        ..., alias=sc.score_gh1_ghg_area_time_cov_p
    )

    score_gh1_ghg_lfl_percent_change_s12: float = Field(
        ..., alias=sc.score_gh1_ghg_lfl_percent_change_s12
    )
    score_gh1_ghg_lfl_percent_change_s3: float = Field(
        ..., alias=sc.score_gh1_ghg_lfl_percent_change_s3
    )
    score_gh1_ghg_lfl_percent_change: float = Field(
        ..., alias=sc.score_gh1_ghg_lfl_percent_change
    )
