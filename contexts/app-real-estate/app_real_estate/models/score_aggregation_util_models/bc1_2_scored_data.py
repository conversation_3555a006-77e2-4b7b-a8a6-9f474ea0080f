"""
BC1.2 Scored Data Model for Real Estate Properties

This module defines the BC1_2ScoredData class, which represents scored data
for the BC1.2 (Building Certification 1.2) standard in real estate analytics.
It extends the BasePropertiesDataModel to include specific fields and validations
for BC1.2 scoring.

The model serves as a contract for data aggregation in the BC1.2 process and
is the expected output format of the BC2ScoreCalculator steps.

Classes:
    BC1_2ScoredData: Represents scored data for a real estate asset under BC1.2 standard.

Note:
    This model allows for optional BC1.2 coverage scores to accommodate data merging
    scenarios where some assets may not have certification data.

See Also:
    BasePropertiesDataModel: The base class for property data models.
    app_real_estate.column_names.score_columns: Defines column aliases for scoring data.
    app_real_estate.column_names.asset_characteristics_columns: Defines column aliases for asset characteristics.
"""

from pydantic import Field
from pandantic import Optional
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.asset_characteristics_columns as asc
from app_real_estate.models.score_aggregation_util_models.base_properties_data_model import (
    BasePropertiesDataModel,
)


class BC1_2ScoredData(BasePropertiesDataModel):
    """
    BC1.2 scored data model for real estate assets.

    This class extends BasePropertiesDataModel to include specific fields
    related to BC1.2 scoring. It serves as a contract for data aggregation
    in the BC1.2 process and is the expected output format of the
    BC2ScoreCalculator steps.

    Attributes:
        asset_size_m2 (float): The size of the asset in square meters.
        asset_ownership (float): The ownership percentage of the asset.
        score_bc1_2_coverage (Optional[float]): The BC1.2 coverage score,
            if available. This field is optional to accommodate data merging
            scenarios where some assets may not have certification data.
    """

    asset_size_m2: float = Field(..., alias=asc.asset_size_m2)
    asset_ownership: float = Field(..., alias=asc.asset_ownership)
    score_bc1_2_coverage: Optional[float] = Field(None, alias=sc.score_bc1_2_coverage)
