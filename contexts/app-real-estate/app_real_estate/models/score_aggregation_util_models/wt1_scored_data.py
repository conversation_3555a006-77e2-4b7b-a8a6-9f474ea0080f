"""
WT1 Scored Data Model for Real Estate Water Management

This module defines the WT1ScoredData class, which represents scored data
for the WT1 (Water 1) standard in real estate water management analytics.
It extends the BasePropertiesDataModel to include specific fields for WT1 scoring.

The model serves as the output format for WT1Scoring, encapsulating various
water management metrics and scores at the asset level.

Classes:
    WT1ScoredData: Represents scored data for a real estate asset under WT1 standard.

Note:
    This model includes water-related scores and metrics, including
    area-time coverage percentages, like-for-like (LFL) percentage changes,
    and recycled water usage metrics.

See Also:
    BasePropertiesDataModel: The base class for property data models.
    app_real_estate.column_names.score_columns: Defines column aliases for scoring data.
    app_real_estate.column_names.asset_characteristics_columns: Defines column aliases for asset characteristics.
"""

from pydantic import field_validator, Field
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.asset_characteristics_columns as asc
from app_real_estate.models.score_aggregation_util_models.base_properties_data_model import (
    BasePropertiesDataModel,
)


class WT1ScoredData(BasePropertiesDataModel):
    """
    WT1 scored data model for real estate water management at the asset level.

    This class extends BasePropertiesDataModel to include specific fields
    related to WT1 scoring. It serves as the output format for WT1Scoring,
    encompassing various water management metrics and scores.

    Attributes:
        asset_size_m2 (float): The size of the asset in square meters.
        asset_ownership (float): The ownership percentage of the asset.
        score_wt1_wat_area_time_cov_p_lc (float): Water area-time coverage percentage (location-based).
        score_wt1_wat_area_time_cov_p_tc (float): Water area-time coverage percentage (time-based).
        score_wt1_wat_area_time_cov_p (float): Overall water area-time coverage percentage.
        score_wt1_wat_lfl_percent_change_lc (float): Like-for-like water percentage change (location-based).
        score_wt1_wat_lfl_percent_change_tc (float): Like-for-like water percentage change (time-based).
        score_wt1_wat_lfl_percent_change (float): Overall like-for-like water percentage change.
        score_wt1_wat_rec_ons (float): On-site water recycling score (0 or 1).
        score_wt1_wat_rec_percent_change (float): Recycled water usage percentage change.
        score_wt1_wat_rec_performance (float): Recycled water performance score.
    """

    asset_size_m2: float = Field(..., alias=asc.asset_size_m2)
    asset_ownership: float = Field(..., alias=asc.asset_ownership)

    score_wt1_wat_area_time_cov_p_lc: float = Field(
        ..., alias=sc.score_wt1_wat_area_time_cov_p_lc
    )
    score_wt1_wat_area_time_cov_p_tc: float = Field(
        ..., alias=sc.score_wt1_wat_area_time_cov_p_tc
    )
    score_wt1_wat_area_time_cov_p: float = Field(
        ..., alias=sc.score_wt1_wat_area_time_cov_p
    )

    score_wt1_wat_lfl_percent_change_lc: float = Field(
        ..., alias=sc.score_wt1_wat_lfl_percent_change_lc
    )
    score_wt1_wat_lfl_percent_change_tc: float = Field(
        ..., alias=sc.score_wt1_wat_lfl_percent_change_tc
    )
    score_wt1_wat_lfl_percent_change: float = Field(
        ..., alias=sc.score_wt1_wat_lfl_percent_change
    )

    score_wt1_wat_rec_ons: float = Field(..., alias=sc.score_wt1_wat_rec_ons)
    score_wt1_wat_rec_percent_change: float = Field(
        ..., alias=sc.score_wt1_wat_rec_percent_change
    )
    score_wt1_wat_rec_performance: float = Field(
        ..., alias=sc.score_wt1_wat_rec_performance
    )

    @field_validator("score_wt1_wat_rec_ons")
    def validate_score_wt1_wat_rec_ons(cls, v):
        """
        Validate that the on-site water recycling score is either 0 or 1.

        Args:
            v (float): The value to be validated.

        Raises:
            ValueError: If the value is not 0 or 1.

        Returns:
            float: The validated value (0 or 1).
        """
        if v not in [0, 1]:
            raise ValueError("score_wt1_wat_rec_ons must be 0 or 1")
        return v
