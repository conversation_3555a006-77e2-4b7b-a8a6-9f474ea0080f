"""
WS1 Scored Data Model for Real Estate Waste Management

This module defines the WS1ScoredData class, which represents scored data
for the WS1 (Waste 1) standard in real estate waste management analytics.
It extends the BasePropertiesDataModel to include specific fields for WS1 scoring.

The model serves as a contract for data aggregation in the WS1 process,
encapsulating waste management metrics and scores.

Classes:
    WS1ScoredData: Represents scored data for a real estate asset under WS1 standard.

Note:
    This model includes waste-related scores and metrics, including
    area-time coverage percentages and waste diversion percentages.

See Also:
    BasePropertiesDataModel: The base class for property data models.
    app_real_estate.column_names.score_columns: Defines column aliases for scoring data.
    app_real_estate.column_names.asset_characteristics_columns: Defines column aliases for asset characteristics.
"""

from pydantic import Field
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.asset_characteristics_columns as asc
from app_real_estate.models.score_aggregation_util_models.base_properties_data_model import (
    BasePropertiesDataModel,
)


class WS1ScoredData(BasePropertiesDataModel):
    """
    WS1 scored data model for real estate waste management.

    This class extends BasePropertiesDataModel to include specific fields
    related to WS1 scoring. It serves as a contract for data aggregation
    in the WS1 process, encompassing waste management metrics and scores.

    Attributes:
        asset_size_m2 (float): The size of the asset in square meters.
        asset_ownership (float): The ownership percentage of the asset.
        score_ws1_was_area_cov_p (float): Waste area-time coverage percentage.
        score_ws1_was_diverted_percent (float): Percentage of waste diverted from landfills.

    Note:
        All attributes in this class should be provided when aggregating WS1 scored data.
        This ensures comprehensive waste management analysis for the real estate asset.
    """

    asset_size_m2: float = Field(..., alias=asc.asset_size_m2)
    asset_ownership: float = Field(..., alias=asc.asset_ownership)

    score_ws1_was_area_cov_p_lc: float = Field(
        ..., alias=sc.score_ws1_was_area_cov_p_lc
    )
    score_ws1_was_area_cov_p_tc: float = Field(
        ..., alias=sc.score_ws1_was_area_cov_p_tc
    )
    score_ws1_was_area_cov_p: float = Field(..., alias=sc.score_ws1_was_area_cov_p)

    score_ws1_was_diverted_percent: float = Field(
        ..., alias=sc.score_ws1_was_diverted_percent
    )
