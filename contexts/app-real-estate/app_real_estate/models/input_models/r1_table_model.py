import pandera as pa
from pandera.typing import Series


class R1TableModel(pa.DataFrameModel):
    RESPONSE_ID: Series[int] = pa.Field()
    PRT_TYPE: Series[str] = pa.Field()
    COUNTRY: Series[str] = pa.Field()

    R_1_TBL_PGAV: Series[float] = pa.Field(ge=0, le=100)
    R_1_TBL_AST: Series[int] = pa.Field()
    R_1_TBL_AREA: Series[float] = pa.Field()

    class Config:
        unique = [
            "RESPONSE_ID",
            "COUNTRY",
            "PRT_TYPE",
        ]
