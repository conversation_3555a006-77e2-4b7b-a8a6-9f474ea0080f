import pandera as pa
from pandera.typing import Series


class BCSurveyTableModel(pa.DataFrameModel):
    RESPONSE_ID: Series[int] = pa.Field()
    PRT_TYPE: Series[str] = pa.Field()
    COUNTRY: Series[str] = pa.Field()

    ID: Series[int] = pa.Field()
    LEVEL: Series[str] = pa.Field()

    AST: Series[int] = pa.Field()
    AGE: Series[float] = pa.Field()

    COV: Series[float] = pa.Field()
    PCOV: Series[float] = pa.Field(ge=0, le=100)
    PGAV: Series[float] = pa.Field(ge=0, le=100, nullable=True)

    class Config:
        unique = [
            "RESPONSE_ID",
            "COUNTRY",
            "PRT_TYPE",
            "ID",
            "LEVEL",
        ]
