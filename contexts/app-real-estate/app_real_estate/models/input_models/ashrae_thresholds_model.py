import pandera as pa
from pandera.typing import Series

from app_real_estate.constants.helper_enumerators import ClimateZone


class AshraeThresholdsModel(pa.DataFrameModel):
    property_type_code: Series[str] = pa.Field()
    climate_zone: Series[str] = pa.Field(isin=[cz.value for cz in ClimateZone])
    ashrae_intensity_threshold: Series[float] = pa.Field(ge=0, coerce=True)

    class Config:
        unique = [
            "property_type_code",
            "climate_zone",
            "ashrae_intensity_threshold",
        ]
