import pandera as pa
from pandera.typing import Series
from app_real_estate.models.input_models.r1_table_model import R1TableModel


class R1TableLenderModel(R1TableModel):
    RESPONSE_ID: Series[int] = pa.Field()
    PRT_TYPE: Series[str] = pa.Field()
    COUNTRY: Series[str] = pa.Field()

    R_1_TBL_AST: Series[int] = pa.Field()
    R_1_TBL_AREA: Series[float] = pa.Field()
    R_1_TBL_PGAV: Series[float] = pa.Field(ge=0, le=100, nullable=True)
