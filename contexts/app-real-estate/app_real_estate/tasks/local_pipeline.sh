python -m real_estate.tasks.PrepareBenchmarkTask \
 --input_data_location asset_data.parquet \
 --output_location output \
 --indicator en1
python -m real_estate.tasks.PrepareBenchmarkTask \
 --input_data_location asset_data.parquet \
 --output_location output \
 --indicator gh1
python -m real_estate.tasks.PrepareBenchmarkTask \
 --input_data_location asset_data.parquet \
 --output_location output \
 --indicator wt1
python -m real_estate.tasks.PrepareBenchmarkTask \
 --input_data_location asset_data.parquet \
 --output_location output \
 --indicator ws1
python -m real_estate.tasks.PrepareBenchmarkTask \
 --input_data_location asset_data_bc.parquet \
 --output_location output \
 --certification_data_location certification_data.parquet \
 --certification_table_location certification_table.parquet \
 --indicator BC1.1
 python -m real_estate.tasks.PrepareBenchmarkTask \
 --input_data_location asset_data_bc.parquet \
 --output_location output \
 --certification_data_location certification_data.parquet \
 --certification_table_location certification_table.parquet \
 --indicator BC1.2
python -m real_estate.tasks.PrepareBenchmarkTask \
 --input_data_location asset_data_bc.parquet \
 --output_location output \
 --energy_ratings_data_location energy_ratings_data.parquet \
 --indicator BC2


python -m real_estate.tasks.BenchmarkTask \
 --input_data_location output/task=prepare_data/indicator=en1/asset_data_cy.parquet \
 --output_location output \
 --indicator en1 \
 --extra_dataframes output/task=prepare_data/indicator=en1/descriptions.parquet
python -m real_estate.tasks.BenchmarkTask \
 --input_data_location output/task=prepare_data/indicator=gh1/asset_data_cy.parquet \
 --output_location output \
 --indicator gh1 \
 --extra_dataframes output/task=prepare_data/indicator=gh1/descriptions.parquet
python -m real_estate.tasks.BenchmarkTask \
 --input_data_location output/task=prepare_data/indicator=wt1/asset_data_cy.parquet \
 --output_location output \
 --indicator wt1 \
 --extra_dataframes output/task=prepare_data/indicator=wt1/descriptions.parquet
python -m real_estate.tasks.BenchmarkTask \
 --input_data_location output/task=prepare_data/indicator=ws1/asset_data_cy.parquet \
 --output_location output \
 --indicator ws1
python -m real_estate.tasks.BenchmarkTask \
 --input_data_location output/task=prepare_data/indicator=BC1.1/certification_data.parquet \
 --output_location output \
 --indicator BC1.1 \
 --extra_dataframes output/task=prepare_data/indicator=BC1.1/descriptions.parquet
python -m real_estate.tasks.BenchmarkTask \
 --input_data_location output/task=prepare_data/indicator=BC1.2/certification_data.parquet \
 --output_location output \
 --indicator BC1.2 \
 --extra_dataframes output/task=prepare_data/indicator=BC1.2/descriptions.parquet
python -m real_estate.tasks.BenchmarkTask \
 --input_data_location output/task=prepare_data/indicator=BC2/asset_data_cy.parquet \
 --output_location output \
 --indicator BC2
