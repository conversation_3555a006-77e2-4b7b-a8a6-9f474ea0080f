python -m real_estate.tasks.PrepareBenchmarkTask \
 --input_data_location asset_data.parquet \
 --output_location output \
 --indicator en1

python -m real_estate.tasks.BenchmarkTask \
 --input_data_location output/task=prepare_data/indicator=en1/asset_data_cy.parquet \
 --output_location output \
 --indicator en1 \
 --extra_dataframes output/task=prepare_data/indicator=en1/descriptions.parquet

python -m real_estate.tasks.IndicatorScorerTask \
  --asset_data_cy output/task=prepare_data/indicator=en1/asset_data_cy.parquet \
  --memberships_folder output/task=benchmark/indicator=en1/sub_type=memberships \
  --indicator en1 \
  --output_location output
