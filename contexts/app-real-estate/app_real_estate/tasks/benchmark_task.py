from app_real_estate.tasks.default_task import DefaultTask
from app_real_estate.transformation.benchmarking.benchmarker import Benchmarker
import argparse
import pandas as pd
from typing import List, Optional, Tuple, Dict
from gresb_utils.setup_logging import setup_logger


class BenchmarkTask(DefaultTask):
    def __init__(self):
        """
        Initializes the task by setting up the logger for logging messages.
        Inherits from the DefaultTask class.
        """
        super().__init__()
        self.logger = setup_logger("BenchmarkTask")

    def run(self, args: Optional[argparse.Namespace] = None) -> None:
        """
        Orchestrates the workflow of the benchmarking task.
        Parses the command-line arguments, processes the data, and writes the output.
        """
        args = args or self.parse_commandline_arguments()
        self.logger.info(f"BenchmarkTask for {args.indicator}")

        input_dataframe, extra_parameters = self.input(args)

        self.logger.info("Start processing")
        groups_dict, memberships_dict = self.process(
            args, input_dataframe, extra_parameters
        )
        self.logger.info(f"Finished processing for {args.indicator}.")

        self.write(args, groups_dict, memberships_dict)
        self.logger.info(f"Task finished for {args.indicator}.")

    def input(
        self, args: argparse.Namespace
    ) -> Tuple[pd.DataFrame, List[pd.DataFrame]]:
        """
        Handles the data input process. Imports the main input dataframe and any extra dataframes provided.

        Args:
            args (argparse.Namespace): Parsed command-line arguments containing file paths.

        Returns:
            Tuple[pd.DataFrame, List[pd.DataFrame]]: A tuple containing the main input dataframe and a list of additional dataframes.
        """
        input_dataframe = self.importer.import_data(args.input_data_location)

        extra_parameters = (
            [
                self.importer.import_data(dataframe)
                for dataframe in args.extra_dataframes
            ]
            if "extra_dataframes" in args
            else []
        )

        return input_dataframe, extra_parameters

    def parse_commandline_arguments(self) -> argparse.Namespace:
        """
        Parses the command-line arguments necessary for running the benchmarking task.

        Returns:
            argparse.Namespace: Parsed arguments including input and output locations, benchmark indicator, and extra dataframes.
        """
        # Parse script arguments
        parser = argparse.ArgumentParser(
            description="Run Real Estate Benchmark scoring model"
        )
        parser.add_argument(
            "--input_data_location",
            type=str,
            help="Location of the input data file.",
            required=True,
        )
        parser.add_argument(
            "--output_location",
            type=str,
            help="Location where output files will be stored.",
            required=True,
        )
        parser.add_argument(
            "--indicator",
            type=str,
            help="Indicator of benchmark to run (e.g., 'en1', 'gh1', 'wt1', 'ws1').",
            required=True,
        )
        parser.add_argument(
            "--extra_dataframes",
            nargs="*",
            help="Additional dataframes to be used in benchmarking.",
        )

        return parser.parse_args()

    def process(
        self,
        args: argparse.Namespace,
        input_dataframe: pd.DataFrame,
        extra_parameters: List[pd.DataFrame],
    ) -> Tuple[Dict[str, pd.DataFrame], Dict[str, pd.DataFrame]]:
        """
        Processes the input data and runs the appropriate benchmarking model based on the specified indicator.

        Args:
            args (argparse.Namespace): Command-line arguments containing information about the benchmarking indicator.
            input_dataframe (pd.DataFrame): Main input data for benchmarking.
            extra_parameters (List[pd.DataFrame]): List of additional dataframes to assist in benchmarking.

        Returns:
            Tuple[Dict[str, pd.DataFrame], Dict[str, pd.DataFrame]]: A tuple containing two dictionaries:
                - groups_dict: Benchmark groups data.
                - memberships_dict: Memberships data.
        """
        benchmarker = Benchmarker()

        if args.indicator == "en1":
            (groups_dict, memberships_dict) = benchmarker.benchmark_metrics_en1(
                input_dataframe, *extra_parameters
            )
        elif args.indicator == "gh1":
            (groups_dict, memberships_dict) = benchmarker.benchmark_metrics_gh1(
                input_dataframe, *extra_parameters
            )
        elif args.indicator == "wt1":
            (groups_dict, memberships_dict) = benchmarker.benchmark_metrics_wt1(
                input_dataframe, *extra_parameters
            )
        elif args.indicator == "ws1":
            (groups_dict, memberships_dict) = benchmarker.benchmark_metrics_ws1(
                input_dataframe
            )
        # elif args.indicator in ["BC1.1", "BC1.2"]:
        #     (groups_dict, memberships_dict) = benchmarker.benchmark_metrics_bc1(
        #         input_dataframe, extra_parameters[0], args.indicator
        #     )
        # elif args.indicator == "BC2":
        #     (groups_dict, memberships_dict) = benchmarker.benchmark_metrics_bc2(
        #         input_dataframe
        #     )
        else:
            raise NotImplementedError(f"'{args.indicator}' is not implemented")

        return groups_dict, memberships_dict

    def write(
        self,
        args: argparse.Namespace,
        groups_dict: Dict[str, pd.DataFrame],
        memberships_dict: Dict[str, pd.DataFrame],
    ) -> None:
        """
        Writes the benchmarking results to the specified output location in parquet format.

        Args:
            args (argparse.Namespace): Command-line arguments with output location and benchmarking indicator.
            groups_dict (Dict[str, pd.DataFrame]): Benchmark groups data to be written.
            memberships_dict (Dict[str, pd.DataFrame]): Benchmark memberships data to be written.
        """
        self.exporter.save_dict_to_parquet(
            groups_dict,
            args.output_location,
            f"task=benchmark/indicator={args.indicator}/sub_type=groups",
        )

        self.exporter.save_dict_to_parquet(
            memberships_dict,
            args.output_location,
            f"task=benchmark/indicator={args.indicator}/sub_type=memberships",
        )


if __name__ == "__main__":
    BenchmarkTask().run()
