python -m real_estate.tasks.PrepareBenchmarkTask \
 --input_data_location asset_data.parquet \
 --output_location output \
 --indicator ws1

python -m real_estate.tasks.BenchmarkTask \
 --input_data_location output/task=prepare_data/indicator=ws1/asset_data_cy.parquet \
 --output_location output \
 --indicator ws1

python -m real_estate.tasks.IndicatorScorerTask \
  --asset_data_cy output/task=prepare_data/indicator=ws1/asset_data_cy.parquet \
  --memberships_folder output/task=benchmark/indicator=ws1/sub_type=memberships \
  --indicator ws1 \
  --output_location output
