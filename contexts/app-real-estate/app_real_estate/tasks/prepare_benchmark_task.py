import argparse
from app_real_estate.tasks.default_task import Default<PERSON><PERSON>
from app_real_estate.transformation.metric_calculation.data_preparer import (
    DataPreparer,
)
from typing import Optional
import pandas as pd
from gresb_utils.setup_logging import setup_logger


class PrepareBenchmarkTask(DefaultTask):
    """
    PrepareBenchmarkTask is responsible for preparing benchmark data for a specific indicator of real estate data.
    It reads input data, processes it using the appropriate transformations, and writes the transformed data to the specified output location.
    """

    def __init__(self):
        """
        Initializes the task by setting up the logger for logging messages.
        """
        super().__init__()
        self.logger = setup_logger("PrepareBenchmarkTask")

    def run(self, args: Optional[argparse.Namespace] = None) -> None:
        """
        Executes the task by running the following steps:
        1. Parses command-line arguments.
        2. Reads the input data.
        3. Processes the input data based on the 'indicator' argument.
        4. Writes the processed data to the output location.
        """
        args = args or self.parse_commandline_arguments()

        self.logger.info(f"PrepareBenchmarkTask for {args.indicator}.")

        # Process data
        data = self.process(args)

        self.logger.info(f"Finished processing {args.indicator}.")

        # Write the processed data
        self.write(args, data)
        self.logger.info(f"Task finished for {args.indicator}.")

    def parse_commandline_arguments(self) -> argparse.Namespace:
        """
        Parses command-line arguments for input data location, output location, and the indicator of data to process.

        Returns:
            argparse.Namespace: Contains the parsed arguments.
        """
        parser = argparse.ArgumentParser(
            description="Run Real Estate Prepare Benchmark scoring model"
        )
        parser.add_argument(
            "--input_data_location",
            type=str,
            help="Path to the input data file.",
            required=True,
        )
        parser.add_argument(
            "--output_location",
            type=str,
            help="Directory where output data will be saved.",
            required=True,
        )
        parser.add_argument(
            "--indicator",
            type=str,
            help="Indicator of data processing to perform (e.g., 'en1', 'gh1', etc.).",
            required=True,
        )
        args = parser.parse_known_args()[0]

        if args.indicator.startswith("BC1"):
            parser.add_argument(
                "--certification_data_location",
                type=str,
                required=True,
            )
            parser.add_argument(
                "--certification_table_location",
                type=str,
                required=True,
            )
        elif args.indicator.startswith("BC2"):
            parser.add_argument(
                "--energy_ratings_data_location",
                type=str,
                required=True,
            )
        elif args.indicator.startswith("EN1"):
            parser.add_argument(
                "--weather_stations_data_location",
                type=str,
                required=True,
            )
            parser.add_argument(
                "--ashrae_thresholds_data_location",
                type=str,
                required=True,
            )

        return parser.parse_args()

    def process(self, args: argparse.Namespace) -> dict[str, pd.DataFrame]:
        """
        Processes the input data based on the specified indicator argument. It uses different preparation methods
        from the DataPreparer class depending on the indicator.

        Args:
            args (argparse.Namespace): Parsed command-line arguments.

        Returns:
            Tuple: Returns the processed asset data, asset data CY, and any additional parameters.
        """
        datapreparer = DataPreparer()

        # Mapping of indicators to specific data preparation methods
        lookup = {
            "gh1": datapreparer.prepare_data_gh1,
            "wt1": datapreparer.prepare_data_wt1,
            "ws1": datapreparer.prepare_data_ws1,
        }

        input_dataframe = self.importer.import_data(args.input_data_location)
        weather_stations = self.importer.import_data(
            args.weather_stations_data_location
        )
        ashrae_thresholds = self.importer.import_data(
            args.ashrae_thresholds_data_location
        )

        # Apply the corresponding method for the indicator
        if args.indicator in lookup:
            data = lookup[args.indicator](input_dataframe)
        elif args.indicator == "en1":
            data = datapreparer.prepare_data_en1(
                input_dataframe, weather_stations, ashrae_thresholds
            )
        # elif args.indicator.startswith("BC1"):
        #     data = datapreparer.prepare_data_bc1(
        #         input_dataframe,
        #         self.importer.import_data(args.certification_data_location),
        #         self.importer.import_data(args.certification_table_location),
        #     )
        # elif args.indicator.startswith("BC2"):
        #     data = datapreparer.prepare_data_bc2(
        #         input_dataframe,
        #         self.importer.import_data(args.energy_ratings_data_location),
        #     )
        else:
            raise NotImplementedError(f"'{args.indicator}' is not implemented")

        return data

    def write(self, args: argparse.Namespace, data: dict[str, pd.DataFrame]) -> None:
        """
        Writes the processed data to the specified output location in a parquet format. If there are any
        additional parameters, they are also written to the output.

        Args:
            args (argparse.Namespace): Parsed command-line arguments.
            asset_data (Any): The primary processed asset data.
            asset_data_cy (Any): The processed asset data CY.
            additional_params (List[Any]): Additional parameters (if any) produced during data processing.
        """
        # Save the main data to a parquet file
        self.exporter.save_dict_to_parquet(
            data,
            args.output_location,
            f"task=prepare_data/indicator={args.indicator}",
        )


if __name__ == "__main__":
    PrepareBenchmarkTask().run()
