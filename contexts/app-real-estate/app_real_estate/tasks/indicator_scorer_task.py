import argparse
from typing import Dict, Optional, Tuple
from app_real_estate.tasks.default_task import Default<PERSON><PERSON>
from app_real_estate.transformation.scoring.indicator_scorer import IndicatorScorer
from gresb_utils.setup_logging import setup_logger
import pandas as pd


class IndicatorScorerTask(DefaultTask):
    def __init__(self):
        """
        Initializes the IndicatorScorerTask by setting up the logger for logging messages.
        Inherits from DefaultTask.
        """
        super().__init__()
        self.logger = setup_logger("IndicatorScorerTask")

    def run(self, args: Optional[argparse.Namespace] = None) -> None:
        """
        Main execution method for the task.

        Parses command-line arguments, loads input data, processes scoring
        based on the specified indicator, and writes the output data.
        """
        args = args or self.parse_commandline_arguments()
        self.logger.info(f"Starting IndicatorScorerTask for {args.indicator}")

        # Load the input data
        asset_data_cy, memberships = self.input(args)

        # Process the data to generate scores
        self.logger.info("Starting processing of scores")
        scores = self.process(args, asset_data_cy, memberships)
        self.logger.info(f"Finished processing scores for {args.indicator}")

        # Write output data
        self.write(args, scores)
        self.logger.info(f"Task completed for {args.indicator}")

    def parse_commandline_arguments(self) -> argparse.Namespace:
        """
        Parses the command-line arguments required for running the indicator scorer task.

        Returns:
            argparse.Namespace: Parsed arguments including input and output file locations,
                                as well as the indicator type to process.
        """
        parser = argparse.ArgumentParser(description="Run the Indicator Scorer model")
        parser.add_argument(
            "--asset_data_cy",
            type=str,
            help="File path for the asset data of the current year.",
            required=True,
        )
        parser.add_argument(
            "--memberships_folder",
            type=str,
            help="Directory path containing membership files.",
            required=True,
        )
        parser.add_argument(
            "--indicator",
            type=str,
            help="Indicator type to score (e.g., 'en1', 'gh1', 'wt1', 'ws1').",
            required=True,
        )
        parser.add_argument(
            "--output_location",
            type=str,
            help="File path for storing the output scores.",
            required=True,
        )

        return parser.parse_args()

    def input(
        self, args: argparse.Namespace
    ) -> Tuple[pd.DataFrame, Dict[str, pd.DataFrame]]:
        """
        Loads the input data necessary for the task, including the main asset data
        and additional membership data.

        Args:
            args (argparse.Namespace): Parsed command-line arguments containing file paths.

        Returns:
            Tuple[pd.DataFrame, Dict[str, pd.DataFrame]]: A tuple containing the main
                                                          asset data DataFrame and a dictionary
                                                          of membership DataFrames.
        """
        asset_data_cy = self.importer.import_data(args.asset_data_cy)
        memberships = self.importer.import_folder_data(args.memberships_folder)

        return asset_data_cy, memberships

    def process(
        self,
        args: argparse.Namespace,
        asset_data_cy: pd.DataFrame,
        memberships: Dict[str, pd.DataFrame],
    ) -> pd.DataFrame:
        """
        Processes the input data to score assets based on the specified indicator.

        Args:
            args (argparse.Namespace): Parsed arguments including the indicator type.
            asset_data_cy (pd.DataFrame): DataFrame containing the main asset data.
            memberships (Dict[str, pd.DataFrame]): Dictionary of membership DataFrames.

        Returns:
            pd.DataFrame: DataFrame containing scores based on the selected indicator.

        Raises:
            NotImplementedError: If the indicator is not recognized.
        """
        scorer = IndicatorScorer()

        # Select scoring method based on indicator type
        if args.indicator == "en1":
            return scorer.score_en1(asset_data_cy, memberships)
        elif args.indicator == "gh1":
            return scorer.score_gh1(asset_data_cy, memberships)
        elif args.indicator == "wt1":
            return scorer.score_wt1(asset_data_cy, memberships)
        elif args.indicator == "ws1":
            return scorer.score_ws1(asset_data_cy, memberships)
        else:
            raise NotImplementedError(
                f"Indicator '{args.indicator}' is not implemented"
            )

    def write(self, args: argparse.Namespace, scores: pd.DataFrame) -> None:
        """
        Saves the computed scores to a specified output location in a Parquet format.

        Args:
            args (argparse.Namespace): Parsed arguments containing the output file path and indicator.
            scores (pd.DataFrame): DataFrame containing the scored results.
        """
        data_dict = {"scores": scores}

        # Save the scores DataFrame to Parquet
        self.exporter.save_dict_to_parquet(
            data_dict,
            args.output_location,
            f"task=scores/indicator={args.indicator}",
        )


if __name__ == "__main__":
    IndicatorScorerTask().run()
