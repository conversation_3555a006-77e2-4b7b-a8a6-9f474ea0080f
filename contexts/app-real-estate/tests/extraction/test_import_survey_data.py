import pandas as pd
import pytest

from app_real_estate.extraction.import_survey_data import (
    R1T<PERSON><PERSON><PERSON>atter,
    BCTableFormatter,
)
from app_real_estate.models.input_models.bc_survey_tables_model import (
    BCSurveyTableModel,
)
from app_real_estate.models.input_models.r1_table_model import R1TableModel


@pytest.fixture
def r1_table_data():
    return pd.DataFrame(
        {
            "response_id": [1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2],
            "variable": [
                "R_1_TBL:HAS_AGGREGATED?",
                "R_1_TBL_AST_BR.INRW",
                "R_1_TBL_AREA_BR.INRW",
                "R_1_TBL_PGAV_BR.INRW",
            ]
            + [
                "R_1_TBL:HAS_AGGREGATED?",
                "R_1_TBL_AST_FR.INRW",
                "R_1_TBL_AREA_FR.INRW",
                "R_1_TBL_PGAV_FR.INRW",
            ]
            * 2,
            "value": [
                "True",
                "8",
                "100",
                "50",
                "True",
                "10",
                "100",
                "50",
                "True",
                "27",
                "80",
                "100",
            ],
        }
    )


@pytest.fixture
def bc_table_data():
    return pd.DataFrame(
        {
            "response_id": [1] * 16 + [2] * 8,
            "variable": [
                "BC_1.1_TBL:HAS_AGGREGATED?",
                "BC_1.1_TBL_AGE_1_BR.INRW",
                "BC_1.1_TBL_AST_1_BR.INRW",
                "BC_1.1_TBL_ID_1_BR.INRW",
                "BC_1.1_TBL_LEVEL_1_BR.INRW",
                "BC_1.1_TBL_COV_1_BR.INRW",
                "BC_1.1_TBL_PCOV_1_BR.INRW",
                "BC_1.1_TBL_PGAV_1_BR.INRW",
            ]
            + [
                "BC_1.1_TBL:HAS_AGGREGATED?",
                "BC_1.1_TBL_AGE_1_FR.INRW",
                "BC_1.1_TBL_AST_1_FR.INRW",
                "BC_1.1_TBL_ID_1_FR.INRW",
                "BC_1.1_TBL_LEVEL_1_FR.INRW",
                "BC_1.1_TBL_COV_1_FR.INRW",
                "BC_1.1_TBL_PCOV_1_FR.INRW",
                "BC_1.1_TBL_PGAV_1_FR.INRW",
            ]
            * 2,
            "value": [
                "True",
                "8.8",
                "10",
                "989",
                "Gold",
                "80.1",
                "100",
                "50",
                "True",
                "10.1",
                "10",
                "989",
                "Gold",
                "70",
                "100",
                "50",
                "True",
                "1",
                "5",
                "989",
                "Silver",
                "80.5",
                "100",
                "100",
            ],
        }
    )


def test_format_r1_table_data(r1_table_data):
    expected = pd.DataFrame(
        {
            R1TableModel.RESPONSE_ID: [1, 1, 2],
            R1TableModel.COUNTRY: ["BR", "FR", "FR"],
            R1TableModel.PRT_TYPE: ["INRW"] * 3,
            R1TableModel.R_1_TBL_AREA: [100.0, 100.0, 80.0],
            R1TableModel.R_1_TBL_AST: [8, 10, 27],
            R1TableModel.R_1_TBL_PGAV: [50.0, 50.0, 100.0],
        }
    )
    result = R1TableFormatter.format(r1_table_data)
    pd.testing.assert_frame_equal(result, expected)


def test_format_bc_table_data(bc_table_data):
    expected = pd.DataFrame(
        {
            BCSurveyTableModel.RESPONSE_ID: [1, 1, 2],
            BCSurveyTableModel.COUNTRY: ["BR", "FR", "FR"],
            BCSurveyTableModel.PRT_TYPE: ["INRW"] * 3,
            BCSurveyTableModel.AGE: [8.8, 10.1, 1.0],
            BCSurveyTableModel.AST: [10, 10, 5],
            BCSurveyTableModel.COV: [80.1, 70.0, 80.5],
            BCSurveyTableModel.ID: [989, 989, 989],
            BCSurveyTableModel.LEVEL: ["Gold", "Gold", "Silver"],
            BCSurveyTableModel.PCOV: [100.0, 100.0, 100.0],
            BCSurveyTableModel.PGAV: [50.0, 50.0, 100.0],
        }
    )
    result = BCTableFormatter.format(bc_table_data, "BC_1.1_TBL_")
    pd.testing.assert_frame_equal(result, expected)
