import pandas as pd
import pytest
import numpy as np
from app_real_estate.transformation.metric_aggregation.aggregate_lfl_metrics import (
    LFLMetricAggregator,
)
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.water_columns as wc
import app_real_estate.constants.column_names.ghg_columns as gc
import app_real_estate.constants.column_names.score_columns as sc


@pytest.fixture
def mock_asset_data():
    return pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3, 4, 5, 6],
            ac.response_id: [1, 1, 1, 2, 2, 2],
            ac.property_sector: ["Residential"] * 6,
            ac.country: ["NL", "NL", "FR", "NL", "NL", "NL"],
            ac.standing_investment_for_aggregation: [True] * 6,
            ac.asset_ownership: [50, 100, 100, 100, 100, 100],
            sc.score_en1_energy_efficiency: [np.nan] * 4 + [1.0, np.nan],
            ec.en_lfl_outlier_status: ["none"] * 4 + ["rejected"] + ["accepted"],
            ec.en_lfl_p: [-20.0, -10.0, -20.0, -20.0, -50.0, -30.0],
            ec.en_lfl_percent_change_lc: [-20.0, -10.0, -20.0, -20.0, -50.0, -30.0],
            ec.en_lfl_percent_change_tc: [-20.0, -10.0, -20.0, -20.0, -50.0, -30.0],
            ec.en_lfl_abs: [8.0, 9.0, 8.0, 8.0, 5.0, 7.0],
            ec.en_lfl_abs_lc: [8.0, 9.0, 8.0, 8.0, 5.0, 7.0],
            ec.en_lfl_abs_tc: [8.0, 9.0, 8.0, 8.0, 5.0, 7.0],
            ec.en_lfl_abs_ly: [10.0] * 6,
            ec.en_lfl_abs_lc_ly: [10.0] * 6,
            ec.en_lfl_abs_tc_ly: [10.0] * 6,
            wc.wat_lfl_outlier_status: ["none"] * 4 + ["rejected"] + ["accepted"],
            wc.wat_lfl_p: [-20.0, -10.0, -20.0, -20.0, -50.0, -30.0],
            wc.wat_lfl_percent_change_lc: [-20.0, -10.0, -20.0, -20.0, -50.0, -30.0],
            wc.wat_lfl_percent_change_tc: [-20.0, -10.0, -20.0, -20.0, -50.0, -30.0],
            wc.wat_lfl_abs: [8.0, 9.0, 8.0, 8.0, 5.0, 7.0],
            wc.wat_lfl_abs_lc: [8.0, 9.0, 8.0, 8.0, 5.0, 7.0],
            wc.wat_lfl_abs_tc: [8.0, 9.0, 8.0, 8.0, 5.0, 7.0],
            wc.wat_lfl_abs_ly: [10.0] * 6,
            wc.wat_lfl_abs_lc_ly: [10.0] * 6,
            wc.wat_lfl_abs_tc_ly: [10.0] * 6,
            gc.ghg_lfl_outlier_status: ["none"] * 4 + ["rejected"] + ["accepted"],
            gc.ghg_lfl_p: [-20.0, -10.0, -20.0, -20.0, -50.0, -30.0],
            gc.ghg_lfl_percent_change_s12: [-20.0, -10.0, -20.0, -20.0, -50.0, -30.0],
            gc.ghg_lfl_percent_change_s3: [-20.0, -10.0, -20.0, -20.0, -50.0, -30.0],
            gc.ghg_lfl_abs: [8.0, 9.0, 8.0, 8.0, 5.0, 7.0],
            gc.ghg_lfl_abs_s12: [8.0, 9.0, 8.0, 8.0, 5.0, 7.0],
            gc.ghg_lfl_abs_s3: [8.0, 9.0, 8.0, 8.0, 5.0, 7.0],
            gc.ghg_lfl_abs_ly: [10.0] * 6,
            gc.ghg_lfl_abs_s12_ly: [10.0] * 6,
            gc.ghg_lfl_abs_s3_ly: [10.0] * 6,
        }
    )


@pytest.fixture
def lfl_metric_aggregator():
    return LFLMetricAggregator(["response_id", "property_sector", "country"])


def test_aggregate_lfl_absolute_change_equal_df(lfl_metric_aggregator, mock_asset_data):
    result = lfl_metric_aggregator.aggregate_lfl_absolute_change(mock_asset_data)

    expected = pd.DataFrame(
        {
            ac.response_id: [1, 1, 2],
            ac.property_sector: "Residential",
            ac.country: ["FR", "NL", "NL"],
            ec.en_lfl_abs_change: [-2.0, -2.0, -5.0],
            gc.ghg_lfl_abs_change: [-2.0, -2.0, -5.0],
            wc.wat_lfl_abs_change: [-2.0, -2.0, -5.0],
        }
    )

    pd.testing.assert_frame_equal(result, expected)


def test_aggregate_lfl_absolute_change_missing_cy_col(
    lfl_metric_aggregator, mock_asset_data
):
    mock_asset_data.drop(columns=["en_lfl_abs"], inplace=True)

    pytest.raises(
        ValueError,
        lfl_metric_aggregator.aggregate_lfl_absolute_change,
        mock_asset_data,
    )


def test_aggregate_lfl_percent_change_equal_df(lfl_metric_aggregator, mock_asset_data):
    result = lfl_metric_aggregator.aggregate_lfl_percent_change(mock_asset_data)

    expected = pd.DataFrame(
        {
            ac.response_id: [1, 1, 2],
            ac.property_sector: "Residential",
            ac.country: ["FR", "NL", "NL"],
            ec.en_lfl_p: [-20.0, -13.3333, -25.0],
            gc.ghg_lfl_p: [-20.0, -13.3333, -25.0],
            wc.wat_lfl_p: [-20.0, -13.3333, -25.0],
            ec.en_lfl_percent_change_lc: [-20.0, -13.3333, -25.0],
            gc.ghg_lfl_percent_change_s12: [-20.0, -13.3333, -25.0],
            wc.wat_lfl_percent_change_lc: [-20.0, -13.3333, -25.0],
            ec.en_lfl_percent_change_tc: [-20.0, -13.3333, -25.0],
            gc.ghg_lfl_percent_change_s3: [-20.0, -13.3333, -25.0],
            wc.wat_lfl_percent_change_tc: [-20.0, -13.3333, -25.0],
        }
    )

    pd.testing.assert_frame_equal(result, expected)


def test_aggregate_lfl_percent_change_no_lfl_data_equal_df(
    lfl_metric_aggregator, mock_asset_data
):
    mock_asset_data_no_lfl_data = pd.DataFrame(
        [
            [1, 1, "Residential", "NL", True, 100, np.nan]
            + (["none"] + [np.nan] * 9) * 3
        ],
        columns=mock_asset_data.columns,
    )
    result = lfl_metric_aggregator.aggregate_lfl_percent_change(
        mock_asset_data_no_lfl_data
    )

    expected = pd.DataFrame(
        {
            ac.response_id: [1],
            ac.property_sector: "Residential",
            ac.country: ["NL"],
            ec.en_lfl_p: [np.nan],
            gc.ghg_lfl_p: [np.nan],
            wc.wat_lfl_p: [np.nan],
            ec.en_lfl_percent_change_lc: [np.nan],
            gc.ghg_lfl_percent_change_s12: [np.nan],
            wc.wat_lfl_percent_change_lc: [np.nan],
            ec.en_lfl_percent_change_tc: [np.nan],
            gc.ghg_lfl_percent_change_s3: [np.nan],
            wc.wat_lfl_percent_change_tc: [np.nan],
        }
    )

    pd.testing.assert_frame_equal(result, expected)


def test_aggregate_lfl_percent_change_benchmark_equal_df(
    lfl_metric_aggregator, mock_asset_data
):
    # Here we mainly test that the [-30, 30] filter is applied.
    result = lfl_metric_aggregator.aggregate_lfl_percent_change(
        mock_asset_data, for_benchmark=True
    )

    expected = pd.DataFrame(
        {
            ac.response_id: [1, 1, 2],
            ac.property_sector: "Residential",
            ac.country: ["FR", "NL", "NL"],
            ec.en_lfl_percent_change_lc_benchmark: [-20.0, -16.66666, -16.66666],
            gc.ghg_lfl_percent_change_s12_benchmark: [-20.0, -16.66666, -16.66666],
            wc.wat_lfl_percent_change_lc_benchmark: [-20.0, -16.66666, -16.66666],
            ec.en_lfl_percent_change_tc_benchmark: [-20.0, -16.66666, -16.66666],
            gc.ghg_lfl_percent_change_s3_benchmark: [-20.0, -16.66666, -16.66666],
            wc.wat_lfl_percent_change_tc_benchmark: [-20.0, -16.66666, -16.66666],
        }
    )

    pd.testing.assert_frame_equal(result, expected)


def test_aggregate_lfl_asset_count(lfl_metric_aggregator, mock_asset_data):
    result = lfl_metric_aggregator.aggregate_lfl_asset_count(mock_asset_data)
    expected = pd.DataFrame(
        {
            ac.response_id: [1, 1, 2],
            ac.property_sector: "Residential",
            ac.country: ["FR", "NL", "NL"],
            ec.en_lfl_asset_count: [1, 2, 2],
            gc.ghg_lfl_asset_count: [1, 2, 2],
            wc.wat_lfl_asset_count: [1, 2, 2],
        }
    )
    pd.testing.assert_frame_equal(result, expected)


@pytest.fixture
def mock_asset_data_area():
    return pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3, 4, 5, 6],
            ac.response_id: [1, 1, 1, 2, 2, 2],
            ac.property_sector: ["Residential"] * 6,
            ac.asset_size_m2: [100] * 6,
            ac.asset_size_sqft: [100] * 6,
            ac.country: ["NL", "NL", "FR", "NL", "NL", "NL"],
            ac.standing_investment_for_aggregation: [True] * 6,
            ac.asset_ownership: [50, 100, 100, 100, 100, 100],
            sc.score_en1_energy_efficiency: [1.0] + [np.nan] * 3 + [1.0, np.nan],
            ec.en_lfl_outlier_status: ["none"] * 4 + ["rejected"] + ["accepted"],
            wc.wat_lfl_outlier_status: ["rejected"]
            + ["none"] * 3
            + ["rejected"]
            + ["accepted"],
            gc.ghg_lfl_outlier_status: ["rejected"]
            + ["none"] * 3
            + ["rejected"]
            + ["accepted"],
            ec.en_lfl_area_cov_p: [np.nan, 90, 100, 70, 100, 100],
            wc.wat_lfl_area_cov_p: [100, np.nan, np.nan, 70, 100, 100],
            gc.ghg_lfl_area_cov_p: [100, np.nan, np.nan, 70, 100, 100],
            ec.en_lfl_p: [np.nan, -10.0, -20.0, -20.0, -50.0, -30.0],
            wc.wat_lfl_p: [-20.0, np.nan, np.nan, -20.0, -50.0, -30.0],
            gc.ghg_lfl_p: [-20.0, np.nan, np.nan, -20.0, -50.0, -30.0],
        }
    )


def test_aggregate_lfl_area_equal_df(lfl_metric_aggregator, mock_asset_data_area):
    result = lfl_metric_aggregator.aggregate_lfl_area(mock_asset_data_area)
    expected = pd.DataFrame(
        {
            ac.response_id: [1, 1, 2],
            ac.property_sector: "Residential",
            ac.country: ["FR", "NL", "NL"],
            ec.en_lfl_area_m2: [100.0, 90.0, 170.0],
            ec.en_lfl_area_sqft: [100.0, 90.0, 170.0],
            wc.wat_lfl_area_m2: [0.0, 0.0, 170.0],
            wc.wat_lfl_area_sqft: [0.0, 0.0, 170.0],
            gc.ghg_lfl_area_m2: [0.0, 0.0, 170.0],
            gc.ghg_lfl_area_sqft: [0.0, 0.0, 170.0],
        }
    )

    pd.testing.assert_frame_equal(result, expected)


def test_aggregate_lfl_area_missing_area_cols(
    lfl_metric_aggregator, mock_asset_data_area
):
    mock_asset_data_area.drop(
        columns=[ac.asset_size_m2, ac.asset_size_sqft], inplace=True
    )
    pytest.raises(
        ValueError, lfl_metric_aggregator.aggregate_lfl_area, mock_asset_data_area
    )


@pytest.fixture
def mock_asset_data_area_p():
    return pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3, 4, 5, 6],
            ac.response_id: [1, 1, 1, 2, 2, 2],
            ac.property_sector: ["Residential"] * 6,
            ac.asset_size: [100] * 6,
            ac.country: ["NL", "NL", "FR", "NL", "NL", "NL"],
            ac.standing_investment_for_aggregation: [True] * 6,
            ac.asset_ownership: [50, 100, 100, 100, 100, 100],
            sc.score_en1_energy_efficiency: [1.0] + [np.nan] * 3 + [1.0, np.nan],
            ec.en_lfl_outlier_status: ["none"] * 4 + ["rejected"] + ["accepted"],
            wc.wat_lfl_outlier_status: ["rejected"]
            + ["none"] * 3
            + ["rejected"]
            + ["accepted"],
            gc.ghg_lfl_outlier_status: ["rejected"]
            + ["none"] * 3
            + ["rejected"]
            + ["accepted"],
            ec.en_lfl_p: [np.nan, -10.0, -20.0, -20.0, -50.0, -30.0],
            wc.wat_lfl_p: [-20.0, np.nan, np.nan, -20.0, -50.0, -30.0],
            gc.ghg_lfl_p: [-20.0, np.nan, np.nan, -20.0, -50.0, -30.0],
            ec.en_lfl_area_cov_p: [np.nan, 90, 100, 70, 100, 100],
            wc.wat_lfl_area_cov_p: [100, np.nan, np.nan, 70, 100, 100],
            gc.ghg_lfl_area_cov_p: [100, np.nan, np.nan, 70, 100, 100],
            ec.en_lfl_area_cov_p_lc: [np.nan, 90, 100, 70, 100, np.nan],
            wc.wat_lfl_area_cov_p_lc: [100, np.nan, np.nan, 70, 100, np.nan],
            gc.ghg_lfl_area_cov_p_s12: [100, np.nan, np.nan, 70, 100, np.nan],
            ec.en_lfl_area_cov_p_tc: [np.nan, 90, 100, 70, 100, 100],
            wc.wat_lfl_area_cov_p_tc: [100, np.nan, np.nan, 70, 100, 100],
            gc.ghg_lfl_area_cov_p_s3: [100, np.nan, np.nan, 70, 100, 100],
            ec.en_area_weight_lc: [np.nan, 90, 80, 70, 100, np.nan],
            wc.wat_area_weight_lc: [50, np.nan, np.nan, 70, 100, np.nan],
            gc.ghg_area_weight_s12: [50, np.nan, np.nan, 70, 100, np.nan],
            ec.en_area_weight_tc: [np.nan, 10, 20, 30, np.nan, 100],
            wc.wat_area_weight_tc: [50, np.nan, np.nan, 30, np.nan, 100],
            gc.ghg_area_weight_s3: [50, np.nan, np.nan, 30, np.nan, 100],
        }
    )


def test_aggregate_lfl_area_percentage_equal_df(
    lfl_metric_aggregator, mock_asset_data_area_p
):
    result = lfl_metric_aggregator.aggregate_lfl_area_percentage(mock_asset_data_area_p)
    expected = pd.DataFrame(
        {
            ac.response_id: [1, 1, 2],
            ac.property_sector: "Residential",
            ac.country: ["FR", "NL", "NL"],
            ec.en_lfl_area_p: [100.0, 90.0, 85.0],
            gc.ghg_lfl_area_p: [0.0, 0.0, 85.0],
            wc.wat_lfl_area_p: [0.0, 0.0, 85.0],
            ec.en_lfl_area_p_lc: [100.0, 90.0, 70.0],
            gc.ghg_lfl_area_p_s12: [0.0, 0.0, 70.0],
            wc.wat_lfl_area_p_lc: [0.0, 0.0, 70.0],
            ec.en_lfl_area_p_tc: [100.0, 90.0, 93.0769],
            gc.ghg_lfl_area_p_s3: [0.0, 0.0, 93.0769],
            wc.wat_lfl_area_p_tc: [0.0, 0.0, 93.0769],
        }
    )

    pd.testing.assert_frame_equal(result, expected)
