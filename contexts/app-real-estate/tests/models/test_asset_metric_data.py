import pytest
from app_real_estate.models.asset_metric_data import AssetMetricData
import app_real_estate.constants.column_names.asset_characteristics_columns as asc
import pandas as pd
import numpy as np


@pytest.fixture
def asset_metric_data():
    return pd.DataFrame(
        {
            asc.response_id: [1, 2],
            asc.country: ["Country1", "Country2"],
            asc.property_type_code: ["Subtype1", "Subtype2"],
            asc.asset_size_m2: [100.0, 200.0],
            asc.asset_gav: [1000000.0, 2000000.0],
        }
    )


def test_validate_asset_ownership_is_not_na(asset_metric_data):
    asset_metric_data["asset_ownership"] = [50, np.nan]

    with pytest.raises(ValueError):
        AssetMetricData.parse_df(
            dataframe=asset_metric_data, errors="raise", verbose=False
        )


def test_filter_asset_ownership_is_not_na(asset_metric_data):
    asset_metric_data["asset_ownership"] = [50, np.nan]

    amd = AssetMetricData.parse_df(
        dataframe=asset_metric_data, errors="filter", verbose=False
    )

    assert amd.equals(asset_metric_data.loc[:0])


def test_validate_asset_gav_is_not_na(asset_metric_data):
    asset_metric_data[asc.asset_gav] = [1000000.0, np.nan]

    with pytest.raises(ValueError):
        AssetMetricData.parse_df(
            dataframe=asset_metric_data, errors="raise", verbose=False
        )


def test_validate_asset_ownership_filter_out_of_range(asset_metric_data):
    asset_metric_data["asset_ownership"] = [50, 120]

    amd = AssetMetricData.parse_df(dataframe=asset_metric_data, errors="filter")

    assert amd.equals(asset_metric_data.loc[:0])
