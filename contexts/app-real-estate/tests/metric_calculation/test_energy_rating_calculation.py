import pandas as pd
import numpy as np
import pytest
from app_real_estate.transformation.metric_calculation.energy_ratings_calculations import (
    EnergyRatingsCalculations as erc,
)
import app_real_estate.constants.column_names.energy_rating_columns as ec


def test_energy_rating_calculation():
    mock_data = pd.DataFrame(
        {
            ec.asset_energy_rating_id: list(range(1, 11)),
            ec.portfolio_asset_id: [1, 1, 1, 2, 2, 3, 3, 3, 4, 4],
            ec.asset_size_m2: [
                100,
                100,
                100,
                2500,
                2500,
                800,
                800,
                800,
                1250,
                1250,
            ],
            ec.covered_floor_area_m2: [
                79,
                21,
                0,
                2000,
                100,
                780,
                20,
                np.nan,
                1000,
                200,
            ],
        }
    )

    mock_data = erc.add_data_coverage(mock_data)

    expected_coverage = [79, 21, 0, 80, 4, 97.5, 2.5, np.nan, 80, 16]

    assert mock_data[ec.coverage].tolist() == pytest.approx(
        expected_coverage, rel=1e-4, nan_ok=True
    ), "Test for energy rating coverage calculation failed."
