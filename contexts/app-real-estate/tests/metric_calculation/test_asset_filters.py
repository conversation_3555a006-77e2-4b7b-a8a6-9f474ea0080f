import pandas as pd
import numpy as np
import pytest

from app_real_estate.constants.helper_enumerators import Utility
from app_real_estate.transformation.metric_calculation.asset_filters import (
    AssetFilters,
)
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.building_certification_columns as bc
import app_real_estate.constants.building_certification_type as bct


def create_dataframe(data, columns):
    return pd.DataFrame(data, columns=columns)


@pytest.mark.parametrize(
    "certification_data, expected",
    [
        (
            create_dataframe([[bct.operational], [bct.interior]], [bc.type]),
            None,
        ),
        (
            create_dataframe([[None], [bct.interior]], [bc.type]),
            pytest.raises(ValueError),
        ),
        (
            create_dataframe([["yooo"], [bct.operational]], [bc.type]),
            pytest.raises(ValueError),
        ),
    ],
)
def test_validate_certification_data(certification_data, expected):
    if expected is None:
        result = AssetFilters.validate_certification_data(certification_data)
        assert result == expected
    else:
        with expected:
            AssetFilters.validate_certification_data(certification_data)


REQUIRED_COLUMNS = [
    ac.portfolio_asset_id,
    ac.data_year,
    ac.owned_for_aggregation,
    ac.standing_investment_for_aggregation,
]


def create_df_with_missing_column(missing_column):
    columns = {col: [1, 2, 3, 4] for col in REQUIRED_COLUMNS if col != missing_column}
    return pd.DataFrame(columns)


@pytest.mark.parametrize(
    "df, expected",
    [
        # Valid DataFrame with multiple years, including the current year
        (
            pd.DataFrame(
                {
                    ac.portfolio_asset_id: [1, 2, 3, 4],
                    ac.survey_year: [2023, 2023, 2023, 2023],
                    ac.data_year: [2020, 2021, 2022, 2022],
                    ac.owned_for_aggregation: [True, True, False, True],
                    ac.standing_investment_for_aggregation: [
                        True,
                        False,
                        True,
                        True,
                    ],
                }
            ),
            [False, False, False, True],
        ),
        # Valid DataFrame with assets both owned and standing investments
        (
            pd.DataFrame(
                {
                    ac.portfolio_asset_id: [1, 1, 3, 4],
                    ac.survey_year: [2023, 2023, 2023, 2023],
                    ac.data_year: [2022, 2021, 2022, 2022],
                    ac.owned_for_aggregation: [True, True, True, True],
                    ac.standing_investment_for_aggregation: [
                        True,
                        True,
                        True,
                        True,
                    ],
                }
            ),
            [True, True, True, True],
        ),
        # Valid DataFrame with assets either owned or standing investments
        (
            pd.DataFrame(
                {
                    ac.portfolio_asset_id: [1, 2, 3, 4],
                    ac.survey_year: [2023, 2023, 2023, 2023],
                    ac.data_year: [2022, 2022, 2022, 2022],
                    ac.owned_for_aggregation: [True, False, True, False],
                    ac.standing_investment_for_aggregation: [
                        False,
                        True,
                        False,
                        True,
                    ],
                }
            ),
            [False, False, False, False],
        ),
        # DataFrame missing the ac.portfolio_asset_id column
        (
            create_df_with_missing_column(ac.portfolio_asset_id),
            pytest.raises(ValueError),
        ),
        # DataFrame missing the ac.data_year column
        (
            create_df_with_missing_column(ac.data_year),
            pytest.raises(ValueError),
        ),
        # DataFrame missing the ac.owned_for_aggregation column
        (
            create_df_with_missing_column(ac.owned_for_aggregation),
            pytest.raises(ValueError),
        ),
        # DataFrame missing the ac.standing_investment_for_aggregation column
        (
            create_df_with_missing_column(ac.standing_investment_for_aggregation),
            pytest.raises(ValueError),
        ),
    ],
)
def test_is_operational_asset(df, expected):
    if isinstance(expected, list):
        result = AssetFilters.is_operational_asset(df)
        assert result.tolist() == expected
    else:
        with expected:
            AssetFilters.is_operational_asset(df)


@pytest.mark.parametrize(
    "df, expected",
    [
        # Invalid Dataframe because some assets are not standing investment
        (
            pd.DataFrame(
                {
                    ac.portfolio_asset_id: [1, 2, 3, 4],
                    ac.survey_year: [2023, 2023, 2023, 2023],
                    ac.standing_investment_for_aggregation: [
                        True,
                        False,
                        True,
                        True,
                    ],
                }
            ),
            pytest.raises(ValueError),
        ),
        # Valid DataFrame with energy data and assets not qualified
        (
            pd.DataFrame(
                {
                    ac.portfolio_asset_id: [1, 2, 3, 4],
                    ac.survey_year: [2023, 2023, 2023, 2023],
                    ac.standing_investment_for_aggregation: [
                        True,
                        True,
                        True,
                        True,
                    ],
                    ec.en_area_time_cov_p: [100, 80, 74, 100],
                    ec.en_days_data_avail: [355, 330, 364, 364],
                    ec.en_abs_in: [100, 100, 50, 80],
                    ac.asset_vacancy: [10, 9, 20, 21],
                }
            ),
            [True, False, False, False],
        ),
        # DataFrame missing the ac.standing_investment_for_aggregation column
        (
            create_df_with_missing_column(ac.standing_investment_for_aggregation),
            pytest.raises(KeyError),
        ),
    ],
)
def test_filter_intensity_assets(df, expected):
    if isinstance(expected, list):
        result = AssetFilters.filter_intensity_assets(
            df, Utility.Energy, coverage_threshold=75
        )
        assert result == expected
    else:
        with expected:
            AssetFilters.filter_intensity_assets(
                df, Utility.Energy, coverage_threshold=75
            )


@pytest.mark.parametrize(
    "df, expected, for_benchmark, lfl_metric",
    [
        (
            pd.DataFrame(
                {
                    ac.portfolio_asset_id: [1, 2, 3, 4],
                    ec.en_lfl_outlier_status: ["none", "none", "accepted", "rejected"],
                    ec.en_lfl_p: [10, -30, 31, 50],
                    sc.score_en1_energy_efficiency: [1.0, np.nan, np.nan, np.nan],
                }
            ),
            [True, True, False, False],
            True,
            ec.en_lfl_p,
        ),
        (
            pd.DataFrame(
                {
                    ac.portfolio_asset_id: [1, 2, 3, 4],
                    ec.en_lfl_outlier_status: ["none", "none", "accepted", "rejected"],
                    ec.en_lfl_p: [10, -30, 31, 50],
                    sc.score_en1_energy_efficiency: [1.0, np.nan, np.nan, np.nan],
                }
            ),
            [False, True, True, False],
            False,
            None,
        ),
        (
            pd.DataFrame(
                {
                    ac.portfolio_asset_id: [1, 2, 3, 4],
                    ec.en_lfl_outlier_status: ["none", "none", "accepted", "rejected"],
                    ec.en_lfl_p: [10, -30, 31, 50],
                    sc.score_en1_energy_efficiency: [1.0, np.nan, np.nan, np.nan],
                }
            ),
            pytest.raises(ValueError),
            True,
            None,
        ),
    ],
)
def test_filter_lfl_metric_for_aggregation(df, expected, for_benchmark, lfl_metric):
    if isinstance(expected, list):
        result = AssetFilters().filter_lfl_metric_for_aggregation(
            df, Utility.Energy, for_benchmark, lfl_metric
        )
        assert result == expected
    else:
        with expected:
            AssetFilters().filter_lfl_metric_for_aggregation(
                df, Utility.Energy, for_benchmark, lfl_metric
            )
