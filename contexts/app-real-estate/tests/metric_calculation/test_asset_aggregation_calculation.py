import pandas as pd
import numpy as np
import pytest
from app_real_estate.transformation.metric_calculation.asset_aggregation_calculation import (
    AssetAggregationCalculation as aac,
)
from app_real_estate.transformation.metric_calculation.building_certification_calculations import (
    BuildingCertificationsCalculations as bcc,
)
from app_real_estate.transformation.metric_calculation.energy_ratings_calculations import (
    EnergyRatingsCalculations as erc,
)
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.building_certification_columns as bc
import app_real_estate.constants.column_names.energy_rating_columns as ec


def test_asset_aggregation_calculations():
    mock_asset_data = pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3, 4, 5, 1, 3],
            ac.survey_year: [2023] * 7,
            ac.data_year: [2022] * 5 + [2021] * 2,
            ac.asset_size_m2: [100, 2500, 800, 1250, np.nan, 100, 800],
        }
    )

    mock_bc_data = pd.DataFrame(
        {
            bc.building_data_certifications_id: list(range(1, 11)),
            bc.age: [0, 4, 19, 2, 8, 15, 17, 1, 3, 20],
            bc.portfolio_asset_id: [1, 1, 2, 2, 1, 3, 4, 3, 4, 4],
            bc.asset_size_m2: [
                100,
                100,
                2500,
                2500,
                100,
                800,
                1250,
                800,
                1250,
                1250,
            ],
            bc.covered_floor_area_m2: [
                77,
                3,
                2500,
                2500,
                10,
                780,
                50,
                np.nan,
                1000,
                200,
            ],
            bc.type: ["operational"] * 3 + ["design"] * 4 + ["interior"] * 3,
        }
    )

    mock_er_data = pd.DataFrame(
        {
            ec.asset_energy_rating_id: list(range(1, 11)),
            ec.portfolio_asset_id: [1, 1, 1, 2, 2, 3, 3, 3, 4, 4],
            ec.asset_size_m2: [
                100,
                100,
                100,
                2500,
                2500,
                800,
                800,
                800,
                1250,
                1250,
            ],
            ec.covered_floor_area_m2: [
                79,
                21,
                0,
                2000,
                100,
                780,
                20,
                np.nan,
                1000,
                200,
            ],
        }
    )

    mock_bc_data = bcc.add_data_coverage(mock_bc_data)
    mock_er_data = erc.add_data_coverage(mock_er_data)

    mock_asset_data = aac.add_certification_coverage(mock_asset_data, mock_bc_data)
    mock_asset_data = aac.add_certified_floor_area(mock_asset_data)
    mock_asset_data = aac.add_rating_coverage(mock_asset_data, mock_er_data)
    mock_asset_data = aac.add_rated_floor_area(mock_asset_data)

    expected_bc_cov = [90, 100, 97.5, 100, np.nan, 90, 97.5]
    expected_bc_size = [90, 2500, 780, 1250, np.nan, 90, 780]
    expected_er_cov = [100, 84, 100, 96, 0, 100, 100]
    expected_er_size = [100, 2100, 800, 1200, np.nan, 100, 800]

    assert mock_asset_data[ac.certification_coverage].tolist() == pytest.approx(
        expected_bc_cov, rel=1e-4, nan_ok=True
    ), "Test for asset total building certification coverage calculation failed."
    assert mock_asset_data[ac.certified_floor_area_m2].tolist() == pytest.approx(
        expected_bc_size, rel=1e-4, nan_ok=True
    ), "Test for asset total certified floor area calculation failed."
    assert mock_asset_data[ac.rating_coverage].tolist() == pytest.approx(
        expected_er_cov, rel=1e-4, nan_ok=True
    ), "Test for asset total energy rating coverage calculation failed."
    assert mock_asset_data[ac.rated_floor_area_m2].tolist() == pytest.approx(
        expected_er_size, rel=1e-4, nan_ok=True
    ), "Test for asset total certified floor area calculation failed."
