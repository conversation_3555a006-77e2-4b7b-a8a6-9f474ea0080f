import pandas as pd
import numpy as np
import pytest

from app_real_estate.constants.helper_enumerators import Utility
from app_real_estate.transformation.metric_calculation.asset_characteristics_calculation import (
    AssetCharacteristicsCalculations,
)
from app_real_estate.transformation.metric_calculation.lfl_calculations import (
    LFLCalculations,
)
from app_real_estate.transformation.metric_calculation.intensity_calculations import (
    IntensityCalculations,
)
from app_real_estate.transformation.metric_calculation.sustainable_utility_calculations import (
    SustainableUtilityCalculations,
)
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.ghg_columns as gc
import app_real_estate.constants.column_names.water_columns as wc


def test_asset_intensity_renrec():
    mock_data = pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3, 4, 5, 1, 3],
            ac.survey_year: [2024] * 7,
            ac.data_year: [2023] * 5 + [2022] * 2,
            ec.en_days_data_avail: [365] * 7,
            wc.wat_days_data_avail: [365] * 7,
            gc.ghg_days_data_avail: [365] * 7,
            ac.standing_investment_for_aggregation: [True] * 7,
            ac.asset_size_m2: [5771, 1495, 2458, 52078, np.nan, 5771, 2458],
            ac.asset_size_sqft: [5771, 1495, 2458, 52078, np.nan, 5771, 2458],
            ec.en_abs_in: [
                659320.92,
                np.nan,
                357913.00,
                9580029.00,
                np.nan,
                685320.92,
                370913.00,
            ],
            ec.en_abs: [
                659320.92,
                np.nan,
                357920.00,
                9580029.00,
                np.nan,
                685320.92,
                370000.00,
            ],
            wc.wat_abs: [
                1832.000,
                np.nan,
                907.000,
                17417.000,
                np.nan,
                1840.000,
                887.000,
            ],
            wc.wat_abs_in: [
                1832.000,
                np.nan,
                907.000,
                17417.000,
                np.nan,
                1840.000,
                887.000,
            ],
            gc.ghg_abs_in: [
                124.255220,
                np.nan,
                57.114386,
                519.893306,
                np.nan,
                140.255220,
                65.114386,
            ],
            ec.en_ren_abs: [
                361135.92,
                np.nan,
                203645.00,
                635121.00,
                np.nan,
                390135.92,
                185645.00,
            ],
            wc.wat_rec_abs: [
                0.000000,
                np.nan,
                203.000,
                np.nan,
                np.nan,
                0.000000,
                177.000,
            ],
            ec.en_area_time_cov_p: [
                100.000000,
                75.000000,
                100.000000,
                100.000000,
                np.nan,
                100.000000,
                100.000000,
            ],
            gc.ghg_area_time_cov_p: [
                100.000000,
                75.000000,
                100.000000,
                67.000000,
                np.nan,
                100.000000,
                100.000000,
            ],
            wc.wat_area_time_cov_p: [
                100.000000,
                75.000000,
                98.000000,
                100.000000,
                np.nan,
                100.000000,
                56.000000,
            ],
            ac.asset_vacancy: [0, 12, 10, 24, 7, 0, 10],
        }
    )

    expected_en_int = [
        114.247257,
        np.nan,
        145.611473,
        np.nan,
        np.nan,
        118.752542,
        150.900325,
    ]
    expected_wat_int = [
        0.317449,
        np.nan,
        0.37653,
        np.nan,
        np.nan,
        0.318836,
        np.nan,
    ]
    expected_ghg_int = [
        0.021531,
        np.nan,
        0.023236,
        np.nan,
        np.nan,
        0.024303,
        0.026491,
    ]
    expected_ren_imp = [
        -2.153557567507441,
        np.nan,
        6.722468255023017,
        np.nan,
        np.nan,
        np.nan,
        np.nan,
    ]
    expected_rec_imp = [
        0.000000,
        np.nan,
        2.4265732266512856,
        np.nan,
        np.nan,
        np.nan,
        np.nan,
    ]

    result = IntensityCalculations.add_intensity_per_utility(mock_data, Utility.Energy)
    assert result[ec.en_efficiency_int_kwh_m2].tolist() == pytest.approx(
        expected_en_int, rel=1e-4, nan_ok=True
    ), "Test for energy efficiency intensity calculation failed."

    result = IntensityCalculations.add_intensity_per_utility(mock_data, Utility.Water)
    assert result[wc.wat_scored_int_m3_m2].tolist() == pytest.approx(
        expected_wat_int, rel=1e-4, nan_ok=True
    ), "Test for water intensity calculation failed."

    result = IntensityCalculations.add_intensity_per_utility(mock_data, Utility.GHG)
    assert result[gc.ghg_scored_int_ton_m2].tolist() == pytest.approx(
        expected_ghg_int, rel=1e-4, nan_ok=True
    ), "Test for GHG intensity calculation failed."

    result = SustainableUtilityCalculations().add_renewable_energy_percent_change_rate(
        mock_data
    )
    assert result[ec.en_ren_percent_change].tolist() == pytest.approx(
        expected_ren_imp, rel=1e-4, nan_ok=True
    ), "Test for renewable energy percent_change calculation failed."

    result = SustainableUtilityCalculations().add_recycled_water_percent_change_rate(
        mock_data
    )
    assert result[wc.wat_rec_percent_change].tolist() == pytest.approx(
        expected_rec_imp, rel=1e-4, nan_ok=True
    ), "Test for recycled water percent_change calculation failed."


@pytest.fixture
def mock_data_lfl_abs_w():
    return pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3, 4],
            ac.whole_building: [True] * 4,
            wc.wat_tot_w: [1000] * 4,
            ac.tenant_ctrl: [True, True, False, False],
            ec.en_lfl_abs_in: [100, 200, np.nan, 80],
            ec.en_lfl_abs_lc_oe: [np.nan, 20, np.nan, np.nan],
            ec.en_lfl_abs_lc_of: [np.nan, 5, np.nan, 10],
            ec.en_lfl_abs_tc_oe: [10, np.nan, 10, np.nan],
            ec.en_lfl_abs_tc_of: [np.nan, np.nan, 5, 10],
            wc.wat_lfl_abs_in: [100, 50, 200, 300],
            wc.wat_lfl_abs_lc_o: [np.nan, 10, np.nan, 20],
            wc.wat_lfl_abs_tc_o: [np.nan, 10, 5, np.nan],
            ec.en_lfl_abs_lc_bsf: [np.nan] * 4,
            ec.en_lfl_abs_lc_bsd: [np.nan] * 4,
            ec.en_lfl_abs_lc_bse: [np.nan] * 4,
            ec.en_lfl_abs_lc_bcf: [np.nan] * 4,
            ec.en_lfl_abs_lc_bcd: [np.nan] * 4,
            ec.en_lfl_abs_lc_bce: [np.nan] * 4,
            ec.en_lfl_abs_lc_tf: [np.nan] * 4,
            ec.en_lfl_abs_lc_td: [np.nan] * 4,
            ec.en_lfl_abs_lc_te: [np.nan] * 4,
            ec.en_lfl_abs_tc_tf: [np.nan] * 4,
            ec.en_lfl_abs_tc_td: [np.nan] * 4,
            ec.en_lfl_abs_tc_te: [np.nan] * 4,
            gc.ghg_lfl_abs_s1_w: [np.nan] * 4,
            gc.ghg_lfl_abs_s1_o: [np.nan] * 4,
            gc.ghg_lfl_abs_s2_lb_w: [np.nan] * 4,
            gc.ghg_lfl_abs_s2_lb_o: [np.nan] * 4,
            gc.ghg_lfl_abs_s3_w: [np.nan] * 4,
            gc.ghg_lfl_abs_s3_o: [np.nan] * 4,
            wc.wat_lfl_abs_lc_bs: [np.nan] * 4,
            wc.wat_lfl_abs_lc_bc: [np.nan] * 4,
            wc.wat_lfl_abs_lc_t: [np.nan] * 4,
            wc.wat_lfl_abs_tc_t: [np.nan] * 4,
        }
    )


def test_asset_lfl_abs_per_control_whole_building(mock_data_lfl_abs_w):
    mock_data_lfl_abs_w = LFLCalculations().add_energy_lfl_consumption_per_subspaces(
        mock_data_lfl_abs_w
    )
    result = LFLCalculations().add_lfl_consumption_per_control(mock_data_lfl_abs_w)

    expected_en_lc = pd.Series(
        [np.nan, 25, np.nan, 90], index=mock_data_lfl_abs_w.index
    )
    expected_en_tc = pd.Series([110, 200, 15, 10], index=mock_data_lfl_abs_w.index)
    expected_wat_lc = pd.Series([np.nan, 10, 200, 320], index=mock_data_lfl_abs_w.index)
    expected_wat_tc = pd.Series([100, 60, 5, np.nan], index=mock_data_lfl_abs_w.index)

    pd.testing.assert_series_equal(
        result[ec.en_lfl_abs_lc],
        expected_en_lc,
        check_names=False,
        check_dtype=False,
    )
    pd.testing.assert_series_equal(
        result[ec.en_lfl_abs_tc],
        expected_en_tc,
        check_names=False,
        check_dtype=False,
    )
    pd.testing.assert_series_equal(
        result[wc.wat_lfl_abs_lc],
        expected_wat_lc,
        check_names=False,
        check_dtype=False,
    )
    pd.testing.assert_series_equal(
        result[wc.wat_lfl_abs_tc],
        expected_wat_tc,
        check_names=False,
        check_dtype=False,
    )


def test_asset_lfl_per_control_subspaces():
    mock_data = pd.DataFrame(
        {
            ec.en_lfl_abs_in: [np.nan] * 10,
            ec.en_lfl_abs: [np.nan] * 10,
            wc.wat_lfl_abs: [np.nan] * 10,
            gc.ghg_lfl_abs: [np.nan] * 10,
            wc.wat_lfl_abs_in: [np.nan] * 10,
            ec.en_lfl_abs_lc_bsf: [12, 1, 17, 4, 19, 6, 8, 11, 10, 5],
            ec.en_lfl_abs_lc_bsd: [8, 18, 14, 12, 7, 1, 2, 15, 19, 6],
            ec.en_lfl_abs_lc_bse: [
                np.nan,
                12.0,
                2.0,
                3.0,
                10.0,
                9.0,
                6.0,
                17.0,
                20.0,
                18.0,
            ],
            ec.en_lfl_abs_lc_bcf: [
                5.0,
                20.0,
                4.0,
                8.0,
                18.0,
                11.0,
                np.nan,
                12.0,
                1.0,
                3.0,
            ],
            ec.en_lfl_abs_lc_bcd: [7, 6, 8, 7, 3, 20, 4, 9, 14, 15],
            ec.en_lfl_abs_lc_bce: [10, 3, 19, 6, 15, 14, 13, 8, 7, 12],
            ec.en_lfl_abs_lc_tf: [15, 11, 7, 14, 5, 18, 3, 2, 12, 20],
            ec.en_lfl_abs_lc_td: [16, 10, 16, 15, 14, 7, 5, 6, 9, 11],
            ec.en_lfl_abs_lc_te: [11, 13, 5, 11, 9, 10, 17, 14, 18, 4],
            ec.en_lfl_abs_lc_of: [3, 5, 18, 1, 12, 13, 20, 16, 17, 2],
            ec.en_lfl_abs_lc_oe: [9, 19, 9, 2, 8, 4, 11, 13, 6, 1],
            ec.en_lfl_abs_tc_tf: [
                np.nan,
                7.0,
                11.0,
                17.0,
                16.0,
                3.0,
                np.nan,
                14.0,
                12.0,
                7.0,
            ],
            ec.en_lfl_abs_tc_td: [2, 6, 15, 19, 2, 10, 18, 8, 9, 5],
            ec.en_lfl_abs_tc_te: [8, 13, 9, 11, 14, 19, 2, 1, 6, 8],
            ec.en_lfl_abs_tc_of: [4, 4, 12, 13, 10, 7, 11, 12, 14, 16],
            ec.en_lfl_abs_tc_oe: [13, 18, 8, 10, 4, 5, 14, 19, 1, 3],
            gc.ghg_lfl_abs_s1_w: [6, 19, 6, 8, 18, 12, 4, 3, 17, 14],
            gc.ghg_lfl_abs_s1_o: [9, 2, 19, 12, 1, 8, 17, 13, 20, 10],
            gc.ghg_lfl_abs_s2_lb_w: [14, 8, 3, 6, 13, 15, 12, 16, 10, 1],
            gc.ghg_lfl_abs_s2_lb_o: [
                8.0,
                20.0,
                2.0,
                4.0,
                np.nan,
                6.0,
                7.0,
                18.0,
                3.0,
                9.0,
            ],
            gc.ghg_lfl_abs_s2_mb_w: [19, 3, 18, 9, 7, 17, 6, 4, 11, 13],
            gc.ghg_lfl_abs_s2_mb_o: [7, 14, 13, 1, 20, 16, 11, 2, 16, 19],
            gc.ghg_lfl_abs_s3_w: [1, 9, 20, 15, 3, 1, 19, 5, 18, 4],
            gc.ghg_lfl_abs_s3_o: [18, 6, 4, 2, 12, 10, 15, 17, 8, 6],
            wc.wat_lfl_abs_lc_bs: [12, 5, 11, 16, 15, 14, 9, 6, 7, 10],
            wc.wat_lfl_abs_lc_bc: [15, 18, 8, 5, 11, 7, 3, 10, 12, 1],
            wc.wat_lfl_abs_lc_t: [10, 20, 9, 3, 2, 11, 8, 20, 9, 18],
            wc.wat_lfl_abs_lc_o: [11, 1, 3, 14, 10, 13, 16, 9, 5, 2],
            wc.wat_lfl_abs_tc_t: [3, 14, 12, 17, 9, 20, 13, 12, 2, 7],
            wc.wat_lfl_abs_tc_o: [
                9.0,
                np.nan,
                6.0,
                11.0,
                5.0,
                12.0,
                2.0,
                np.nan,
                18.0,
                11.0,
            ],
            ac.whole_building: [False] * 10,
            wc.wat_tot_w: [np.nan] * 10,
            ac.tenant_ctrl: [False] * 10,
            "portfolio_asset_id": [1, 2, 3, 4, 5, 6, 1, 2, 3, 4],
            "survey_year": [
                2024,
                2024,
                2024,
                2024,
                2024,
                2024,
                2024,
                2024,
                2024,
                2024,
            ],
            "data_year": [
                2023,
                2023,
                2023,
                2023,
                2023,
                2023,
                2022,
                2022,
                2022,
                2022,
            ],
        }
    )

    AssetCharacteristicsCalculations()

    mock_data = LFLCalculations().add_energy_lfl_consumption_per_subspaces(mock_data)
    mock_data = LFLCalculations().add_lfl_consumption_per_control(mock_data)
    mock_data = LFLCalculations().add_previous_year_lfl_consumption(mock_data)
    mock_data = LFLCalculations().add_lfl_percent_change_per_control(mock_data)

    expected_en_lfl_abs_lc = [96, 118, 119, 83, 120, 113, 89, 123, 133, 97]
    expected_en_lfl_abs_tc = [27, 48, 55, 70, 46, 44, 45, 54, 42, 39]
    expected_ghg_lfl_abs_s12 = [37, 49, 30, 30, 32, 41, 40, 50, 50, 34]
    expected_ghg_lfl_abs_s3 = [19, 15, 24, 17, 15, 11, 34, 22, 26, 10]
    expected_wat_lfl_abs_lc = [48, 44, 31, 38, 38, 45, 36, 45, 33, 31]
    expected_wat_lfl_abs_tc = [12, 14, 18, 28, 14, 32, 15, 12, 20, 18]

    expected_en_lfl_p_lc = [
        7.8651,
        -4.0650,
        -10.5263,
        -14.43298,
        np.nan,
        np.nan,
        np.nan,
        np.nan,
        np.nan,
        np.nan,
    ]
    expected_en_lfl_p_tc = [
        -40,
        -11.1111,
        30.9523,
        79.4871,
        np.nan,
        np.nan,
        np.nan,
        np.nan,
        np.nan,
        np.nan,
    ]
    expected_ghg_lfl_p_s12 = [
        -7.5,
        -2,
        -40,
        -11.7647,
        np.nan,
        np.nan,
        np.nan,
        np.nan,
        np.nan,
        np.nan,
    ]
    expected_ghg_lfl_p_s3 = [
        -44.1176,
        -31.8181,
        -7.6923,
        70,
        np.nan,
        np.nan,
        np.nan,
        np.nan,
        np.nan,
        np.nan,
    ]
    expected_wat_lfl_p_lc = [
        33.3333,
        -2.2222,
        -6.0606,
        22.5806,
        np.nan,
        np.nan,
        np.nan,
        np.nan,
        np.nan,
        np.nan,
    ]
    expected_wat_lfl_p_tc = [
        -20,
        16.6666,
        -10,
        55.5555,
        np.nan,
        np.nan,
        np.nan,
        np.nan,
        np.nan,
        np.nan,
    ]

    assert mock_data[ec.en_lfl_abs_lc].tolist() == pytest.approx(
        expected_en_lfl_abs_lc, rel=1e-4, nan_ok=True
    ), "Test for LFL energy LC consumption calculation failed."

    assert mock_data[ec.en_lfl_abs_tc].tolist() == pytest.approx(
        expected_en_lfl_abs_tc, rel=1e-4, nan_ok=True
    ), "Test for LFL energy TC consumption calculation failed."

    assert mock_data[gc.ghg_lfl_abs_s12].tolist() == pytest.approx(
        expected_ghg_lfl_abs_s12, rel=1e-4, nan_ok=True
    ), "Test for LFL GHG S12 consumption calculation failed."

    assert mock_data[gc.ghg_lfl_abs_s3].tolist() == pytest.approx(
        expected_ghg_lfl_abs_s3, rel=1e-4, nan_ok=True
    ), "Test for LFL GHG S3 consumption calculation failed."

    assert mock_data[wc.wat_lfl_abs_lc].tolist() == pytest.approx(
        expected_wat_lfl_abs_lc, rel=1e-4, nan_ok=True
    ), "Test for LFL water LC consumption calculation failed."

    assert mock_data[wc.wat_lfl_abs_tc].tolist() == pytest.approx(
        expected_wat_lfl_abs_tc, rel=1e-4, nan_ok=True
    ), "Test for LFL water TC consumption calculation failed."

    assert mock_data[ec.en_lfl_percent_change_lc].tolist() == pytest.approx(
        expected_en_lfl_p_lc, rel=1e-4, nan_ok=True
    ), "Test for LFL percent change for energy LC"

    assert mock_data[ec.en_lfl_percent_change_tc].tolist() == pytest.approx(
        expected_en_lfl_p_tc, rel=1e-4, nan_ok=True
    ), "Test for LFL percent change for energy TC"

    assert mock_data[gc.ghg_lfl_percent_change_s12].tolist() == pytest.approx(
        expected_ghg_lfl_p_s12, rel=1e-4, nan_ok=True
    ), "Test for LFL percent change for GHG S12"

    assert mock_data[gc.ghg_lfl_percent_change_s3].tolist() == pytest.approx(
        expected_ghg_lfl_p_s3, rel=1e-4, nan_ok=True
    ), "Test for LFL percent change for GHG S3"

    assert mock_data[wc.wat_lfl_percent_change_lc].tolist() == pytest.approx(
        expected_wat_lfl_p_lc, rel=1e-4, nan_ok=True
    ), "Test for LFL percent change for water LC"

    assert mock_data[wc.wat_lfl_percent_change_tc].tolist() == pytest.approx(
        expected_wat_lfl_p_tc, rel=1e-4, nan_ok=True
    ), "Test for LFL percent change for water TC"


def test_renewable_per_area():
    mock_data = pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3, 4, 5, 1, 3],
            ac.survey_year: [2024] * 7,
            ac.data_year: [2023] * 5 + [2022] * 2,
            ac.owned_entire_period: [True] * 7,
            ac.ncmr_status: ["Standing Investment"] * 7,
            ec.en_ren_ons_con: [10, 3, 40, 53, 15, 28, 32],
            ec.en_ren_ons_exp: [31, 18, 13, 20, 30, 27, 11],
            ec.en_ren_ons_tpt: [19, 21, 26, 34, 11, 7, 16],
            ec.en_ren_ofs_pbl: [42, 9, 37, 25, 10, 3, 32],
            ec.en_ren_ofs_pbt: [26, 3, 6, 1, 12, 27, 2],
        }
    )

    expected_ren_ons = [60, 42, 79, 107, 56, 62, 59]
    expected_ren_ofs = [68, 12, 43, 26, 22, 30, 34]

    AssetCharacteristicsCalculations()
    mock_data = SustainableUtilityCalculations.add_renewable_generation_per_area(
        mock_data
    )

    assert mock_data[ec.en_ren_ons].tolist() == pytest.approx(
        expected_ren_ons, rel=1e-4, nan_ok=True
    ), "Test for renewable on-site generation calculation failed."

    assert mock_data[ec.en_ren_ofs].tolist() == pytest.approx(
        expected_ren_ofs, rel=1e-4, nan_ok=True
    ), "Test for renewable off-site generation calculation failed."


def test_recycled_onsite():
    mock_data = pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3, 4, 5, 1, 3],
            ac.survey_year: [2024] * 7,
            ac.data_year: [2023] * 5 + [2022] * 2,
            ac.owned_entire_period: [True] * 7,
            ac.ncmr_status: ["Standing Investment"] * 7,
            wc.wat_rec_ons_reu: [47, 25, 20, 32, np.nan, 9, np.nan],
            wc.wat_rec_ons_cap: [5, 30, 45, 10, 45, 41, np.nan],
            wc.wat_rec_ons_ext: [3, 22, 13, np.nan, 23, 36, np.nan],
        }
    )

    expected_rec_ons = [55, 77, 78, 42, 68, 86, 0]

    AssetCharacteristicsCalculations()
    mock_data = SustainableUtilityCalculations.add_recycled_water_consumption_on_site(
        mock_data
    )

    assert mock_data[wc.wat_rec_ons].tolist() == pytest.approx(
        expected_rec_ons, rel=1e-4, nan_ok=True
    ), "Test for on-site water recycled calculation failed."
