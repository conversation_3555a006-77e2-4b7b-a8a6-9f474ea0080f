import pandas as pd
import pytest
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.energy_columns as ec
from app_real_estate.models.input_models.ashrae_thresholds_model import (
    AshraeThresholdsModel,
)
import numpy as np

from app_real_estate.models.input_models.weather_stations_model import (
    WeatherStationsModel,
)
from app_real_estate.transformation.metric_calculation.intensity_calculations import (
    IntensityCalculations,
)


@pytest.fixture
def mock_thresholds():
    thresholds = pd.DataFrame(
        {
            AshraeThresholdsModel.climate_zone: ["4A", "4B"],
            AshraeThresholdsModel.property_type_code: ["OCHO", "OCLO"],
            AshraeThresholdsModel.ashrae_intensity_threshold: [10.0, 12.0],
        }
    )
    AshraeThresholdsModel.validate(thresholds)
    return thresholds


@pytest.fixture
def mock_weather_stations():
    weather_stations = pd.DataFrame(
        {
            WeatherStationsModel.climate_zone: ["4A", "4B"],
            WeatherStationsModel.id: [1, 2],
        }
    )
    WeatherStationsModel.validate(weather_stations)
    return weather_stations


@pytest.fixture
def mock_asset_data():
    return pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3],
            ac.property_type_code: ["OCHO", "OCLO", "XORE"],
            ac.nearest_ashrae_weather_station_id: [1, 2, 2],
        }
    )


def test_add_climate_zones(mock_weather_stations, mock_asset_data):
    expected = pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3],
            ac.property_type_code: ["OCHO", "OCLO", "XORE"],
            ac.nearest_ashrae_weather_station_id: [1, 2, 2],
            ac.climate_zone: ["4A", "4B", "4B"],
        }
    )

    mock_asset_data = IntensityCalculations.add_climate_zones(
        mock_asset_data, mock_weather_stations
    )

    pd.testing.assert_frame_equal(expected, mock_asset_data)


def test_add_ashrae_thresholds(mock_asset_data, mock_thresholds, mock_weather_stations):
    expected = pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3],
            ac.property_type_code: ["OCHO", "OCLO", "XORE"],
            ac.nearest_ashrae_weather_station_id: [1, 2, 2],
            ac.climate_zone: ["4A", "4B", "4B"],
            ec.ashrae_intensity_threshold: [10.0, 12.0, np.nan],
        }
    )

    mock_asset_data = IntensityCalculations.add_climate_zones(
        mock_asset_data, mock_weather_stations
    )
    mock_asset_data = IntensityCalculations.add_ashrae_thresholds(
        mock_asset_data, mock_thresholds
    )

    pd.testing.assert_frame_equal(expected, mock_asset_data)
