import pandas as pd
import pytest
from app_real_estate.transformation.metric_calculation.building_certification_filters import (
    BuildingCertificationFilters,
)
import app_real_estate.constants.column_names.building_certification_columns as bcc


def test_filter_certifications_per_type():
    # Test with valid data
    data = pd.DataFrame(
        {
            bcc.type: ["operational", "design", "operational", "interior"],
            bcc.building_data_certifications_id: [1, 2, 3, 4],
            bcc.portfolio_asset_id: [1, 2, 1, 3],
            bcc.covered_floor_area_m2: [110, 38, 110, 900],
            bcc.year: [2015, 2023, 2010, 2023],
        }
    )

    result = BuildingCertificationFilters.filter_certifications_per_type(
        data, "operational"
    )
    expected = [True, False, True, False]

    assert result.tolist() == expected, "Test case for 'operational' type failed."

    # Test with a type that does not exist
    result = BuildingCertificationFilters.filter_certifications_per_type(
        data, "nonexistent"
    )
    expected = [False, <PERSON>alse, False, False]

    assert result.tolist() == expected, "Test case for 'nonexistent' type failed."

    # Test with missing 'type' column
    with pytest.raises(
        ValueError,
        match="filter_certifications_per_type: The input DataFrame must contain a 'type' column.",
    ):
        data_invalid = pd.DataFrame(data.drop("type", axis=1))
        BuildingCertificationFilters.filter_certifications_per_type(
            data_invalid, "operational"
        )

    # Test with invalid building_type input
    with pytest.raises(
        TypeError,
        match="filter_certifications_per_type: The building_type parameter must be a string.",
    ):
        BuildingCertificationFilters.filter_certifications_per_type(data, 123)
