import pandas as pd
import numpy as np
import pytest
from app_real_estate.transformation.metric_calculation.building_certification_calculations import (
    BuildingCertificationsCalculations as bcc,
)
from app_real_estate.transformation.metric_calculation.common_operations import (
    CommonOperations,
)
import app_real_estate.constants.column_names.building_certification_columns as bc
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.building_certification_table_columns as bctc
import app_real_estate.constants.building_certification_type as bct
import app_real_estate.constants.building_certification_validation_points as bcvp
import app_real_estate.constants.asset_characteristics as asc_const


@pytest.fixture
def mock_data():
    mock_data = pd.DataFrame(
        {
            bc.building_data_certifications_id: list(range(1, 11)),
            bc.age: [0, 4, 19, 2, 8, 15, 17, 1, 3, 20],
            bc.portfolio_asset_id: [1, 1, 2, 2, 1, 3, 4, 3, 4, 4],
            bc.asset_size_m2: [
                100,
                100,
                2500,
                2500,
                100,
                800,
                1250,
                800,
                1250,
                1250,
            ],
            bc.covered_floor_area_m2: [
                79,
                21,
                2500,
                2500,
                100,
                780,
                1000,
                700,
                1000,
                200,
            ],
            bc.type: [bct.operational] * 3 + [bct.design] * 4 + [bct.interior] * 3,
        }
    )
    return mock_data


def test_add_age():
    # Sample data
    data = {bc.year: [2010, 2012, 2015]}
    certification_data = pd.DataFrame(data)
    data_year = 2020

    # Expected result
    expected_data = certification_data.copy()
    expected_data[bc.age] = [10, 8, 5]

    # Test add_age method
    result = bcc.add_age(certification_data, data_year)
    pd.testing.assert_frame_equal(result, expected_data)


def test_add_age_with_future_year():
    # Sample data with a future year
    data = {bc.year: [2010, 2022, 2015]}
    certification_data = pd.DataFrame(data)
    data_year = 2020

    # Test add_age method and expect ValueError
    with pytest.raises(
        ValueError, match="Certification year is bigger than data year."
    ):
        bcc.add_age(certification_data, data_year)


def test_building_certification_coverage(mock_data):
    mock_data = bcc.add_data_coverage(mock_data)
    expected_coverage = [79, 21, 100, 100, 100, 97.5, 80, 87.5, 80, 16]
    assert mock_data[bc.scoring_coverage].tolist() == pytest.approx(
        expected_coverage, rel=1e-4, nan_ok=True
    ), "Test for building certification coverage calculation failed."


def test_building_certification_time_factor(mock_data):
    mock_data = bcc.add_time_factor(mock_data)
    expected_time_factor = [1, 0.5, 0, 1, 0.61, 0.08, 0.08, 1, 0.67, 0]

    assert mock_data[bc.time_factor_weight].tolist() == pytest.approx(
        expected_time_factor, rel=1e-4, nan_ok=True
    ), "Test for time factor weight calculation failed."


def test_get_location_property_type_detail():
    # test to check if the output of the merge is as expected
    certification_data = pd.DataFrame({bc.portfolio_asset_id: [1, 2]})

    asset_data = pd.DataFrame(
        {
            ac.country: ["Country1", "Country2"],
            ac.country_name: ["Country1", "Country2"],
            ac.sub_region: ["SubRegion1", "SubRegion2"],
            ac.region: ["Region1", "Region2"],
            ac.super_region: ["SuperRegion1", "SuperRegion2"],
            ac.property_type_code: ["OCHI", "OCMI"],
            ac.property_subtype: ["Subtype1", "Subtype2"],
            ac.property_type: ["Type1", "Type2"],
            ac.property_sector: ["Sector1", "Sector2"],
            ac.portfolio_asset_id: [1, 2],
        }
    )

    expected_data = certification_data.copy()
    expected_data[ac.country] = ["Country1", "Country2"]
    expected_data[ac.country_name] = ["Country1", "Country2"]
    expected_data[ac.sub_region] = ["SubRegion1", "SubRegion2"]
    expected_data[ac.region] = ["Region1", "Region2"]
    expected_data[ac.super_region] = ["SuperRegion1", "SuperRegion2"]
    expected_data[ac.property_type_code] = ["OCHI", "OCMI"]
    expected_data[ac.property_subtype] = ["Subtype1", "Subtype2"]
    expected_data[ac.property_type] = ["Type1", "Type2"]
    expected_data[ac.property_sector] = ["Sector1", "Sector2"]

    result_data = CommonOperations.get_location_property_type_detail(
        certification_data, asset_data
    )

    pd.testing.assert_frame_equal(result_data, expected_data)


@pytest.mark.parametrize(
    "certification_data, asset_data, expected_result",
    [
        (
            pd.DataFrame(
                {
                    bc.portfolio_asset_id: [1, 2],
                    bc.scoring_coverage: [50, 60],
                    bc.covered_floor_area: [100, 200],
                    bc.year: [2022, 2021],
                    bc.type: [bct.design, bct.design],
                    bc.snapshot_id: [101, 102],
                    bc.company_fund_id: [201, 202],
                    bc.response_id: [1, 2],
                }
            ),
            pd.DataFrame(
                {
                    ac.portfolio_asset_id: [1, 2],
                    ac.snapshot_id: [101, 102],
                    ac.company_fund_id: [201, 202],
                    ac.response_id: [1, 2],
                }
            ),
            pd.DataFrame(
                {
                    bc.portfolio_asset_id: [1, 2, 1, 2],
                    bc.scoring_coverage: [50, 60, 0, 0],
                    bc.covered_floor_area: [100, 200, 0, 0],
                    bc.year: [2022, 2021, 0, 0],
                    bc.type: [
                        bct.design,
                        bct.design,
                        bct.operational,
                        bct.operational,
                    ],
                    bc.snapshot_id: [101, 102, 101, 102],
                    bc.company_fund_id: [201, 202, 201, 202],
                    ac.response_id: [1, 2, 1, 2],
                    bc.building_data_certifications_scoring_id: [1, 2, 3, 4],
                }
            ),
        ),
        (
            pd.DataFrame(
                {
                    bc.portfolio_asset_id: [1, 2],
                    bc.scoring_coverage: [50, 60],
                    bc.covered_floor_area: [100, 200],
                    bc.year: [2022, 2021],
                    bc.type: [bct.operational, bct.operational],
                    bc.snapshot_id: [101, 102],
                    bc.company_fund_id: [201, 202],
                    bc.response_id: [1, 2],
                }
            ),
            pd.DataFrame(
                {
                    ac.portfolio_asset_id: [1, 2],
                    ac.snapshot_id: [101, 102],
                    ac.company_fund_id: [201, 202],
                    ac.response_id: [1, 2],
                }
            ),
            pd.DataFrame(
                {
                    bc.portfolio_asset_id: [1, 2, 1, 2],
                    bc.scoring_coverage: [50, 60, 0, 0],
                    bc.covered_floor_area: [100, 200, 0, 0],
                    bc.year: [2022, 2021, 0, 0],
                    bc.type: [
                        bct.operational,
                        bct.operational,
                        bct.design,
                        bct.design,
                    ],
                    bc.snapshot_id: [101, 102, 101, 102],
                    bc.company_fund_id: [201, 202, 201, 202],
                    ac.response_id: [1, 2, 1, 2],
                    bc.building_data_certifications_scoring_id: [1, 2, 3, 4],
                }
            ),
        ),
        (
            pd.DataFrame(
                {
                    bc.portfolio_asset_id: [1, 2],
                    bc.scoring_coverage: [50, 60],
                    bc.covered_floor_area: [100, 200],
                    bc.year: [2022, 2021],
                    bc.type: [bct.interior, bct.interior],
                    bc.snapshot_id: [101, 102],
                    bc.company_fund_id: [201, 202],
                    bc.response_id: [1, 2],
                }
            ),
            pd.DataFrame(
                {
                    ac.portfolio_asset_id: [1, 2],
                    ac.snapshot_id: [101, 102],
                    ac.company_fund_id: [201, 202],
                    ac.response_id: [1, 2],
                }
            ),
            pd.DataFrame(
                {
                    bc.portfolio_asset_id: [1, 2, 1, 2],
                    bc.scoring_coverage: [50, 60, 0, 0],
                    bc.covered_floor_area: [100, 200, 0, 0],
                    bc.year: [2022, 2021, 0, 0],
                    bc.type: [
                        bct.interior,
                        bct.interior,
                        bct.operational,
                        bct.operational,
                    ],
                    bc.snapshot_id: [101, 102, 101, 102],
                    bc.company_fund_id: [201, 202, 201, 202],
                    ac.response_id: [1, 2, 1, 2],
                    bc.building_data_certifications_scoring_id: [1, 2, 3, 4],
                }
            ),
        ),
        (
            pd.DataFrame(
                {
                    bc.portfolio_asset_id: [1, 2],
                    bc.scoring_coverage: [50, 60],
                    bc.covered_floor_area: [100, 200],
                    bc.year: [2022, 2021],
                    bc.type: [
                        bct.design,
                        bct.interior,
                    ],  # should mock operational
                    bc.snapshot_id: [101, 102],
                    bc.company_fund_id: [201, 202],
                    bc.response_id: [1, 2],
                }
            ),
            pd.DataFrame(
                {
                    ac.portfolio_asset_id: [1, 2],
                    bc.response_id: [1, 2],
                    ac.snapshot_id: [101, 102],
                    ac.company_fund_id: [201, 202],
                }
            ),
            pd.DataFrame(
                {
                    bc.portfolio_asset_id: [1, 2, 1, 2],
                    bc.scoring_coverage: [50, 60, 0, 0],
                    bc.covered_floor_area: [100, 200, 0, 0],
                    bc.year: [2022, 2021, 0, 0],
                    bc.type: [
                        bct.design,
                        bct.interior,
                        bct.operational,
                        bct.operational,
                    ],
                    bc.snapshot_id: [101, 102, 101, 102],
                    bc.company_fund_id: [201, 202, 201, 202],
                    bc.response_id: [1, 2, 1, 2],
                    bc.building_data_certifications_scoring_id: [1, 2, 3, 4],
                }
            ),
        ),
    ],
)
def test_add_uncertified_assets_mock_certifications(
    certification_data, asset_data, expected_result
):
    result = bcc.add_uncertified_assets_mock_certifications(
        certification_data, asset_data
    )
    pd.testing.assert_frame_equal(result, expected_result)


@pytest.mark.parametrize(
    "certification_data, certification_table, expected_result",
    [
        (
            pd.DataFrame({bc.certification_id: [1, 2, 3]}),
            pd.DataFrame(
                {
                    bctc.certification_id: [1, 2, 3],
                    bctc.scoring_status: [
                        bcvp.full_points,
                        bcvp.partial_plus,
                        bcvp.partial_minus,
                    ],
                }
            ),
            pd.DataFrame(
                {
                    bc.certification_id: [1, 2, 3],
                    bc.validation_status: [1, 0.6, 0.3],
                }
            ),
        ),
        # Add more test cases here if needed
    ],
)
def test_add_validation_status(
    certification_data, certification_table, expected_result
):
    result = bcc.add_validation_status(certification_data, certification_table)

    pd.testing.assert_frame_equal(
        result[[bc.certification_id, bc.validation_status]],
        expected_result,
    )


@pytest.mark.parametrize(
    "certification_data, certification_table, expected_result",
    [
        (
            pd.DataFrame({bc.certification_id: [1, 2, 3]}),
            pd.DataFrame(
                {
                    bctc.certification_id: [1, 2, 3],
                    bctc.scoring_status: [
                        np.nan,
                        bcvp.full_points,
                        bcvp.partial_plus,
                    ],
                }
            ),
            pd.DataFrame(
                {bc.certification_id: [1, 2, 3], bc.validation_status: [0, 1, 0.6]}
            ),
        ),
    ],
)
def test_add_validation_status_with_na(
    certification_data, certification_table, expected_result
):
    result = bcc.add_validation_status(certification_data, certification_table)

    pd.testing.assert_frame_equal(
        result[[bc.certification_id, bc.validation_status]],
        expected_result,
    )


def create_dataframe(data, columns):
    return pd.DataFrame(data, columns=columns)


@pytest.mark.parametrize(
    "certification_data, asset_data, expected",
    [
        (
            create_dataframe(
                [[1, 100], [2, 200]],
                [bc.portfolio_asset_id, bc.covered_floor_area],
            ),
            create_dataframe(
                [[1, asc_const.sqft], [2, asc_const.m2]],
                [ac.portfolio_asset_id, ac.area_unit],
            ),
            create_dataframe(
                [[1, 100 * asc_const.sqft_to_sqm], [2, 200]],
                [ac.portfolio_asset_id, bc.covered_floor_area_m2],
            ),
        ),
        (
            create_dataframe(
                [[1, 150], [2, 250]],
                [bc.portfolio_asset_id, bc.covered_floor_area],
            ),
            create_dataframe(
                [[1, asc_const.m2], [2, asc_const.sqft]],
                [ac.portfolio_asset_id, ac.area_unit],
            ),
            create_dataframe(
                [[1, 150], [2, 250 * asc_const.sqft_to_sqm]],
                [ac.portfolio_asset_id, bc.covered_floor_area_m2],
            ),
        ),
        (
            create_dataframe(
                [[1, 150], [2, 250]],
                [bc.portfolio_asset_id, bc.covered_floor_area],
            ),
            create_dataframe(
                [[1, asc_const.m2], [2, asc_const.sqft]],
                [ac.portfolio_asset_id, ac.area_unit],
            ),
            create_dataframe(
                [[1, 150], [2, 250 * 0.092903]],
                [ac.portfolio_asset_id, bc.covered_floor_area_m2],
            ),
        ),
    ],
)
def test_add_covered_floor_area_m2(certification_data, asset_data, expected):
    result = bcc.add_covered_floor_area_m2(certification_data, asset_data)
    pd.testing.assert_frame_equal(
        result[[ac.portfolio_asset_id, bc.covered_floor_area_m2]], expected
    )


@pytest.mark.parametrize(
    "certification_data, asset_data",
    [
        (
            create_dataframe(
                [[1, 100], [2, 200]],
                [ac.portfolio_asset_id, bc.covered_floor_area],
            ),
            create_dataframe(
                [[1, "invalid_unit"], [2, asc_const.m2]],
                [ac.portfolio_asset_id, ac.area_unit],
            ),
        )
    ],
)
def test_add_covered_floor_area_m2_invalid_unit(certification_data, asset_data):
    with pytest.raises(
        ValueError,
        match="filter_asset_is_unit: area_unit should be one of 'm2' or 'sqft'",
    ):
        bcc.add_covered_floor_area_m2(certification_data, asset_data)
