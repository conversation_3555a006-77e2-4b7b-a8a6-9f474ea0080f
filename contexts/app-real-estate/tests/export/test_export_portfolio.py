import pandas as pd
import pytest
import app_real_estate.constants.column_names.key_columns as kc
import app_real_estate.constants.column_names.export_columns as pc
from app_real_estate.constants.helper_enumerators import Indicator
from app_real_estate.transformation.export.indicator_data_formatter import (
    IndicatorDataFormatter,
)


@pytest.fixture
def scored_portfolio_data_dict():
    indicator_columns = IndicatorDataFormatter.indicator_columns

    return {
        Indicator.EN_1: pd.DataFrame(
            [[1] * 16],
            columns=[kc.response_id] + indicator_columns[Indicator.EN_1],
        ),
        Indicator.GH_1: pd.DataFrame(
            [[1] * 11],
            columns=[kc.response_id] + indicator_columns[Indicator.GH_1],
        ),
        Indicator.WS_1: pd.DataFrame(
            [[1] * 7],
            columns=[kc.response_id] + indicator_columns[Indicator.WS_1],
        ),
        Indicator.WT_1: pd.DataFrame(
            [[1] * 15],
            columns=[kc.response_id] + indicator_columns[Indicator.WT_1],
        ),
        Indicator.BC_1_1: pd.DataFrame(
            [[1] * 6],
            columns=[kc.response_id] + indicator_columns[Indicator.BC_1_1],
        ),
        Indicator.BC_1_2: pd.DataFrame(
            [[1] * 6],
            columns=[kc.response_id] + indicator_columns[Indicator.BC_1_2],
        ),
        Indicator.BC_1: pd.DataFrame(
            [[1] * 5],
            columns=[kc.response_id] + indicator_columns[Indicator.BC_1],
        ),
        Indicator.BC_2: pd.DataFrame(
            [[1] * 6],
            columns=[kc.response_id] + indicator_columns[Indicator.BC_2],
        ),
    }


@pytest.fixture
def expected_portfolio_data():
    columns = [
        pc.RESPONSE_ID,
        pc.SCORE_EN_1,
        pc.SCORE_F_EN_1,
        pc.SCORE_P_EN_1,
        pc.SCORE_MAX_EN_1,
        pc.SCORE_GH_1,
        pc.SCORE_F_GH_1,
        pc.SCORE_P_GH_1,
        pc.SCORE_MAX_GH_1,
        pc.SCORE_WS_1,
        pc.SCORE_F_WS_1,
        pc.SCORE_P_WS_1,
        pc.SCORE_MAX_WS_1,
        pc.SCORE_WT_1,
        pc.SCORE_F_WT_1,
        pc.SCORE_P_WT_1,
        pc.SCORE_MAX_WT_1,
        pc.SCORE_BC_1_1,
        pc.SCORE_F_BC_1_1,
        pc.SCORE_P_BC_1_1,
        pc.SCORE_MAX_BC_1_1,
        pc.SCORE_BC_1_2,
        pc.SCORE_F_BC_1_2,
        pc.SCORE_P_BC_1_2,
        pc.SCORE_MAX_BC_1_2,
        pc.SCORE_BC_1,
        pc.SCORE_F_BC_1,
        pc.SCORE_P_BC_1,
        pc.SCORE_MAX_BC_1,
        pc.SCORE_BC_2,
        pc.SCORE_F_BC_2,
        pc.SCORE_P_BC_2,
        pc.SCORE_MAX_BC_2,
    ]
    return pd.DataFrame([[1] * 33], columns=columns)


@pytest.mark.skip("Need to add all columns")
def test_portfolio_data_formatter(scored_portfolio_data_dict, expected_portfolio_data):
    result = IndicatorDataFormatter(scored_portfolio_data_dict).process()

    pd.testing.assert_frame_equal(result, expected_portfolio_data)
