import pandas as pd
import numpy as np
import pytest
from app_real_estate.transformation.benchmarking.calculate_benchmark_metrics import (
    BenchmarkMetricCalculator as bmc,
)
from app_real_estate.transformation.metric_calculation.asset_filters import (
    AssetFilters as af,
)
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.ghg_columns as gc
import app_real_estate.constants.column_names.water_columns as wc
import app_real_estate.constants.column_names.waste_columns as wsc
import app_real_estate.constants.column_names.benchmark_group_memberships_columns as mc
import app_real_estate.constants.column_names.building_certification_columns as bc


def test_utility_coverage_benchmark():
    mock_data = pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3, 4, 5, 6, 7, 1],
            ac.survey_year: [2024] * 8,
            ac.data_year: [2023] * 7 + [2022] * 1,
            ac.standing_investment_for_aggregation: [True] * 8,
            ac.owned_for_aggregation: [True] * 8,
            ac.owned_entire_period: [True] * 8,
            ac.ncmr_status: ["Standing Investment"] * 8,
            ec.en_area_time_cov_p_lc: [100, 32, 4, 78, 67, np.nan, 97, np.nan],
            ec.en_area_time_cov_p_tc: [
                35,
                100,
                49,
                100,
                57,
                np.nan,
                100,
                np.nan,
            ],
            wc.wat_area_time_cov_p_lc: [
                58,
                93,
                8,
                78,
                100,
                np.nan,
                100,
                np.nan,
            ],
            wc.wat_area_time_cov_p_tc: [
                75,
                35,
                50,
                100,
                92,
                np.nan,
                1,
                np.nan,
            ],
            gc.ghg_area_time_cov_p_s12: [
                91,
                37,
                11,
                78,
                100,
                np.nan,
                74,
                np.nan,
            ],
            gc.ghg_area_time_cov_p_s3: [
                85,
                100,
                30,
                100,
                100,
                np.nan,
                100,
                np.nan,
            ],
            wsc.was_area_cov_p_lc: [
                65,
                100,
                30,
                100,
                53,
                np.nan,
                87,
                np.nan,
            ],
            wsc.was_area_cov_p_tc: [
                65,
                100,
                30,
                100,
                53,
                np.nan,
                87,
                np.nan,
            ],
        }
    )

    mock_memberships = pd.DataFrame(
        {
            mc.id: [1, 2, 3, 4, 5, 7],
            mc.group_id: [1, 1, 2, 2, 1, 2],
            mc.portfolio_asset_id: [1, 2, 3, 4, 5, 7],
            mc.is_benchmarkable: [True] * 6,
        }
    )

    c = bmc()

    is_cy_op = af.filter_current_year_operational_assets(mock_data)
    valid_data = mock_data[is_cy_op]
    valid_data = valid_data.drop(5)
    mock_memberships = c.calculate_en1_coverage_benchmark(
        valid_data, mock_memberships, "lc"
    )
    mock_memberships = c.calculate_en1_coverage_benchmark(
        valid_data, mock_memberships, "tc"
    )
    mock_memberships = c.calculate_gh1_coverage_benchmark(
        valid_data, mock_memberships, "s12"
    )
    mock_memberships = c.calculate_gh1_coverage_benchmark(
        valid_data, mock_memberships, "s3"
    )
    mock_memberships = c.calculate_wt1_coverage_benchmark(
        valid_data, mock_memberships, "lc"
    )
    mock_memberships = c.calculate_wt1_coverage_benchmark(
        valid_data, mock_memberships, "tc"
    )
    mock_memberships = c.calculate_ws1_coverage_benchmark(
        valid_data, mock_memberships, "lc"
    )
    mock_memberships = c.calculate_ws1_coverage_benchmark(
        valid_data, mock_memberships, "tc"
    )

    expected_mean_en_lc = [
        0.663333,
        0.663333,
        0.596666,
        0.596666,
        0.663333,
        0.596666,
    ]
    expected_mean_en_tc = [0.64, 0.64, 0.83, 0.83, 0.64, 0.83]
    expected_mean_ghg_s12 = [0.76, 0.76, 0.543333, 0.543333, 0.76, 0.543333]
    expected_mean_ghg_s3 = [0.95, 0.95, 0.766666, 0.766666, 0.95, 0.766666]
    expected_mean_wat_lc = [0.836666, 0.836666, 0.62, 0.62, 0.836666, 0.62]
    expected_mean_wat_tc = [
        0.673333,
        0.673333,
        0.503333,
        0.503333,
        0.673333,
        0.503333,
    ]
    expected_mean_was_lc = [
        0.726666,
        0.726666,
        0.723333,
        0.723333,
        0.726666,
        0.723333,
    ]
    expected_mean_was_tc = [
        0.726666,
        0.726666,
        0.723333,
        0.723333,
        0.726666,
        0.723333,
    ]

    assert mock_memberships[mc.mean_en_area_time_cov_p_lc].tolist() == pytest.approx(
        expected_mean_en_lc, rel=1e-4, nan_ok=True
    ), "Test for benchmark mean landlord controlled energy coverage calculation failed."

    assert mock_memberships[mc.mean_en_area_time_cov_p_tc].tolist() == pytest.approx(
        expected_mean_en_tc, rel=1e-4, nan_ok=True
    ), "Test for benchmark mean tenant controlled energy coverage calculation failed."

    assert mock_memberships[mc.mean_ghg_area_time_cov_p_s12].tolist() == pytest.approx(
        expected_mean_ghg_s12, rel=1e-4, nan_ok=True
    ), "Test for benchmark mean GHG scope 1 and 2 coverage calculation failed."

    assert mock_memberships[mc.mean_ghg_area_time_cov_p_s3].tolist() == pytest.approx(
        expected_mean_ghg_s3, rel=1e-4, nan_ok=True
    ), "Test for benchmark mean GHG scope 3 coverage calculation failed."

    assert mock_memberships[mc.mean_wat_area_time_cov_p_lc].tolist() == pytest.approx(
        expected_mean_wat_lc, rel=1e-4, nan_ok=True
    ), "Test for benchmark mean landlord controlled water coverage calculation failed."

    assert mock_memberships[mc.mean_wat_area_time_cov_p_tc].tolist() == pytest.approx(
        expected_mean_wat_tc, rel=1e-4, nan_ok=True
    ), "Test for benchmark mean tenant controlled water coverage calculation failed."

    assert mock_memberships[mc.mean_was_area_cov_p_lc].tolist() == pytest.approx(
        expected_mean_was_lc, rel=1e-4, nan_ok=True
    ), "Test for benchmark mean landlord controlled waste coverage calculation failed."

    assert mock_memberships[mc.mean_was_area_cov_p_tc].tolist() == pytest.approx(
        expected_mean_was_tc, rel=1e-4, nan_ok=True
    ), "Test for benchmark mean tenant controlled waste coverage calculation failed."


def test_diverted_waste_benchmark():
    mock_data = pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3, 4, 5, 6, 7, 1],
            ac.survey_year: [2024] * 8,
            ac.data_year: [2023] * 7 + [2022] * 1,
            ac.standing_investment_for_aggregation: [True] * 8,
            ac.owned_for_aggregation: [True] * 8,
            ac.owned_entire_period: [True] * 8,
            ac.ncmr_status: ["Standing Investment"] * 8,
            ac.asset_size_m2: [
                5771,
                1495,
                2458,
                52078,
                np.nan,
                5771,
                2458,
                5771,
            ],
            wsc.was_pabs_div: [0, 100, 30, 100, 53, np.nan, 87, np.nan],
        }
    )

    mock_memberships = pd.DataFrame(
        {
            mc.id: [1, 2, 3, 4, 5, 6],
            mc.group_id: [1, 1, 2, 2, 1, 2],
            mc.portfolio_asset_id: [1, 2, 3, 4, 5, 7],
            mc.is_benchmarkable: [True] * 6,
        }
    )

    c = bmc()

    # Correct way to actually use the function, otherwise it throws an error.
    is_cy_op = af.filter_current_year_operational_assets(mock_data)
    mock_memberships = c.calculate_diverted_waste_coverage_benchmark(
        mock_data[is_cy_op], mock_memberships
    )

    expected_mean_div_percent = [0.51, 0.51, 0.7233, 0.7233, 0.51, 0.7233]

    assert mock_memberships[mc.mean_was_diverted_percent].tolist() == pytest.approx(
        expected_mean_div_percent, rel=1e-4, nan_ok=True
    ), "Test for benchmark mean diverted waste calculation failed."


def test_energy_ratings_coverage_benchmark():
    mock_data = pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3, 4, 5, 6, 7, 1],
            ac.survey_year: [2024] * 8,
            ac.data_year: [2023] * 7 + [2022] * 1,
            ac.standing_investment_for_aggregation: [True] * 8,
            ac.owned_for_aggregation: [True] * 8,
            ac.owned_entire_period: [True] * 8,
            ac.ncmr_status: ["Standing Investment"] * 8,
            ac.asset_size_m2: [
                5771,
                1495,
                2458,
                52078,
                np.nan,
                5771,
                2458,
                5771,
            ],
            ac.rating_coverage: [1, 99, 29, 100, 42, np.nan, 34, np.nan],
        }
    )

    mock_memberships = pd.DataFrame(
        {
            mc.id: [1, 2, 3, 4, 5, 6],
            mc.group_id: [1, 1, 2, 2, 1, 2],
            mc.portfolio_asset_id: [1, 2, 3, 4, 5, 7],
            mc.is_benchmarkable: [True] * 6,
        }
    )

    c = bmc()

    # Correct way to actually use the function, otherwise it throws an error.
    is_cy_op = af.filter_current_year_operational_assets(mock_data)
    mock_memberships = c.calculate_energy_rating_coverage_benchmark(
        mock_data[is_cy_op], mock_memberships
    )

    expected_mean_er_coverage = [
        0.4733,
        0.4733,
        0.5433,
        0.5433,
        0.4733,
        0.5433,
    ]

    assert mock_memberships[mc.mean_energy_rating_coverage].tolist() == pytest.approx(
        expected_mean_er_coverage, rel=1e-4, nan_ok=True
    ), "Test for benchmark mean energy ratings coverage calculation failed."


def test_building_certifications_coverage_benchmark():
    mock_data = pd.DataFrame(
        {
            bc.building_data_certifications_scoring_id: [1, 2, 3, 4, 5, 6, 7],
            bc.survey_year: [2024] * 7,
            bc.scoring_coverage: [3, 75, 29, 100, 42, 34, np.nan],
        }
    )

    mock_memberships = pd.DataFrame(
        {
            mc.id: [1, 2, 3, 4, 5, 6],
            mc.group_id: [1, 1, 1, 2, 2, 2],
            mc.building_data_certifications_scoring_id: [1, 2, 3, 4, 5, 6],
            mc.is_benchmarkable: [True] * 6,
        }
    )

    memberships_missing_value = pd.DataFrame(
        {
            mc.id: [1, 2, 3, 4, 5, 6, 7],
            mc.group_id: [1, 1, 1, 2, 2, 2, 2],
            mc.building_data_certifications_scoring_id: [1, 2, 3, 4, 5, 6, 7],
            mc.is_benchmarkable: [True] * 7,
        }
    )

    c = bmc()

    mock_memberships = c.calculate_certification_coverage_benchmark(
        mock_data, mock_memberships
    )

    expected_mean_bc_coverage = [
        0.35666,
        0.35666,
        0.35666,
        0.58666,
        0.58666,
        0.58666,
    ]

    assert mock_memberships[mc.mean_certification_coverage].tolist() == pytest.approx(
        expected_mean_bc_coverage, rel=1e-4, nan_ok=True
    ), "Test for benchmark ranks for building certifications coverage calculation failed."

    # Test with missing values in benchmark value.
    with pytest.raises(
        ValueError,
        match="calculate_benchmark: missing values .*",
    ):
        c.calculate_certification_coverage_benchmark(
            mock_data, memberships_missing_value
        )


def test_intensity_benchmark():
    mock_data = pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3, 4, 5, 6, 7, 1, 8],
            ac.survey_year: [2024] * 9,
            ac.data_year: [2023] * 7 + [2022] * 1 + [2023],
            ac.standing_investment_for_aggregation: [True] * 9,
            ac.owned_for_aggregation: [True] * 9,
            ac.owned_entire_period: [True] * 9,
            ac.ncmr_status: ["Standing Investment"] * 9,
            ec.en_efficiency_int_kwh_m2: [90, 3, 55, 84, 42, 33, 34, np.nan, np.nan],
            gc.ghg_scored_int_ton_m2: [69, 99, 99, 10, 25, 10, 100, np.nan, np.nan],
            wc.wat_scored_int_m3_m2: [16, 78, 2, 86, 12, 50, 77, np.nan, np.nan],
        }
    )

    mock_memberships = pd.DataFrame(
        {
            mc.id: [1, 2, 3, 4, 5, 6, 7],
            mc.group_id: [1, 1, 1, 1, 2, 2, 2],
            mc.portfolio_asset_id: [1, 2, 3, 4, 5, 6, 7],
            mc.is_benchmarkable: [True] * 7,
        }
    )

    c = bmc()

    is_cy_op = af.filter_current_year_operational_assets(mock_data)
    current_year_data = mock_data[is_cy_op]

    mock_memberships = c.calculate_energy_efficiency_benchmark(
        current_year_data, mock_memberships
    )
    mock_memberships = c.calculate_ghg_intensity_benchmark(
        current_year_data, mock_memberships
    )
    mock_memberships = c.calculate_water_intensity_benchmark(
        current_year_data, mock_memberships
    )

    expected_rank_en_int = [1, 0, 0.5, 1, 1, 0, 1]
    expected_rank_ghg_int = [0.33333, 1, 1, 0, 1, 0, 1]
    expected_rank_wat_int = [0.5, 1, 0, 1, 0, 1, 1]

    expected_10th_en = [18.6, 18.6, 18.6, 18.6, 33.2, 33.2, 33.2]
    expected_90th_en = [88.2, 88.2, 88.2, 88.2, 40.4, 40.4, 40.4]

    assert mock_memberships[mc.rank_en_efficiency_intensity].tolist() == pytest.approx(
        expected_rank_en_int, rel=1e-4, nan_ok=True
    ), "Test for benchmark ranks for energy intensity failed."

    assert mock_memberships[mc.rank_ghg_intensity].tolist() == pytest.approx(
        expected_rank_ghg_int, rel=1e-4, nan_ok=True
    ), "Test for benchmark ranks for GHG intensity failed."

    assert mock_memberships[mc.rank_wat_intensity].tolist() == pytest.approx(
        expected_rank_wat_int, rel=1e-4, nan_ok=True
    ), "Test for benchmark ranks for water intensity failed."

    assert mock_memberships[
        mc.percentile_10th_en_efficiency_intensity
    ].tolist() == pytest.approx(
        expected_10th_en, rel=1e-4, nan_ok=True
    ), "Test for 10th percentile energy intensity benchmark failed."

    assert mock_memberships[
        mc.percentile_90th_en_efficiency_intensity
    ].tolist() == pytest.approx(
        expected_90th_en, rel=1e-4, nan_ok=True
    ), "Test for 90th percentile energy intensity benchmark failed."

    # Test with multiple year data.
    with pytest.raises(
        ValueError,
        match="calculate_benchmark: the benchmark should be created using current year data. Please remove previous year data.",
    ):
        c.calculate_energy_efficiency_benchmark(mock_data, mock_memberships)

    # Test with missing values in benchmark value.
    with pytest.raises(
        ValueError,
        match="calculate_benchmark: missing values .*",
    ):
        memberships_missing_value = pd.DataFrame(
            {
                mc.id: [1, 2, 3, 4, 5, 6, 7, 8],
                mc.group_id: [1, 1, 1, 1, 2, 2, 2, 2],
                mc.portfolio_asset_id: [1, 2, 3, 4, 5, 6, 7, 8],
                mc.is_benchmarkable: [True] * 8,
            }
        )
        c.calculate_energy_efficiency_benchmark(
            current_year_data, memberships_missing_value
        )
