import pandas as pd
import numpy as np
import pytest
from app_real_estate.transformation.benchmarking.calculate_percent_change_benchmark_metric import (
    PercentChangeMetricBenchmarkCalculator as bmc,
)
from app_real_estate.transformation.metric_calculation.asset_filters import (
    AssetFilters as af,
)
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.ghg_columns as gc
import app_real_estate.constants.column_names.water_columns as wc
import app_real_estate.constants.column_names.benchmark_group_memberships_columns as mc


def test_utility_lfl_benchmark():
    mock_data = pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3, 4, 5, 6, 7, 8, 9, 1],
            ac.survey_year: [2024] * 10,
            ac.data_year: [2023] * 9 + [2022] * 1,
            ac.standing_investment_for_aggregation: [True] * 10,
            ac.owned_for_aggregation: [True] * 10,
            ac.owned_entire_period: [True] * 10,
            ac.ncmr_status: ["Standing Investment"] * 10,
            ac.asset_size_m2: [
                5771,
                1495,
                2458,
                52078,
                np.nan,
                5771,
                2458,
                1000,
                10,
                5771,
            ],
            ec.en_lfl_percent_change_lc: [
                -10,
                -12,
                2,
                -8,
                -11,
                np.nan,
                -2,
                -1,
                3,
                np.nan,
            ],
            ec.en_lfl_percent_change_tc: [
                -10,
                -12,
                2,
                -8,
                -11,
                np.nan,
                -2,
                -1,
                4,
                np.nan,
            ],
            gc.ghg_lfl_percent_change_s12: [
                -9,
                -7,
                3,
                -4,
                -10,
                np.nan,
                -3,
                -1,
                -5,
                np.nan,
            ],
            gc.ghg_lfl_percent_change_s3: [
                -9,
                -7,
                3,
                -4,
                -10,
                np.nan,
                -3,
                -1,
                -3,
                np.nan,
            ],
            wc.wat_lfl_percent_change_lc: [
                -8,
                -12,
                1,
                -6,
                -9,
                np.nan,
                -1,
                -1,
                8,
                np.nan,
            ],
            wc.wat_lfl_percent_change_tc: [
                -8,
                -12,
                1,
                -6,
                -9,
                np.nan,
                -1,
                -1,
                -3,
                np.nan,
            ],
            ec.en_lfl_outlier_status: ["none"] * 10,
            gc.ghg_lfl_outlier_status: ["none"] * 10,
            wc.wat_lfl_outlier_status: ["none"] * 10,
        }
    )

    mock_memberships = pd.DataFrame(
        {
            mc.id: [1, 2, 3, 4, 5, 6, 7, 8],
            mc.group_id: [1, 1, 2, 2, 1, 2, 2, 1],
            mc.portfolio_asset_id: [1, 2, 3, 4, 5, 7, 8, 9],
            mc.is_benchmarkable: [True] * 7 + [False],
        }
    )

    c = bmc()

    is_cy_op = af.filter_current_year_operational_assets(mock_data)
    current_year_data = mock_data[is_cy_op]
    mock_memberships = c.calculate_en1_lfl_benchmark(
        current_year_data, mock_memberships, "lc"
    )
    mock_memberships = c.calculate_en1_lfl_benchmark(
        current_year_data, mock_memberships, "tc"
    )
    mock_memberships = c.calculate_gh1_lfl_benchmark(
        current_year_data, mock_memberships, "s12"
    )
    mock_memberships = c.calculate_gh1_lfl_benchmark(
        current_year_data, mock_memberships, "s3"
    )
    mock_memberships = c.calculate_wt1_lfl_benchmark(
        current_year_data, mock_memberships, "lc"
    )
    mock_memberships = c.calculate_wt1_lfl_benchmark(
        current_year_data, mock_memberships, "tc"
    )

    expected_bench_en_lfl_lc = [0.8695, 1, 0, 1, 0.9565, 0.5714, 0.2857, 0]
    expected_bench_ghg_lfl_s12 = [
        0.9473,
        0.7368,
        0,
        1,
        1,
        0.9230,
        0.30769,
        0.52631,
    ]
    expected_bench_wat_lfl_lc = [0.7619, 1, 0, 1, 0.8571, 0.44444, 0.44444, 0]

    expected_mean_en_lfl_lc = [
        0.9420,
        0.9420,
        0.46427,
        0.46427,
        0.9420,
        0.46427,
        0.46427,
        0.9420,
    ]
    expected_mean_ghg_lfl_s12 = [
        0.89469,
        0.89469,
        0.55765,
        0.55765,
        0.89469,
        0.55765,
        0.55765,
        0.89469,
    ]
    expected_mean_wat_lfl_lc = [
        0.87299,
        0.87299,
        0.4722,
        0.4722,
        0.87299,
        0.4722,
        0.4722,
        0.87299,
    ]

    assert mock_memberships[
        mc.bench_en_lfl_percent_change_lc
    ].tolist() == pytest.approx(
        expected_bench_en_lfl_lc, rel=1e-4, nan_ok=True
    ), "Test for energy benchmark LFL percent change value calculation failed."

    assert mock_memberships[
        mc.bench_ghg_lfl_percent_change_s12
    ].tolist() == pytest.approx(
        expected_bench_ghg_lfl_s12, rel=1e-4, nan_ok=True
    ), "Test for GHG benchmark LFL percent change value calculation failed."

    assert mock_memberships[
        mc.bench_wat_lfl_percent_change_lc
    ].tolist() == pytest.approx(
        expected_bench_wat_lfl_lc, rel=1e-4, nan_ok=True
    ), "Test for water benchmark LFL percent change value calculation failed."

    assert mock_memberships[mc.mean_en_lfl_percent_change_lc].tolist() == pytest.approx(
        expected_mean_en_lfl_lc, rel=1e-4, nan_ok=True
    ), "Test for energy benchmark LFL percent change mean calculation failed."

    assert mock_memberships[
        mc.mean_ghg_lfl_percent_change_s12
    ].tolist() == pytest.approx(
        expected_mean_ghg_lfl_s12, rel=1e-4, nan_ok=True
    ), "Test for GHG benchmark LFL percent change mean calculation failed."

    assert mock_memberships[
        mc.mean_wat_lfl_percent_change_lc
    ].tolist() == pytest.approx(
        expected_mean_wat_lfl_lc, rel=1e-4, nan_ok=True
    ), "Test for water benchmark LFL percent change mean calculation failed."

    # Test with multiple year data.
    with pytest.raises(
        ValueError,
        match="calculate_percent_change_benchmark: the benchmark should be created using current year data. Please remove previous year data.",
    ):
        c.calculate_en1_lfl_benchmark(mock_data, mock_memberships, "lc")

    # Test with missing values in benchmark value.
    with pytest.raises(
        ValueError,
        match="calculate_percent_change_benchmark: missing values .*",
    ):
        memberships_missing_value = pd.DataFrame(
            {
                mc.id: [1, 2, 3, 4, 5, 6, 7, 8],
                mc.group_id: [1, 1, 2, 2, 1, 1, 2, 2],
                mc.portfolio_asset_id: [1, 2, 3, 4, 5, 6, 7, 8],
                mc.is_benchmarkable: [True] * 8,
            }
        )
        c.calculate_en1_lfl_benchmark(
            current_year_data, memberships_missing_value, "lc"
        )


def test_ren_rec_benchmark():
    mock_data = pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3, 4, 5, 6, 7, 8, 1],
            ac.survey_year: [2024] * 9,
            ac.data_year: [2023] * 8 + [2022] * 1,
            ac.standing_investment_for_aggregation: [True] * 9,
            ac.owned_for_aggregation: [True] * 9,
            ac.owned_entire_period: [True] * 9,
            ac.ncmr_status: ["Standing Investment"] * 9,
            ac.asset_size_m2: [
                5771,
                1495,
                2458,
                52078,
                np.nan,
                5771,
                2458,
                1000,
                5771,
            ],
            ec.en_ren_percent_change: [
                17,
                13,
                -3,
                7,
                14,
                np.nan,
                1,
                2,
                np.nan,
            ],
            wc.wat_rec_percent_change: [4, 5, -2, 8, 11, np.nan, 2, 1, np.nan],
        }
    )

    mock_memberships = pd.DataFrame(
        {
            mc.id: [1, 2, 3, 4, 5, 6],
            mc.group_id: [1, 1, 2, 1, 2, 2],
            mc.portfolio_asset_id: [1, 2, 4, 5, 7, 8],
            mc.is_benchmarkable: [True] * 6,
        }
    )

    c = bmc()

    is_cy_op = af.filter_current_year_operational_assets(mock_data)
    current_year_data = mock_data[is_cy_op]
    mock_memberships = c.calculate_en1_renewable_benchmark(
        current_year_data, mock_memberships
    )
    mock_memberships = c.calculate_wt1_recycled_benchmark(
        current_year_data, mock_memberships
    )

    expected_bench_en_ren_imp = [1, 0.8387, 1, 0.9032, 0.22222, 0.44444]
    expected_bench_wat_rec_imp = [0.5, 0.625, 1, 1, 0.4, 0.2]

    expected_mean_en_ren_imp = [
        0.91396,
        0.91396,
        0.55555,
        0.91396,
        0.55555,
        0.55555,
    ]
    expected_mean_wat_rec_imp = [
        0.7083,
        0.7083,
        0.5333,
        0.7083,
        0.5333,
        0.5333,
    ]

    assert mock_memberships[mc.bench_en_ren_percent_change].tolist() == pytest.approx(
        expected_bench_en_ren_imp, rel=1e-4, nan_ok=True
    ), "Test for renewable energy benchmark percent change value calculation failed."

    assert mock_memberships[mc.bench_wat_rec_percent_change].tolist() == pytest.approx(
        expected_bench_wat_rec_imp, rel=1e-4, nan_ok=True
    ), "Test for recycled water benchmark percent change value calculation failed."

    assert mock_memberships[mc.mean_en_ren_percent_change].tolist() == pytest.approx(
        expected_mean_en_ren_imp, rel=1e-4, nan_ok=True
    ), "Test for renewable energy benchmark percent change mean calculation failed."

    assert mock_memberships[mc.mean_wat_rec_percent_change].tolist() == pytest.approx(
        expected_mean_wat_rec_imp, rel=1e-4, nan_ok=True
    ), "Test for recycled water benchmark percent change mean calculation failed."

    # Test with multiple year data.
    with pytest.raises(
        ValueError,
        match="calculate_percent_change_benchmark: the benchmark should be created using current year data. Please remove previous year data.",
    ):
        c.calculate_en1_renewable_benchmark(mock_data, mock_memberships)

    # Test with missing values in benchmark value.
    with pytest.raises(
        ValueError,
        match="calculate_percent_change_benchmark: missing values .*",
    ):
        memberships_missing_value = pd.DataFrame(
            {
                mc.id: [1, 2, 3, 4, 5, 6, 7, 8],
                mc.group_id: [1, 1, 2, 2, 1, 1, 2, 2],
                mc.portfolio_asset_id: [1, 2, 3, 4, 5, 6, 7, 8],
                mc.is_benchmarkable: [True] * 8,
            }
        )
        c.calculate_en1_renewable_benchmark(
            current_year_data, memberships_missing_value
        )
