import pandas as pd
from app_real_estate.transformation.scoring_model import ScoringModel


class TestScoringModelUnit:
    """Unit tests for ScoringModel class methods."""

    def test_merge_filtered_scores_with_data_logic(self):
        """Test the _merge_filtered_scores_with_data utility method"""
        scoring_model = ScoringModel()

        # Create test data that represents a realistic scenario
        original_data = pd.DataFrame(
            {
                "portfolio_asset_id": [1, 2, 3, 4, 5],
                "name": ["Asset_A", "Asset_B", "Asset_C", "Asset_D", "Asset_E"],
                "value": [10, 20, 30, 40, 50],
            }
        )

        # Filtered data represents assets that were scored (subset of original)
        filtered_data = pd.DataFrame(
            {
                "portfolio_asset_id": [1, 3, 5],
                "name": ["Asset_A", "Asset_C", "Asset_E"],
                "value": [10, 30, 50],
                "score_en1": [0.8, 0.6, 0.9],
                "score_en1_coverage": [0.9, 0.7, 0.95],
            }
        )

        # Execute - this tests the actual merge logic
        result = scoring_model._merge_filtered_scores_with_data(
            filtered_data, original_data, "portfolio_asset_id"
        )

        # Verify merge worked correctly
        assert len(result) == 5  # All original assets preserved
        assert "score_en1" in result.columns
        assert "score_en1_coverage" in result.columns

        # Verify scored assets have scores
        assert result.loc[result["portfolio_asset_id"] == 1, "score_en1"].iloc[0] == 0.8
        assert result.loc[result["portfolio_asset_id"] == 3, "score_en1"].iloc[0] == 0.6
        assert result.loc[result["portfolio_asset_id"] == 5, "score_en1"].iloc[0] == 0.9

        # Verify unscored assets have NaN scores
        assert result.loc[result["portfolio_asset_id"] == 2, "score_en1"].isna().iloc[0]
        assert result.loc[result["portfolio_asset_id"] == 4, "score_en1"].isna().iloc[0]

        # Verify original data integrity
        assert result["name"].tolist() == [
            "Asset_A",
            "Asset_B",
            "Asset_C",
            "Asset_D",
            "Asset_E",
        ]
