import pytest
import pandas as pd
import numpy as np
from unittest.mock import patch, ANY
from app_real_estate.transformation.scoring_model import ScoringModel
from app_real_estate.models.aggregated_score import AggregatedScore
from app_real_estate.constants.indicator_subscore_weights import indicator_weights
from app_real_estate.transformation.scoring.bc1_asset_score_calculator import (
    BC1AssetScoreCalculator,
)
from app_real_estate.transformation.scoring.common_indicator_score_utils import (
    CommonIndicatorScoreUtils,
)
from app_real_estate.transformation.scoring.benchmark_score_utils import (
    BenchmarkScoreUtils,
)
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.building_certification_columns as bc
import app_real_estate.constants.column_names.benchmark_group_memberships_columns as mc


class TestScoringModelComponent:
    """Component tests for ScoringModel class - focusing on business logic and critical integration points."""

    @pytest.fixture
    def mock_asset_data(self):
        """Create minimal mock asset data for testing."""
        return pd.DataFrame(
            {
                "portfolio_asset_id": [1, 2, 3],
                "response_id": [101, 102, 103],
                "company_fund_id": [201, 202, 203],
                "survey_year": [2023, 2023, 2023],
                "snapshot_id": [1, 1, 1],
                "data_year": [2023, 2023, 2023],
                "country": ["US", "UK", "DE"],
                "property_type_code": ["OFF", "RET", "OFF"],
                "standing_investment_for_aggregation": [1.0, 1.0, 1.0],
                "owned_for_aggregation": [1.0, 1.0, 1.0],
                "asset_ownership": [100.0, 100.0, 100.0],
            }
        )

    @pytest.fixture
    def mock_certification_data(self):
        """Create minimal mock certification data."""
        return pd.DataFrame(
            {
                "building_data_certifications_scoring_id": [1, 2, 3],
                "portfolio_asset_id": [1, 2, 3],
                "response_id": [101, 102, 103],
                "country": ["US", "UK", "DE"],
                "property_type_code": ["OFF", "RET", "OFF"],
                "data_year": [2023, 2023, 2023],
                "year": [2023, 2023, 2023],
                "type": ["design", "construction", "operational"],
                "scoring_coverage": [0.8, 0.9, 0.85],
                "indicator": ["BC1.1", "BC1.1", "BC1.2"],
            }
        )

    @pytest.fixture
    def mock_r1_table(self):
        """Create minimal mock R1 table data."""
        return pd.DataFrame(
            {
                "response_id": [101, 102, 103],
                "property_type_code": ["OFF", "RET", "OFF"],
                "country": ["US", "UK", "DE"],
                "data_year": [2023, 2023, 2023],
                "pgav": [1000000, 1500000, 1200000],
            }
        )

    @pytest.fixture
    def scoring_model(self):
        """Create ScoringModel instance."""
        return ScoringModel()

    def test_score_en1_integration_flow(
        self, scoring_model, mock_asset_data, mock_r1_table
    ):
        """Test EN1 scoring pipeline integration - representative test for all utility indicators."""
        # Mock the component methods
        with patch.object(
            scoring_model, "data_preparer"
        ) as mock_data_preparer, patch.object(
            scoring_model, "benchmarker"
        ) as mock_benchmarker, patch.object(
            scoring_model, "indicator_scorer"
        ) as mock_indicator_scorer, patch.object(
            scoring_model, "indicator_aggregator"
        ) as mock_indicator_aggregator:

            # Setup mock returns - testing that data flows through the pipeline correctly
            mock_data_preparer.prepare_data_en1.return_value = {
                "asset_data": mock_asset_data,
                "asset_data_cy": mock_asset_data,
                "descriptions": pd.DataFrame(),
            }

            mock_groups = {"group1": pd.DataFrame()}
            mock_memberships = {"membership1": pd.DataFrame()}
            mock_benchmarker.benchmark_metrics_en1.return_value = (
                mock_groups,
                mock_memberships,
            )

            mock_scored_data = mock_asset_data.copy()
            mock_scored_data["score_en1"] = [0.8, 0.9, 0.7]
            mock_indicator_scorer.score_en1.return_value = mock_scored_data

            mock_aggregated_score = AggregatedScore(
                portfolio_level=pd.DataFrame(
                    {"response_id": [101, 102], "score_en1": [0.8, 0.9]}
                ),
                propertysubtype_country_responseid=pd.DataFrame(
                    {"response_id": [101, 102], "score_en1": [0.8, 0.9]}
                ),
                propertysubtype_responseid=pd.DataFrame(
                    {"response_id": [101, 102], "score_en1": [0.8, 0.9]}
                ),
            )
            mock_indicator_aggregator.aggregate_score_en1.return_value = (
                mock_aggregated_score
            )

            # Execute
            result = scoring_model.score_en1(
                mock_asset_data, pd.DataFrame(), pd.DataFrame(), mock_r1_table
            )

            # Verify pipeline orchestration
            assert len(result) == 4
            scored_data, groups_dict, memberships_dict, aggregated_scores = result

            # Verify components are called in correct order with correct parameters
            mock_data_preparer.prepare_data_en1.assert_called_once()
            mock_benchmarker.benchmark_metrics_en1.assert_called_once()
            mock_indicator_scorer.score_en1.assert_called_once_with(
                ANY, mock_memberships
            )
            mock_indicator_aggregator.aggregate_score_en1.assert_called_once_with(
                scored_data_en=ANY, r1_table=mock_r1_table
            )

            # Verify return structure
            assert isinstance(scored_data, pd.DataFrame)
            assert isinstance(aggregated_scores, AggregatedScore)

    def test_score_bc1_combination_logic(self, scoring_model, mock_r1_table):
        """Test BC1 score combination functionality - tests actual business logic."""
        # Create mock aggregated scores for BC1.1 and BC1.2
        bc1_1_score = AggregatedScore(
            portfolio_level=pd.DataFrame(
                {
                    "response_id": [101, 102],
                    "data_year": [2023, 2023],
                    "score_bc1_1": [0.8, 0.9],
                }
            ),
            propertysubtype_country_responseid=pd.DataFrame(
                {
                    "response_id": [101, 102],
                    "property_type_code": ["OFF", "RET"],
                    "country": ["US", "UK"],
                    "data_year": [2023, 2023],
                    "score_bc1_1": [0.8, 0.9],
                }
            ),
            propertysubtype_responseid=pd.DataFrame(
                {
                    "response_id": [101, 102],
                    "property_type_code": ["OFF", "RET"],
                    "data_year": [2023, 2023],
                    "score_bc1_1": [0.8, 0.9],
                }
            ),
        )

        bc1_2_score = AggregatedScore(
            portfolio_level=pd.DataFrame(
                {
                    "response_id": [101, 102],
                    "data_year": [2023, 2023],
                    "score_bc1_2": [0.7, 0.8],
                }
            ),
            propertysubtype_country_responseid=pd.DataFrame(
                {
                    "response_id": [101, 102],
                    "property_type_code": ["OFF", "RET"],
                    "country": ["US", "UK"],
                    "data_year": [2023, 2023],
                    "score_bc1_2": [0.7, 0.8],
                }
            ),
            propertysubtype_responseid=pd.DataFrame(
                {
                    "response_id": [101, 102],
                    "property_type_code": ["OFF", "RET"],
                    "data_year": [2023, 2023],
                    "score_bc1_2": [0.7, 0.8],
                }
            ),
        )

        # Execute - this tests the actual merge logic
        result = scoring_model.score_bc1(bc1_1_score, bc1_2_score, mock_r1_table)

        # Verify the merge worked correctly
        assert isinstance(result, AggregatedScore)
        assert "score_bc1_1" in result.portfolio_level.columns
        assert "score_bc1_2" in result.portfolio_level.columns
        assert len(result.portfolio_level) == 2

        # Verify data integrity after merge
        assert result.portfolio_level["score_bc1_1"].tolist() == [0.8, 0.9]
        assert result.portfolio_level["score_bc1_2"].tolist() == [0.7, 0.8]

    def test_bc1_indicator_validation(
        self, scoring_model, mock_asset_data, mock_certification_data, mock_r1_table
    ):
        """Test error handling for invalid indicator names"""
        with pytest.raises(
            ValueError,
            match="valid indicator names for BC1 scoring are 'BC1.1' and 'BC1.2'",
        ):
            scoring_model.score_bc1_1_or_2(
                mock_asset_data,
                mock_certification_data,
                pd.DataFrame(),
                mock_r1_table,
                "INVALID_INDICATOR",
            )

    def test_bc1_1_or_2_asset_level_flow(
        self, scoring_model, mock_asset_data, mock_certification_data, mock_r1_table
    ):
        """Test BC1.1/BC1.2 asset level scoring flow - tests the unique BC1 pipeline structure."""
        with patch.object(
            scoring_model, "data_preparer"
        ) as mock_data_preparer, patch.object(
            scoring_model, "benchmarker"
        ) as mock_benchmarker, patch.object(
            scoring_model, "indicator_scorer"
        ) as mock_indicator_scorer, patch.object(
            scoring_model, "indicator_aggregator"
        ) as mock_indicator_aggregator:

            # Setup mocks for BC1.1 specific flow
            mock_data_preparer.prepare_data_bc1_asset_level.return_value = (
                mock_asset_data,
                mock_certification_data,
                np.array(
                    [False, False, True]
                ),  # is_operational (BC1.1 uses non-operational)
                np.array([True, True, True]),  # is_cy_op
            )

            mock_benchmarker.benchmark_metrics_bc1.return_value = ({}, {})

            mock_scored_cert = mock_certification_data.copy()
            mock_scored_cert["score_bc1_1"] = [0.8, 0.9, 0.0]
            mock_scored_asset = mock_asset_data.copy()
            mock_scored_asset["score_bc1_1"] = [0.8, 0.9, 0.0]
            mock_indicator_scorer.score_bc1_1.return_value = (
                mock_scored_cert,
                mock_scored_asset,
            )

            mock_aggregated_score = AggregatedScore(
                portfolio_level=pd.DataFrame(),
                propertysubtype_country_responseid=pd.DataFrame(),
                propertysubtype_responseid=pd.DataFrame(),
            )
            mock_indicator_aggregator.aggregate_score_bc1_1.return_value = (
                mock_aggregated_score
            )

            # Execute
            result = scoring_model.score_bc1_1_or_2(
                mock_asset_data,
                mock_certification_data,
                pd.DataFrame(),
                mock_r1_table,
                "BC1.1",
            )

            # Verify BC1-specific flow
            assert (
                len(result) == 5
            )  # BC1 returns 5 elements (different from utility indicators)
            (
                scored_assets,
                scored_certs,
                groups_dict,
                memberships_dict,
                aggregated_scores,
            ) = result

            # Verify BC1-specific method calls
            mock_data_preparer.prepare_data_bc1_asset_level.assert_called_once()
            mock_indicator_scorer.score_bc1_1.assert_called_once()  # BC1.1 specific scorer
            mock_indicator_aggregator.aggregate_score_bc1_1.assert_called_once()

    def test_scoring_model_uses_correct_weights_by_report_type(self):
        """Test that ScoringModel instances use the correct weights based on report type."""
        # Create scoring models for different report types
        re_scoring_model = ScoringModel("re")
        res_scoring_model = ScoringModel("res")

        # Verify that the indicator scorer propagates the report type correctly
        assert re_scoring_model.indicator_scorer.report_type == "re"
        assert res_scoring_model.indicator_scorer.report_type == "res"

        # Verify that the weight instances are actually different (not the same object)
        assert (
            re_scoring_model.indicator_scorer.report_type_score_weights
            != res_scoring_model.indicator_scorer.report_type_score_weights
        )

        # The critical test: BC1 weights should differ between the two models
        assert (
            re_scoring_model.indicator_scorer.report_type_score_weights.BC1_1_MAX_SCORE
            != res_scoring_model.indicator_scorer.report_type_score_weights.BC1_1_MAX_SCORE
        )

    def test_report_type_propagation_in_scoring_model(self):
        """Test that ScoringModel properly propagates report types to its components."""
        # Test default report type
        default_model = ScoringModel()
        assert default_model.report_type == "re"

        # Test explicit report types
        re_model = ScoringModel("re")
        assert re_model.report_type == "re"

        res_model = ScoringModel("res")
        assert res_model.report_type == "res"

        # Critical logic: Verify that components get the correct report type
        assert re_model.indicator_scorer.report_type == "re"
        assert re_model.indicator_aggregator.report_type == "re"

        assert res_model.indicator_scorer.report_type == "res"
        assert res_model.indicator_aggregator.report_type == "res"

    def test_bc1_score_calculation_differs_by_report_type(self):
        """
        Test that BC1 scores actually differ when calculated with different report types.

        This test demonstrates the integration between BC1AssetScoreCalculator,
        CommonIndicatorScoreUtils, BenchmarkScoreUtils, and the indicator weights system.
        """
        # Create realistic test data
        certification_data = pd.DataFrame(
            {
                bc.building_data_certifications_scoring_id: [1, 2, 3],
                bc.portfolio_asset_id: [1, 2, 3],
                bc.scoring_coverage: [0.8, 0.9, 0.7],
                bc.type: ["design", "design", "design"],
                bc.time_factor_weight: [1.0, 1.0, 1.0],
                bc.validation_status: [1.0, 1.0, 1.0],
            }
        )

        asset_data = pd.DataFrame(
            {
                ac.portfolio_asset_id: [1, 2, 3],
            }
        )

        memberships = pd.DataFrame(
            {
                mc.building_data_certifications_scoring_id: [1, 2, 3],
                mc.is_active: [True, True, True],
                mc.bench_certification_coverage: [0.8, 0.9, 0.7],
                mc.mean_certification_coverage: [0.8, 0.8, 0.8],
            }
        )

        # Create calculators for both report types
        re_weights = indicator_weights("re")
        res_weights = indicator_weights("res")

        re_calculator = BC1AssetScoreCalculator(
            "BC1.1",
            CommonIndicatorScoreUtils(),
            BenchmarkScoreUtils(),
            re_weights.BC1_1_SUBSCORE_WEIGHTS,
        )

        res_calculator = BC1AssetScoreCalculator(
            "BC1.1",
            CommonIndicatorScoreUtils(),
            BenchmarkScoreUtils(),
            res_weights.BC1_1_SUBSCORE_WEIGHTS,
        )

        # Calculate scores with both calculators
        re_cert_result, re_asset_result = re_calculator.calculate_asset_score(
            certification_data.copy(),
            asset_data.copy(),
            {bc.scoring_coverage: memberships},
        )

        res_cert_result, res_asset_result = res_calculator.calculate_asset_score(
            certification_data.copy(),
            asset_data.copy(),
            {bc.scoring_coverage: memberships},
        )

        # The critical test: scores should be different between report types
        re_scores = re_asset_result["score_bc1_1"].tolist()
        res_scores = res_asset_result["score_bc1_1"].tolist()

        # All scores should be different (the key behavioral test)
        for re_score, res_score in zip(re_scores, res_scores):
            assert (
                re_score != res_score
            ), f"RE score ({re_score}) should differ from RES score ({res_score})"

        # Since RE has higher weights, RE scores should be higher
        for re_score, res_score in zip(re_scores, res_scores):
            assert (
                re_score > res_score
            ), f"RE score ({re_score}) should be higher than RES score ({res_score})"
