import pytest
import pandas as pd
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.building_certification_columns as bc
import app_real_estate.constants.column_names.benchmark_group_memberships_columns as mc
import app_real_estate.constants.column_names.score_columns as sc
from app_real_estate.transformation.scoring.common_indicator_score_utils import (
    CommonIndicatorScoreUtils,
)
from app_real_estate.transformation.scoring.benchmark_score_utils import (
    BenchmarkScoreUtils,
)
from app_real_estate.transformation.scoring.bc1_asset_score_calculator import (
    BC1AssetScoreCalculator,
)
from app_real_estate.transformation.scoring.bc2_score_calculator import (
    BC2ScoreCalculator,
)
import app_real_estate.constants.indicator_subscore_weights as subscore_weights


@pytest.fixture
def bc1_1_score_calculator():
    return BC1AssetScoreCalculator(
        "BC1.1",
        CommonIndicatorScoreUtils(),
        BenchmarkScoreUtils(),
        subscore_weights.indicator_weights("re").BC1_1_SUBSCORE_WEIGHTS,
    )


@pytest.mark.parametrize(
    "certification_data, asset_data, memberships, expected_certification_data, expected_asset_data",
    [
        (
            pd.DataFrame(
                {
                    bc.building_data_certifications_scoring_id: list(range(1, 10)),
                    bc.portfolio_asset_id: [1, 1, 2, 3, 4, 5, 6, 7, 8],
                    bc.certification_id: [1, 2, 1, 2, 2, 3, 3, 1, 1],
                    bc.scoring_coverage: [10, 100, 80, 60, 70, 20, 30, 0, 10],
                    bc.type: ["design"] * 9,
                    bc.time_factor_weight: [1, 0.80, 1, 0.5, 0.9, 1, 1, 1, 0],
                    bc.validation_status: [1, 0.6, 1, 0.6, 0.6, 1, 1, 1, 1],
                }
            ),
            pd.DataFrame(
                {
                    ac.portfolio_asset_id: list(range(1, 9)),
                    ac.certification_coverage: [
                        100,
                        80,
                        60,
                        70,
                        20,
                        30,
                        0,
                        10,
                    ],
                }
            ),
            pd.DataFrame(
                {
                    mc.id: list(range(1, 10)),
                    mc.group_id: [1, 1, 1, 1, 1, 2, 2, 2, 2],
                    mc.building_data_certifications_scoring_id: list(range(1, 10)),
                    mc.is_active: [True] * 9,
                    mc.bench_certification_coverage: [
                        0.1,
                        1,
                        0.8,
                        0.6,
                        0.7,
                        0.2,
                        0.3,
                        0,
                        0.1,
                    ],
                    mc.mean_certification_coverage: [0.64] * 5 + [0.15] * 4,
                }
            ),
            pd.DataFrame(
                {
                    sc.certification_score_bc1_1_coverage: [
                        0.1,
                        1,
                        0.8,
                        0.6,
                        0.7,
                        0.569918,
                        0.673173,
                        0,
                        0.407498,
                    ],
                    sc.certification_score_bc1_1: [
                        0.1,
                        0.48,
                        0.8,
                        0.18,
                        0.378,
                        0.569918,
                        0.673173,
                        0,
                        0,
                    ],
                }
            ),
            pd.DataFrame(
                {
                    sc.score_bc1_1_coverage: [
                        0.58,
                        0.8,
                        0.18,
                        0.378,
                        0.569918,
                        0.673173,
                        0,
                        0,
                    ],
                    sc.score_bc1_1: [
                        4.06,
                        5.6,
                        1.26,
                        2.646,
                        3.98942,
                        4.712211,
                        0,
                        0,
                    ],
                }
            ),
        ),
    ],
)
def test_bc1_1_score_calculator(
    bc1_1_score_calculator,
    certification_data,
    asset_data,
    memberships,
    expected_certification_data,
    expected_asset_data,
):
    result = bc1_1_score_calculator.calculate_asset_score(
        certification_data, asset_data, dict({bc.scoring_coverage: memberships})
    )
    res_certification, res_asset = result

    expected_certification_data = pd.concat(
        [certification_data, expected_certification_data], axis=1
    )
    expected_asset_data = pd.concat([asset_data, expected_asset_data], axis=1)

    pd.testing.assert_frame_equal(res_certification, expected_certification_data)
    pd.testing.assert_frame_equal(res_asset, expected_asset_data)


@pytest.fixture
def bc1_2_score_calculator():
    return BC1AssetScoreCalculator(
        "BC1.2",
        CommonIndicatorScoreUtils(),
        BenchmarkScoreUtils(),
        subscore_weights.indicator_weights("re").BC1_2_SUBSCORE_WEIGHTS,
    )


@pytest.mark.parametrize(
    "certification_data, asset_data, memberships, expected_certification_data, expected_asset_data",
    [
        (
            pd.DataFrame(
                {
                    bc.building_data_certifications_scoring_id: list(range(1, 10)),
                    bc.portfolio_asset_id: [1, 1, 2, 3, 4, 5, 6, 7, 8],
                    bc.certification_id: [1, 2, 1, 2, 2, 3, 3, 1, 1],
                    bc.scoring_coverage: [10, 100, 80, 60, 70, 20, 30, 0, 10],
                    bc.type: ["operational"] * 9,
                    bc.time_factor_weight: [1, 0.80, 1, 0.5, 0.9, 1, 1, 1, 0],
                    bc.validation_status: [1, 0.6, 1, 0.6, 0.6, 1, 1, 1, 1],
                }
            ),
            pd.DataFrame(
                {
                    ac.portfolio_asset_id: list(range(1, 9)),
                    ac.certification_coverage: [
                        100,
                        80,
                        60,
                        70,
                        20,
                        30,
                        0,
                        10,
                    ],
                }
            ),
            pd.DataFrame(
                {
                    mc.id: list(range(1, 10)),
                    mc.group_id: [1, 1, 1, 1, 1, 2, 2, 2, 2],
                    mc.building_data_certifications_scoring_id: list(range(1, 10)),
                    mc.is_active: [True] * 9,
                    mc.bench_certification_coverage: [
                        0.1,
                        1,
                        0.8,
                        0.6,
                        0.7,
                        0.2,
                        0.3,
                        0,
                        0.1,
                    ],
                    mc.mean_certification_coverage: [0.64] * 5 + [0.15] * 4,
                }
            ),
            pd.DataFrame(
                {
                    sc.certification_score_bc1_2_coverage: [
                        0.1,
                        1,
                        0.8,
                        0.6,
                        0.7,
                        0.569918,
                        0.673173,
                        0,
                        0.407498,
                    ],
                    sc.certification_score_bc1_2: [
                        0.1,
                        0.48,
                        0.8,
                        0.18,
                        0.378,
                        0.569918,
                        0.673173,
                        0,
                        0,
                    ],
                }
            ),
            pd.DataFrame(
                {
                    sc.score_bc1_2_coverage: [
                        0.58,
                        0.8,
                        0.18,
                        0.378,
                        0.569918,
                        0.673173,
                        0,
                        0,
                    ],
                    sc.score_bc1_2: [
                        4.93,
                        6.80,
                        1.53,
                        3.213,
                        4.844,
                        5.721,
                        0,
                        0,
                    ],
                }
            ),
        ),
    ],
)
def test_bc1_2_score_calculator(
    bc1_2_score_calculator,
    certification_data,
    asset_data,
    memberships,
    expected_certification_data,
    expected_asset_data,
):
    result = bc1_2_score_calculator.calculate_asset_score(
        certification_data, asset_data, dict({bc.scoring_coverage: memberships})
    )
    res_certification, res_asset = result

    expected_certification_data = pd.concat(
        [certification_data, expected_certification_data], axis=1
    )
    expected_asset_data = pd.concat([asset_data, expected_asset_data], axis=1)

    pd.testing.assert_frame_equal(res_certification, expected_certification_data)
    pd.testing.assert_frame_equal(res_asset, expected_asset_data, rtol=1e-3)


@pytest.fixture
def bc2_score_calculator():
    return BC2ScoreCalculator(
        CommonIndicatorScoreUtils(),
        BenchmarkScoreUtils(),
        subscore_weights.indicator_weights("re").BC2_SUBSCORE_WEIGHTS,
    )


@pytest.mark.parametrize(
    "asset_data, memberships, expected_asset_data",
    [
        (
            pd.DataFrame(
                {
                    ac.portfolio_asset_id: list(range(1, 9)),
                    ac.rating_coverage: [100, 80, 60, 50, 20, 30, 0, 10],
                }
            ),
            pd.DataFrame(
                {
                    mc.id: list(range(1, 9)),
                    mc.group_id: [1, 1, 1, 1, 2, 2, 2, 2],
                    mc.portfolio_asset_id: list(range(1, 9)),
                    mc.is_active: [True] * 8,
                    mc.bench_energy_rating_coverage: [
                        1,
                        0.8,
                        0.6,
                        0.5,
                        0.2,
                        0.3,
                        0,
                        0.1,
                    ],
                    mc.mean_energy_rating_coverage: [0.725] * 4 + [0.15] * 4,
                }
            ),
            pd.DataFrame(
                {
                    sc.score_bc2_coverage: [
                        1,
                        0.8,
                        0.6,
                        0.5,
                        0.569918,
                        0.673173,
                        0,
                        0.407498,
                    ],
                    sc.score_bc2: [
                        2.0,
                        1.6,
                        1.2,
                        1.0,
                        1.139836,
                        1.346346,
                        0,
                        0.814996,
                    ],
                }
            ),
        ),
    ],
)
def test_bc2_score_calculator(
    bc2_score_calculator, asset_data, memberships, expected_asset_data
):
    result = bc2_score_calculator.calculate_asset_score(
        asset_data, dict({ac.rating_coverage: memberships})
    )

    expected_asset_data = pd.concat([asset_data, expected_asset_data], axis=1)

    pd.testing.assert_frame_equal(result, expected_asset_data)
