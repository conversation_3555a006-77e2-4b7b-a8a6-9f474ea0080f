import pandas as pd
import numpy as np
import pytest
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.benchmark_group_memberships_columns as mc
from app_real_estate.transformation.scoring.en1_score_calculator import (
    EN1ScoreCalculator,
)


@pytest.fixture
def asset_data():
    return pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3, 4, 5, 6],
            ec.en_efficiency_int_kwh_m2: [np.nan, 20, 10, 15, 30, np.nan],
            ec.en_int_outlier_status: ["none"] * 4 + ["accepted", "rejected"],
        }
    )


@pytest.fixture
def memberships():
    return pd.DataFrame(
        {
            mc.portfolio_asset_id: [2, 3, 4, 5],
            mc.group_id: [1] * 4,
            mc.bench_en_efficiency_intensity: [20, 10, 15, 30],
            mc.rank_en_efficiency_intensity: [0.75, 0.25, 0.5, 1.0],
            mc.is_active: True,
            mc.is_benchmarkable: [True] * 3 + [False],
        }
    )


@pytest.mark.skip("This methodology for intensity scoring is outdated")
def test_intensity_scoring(asset_data, memberships):
    result = EN1ScoreCalculator.calculate_energy_efficiency_score(
        asset_data, {ec.en_efficiency_int_kwh_m2: memberships}
    )
    result = result[sc.score_en1_energy_efficiency]

    expected_scores = pd.Series(
        [np.nan, 0.25, 0.75, 0.5, 0, np.nan], index=result.index
    )

    pd.testing.assert_series_equal(result, expected_scores, check_names=False)
