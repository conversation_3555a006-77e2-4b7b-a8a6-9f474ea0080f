import pandas as pd
import numpy as np
import pytest

from app_real_estate.constants.helper_enumerators import Utility, Control, Scope
from app_real_estate.transformation.scoring.control_based_score_utils import (
    ControlBasedScoreUtils,
)
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.ghg_columns as gc
import app_real_estate.constants.column_names.water_columns as wc
import app_real_estate.constants.column_names.waste_columns as wsc
import app_real_estate.constants.column_names.score_columns as sc


@pytest.fixture
def mock_asset_control_data_en():
    return pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3, 4, 5, 6],
            ac.whole_building: [True, True, False, False, False, False],
            ac.tenant_ctrl: [True, False, False, False, False, False],
            ac.asset_size_tenant_tenant_m2: [
                np.nan,
                np.nan,
                np.nan,
                180,
                100,
                60,
            ],
            ac.asset_size_tenant_m2: [np.nan, np.nan, 1000, 200, 100, 80],
            ac.asset_size_tenant_landlord_m2: [
                np.nan,
                np.nan,
                1000,
                20,
                100,
                40,
            ],
            ac.asset_size_shared_m2: [np.nan, np.nan, 1000, 190, 90, 80],
            ac.asset_size_common_m2: [np.nan, np.nan, 100, 10, 10, 10],
        }
    )


def test_landlord_controlled_weight_en(mock_asset_control_data_en):
    result = ControlBasedScoreUtils().calculate_subscore_weight_per_control(
        mock_asset_control_data_en, Control.LC, False, Utility.Energy
    )
    expected = pd.Series(
        [0, 1, 1, 0.46, 0.7, 0.64], index=mock_asset_control_data_en.index
    )

    pd.testing.assert_series_equal(expected, result)


def test_tenant_controlled_weight_en(mock_asset_control_data_en):
    result = ControlBasedScoreUtils().calculate_subscore_weight_per_control(
        mock_asset_control_data_en, Control.TC, False, Utility.Energy
    )
    expected = pd.Series(
        [1, 0, 0, 0.54, 0.3, 0.36], index=mock_asset_control_data_en.index
    )

    pd.testing.assert_series_equal(expected, result)


@pytest.fixture
def mock_asset_control_data_wat():
    return pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3, 4, 5, 6],
            wc.wat_tot_w: [100, 200, np.nan, np.nan, np.nan, np.nan],
            ac.tenant_ctrl: [True, False, False, False, False, False],
            wc.wat_tot_tc_t: [np.nan, np.nan, np.nan, 180, 100, 60],
            ac.asset_size_tenant_m2: [np.nan, np.nan, 1000, 200, 100, 80],
            wc.wat_tot_lc_t: [np.nan, np.nan, 1000, 20, 100, 40],
            wc.wat_tot_lc_bs_m2: [np.nan, np.nan, 1000, 190, 90, 80],
            wc.wat_tot_lc_bc_m2: [np.nan, np.nan, 100, 10, 10, 10],
        }
    )


def test_landlord_controlled_weight_wat(mock_asset_control_data_wat):
    result = ControlBasedScoreUtils().calculate_subscore_weight_per_control(
        mock_asset_control_data_wat, Control.LC, False, Utility.Water
    )
    expected = pd.Series(
        [0, 1, 1, 0.46, 0.7, 0.64], index=mock_asset_control_data_wat.index
    )

    pd.testing.assert_series_equal(expected, result)


def test_tenant_controlled_weight_wat(mock_asset_control_data_wat):
    result = ControlBasedScoreUtils().calculate_subscore_weight_per_control(
        mock_asset_control_data_wat, Control.TC, False, Utility.Water
    )
    expected = pd.Series(
        [1, 0, 0, 0.54, 0.3, 0.36], index=mock_asset_control_data_wat.index
    )

    pd.testing.assert_series_equal(expected, result)


@pytest.fixture
def mock_asset_scopes_data():
    return pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3, 4],
            ac.asset_ownership: [100, 0, 50, 80],
            gc.ghg_area_p_s12: [np.nan, 80, 100, 50],
            gc.ghg_lfl_abs_s12_ly: [np.nan, 80, 100, 50],
            gc.ghg_area_p_s3: [30, 60, np.nan, 100],
            gc.ghg_lfl_abs_s3_ly: [30, 60, np.nan, 100],
        }
    )


def test_s12_weight(mock_asset_scopes_data):
    result = ControlBasedScoreUtils().calculate_subscore_weight_per_scope(
        mock_asset_scopes_data, Scope.S12, is_lfl=False
    )
    expected = pd.Series([0, 0, 0.5, 0.4], index=mock_asset_scopes_data.index)

    pd.testing.assert_series_equal(expected, result)


def test_s3_weight(mock_asset_scopes_data):
    result = ControlBasedScoreUtils().calculate_subscore_weight_per_scope(
        mock_asset_scopes_data, Scope.S3, is_lfl=False
    )
    expected = pd.Series([0.3, 0, 0, 0.8], index=mock_asset_scopes_data.index)

    pd.testing.assert_series_equal(expected, result)


def test_s12_weight_lfl(mock_asset_scopes_data):
    result = ControlBasedScoreUtils().calculate_subscore_weight_per_scope(
        mock_asset_scopes_data, Scope.S12, is_lfl=True
    )
    expected = pd.Series([0, 0, 0.5, 0.4], index=mock_asset_scopes_data.index)

    pd.testing.assert_series_equal(expected, result)


def test_s3_weight_lfl(mock_asset_scopes_data):
    result = ControlBasedScoreUtils().calculate_subscore_weight_per_scope(
        mock_asset_scopes_data, Scope.S3, is_lfl=True
    )
    expected = pd.Series([0.3, 0, 0, 0.8], index=mock_asset_scopes_data.index)

    pd.testing.assert_series_equal(expected, result)


@pytest.fixture
def mock_asset_waste_control_data():
    return pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3, 4],
            ac.asset_ownership: [100, 0, 50, 80],
            wsc.was_area_p_lc: [np.nan, 80, 100, 50],
            wsc.was_area_p_tc: [30, 60, np.nan, 100],
        }
    )


def test_was_lc_weight(mock_asset_waste_control_data):
    result = ControlBasedScoreUtils().calculate_waste_subscore_weight_per_control(
        mock_asset_waste_control_data, Control.LC
    )
    expected = pd.Series([0, 0, 0.5, 0.4], index=mock_asset_waste_control_data.index)

    pd.testing.assert_series_equal(expected, result)


def test_was_tc_weight(mock_asset_waste_control_data):
    result = ControlBasedScoreUtils().calculate_waste_subscore_weight_per_control(
        mock_asset_waste_control_data, Control.TC
    )
    expected = pd.Series([0.3, 0, 0, 0.8], index=mock_asset_waste_control_data.index)

    pd.testing.assert_series_equal(expected, result)


@pytest.fixture
def mock_data_sum_subscores_en():
    return pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3, 4, 5],
            ac.whole_building: [True, True, False, False, False],
            ac.tenant_ctrl: [True, False, False, False, False],
            ac.asset_size_tenant_tenant_m2: [np.nan, np.nan, np.nan, 180, 180],
            ac.asset_size_tenant_m2: [np.nan, np.nan, 1000, 200, 200],
            ac.asset_size_tenant_landlord_m2: [np.nan, np.nan, 1000, 20, 20],
            ac.asset_size_shared_m2: [np.nan, np.nan, 1000, 190, 190],
            ac.asset_size_common_m2: [np.nan, np.nan, 100, 10, 10],
            ec.en_lfl_abs: [100, 100, 300, 300, 300],
            ec.en_lfl_abs_lc: [0, 100, 300, 250, np.nan],
            ec.en_lfl_abs_in: [90, 100, 200, 200, 300],
            ec.en_lfl_abs_ly: [100, 100, 300, 300, 200],
            ec.en_lfl_abs_in_ly: [90, 100, 200, 200, 200],
            ec.en_lfl_abs_lc_bc_ly: [np.nan, np.nan, 300, 250, np.nan],
            ec.en_lfl_abs_lc_bs_ly: [np.nan, np.nan, 300, 250, np.nan],
            ec.en_lfl_abs_lc_t_ly: [np.nan, np.nan, 200, 50, np.nan],
            ec.en_lfl_abs_tc_t_ly: [np.nan, np.nan, np.nan, 150, 100],
            ec.en_lfl_abs_lc_o_ly: [10, 0, 100, 0, 0],
            ec.en_lfl_abs_tc_o_ly: [0, 0, 0, 100, 0],
            sc.score_en1_en_area_time_cov_p_lc: [np.nan, 1, np.nan, 0.5, np.nan],
            sc.score_en1_en_area_time_cov_p_tc: [1, np.nan, np.nan, 0.4, 1],
        }
    )


def test_sum_control_based_subscores_en_cov(mock_data_sum_subscores_en):
    result = ControlBasedScoreUtils().sum_control_based_subscores(
        mock_data_sum_subscores_en,
        sc.score_en1_en_area_time_cov_p_lc,
        sc.score_en1_en_area_time_cov_p_tc,
        False,
        Utility.Energy,
    )
    expected = pd.Series(
        [1, 1, np.nan, 0.446, 0.54], index=mock_data_sum_subscores_en.index
    )

    pd.testing.assert_series_equal(expected, result)


def test_lc_weight_en_lfl(mock_data_sum_subscores_en):
    result = ControlBasedScoreUtils().calculate_subscore_weight_per_control(
        mock_data_sum_subscores_en, Control.LC, True, Utility.Energy
    )
    expected = pd.Series(
        [0.1, 1, 1, 0.366666, 0], index=mock_data_sum_subscores_en.index
    )

    pd.testing.assert_series_equal(expected, result, check_exact=False)


def test_tc_weight_en_lfl(mock_data_sum_subscores_en):
    result = ControlBasedScoreUtils().calculate_subscore_weight_per_control(
        mock_data_sum_subscores_en, Control.TC, True, Utility.Energy
    )
    expected = pd.Series(
        [0.9, 0, 0, 0.63333, 1], index=mock_data_sum_subscores_en.index
    )

    pd.testing.assert_series_equal(expected, result)


def test_sum_control_based_subscores_en_lfl(mock_data_sum_subscores_en):
    result = ControlBasedScoreUtils().sum_control_based_subscores(
        mock_data_sum_subscores_en,
        sc.score_en1_en_area_time_cov_p_lc,
        sc.score_en1_en_area_time_cov_p_tc,
        True,
        Utility.Energy,
    )
    expected = pd.Series(
        [0.9, 1, np.nan, 0.436666, 1], index=mock_data_sum_subscores_en.index
    )

    pd.testing.assert_series_equal(expected, result)


@pytest.fixture
def mock_data_sum_subscores_ghg():
    return pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3, 4],
            ac.asset_ownership: [100, 50, 80, 30],
            gc.ghg_area_p_s12: [np.nan, 80, 50, 100],
            gc.ghg_lfl_abs_s12_ly: [np.nan, 80, 50, 100],
            gc.ghg_area_p_s3: [30, np.nan, 100, 50],
            gc.ghg_lfl_abs_s3_ly: [30, np.nan, 100, 50],
            sc.score_gh1_ghg_area_time_cov_p_s12: [np.nan, 1, np.nan, 0.5],
            sc.score_gh1_ghg_area_time_cov_p_s3: [1, np.nan, np.nan, 0.4],
        }
    )


def test_sum_control_based_subscores_ghg_cov(mock_data_sum_subscores_ghg):
    result = ControlBasedScoreUtils().sum_control_based_subscores(
        mock_data_sum_subscores_ghg,
        sc.score_gh1_ghg_area_time_cov_p_s12,
        sc.score_gh1_ghg_area_time_cov_p_s3,
        False,
        Utility.GHG,
    )
    expected = pd.Series(
        [1, 1, np.nan, 0.466666], index=mock_data_sum_subscores_ghg.index
    )

    pd.testing.assert_series_equal(expected, result, check_exact=False)


def test_sum_control_based_subscores_ghg_lfl(mock_data_sum_subscores_ghg):
    result = ControlBasedScoreUtils().sum_control_based_subscores(
        mock_data_sum_subscores_ghg,
        sc.score_gh1_ghg_area_time_cov_p_s12,
        sc.score_gh1_ghg_area_time_cov_p_s3,
        True,
        Utility.GHG,
    )
    expected = pd.Series(
        [1, 1, np.nan, 0.466666], index=mock_data_sum_subscores_ghg.index
    )

    pd.testing.assert_series_equal(expected, result, check_exact=False)
