import pytest

from app_real_estate.constants.indicator_subscore_weights import indicator_weights
from app_real_estate.transformation.scoring.benchmark_score_utils import (
    BenchmarkScoreUtils,
)
from app_real_estate.transformation.scoring.common_indicator_score_utils import (
    CommonIndicatorScoreUtils,
)
from app_real_estate.transformation.scoring.control_based_score_utils import (
    ControlBasedScoreUtils,
)
from app_real_estate.transformation.scoring.en1_score_calculator import (
    EN1ScoreCalculator,
)
import pandas as pd
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.energy_columns as ec
import numpy as np


@pytest.fixture
def en1_score_calculator():
    return EN1ScoreCalculator(
        asset_score_utils=CommonIndicatorScoreUtils(),
        benchmark_score_utils=BenchmarkScoreUtils(),
        control_score_utils=ControlBasedScoreUtils(),
        subscore_weights=indicator_weights("re").EN1_SUBSCORE_WEIGHTS,
    )


@pytest.fixture
def mock_asset_data():
    return pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3, 4],
            ec.en_efficiency_int_kwh_m2: [10.0, 9.0, np.nan, np.nan],
            ec.ashrae_intensity_threshold: [10.0, 10.0, 10.0, np.nan],
            sc.score_en1_en_lfl_percent_change: [1.0, 0.0, np.nan, 0.5],
            sc.score_en1_lfl_availability: [1.0, 1.0, np.nan, 0.0],
        }
    )


def test_energy_efficiency_scoring(mock_asset_data, en1_score_calculator):
    expected = pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3, 4],
            ec.en_efficiency_int_kwh_m2: [10.0, 9.0, np.nan, np.nan],
            ec.ashrae_intensity_threshold: [10.0, 10.0, 10.0, np.nan],
            sc.score_en1_en_lfl_percent_change: [1.0, 0.0, np.nan, 0.5],
            sc.score_en1_lfl_availability: [1.0, 1.0, np.nan, 0.0],
            sc.score_en1_energy_efficiency: [1.0, 1.0, np.nan, np.nan],
        }
    )

    result = en1_score_calculator.calculate_asset_energy_efficiency_score(
        mock_asset_data
    )

    pd.testing.assert_frame_equal(expected, result)


def test_add_total_lfl_score(mock_asset_data, en1_score_calculator):
    expected = pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3, 4],
            ec.en_efficiency_int_kwh_m2: [10.0, 9.0, np.nan, np.nan],
            ec.ashrae_intensity_threshold: [10.0, 10.0, 10.0, np.nan],
            sc.score_en1_en_lfl_percent_change: [1.0, 0.0, np.nan, 0.5],
            sc.score_en1_lfl_availability: [1.0, 1.0, np.nan, 0.0],
            sc.score_en1_en_lfl: [1.0, 0.2, np.nan, 0.4],
        }
    )
    result = en1_score_calculator._calculate_total_lfl_score(mock_asset_data)

    pd.testing.assert_frame_equal(expected, result)


def test_calculate_energy_performance_score(mock_asset_data, en1_score_calculator):
    expected = pd.DataFrame(
        {
            ac.portfolio_asset_id: [1, 2, 3, 4],
            ec.en_efficiency_int_kwh_m2: [10.0, 9.0, np.nan, np.nan],
            ec.ashrae_intensity_threshold: [10.0, 10.0, 10.0, np.nan],
            sc.score_en1_en_lfl_percent_change: [1.0, 0.0, np.nan, 0.5],
            sc.score_en1_lfl_availability: [1.0, 1.0, np.nan, 0.0],
            sc.score_en1_en_lfl: [1.0, 0.2, np.nan, 0.4],
            sc.score_en1_energy_efficiency: [1.0, 1.0, np.nan, np.nan],
            sc.score_en1_energy_performance: [1.0, 1.0, np.nan, 0.4],
        }
    )

    result = en1_score_calculator._calculate_total_lfl_score(mock_asset_data)
    result = en1_score_calculator.calculate_asset_energy_efficiency_score(result)
    result = en1_score_calculator._calculate_asset_energy_performance_score(result)

    pd.testing.assert_frame_equal(expected, result)
