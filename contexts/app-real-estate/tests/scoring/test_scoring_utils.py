import pandas as pd
import numpy as np
import pytest
from app_real_estate.transformation.scoring.benchmark_score_utils import (
    BenchmarkScoreUtils,
)
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.benchmark_group_memberships_columns as mc


def test_score_on_curve_valid():
    mock_values = [0.4, 0.3, 0.2, 0.6, 1, -0.000004, 1.0000004, np.nan]
    mock_metrics = [0.4, 0.1, 0.45, 0.49999, 0.3, 0.2, 0.3, 0.4]

    vectorized_func = np.vectorize(BenchmarkScoreUtils.score_on_curve)
    result = vectorized_func(mock_values, mock_metrics)
    expected = [0.5, 0.73248676, 0.23468104, 0.6000096, 1.0, 0.0, 1.0, np.nan]

    pd.testing.assert_series_equal(pd.Series(result), pd.Series(expected))


def test_score_on_curve_missing_metric():
    with pytest.raises(
        ValueError, match="score_on_curve: Invalid input values detected."
    ):
        BenchmarkScoreUtils.score_on_curve(0.1, np.nan)


def test_score_on_curve_invalid_value():
    with pytest.raises(
        ValueError, match="score_on_curve: Invalid input values detected."
    ):
        BenchmarkScoreUtils.score_on_curve(-0.1, 0.4)

    with pytest.raises(
        ValueError, match="score_on_curve: Invalid input values detected."
    ):
        BenchmarkScoreUtils.score_on_curve(1.0001, 0.4)


def test_score_on_curve_invalid_metric():
    with pytest.raises(
        ValueError, match="score_on_curve: Invalid input values detected."
    ):
        BenchmarkScoreUtils.score_on_curve(0.1, 0.5)

    with pytest.raises(
        ValueError, match="score_on_curve: Invalid input values detected."
    ):
        BenchmarkScoreUtils.score_on_curve(0.1, -0.00001)


def test_percent_change_performance_score():
    # Sample data
    data = {
        sc.score_en1_en_ren_percent_change: [1, 0.5, 0.2, 0.8, 0.3],
        ec.en_ren_rate: [10, 30, 36, 9, 50],
    }
    data = pd.DataFrame(data)
    # Expected result
    expected = [0.505, 0.37, 0.3088, 0.41305, 0.45]

    # Test calculate_percent_change_performance_score method
    vectorized_func = np.vectorize(
        BenchmarkScoreUtils.calculate_percent_change_performance_score
    )
    result = vectorized_func(
        data[sc.score_en1_en_ren_percent_change], data[ec.en_ren_rate]
    )
    pd.testing.assert_series_equal(pd.Series(result), pd.Series(expected))


@pytest.mark.parametrize(
    "asset_data, memberships",
    [
        (
            pd.DataFrame(
                {
                    ac.portfolio_asset_id: list(range(1, 11)),
                    ec.en_ren_rate: [
                        10,
                        30,
                        26,
                        87,
                        100,
                        65,
                        110,
                        0,
                        99,
                        np.nan,
                    ],
                    ec.en_ren_percent_change: [
                        -10,
                        30,
                        25,
                        -2,
                        27,
                        8,
                        3,
                        50,
                        11,
                        np.nan,
                    ],
                }
            ),
            pd.DataFrame(
                {
                    mc.id: list(range(1, 10)),
                    mc.group_id: [1, 1, 1, 1, 1, 2, 2, 2, 2],
                    mc.portfolio_asset_id: list(range(1, 10)),
                    mc.is_active: [True] * 9,
                    mc.bench_en_ren_percent_change: [
                        0.11494,
                        0.34482,
                        0.29885,
                        1,
                        1,
                        0.65491,
                        1.0,
                        0,
                        0.99748,
                    ],
                    mc.mean_en_ren_percent_change: [
                        0.551722,
                        0.551722,
                        0.551722,
                        0.551722,
                        0.551722,
                        0.663097,
                        0.663097,
                        0.663097,
                        0.663097,
                    ],
                }
            ),
        ),
    ],
)
def test_asset_performance_score(asset_data, memberships):
    memberships_dict = {ec.en_ren_percent_change: memberships}
    result_data = BenchmarkScoreUtils().calculate_asset_performance_score(
        asset_data,
        memberships_dict,
        ec.en_ren_rate,
        ec.en_ren_percent_change,
        mc.bench_en_ren_percent_change,
        mc.mean_en_ren_percent_change,
        sc.score_en1_en_ren_percent_change,
        sc.score_en1_en_ren_performance,
    )

    # Check the result of score on curve on score_en1_en_ren_percent_change
    # The score is the same as the benchmark values because the benchmark metric is invalid for all (> 0,5)
    expected_percent_change_score = pd.Series(
        memberships[mc.bench_en_ren_percent_change].tolist() + [0],
        name=sc.score_en1_en_ren_percent_change,
    )
    pd.testing.assert_series_equal(
        expected_percent_change_score,
        result_data[sc.score_en1_en_ren_percent_change],
    )

    # Check the performance score on score_en1_en_ren_performance
    expected_performance_score = pd.Series(
        [
            0.106723,
            0.315687,
            0.2743745,
            0.87845,
            1.0,
            0.65085925,
            1.0,
            0,
            0.9900374,
            0,
        ],
        name=sc.score_en1_en_ren_performance,
    )
    pd.testing.assert_series_equal(
        expected_performance_score,
        result_data[sc.score_en1_en_ren_performance],
    )
