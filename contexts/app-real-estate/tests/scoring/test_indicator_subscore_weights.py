import pytest
from app_real_estate.constants.indicator_subscore_weights import indicator_weights


class TestIndicatorSubscoreWeights:
    """Unit tests for indicator subscore weights classes and factory function."""

    def test_indicator_weights_factory_function(self):
        """Test that the indicator_weights factory function returns correct weight classes and handles errors."""
        # Test Real Estate weights
        re_weights = indicator_weights("re")
        assert re_weights.__class__.__name__ == "RealEstateWeights"

        # Test Residential weights
        res_weights = indicator_weights("res")
        assert res_weights.__class__.__name__ == "ResidentialWeights"

        # Test invalid report type - this is critical error handling logic
        with pytest.raises(ValueError, match="Unsupported report type: invalid"):
            indicator_weights("invalid")

    def test_bc1_weights_differ_between_report_types(self):
        """Test that BC1 weights are different between RE and RES report types."""
        re_weights = indicator_weights("re")
        res_weights = indicator_weights("res")

        # The critical logic is that BC1 weights should be different between report types
        # We don't care about the exact values, just that they differ
        assert re_weights.BC1_1_MAX_SCORE != res_weights.BC1_1_MAX_SCORE
        assert re_weights.BC1_2_MAX_SCORE != res_weights.BC1_2_MAX_SCORE
        assert re_weights.BC1_MAX_SCORE != res_weights.BC1_MAX_SCORE

    def test_bc1_subscore_weights_calculation_logic(self):
        """Test that BC1 subscore weights are calculated correctly using the fractional formula."""
        re_weights = indicator_weights("re")
        res_weights = indicator_weights("res")

        # Test the critical calculation logic - fractional weights should sum to BC1_MAX_SCORE
        re_bc1_weights = re_weights.BC1_SUBSCORE_WEIGHTS
        total_re_bc1_weight = (
            re_bc1_weights["score_bc1_1_fraction"]
            + re_bc1_weights["score_bc1_2_fraction"]
        )
        assert abs(total_re_bc1_weight - re_weights.BC1_MAX_SCORE) < 1e-10

        res_bc1_weights = res_weights.BC1_SUBSCORE_WEIGHTS
        total_res_bc1_weight = (
            res_bc1_weights["score_bc1_1_fraction"]
            + res_bc1_weights["score_bc1_2_fraction"]
        )
        assert abs(total_res_bc1_weight - res_weights.BC1_MAX_SCORE) < 1e-10

        # Verify that the fractional weights are different between report types (the key behavior)
        assert (
            re_bc1_weights["score_bc1_1_fraction"]
            != res_bc1_weights["score_bc1_1_fraction"]
        )
        assert (
            re_bc1_weights["score_bc1_2_fraction"]
            != res_bc1_weights["score_bc1_2_fraction"]
        )

    def test_dynamic_subscore_weights_properties_structure(self):
        """Test that the dynamic subscore weight properties return correct structure."""
        re_weights = indicator_weights("re")

        # Test that properties return dictionaries with expected keys (structure test, not values)
        en1_weights = re_weights.EN1_SUBSCORE_WEIGHTS
        assert isinstance(en1_weights, dict)
        assert "score_en1_en_area_time_cov_p" in en1_weights
        assert "score_en1_energy_performance" in en1_weights

        bc1_weights = re_weights.BC1_SUBSCORE_WEIGHTS
        assert isinstance(bc1_weights, dict)
        assert "score_bc1_1_fraction" in bc1_weights
        assert "score_bc1_2_fraction" in bc1_weights
