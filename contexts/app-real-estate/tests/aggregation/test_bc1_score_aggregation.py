import pandas as pd
import pandas.testing as pdt
import app_real_estate.constants.column_names.asset_characteristics_columns as asc
import app_real_estate.constants.column_names.score_columns as sc
from tests.aggregation.agg_test_utils import AggregationTestUtils
from app_real_estate.transformation.aggregation.indicator.bc1.bc1_score_aggregator import (
    BC1ScoreAggregator,
)


def test_bc1_score_aggregation():
    data_dict = {
        ("Commercial", 1): {
            sc.score_bc1_1_fraction: 0.603846,
            sc.score_bc1_2_fraction: 0.603846,
        },
        ("Residential", 1): {
            sc.score_bc1_1_fraction: 0.280645,
            sc.score_bc1_2_fraction: 0.280645,
        },
    }

    expected_output = AggregationTestUtils.get_multiindex_df(
        data_dict=data_dict, names=[asc.property_subtype, asc.response_id]
    )

    bc1_score_aggregator = BC1ScoreAggregator(data=expected_output)
    actual_output = bc1_score_aggregator.process()

    expected_bc1_score = pd.Series(
        [5.132691, 2.385482],
        index=pd.MultiIndex.from_tuples(
            [("Commercial", 1), ("Residential", 1)],
            names=[asc.property_subtype, asc.response_id],
        ),
        name=sc.score_bc1,
    )

    pdt.assert_series_equal(expected_bc1_score, actual_output[sc.score_bc1])
