import pytest
import pandas as pd
from app_real_estate.transformation.aggregation.processor.add_area_ownership_weight_factor import (
    AddAreaOwnershipWeightFactor,
)
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.asset_characteristics_columns as asc


# Test data for valid cases
valid_data = [
    (
        pd.DataFrame(
            {
                asc.asset_ownership: [50, 100, 75],
                asc.asset_size_m2: [100, 200, 300],
            }
        ),
        [50.0, 200.0, 225.0],
    ),
    (
        pd.DataFrame(
            {
                asc.asset_ownership: [0, 100, 50],
                asc.asset_size_m2: [0, 1000, 500],
            }
        ),
        [0.0, 1000.0, 250.0],
    ),
]

# Test data for edge cases
edge_cases_data = [
    (
        pd.DataFrame(
            {
                asc.asset_ownership: [0, 0, 0],
                asc.asset_size_m2: [100, 200, 300],
            }
        ),
        [0.0, 0.0, 0.0],
    ),
    (
        pd.DataFrame(
            {asc.asset_ownership: [50, 100, 75], asc.asset_size_m2: [0, 0, 0]}
        ),
        [0.0, 0.0, 0.0],
    ),
    (
        pd.DataFrame(
            {
                asc.asset_ownership: [100, 100, 100],
                asc.asset_size_m2: [100, 200, 300],
            }
        ),
        [100.0, 200.0, 300.0],
    ),
    (
        pd.DataFrame(
            {
                asc.asset_ownership: [50, 100, 75],
                asc.asset_size_m2: [1e6, 2e6, 3e6],
            }
        ),
        [5e5, 2e6, 2.25e6],
    ),
]


@pytest.mark.parametrize("data, expected", valid_data)
def test_add_area_ownership_weight_factor_valid(data, expected):
    processor = AddAreaOwnershipWeightFactor(data)
    result = processor.process()
    assert result[sc.asset_size_owned_m2].tolist() == expected


@pytest.mark.parametrize("data, expected", edge_cases_data)
def test_add_area_ownership_weight_factor_edge_cases(data, expected):
    processor = AddAreaOwnershipWeightFactor(data)
    result = processor.process()
    assert result[sc.asset_size_owned_m2].tolist() == expected
