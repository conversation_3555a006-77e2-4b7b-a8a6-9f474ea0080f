import pytest
import pandas as pd
import numpy as np
import pandas.testing as pdt
import app_real_estate.constants.column_names.asset_characteristics_columns as asc
import app_real_estate.constants.column_names.score_columns as sc
from app_real_estate.transformation.aggregation.indicator.en1.en1_score_aggregator_response_id import (
    EN1ScoreAggregator_ResponseId,
)
from app_real_estate.transformation.aggregation.indicator.en1.en1_score_aggregator_property_subtype_response_id import (
    EN1ScoreAggregator_PropertySubtype_ResponseId,
)
from app_real_estate.transformation.aggregation.indicator.en1.en1_score_aggregator_property_subtype_country_response_id import (
    EN1ScoreAggregator_PropertySubtype_Country_ResponseId,
)
from app_real_estate.models.score_aggregation_util_models.en1_scored_data import (
    EN1ScoredData,
)
from tests.aggregation.agg_test_utils import AggregationTestUtils
from app_real_estate.models.input_models.r1_table_model import R1TableModel
from app_real_estate.transformation.aggregation.merger.r1_table_merger import (
    R1TableMerger,
)


@pytest.fixture
def mock_r1_table():
    return pd.DataFrame(
        {
            R1TableModel.RESPONSE_ID: [1, 1, 2],
            R1TableModel.COUNTRY: ["JP", "NL", "NL"],
            R1TableModel.PRT_TYPE: ["OCHI", "OCHI", "TSOT"],
            R1TableModel.R_1_TBL_PGAV: [45.8027, 41.17, 12.74],
            R1TableModel.R_1_TBL_AST: [10, 3, 7],
            R1TableModel.R_1_TBL_AREA: [200.0, 250.0, 540.0],
        }
    )


@pytest.fixture
def mock_en1_scored_data():
    return pd.DataFrame(
        {
            "response_id": [1, 1, 1, 2],
            asc.data_year: [2023] * 4,
            "country": ["JP", "JP", "NL", "NL"],
            "property_type_code": [
                "OCHI",
                "OCHI",
                "OCHI",
                "TSOT",
            ],
            asc.asset_ownership: [10, 80, 30, 100],
            asc.asset_size_m2: [200, 250, 300, 540],
            sc.score_en1_en_area_time_cov_p: [0.3, 0.2, 0.7, 0.5],
            sc.score_en1_lfl_availability: [1, 0, 1, 1],
            sc.score_en1_en_lfl_percent_change: [0.03, 0.2, 0.7, 0.34],
            sc.score_en1_en_ren_ons: [1, 1, 0, 0],
            sc.score_en1_en_ren_ofs: [1, 0, 1, 0],
            sc.score_en1_en_ren_availability: [1, 1, 0.5, 0],
            sc.score_en1_en_ren_percent_change: [1.0, 0.23, 0.7, 0.5],
            sc.score_en1_en_ren_performance: [1.0, 0.4, 0.7, 0.5],
            sc.score_en1_en_lfl_percent_change_lc: [0.3, 0.2, 0.7, 0.9],
            sc.score_en1_en_lfl_percent_change_tc: [0.3, 0.2, 0.7, 0.9],
            sc.score_en1_en_area_time_cov_p_lc: [0.4, 0.8, 0.3, 0.5],
            sc.score_en1_en_area_time_cov_p_tc: [0.08, 0.2, 0.7, 0.5],
            sc.score_en1_energy_efficiency: [1.0, np.nan, np.nan, 1.0],
            sc.score_en1_en_lfl: [0.224, 0.16, 0.76, 0.472],
            sc.score_en1_energy_performance: [1.0, 0.16, 0.76, 1.0],
        }
    )


def test_parse_r1_table(mock_r1_table, mock_en1_scored_data):
    r1_table = R1TableModel.to_schema().validate(mock_r1_table)

    merged_data = R1TableMerger(
        r1_data=r1_table,
        data=mock_en1_scored_data,
        merge_on_keys=["response_id", "country", "property_type_code"],
    ).merge()

    selected_merged_data = merged_data[
        ["pgav", R1TableModel.R_1_TBL_AST, R1TableModel.R_1_TBL_AREA]
    ]

    expected_data = pd.DataFrame(
        {
            "pgav": [45.8027, 45.8027, 41.17, 12.74],
            R1TableModel.R_1_TBL_AST: [10, 10, 3, 7],
            R1TableModel.R_1_TBL_AREA: [200.0, 200.0, 250.0, 540.0],
        },
        index=pd.MultiIndex.from_tuples(
            [
                (1, "JP", "OCHI"),
                (1, "JP", "OCHI"),
                (1, "NL", "OCHI"),
                (2, "NL", "TSOT"),
            ],
            names=["response_id", "country", "property_type_code"],
        ),
    )

    pdt.assert_frame_equal(selected_merged_data, expected_data)


def test_en1_parse_dataclass(mock_en1_scored_data):
    # asset True if the expected dataframe is parsed to the dataclass
    en1_scored_data = EN1ScoredData.parse_df(
        dataframe=mock_en1_scored_data, verbose=True
    )
    pdt.assert_frame_equal(en1_scored_data, mock_en1_scored_data)


def test_en1_score_aggregation_on_property_subtype_and_response_id(
    mock_en1_scored_data, mock_r1_table
):
    aggregator = EN1ScoreAggregator_PropertySubtype_ResponseId(
        mock_en1_scored_data, mock_r1_table
    )
    result = aggregator.process()

    data_dict = {
        ("TSOT", 2, 2023): {
            sc.score_en1_en_area_time_cov_p: 0.5,
            sc.score_en1_lfl_availability: 1,
            sc.score_en1_en_lfl_percent_change: 0.34,
            sc.score_en1_en_ren_ons: 0,
            sc.score_en1_en_ren_ofs: 0,
            sc.score_en1_en_ren_availability: 0,
            sc.score_en1_en_ren_percent_change: 0.500000,
            sc.score_en1_en_ren_performance: 0.5,
            sc.score_en1_en_lfl_percent_change_lc: 0.900000,
            sc.score_en1_en_lfl_percent_change_tc: 0.900000,
            sc.score_en1_en_area_time_cov_p_lc: 0.500000,
            sc.score_en1_en_area_time_cov_p_tc: 0.500000,
            sc.score_en1_energy_efficiency: 1,
            sc.score_en1_energy_efficiency_absolute: 2.5,
            sc.score_en1_energy_efficiency_percent: 100,
            sc.score_en1_energy_efficiency_max: 2.5,
            sc.score_en1_en_lfl: 0.472,
            sc.score_en1_energy_performance: 1.0,
            sc.score_en1: 7.75,
            sc.score_en1_fraction: 0.553571,
            sc.score_en1_percent: 55.3571,
            sc.score_en1_max: 14.0,
            sc.pgav: 12.74,
        },
        ("OCHI", 1, 2023): {
            sc.score_en1_en_area_time_cov_p: 0.441471,
            sc.score_en1_lfl_availability: 0.521242,
            sc.score_en1_en_lfl_percent_change: 0.428544,
            sc.score_en1_en_ren_ons: 0.526633,
            sc.score_en1_en_ren_ofs: 0.521242,
            sc.score_en1_en_ren_availability: 0.763316,
            sc.score_en1_en_ren_percent_change: 0.489346,
            sc.score_en1_en_ren_performance: 0.570735,
            sc.score_en1_en_lfl_percent_change_lc: 0.441471,
            sc.score_en1_en_lfl_percent_change_tc: 0.441471,
            sc.score_en1_en_area_time_cov_p_lc: 0.544166,
            sc.score_en1_en_area_time_cov_p_tc: 0.430938,
            sc.score_en1_energy_efficiency: 0.526633,
            sc.score_en1_energy_efficiency_absolute: 1.31658,
            sc.score_en1_energy_efficiency_percent: 52.6633,
            sc.score_en1_energy_efficiency_max: 2.5,
            sc.score_en1_en_lfl: 0.4470842,
            sc.score_en1_energy_performance: 0.484235,
            sc.score_en1: 6.86787,
            sc.score_en1_fraction: 0.490562,
            sc.score_en1_percent: 49.0562,
            sc.score_en1_max: 14.0,
            sc.pgav: 86.9727,
        },
    }
    expected_output = AggregationTestUtils.get_multiindex_df(
        data_dict, names=["property_type_code", "response_id", asc.data_year]
    )

    # TODO: remove following once the test is updated with all columns
    common_cols = list(set(result.columns).intersection(expected_output.columns))
    result = result[common_cols]
    pdt.assert_frame_equal(result, expected_output, check_like=True)


def test_en1_score_aggregation_on_country_and_property_subtype_response_id(
    mock_en1_scored_data, mock_r1_table
):
    aggregator = EN1ScoreAggregator_PropertySubtype_Country_ResponseId(
        mock_en1_scored_data, mock_r1_table
    )
    result = aggregator.process()

    data_dict = {
        ("TSOT", "NL", 2, 2023): {
            sc.pgav: 12.74,
            sc.score_en1_en_area_time_cov_p: 0.5,
            sc.score_en1_lfl_availability: 1,
            sc.score_en1_en_lfl_percent_change: 0.34,
            sc.score_en1_en_ren_ons: 0,
            sc.score_en1_en_ren_ofs: 0,
            sc.score_en1_en_ren_availability: 0,
            sc.score_en1_en_ren_percent_change: 0.500000,
            sc.score_en1_en_ren_performance: 0.5,
            sc.score_en1_en_lfl_percent_change_lc: 0.900000,
            sc.score_en1_en_lfl_percent_change_tc: 0.900000,
            sc.score_en1_en_area_time_cov_p_lc: 0.500000,
            sc.score_en1_en_area_time_cov_p_tc: 0.500000,
            sc.score_en1_energy_efficiency: 1,
            sc.score_en1_energy_efficiency_absolute: 2.5,
            sc.score_en1_energy_efficiency_percent: 100,
            sc.score_en1_energy_efficiency_max: 2.5,
            sc.score_en1_energy_performance: 1.0,
            sc.score_en1_en_lfl: 0.472,
            sc.asset_size_owned_m2: 540.0,
            sc.score_en1: 7.75,
            sc.score_en1_max: 14.0,
            sc.score_en1_fraction: 0.553571,
            sc.score_en1_percent: 55.3571,
            R1TableModel.R_1_TBL_AST: 7,
            R1TableModel.R_1_TBL_AREA: 540.0,
        },
        ("OCHI", "JP", 1, 2023): {
            sc.pgav: 45.8027,
            sc.score_en1_en_area_time_cov_p: 0.209091,
            sc.score_en1_lfl_availability: 0.090909,
            sc.score_en1_en_lfl_percent_change: 0.184545,
            sc.score_en1_en_ren_ons: 1.0,
            sc.score_en1_en_ren_ofs: 0.090909,
            sc.score_en1_en_ren_availability: 1,
            sc.score_en1_en_ren_percent_change: 0.3,
            sc.score_en1_en_ren_performance: 0.454545,
            sc.score_en1_en_lfl_percent_change_lc: 0.209091,
            sc.score_en1_en_lfl_percent_change_tc: 0.209091,
            sc.score_en1_en_area_time_cov_p_lc: 0.763636,
            sc.score_en1_en_area_time_cov_p_tc: 0.189091,
            sc.score_en1_energy_efficiency: 1,
            sc.score_en1_energy_efficiency_absolute: 2.5,
            sc.score_en1_energy_efficiency_percent: 100,
            sc.score_en1_energy_efficiency_max: 2.5,
            sc.score_en1_en_lfl: 0.165818,
            sc.score_en1_energy_performance: 0.236363,
            sc.asset_size_owned_m2: 220.0,
            sc.score_en1: 4.277272,
            sc.score_en1_max: 14.0,
            sc.score_en1_fraction: 0.305519,
            sc.score_en1_percent: 30.5519,
            R1TableModel.R_1_TBL_AST: 10,
            R1TableModel.R_1_TBL_AREA: 200.0,
        },
        ("OCHI", "NL", 1, 2023): {
            sc.pgav: 41.17,
            sc.score_en1_en_area_time_cov_p: 0.7,
            sc.score_en1_lfl_availability: 1.0,
            sc.score_en1_en_lfl_percent_change: 0.7,
            sc.score_en1_en_ren_ons: 0.0,
            sc.score_en1_en_ren_ofs: 1.0,
            sc.score_en1_en_ren_availability: 0.5,
            sc.score_en1_en_ren_percent_change: 0.7,
            sc.score_en1_en_ren_performance: 0.7,
            sc.score_en1_en_lfl_percent_change_lc: 0.7,
            sc.score_en1_en_lfl_percent_change_tc: 0.7,
            sc.score_en1_en_area_time_cov_p_lc: 0.3,
            sc.score_en1_en_area_time_cov_p_tc: 0.7,
            sc.score_en1_energy_efficiency: 0.0,
            sc.score_en1_energy_efficiency_absolute: 0.0,
            sc.score_en1_energy_efficiency_percent: 0.0,
            sc.score_en1_energy_efficiency_max: 2.5,
            sc.score_en1_en_lfl: 0.76,
            sc.score_en1_energy_performance: 0.76,
            sc.asset_size_owned_m2: 90.0,
            sc.score_en1: 9.75,
            sc.score_en1_max: 14.0,
            sc.score_en1_fraction: 0.696429,
            sc.score_en1_percent: 69.642857,
            R1TableModel.R_1_TBL_AST: 3,
            R1TableModel.R_1_TBL_AREA: 250.0,
        },
    }
    expected_output = AggregationTestUtils.get_multiindex_df(
        data_dict,
        names=["property_type_code", "country", "response_id", asc.data_year],
    )

    # TODO: remove following once the test is updated with all columns
    common_cols = list(set(result.columns).intersection(expected_output.columns))
    result = result[common_cols]
    pdt.assert_frame_equal(result, expected_output, check_like=True)


def test_en1_score_aggregation_to_portfolio_level_weighted_mean(
    mock_en1_scored_data, mock_r1_table
):
    aggregator = EN1ScoreAggregator_ResponseId(mock_en1_scored_data, mock_r1_table)
    result = aggregator.aggregate_weighted_data()

    df = pd.DataFrame(
        {
            sc.pgav: [1.0, 1.0],
            sc.score_en1_en_area_time_cov_p: [0.441471, 0.5],
            sc.score_en1_lfl_availability: [0.521243, 1],
            sc.score_en1_en_lfl_percent_change: [0.428545, 0.34],
            sc.score_en1_en_ren_ons: [0.526633, 0],
            sc.score_en1_en_ren_ofs: [0.521243, 0],
            sc.score_en1_en_ren_percent_change: [0.489347, 0.5],
            sc.score_en1_en_ren_performance: [0.570736, 0.5],
            sc.score_en1_en_lfl_percent_change_lc: [0.441471, 0.9],
            sc.score_en1_en_lfl_percent_change_tc: [0.441471, 0.9],
            sc.score_en1_en_area_time_cov_p_lc: [0.544166, 0.5],
            sc.score_en1_en_area_time_cov_p_tc: [0.430938, 0.5],
            sc.score_en1_energy_efficiency: [0.526633, 1],
            sc.score_en1_en_lfl: [0.4470842, 0.472],
            sc.score_en1_energy_performance: [0.484235, 1.0],
        },
        index=[[1, 2], [2023, 2023]],
    )

    df.index.names = ["response_id", asc.data_year]

    # TODO: remove following once the test is updated with all columns
    common_cols = list(set(result.columns).intersection(df.columns))
    result = result[common_cols]
    pdt.assert_frame_equal(result, df, check_like=True)


def test_en1_score_aggregation_to_portfolio_level(mock_en1_scored_data, mock_r1_table):
    aggregator = EN1ScoreAggregator_ResponseId(mock_en1_scored_data, mock_r1_table)
    result = aggregator.process()

    df = pd.DataFrame(
        {
            sc.pgav: [1.0, 1.0],
            sc.score_en1_en_area_time_cov_p: [0.441471, 0.5],
            sc.score_en1_en_area_time_cov_p_lc: [0.544166, 0.5],
            sc.score_en1_en_area_time_cov_p_tc: [0.430938, 0.5],
            sc.score_en1_lfl_availability: [0.521243, 1],
            sc.score_en1_en_lfl_percent_change: [0.428545, 0.34],
            sc.score_en1_energy_performance: [0.484235, 1.0],
            sc.score_en1_en_ren_ons: [0.526633, 0],
            sc.score_en1_en_ren_ofs: [0.521243, 0],
            sc.score_en1_en_ren_availability: [0.763316, 0],
            sc.score_en1_en_ren_performance: [0.570736, 0.5],
            sc.score_en1_en_ren_percent_change: [0.489347, 0.5],
            sc.score_en1_en_lfl_percent_change_lc: [0.441471, 0.9],
            sc.score_en1_en_lfl_percent_change_tc: [0.441471, 0.9],
        },
        index=[1, 2],
    )

    score_en1_portfolio_1 = (
        df.score_en1_en_area_time_cov_p.values[0] * 8.5
        + df.score_en1_energy_performance.values[0] * 2.5
        + df.score_en1_en_ren_availability.values[0] * 1.0
        + df.score_en1_en_ren_performance.values[0] * 2.0
    )

    score_en1_portfolio_2 = (
        df.score_en1_en_area_time_cov_p.values[1] * 8.5
        + df.score_en1_energy_performance.values[1] * 2.5
        + df.score_en1_en_ren_availability.values[1] * 1.0
        + df.score_en1_en_ren_performance.values[1] * 2.0
    )

    df.index.names = ["response_id"]

    assert round(result.score_en1.values[0], 5) == round(score_en1_portfolio_1, 5)
    assert round(result.score_en1.values[1], 5) == round(score_en1_portfolio_2, 5)
