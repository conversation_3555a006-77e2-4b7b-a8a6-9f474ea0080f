import pandas as pd
import pytest

from app_real_estate.models.input_models.r1_table_model import R1TableModel
from app_real_estate.transformation.aggregation.certification_aggregation_utils import (
    CertificationAggregationUtils,
)
from app_real_estate.transformation.aggregation.energy_rating_metric_aggregator import (
    EnergyRatingMetricAggregator,
)
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.energy_rating_columns as erc


@pytest.fixture
def mock_energy_ratings_data():
    return pd.DataFrame(
        {
            erc.response_id: [1, 1, 1, 1, 2, 2],
            erc.portfolio_asset_id: list(range(1, 7)),
            ac.country: (["FR"] * 3 + ["NL"] + ["FR"] * 2),
            ac.property_sector: ["Office"] * 6,
            ac.property_type_code: ["OCHI"] * 6,
            erc.energy_rating_id: [1, 1, 2, 3, 1, 1],
            erc.energy_rating_name: (
                ["EU EPC - A"] * 2
                + ["EU EPC - B", "NABERS Energy - 5 Stars"]
                + ["EU EPC - A"] * 2
            ),
            erc.asset_size_m2: [100] * 6,
            erc.asset_ownership: [100] * 6,
            erc.covered_floor_area_m2: [40, 60, 20, 100, 50, 50],
        }
    )


@pytest.fixture
def mock_r1_table():
    return pd.DataFrame(
        {
            R1TableModel.RESPONSE_ID: [1, 1, 2],
            R1TableModel.COUNTRY: ["FR", "NL", "FR"],
            R1TableModel.PRT_TYPE: ["OCHI", "OCHI", "OCHI"],
            R1TableModel.R_1_TBL_AST: [10, 10, 20],
            R1TableModel.R_1_TBL_AREA: [200, 100, 200],
            R1TableModel.R_1_TBL_PGAV: [80, 20, 100],
        }
    )


@pytest.fixture
def mock_property_type_sector_mapping():
    return pd.DataFrame({"PRT_TYPE": ["OCHI"], "PRT_SECTOR": ["Office"]})


@pytest.fixture
def er_metric_aggregator_ctr_sector(
    mock_energy_ratings_data,
    mock_r1_table,
    mock_property_type_sector_mapping,
):
    return EnergyRatingMetricAggregator(
        energy_ratings_data=mock_energy_ratings_data,
        r1_table=mock_r1_table,
        portfolio_level=False,
        property_type_sector_mapping=mock_property_type_sector_mapping,
    )


@pytest.fixture
def er_metric_aggregator_portfolio(
    mock_energy_ratings_data,
    mock_r1_table,
):
    return EnergyRatingMetricAggregator(
        energy_ratings_data=mock_energy_ratings_data,
        r1_table=mock_r1_table,
        portfolio_level=True,
    )


@pytest.fixture
def aggregation_utils_ctr_sector(mock_r1_table, mock_property_type_sector_mapping):
    return CertificationAggregationUtils(
        ["COUNTRY", "PRT_SECTOR"], mock_r1_table, mock_property_type_sector_mapping
    )


@pytest.fixture
def mock_transformed_er_data(er_metric_aggregator_ctr_sector, mock_energy_ratings_data):
    return er_metric_aggregator_ctr_sector.prepare_energy_ratings_data(
        mock_energy_ratings_data
    )


def test_prepare_energy_ratings_data_equal_df(
    er_metric_aggregator_ctr_sector, mock_energy_ratings_data
):
    transformed_data = er_metric_aggregator_ctr_sector.prepare_energy_ratings_data(
        mock_energy_ratings_data
    )

    expected_data = pd.DataFrame(
        {
            "RESPONSE_ID": [1, 1, 1, 1, 2, 2],
            "TYPE": ["EN"] * 6,
            "COUNTRY": (["FR"] * 3 + ["NL"] + ["FR"] * 2),
            "PRT_SECTOR": ["Office"] * 6,
            "PRT_TYPE": ["OCHI"] * 6,
            "ID": [1, 1, 2, 3, 1, 1],
            "NAME": (
                ["EU EPC - A"] * 2
                + ["EU EPC - B", "NABERS Energy - 5 Stars"]
                + ["EU EPC - A"] * 2
            ),
            erc.portfolio_asset_id: list(range(1, 7)),
            "COV": [40.0, 60.0, 20.0, 100.0, 50.0, 50.0],
            ac.asset_size_owned_m2: [100.0] * 6,
        }
    )

    pd.testing.assert_frame_equal(transformed_data, expected_data)


def test_prepare_energy_ratings_data_empty_energy_ratings_data(
    er_metric_aggregator_ctr_sector,
):
    with pytest.raises(
        ValueError,
        match="Empty energy ratings data",
    ):
        er_metric_aggregator_ctr_sector.prepare_energy_ratings_data(pd.DataFrame())


def test_add_rated_area_percent_level1(
    aggregation_utils_ctr_sector, mock_transformed_er_data
):
    level1 = ["COUNTRY", "PRT_SECTOR", "TYPE", "ID", "NAME"]
    pcov_result = aggregation_utils_ctr_sector.add_certified_area_percent(
        mock_transformed_er_data, level1
    )

    expected = pd.DataFrame(
        {
            "RESPONSE_ID": [1, 1, 1, 2],
            "COUNTRY": (["FR"] * 2 + ["NL"] + ["FR"]),
            "PRT_SECTOR": ["Office"] * 4,
            "TYPE": ["EN"] * 4,
            "ID": [1, 2, 3, 1],
            "NAME": ["EU EPC - A"]
            + ["EU EPC - B", "NABERS Energy - 5 Stars"]
            + ["EU EPC - A"],
            "PCOV": [50.0, 10.0, 100.0, 50.0],
        }
    )
    expected = expected.sort_values(["RESPONSE_ID"] + level1)

    pd.testing.assert_frame_equal(
        pcov_result.reset_index(drop=True), expected.reset_index(drop=True)
    )


def test_add_rated_area_percent_level2(
    aggregation_utils_ctr_sector, mock_transformed_er_data
):
    level2 = ["COUNTRY", "PRT_SECTOR", "TYPE"]
    pcov_result = aggregation_utils_ctr_sector.add_certified_area_percent(
        mock_transformed_er_data, level2
    )

    expected = pd.DataFrame(
        {
            "RESPONSE_ID": [1, 1, 2],
            "COUNTRY": ["FR", "NL", "FR"],
            "PRT_SECTOR": ["Office"] * 3,
            "TYPE": ["EN"] * 3,
        }
    )
    expected["PCOV"] = [60.0, 100.0, 50.0]
    expected = expected.sort_values(["RESPONSE_ID"] + level2)

    pd.testing.assert_frame_equal(
        pcov_result.reset_index(drop=True), expected.reset_index(drop=True)
    )


def test_add_benchmark_certified_area_percentage(
    aggregation_utils_ctr_sector, mock_transformed_er_data
):
    level2 = ["COUNTRY", "PRT_SECTOR", "TYPE"]
    agg_certs = aggregation_utils_ctr_sector.add_certified_area_percent(
        mock_transformed_er_data, level2
    )
    result = aggregation_utils_ctr_sector.add_benchmark_certified_area_percentage(
        agg_certs, level2
    )

    expected = agg_certs
    expected["BENCH_PCOV"] = [55.0, 100.0, 55.0]
    expected = expected.sort_values(["RESPONSE_ID"] + level2)

    pd.testing.assert_frame_equal(
        result.reset_index(drop=True), expected.reset_index(drop=True)
    )
