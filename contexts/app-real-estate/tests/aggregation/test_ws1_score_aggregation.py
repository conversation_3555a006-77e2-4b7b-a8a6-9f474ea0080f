import pytest
import pandas as pd
import pandas.testing as pdt
import app_real_estate.constants.column_names.asset_characteristics_columns as asc
import app_real_estate.constants.column_names.score_columns as sc
from app_real_estate.models.input_models.r1_table_model import R1TableModel
from app_real_estate.transformation.aggregation.indicator.ws1.ws1_score_aggregator_response_id import (
    WS1ScoreAggregator_ResponseId,
)
from app_real_estate.transformation.aggregation.indicator.ws1.ws1_score_aggregator_property_subtype_response_id import (
    WS1ScoreAggregator_PropertySubtype_ResponseId,
)
from app_real_estate.models.score_aggregation_util_models.ws1_scored_data import (
    WS1ScoredData,
)
from tests.aggregation.agg_test_utils import AggregationTestUtils


@pytest.fixture
def mock_r1_table():
    return pd.DataFrame(
        {
            R1TableModel.RESPONSE_ID: [1, 1, 2],
            R1TableModel.COUNTRY: ["JP", "NL", "NL"],
            R1TableModel.PRT_TYPE: ["OCHI", "OCHI", "TSOT"],
            R1TableModel.R_1_TBL_PGAV: [45.8027, 41.17, 12.74],
        }
    )


@pytest.fixture
def mock_ws1_scored_data():
    return pd.DataFrame(
        {
            "response_id": [1, 1, 1, 2],
            asc.data_year: [2023] * 4,
            "country": ["JP", "JP", "NL", "NL"],
            "property_type_code": [
                "OCHI",
                "OCHI",
                "OCHI",
                "TSOT",
            ],
            asc.asset_ownership: [10, 80, 30, 100],
            asc.asset_size_m2: [200, 250, 300, 540],
            sc.score_ws1_was_area_cov_p: [0.3, 0.2, 0.7, 0.5],
            sc.score_ws1_was_area_cov_p_lc: [0.3, 0.2, 0.7, 0.5],
            sc.score_ws1_was_area_cov_p_tc: [0.3, 0.2, 0.7, 0.5],
            sc.score_ws1_was_diverted_percent: [0.3, 0.2, 0.7, 0.5],
        }
    )


def test_ws1_parse_dataclass(mock_ws1_scored_data):
    ws1_scored_data = WS1ScoredData.parse_df(
        dataframe=mock_ws1_scored_data, verbose=True
    )
    pdt.assert_frame_equal(ws1_scored_data, mock_ws1_scored_data, check_like=True)


def test_ws1_score_aggregation_on_property_subtype_and_response_id(
    mock_ws1_scored_data: pd.DataFrame, mock_r1_table: pd.DataFrame
):
    ws1_score_aggregator = WS1ScoreAggregator_PropertySubtype_ResponseId(
        data=mock_ws1_scored_data, r1_table=mock_r1_table
    )
    result = ws1_score_aggregator.process()

    data_dict = {
        ("TSOT", 2, 2023): {
            sc.score_ws1_was_area_cov_p: 0.5,
            sc.score_ws1_was_area_cov_p_lc: 0.5,
            sc.score_ws1_was_area_cov_p_tc: 0.5,
            sc.score_ws1_was_diverted_percent: 0.5,
            sc.score_ws1: 2.0,
            sc.score_ws1_fraction: 0.5,
            sc.score_ws1_percent: 50.0,
            sc.score_ws1_max: 4.0,
            sc.pgav: 12.74,
        },
        ("OCHI", 1, 2023): {
            sc.score_ws1_was_area_cov_p: 0.441471,
            sc.score_ws1_was_area_cov_p_lc: 0.441471,
            sc.score_ws1_was_area_cov_p_tc: 0.441471,
            sc.score_ws1_was_diverted_percent: 0.441471,
            sc.score_ws1: 1.76588,
            sc.score_ws1_fraction: 0.441471,
            sc.score_ws1_percent: 44.1471,
            sc.score_ws1_max: 4.0,
            sc.pgav: 86.9727,
        },
    }

    expected_result = AggregationTestUtils.get_multiindex_df(
        data_dict=data_dict,
        names=["property_type_code", "response_id", asc.data_year],
    )

    # TODO: remove following once the test is updated with all columns
    common_cols = list(set(result.columns).intersection(expected_result.columns))
    result = result[common_cols]
    pdt.assert_frame_equal(result, expected_result, check_like=True)


def test_ws1_score_aggregation_on_portfolio_level(
    mock_ws1_scored_data: pd.DataFrame, mock_r1_table
):
    ws1_score_aggregator = WS1ScoreAggregator_ResponseId(
        data=mock_ws1_scored_data, r1_table=mock_r1_table
    )
    result = ws1_score_aggregator.process()

    expected_output = pd.DataFrame(
        {
            sc.score_ws1_was_area_cov_p: [0.441471, 0.5],
            sc.score_ws1_was_area_cov_p_lc: [0.441471, 0.5],
            sc.score_ws1_was_area_cov_p_tc: [0.441471, 0.5],
            sc.score_ws1_was_diverted_percent: [0.441471, 0.5],
            sc.score_ws1: [1.765884, 2.0],
            sc.pgav: [1.0, 1.0],
            sc.score_ws1_fraction: [0.441471, 0.5],
            sc.score_ws1_percent: [44.1471, 50.0],
            sc.score_ws1_max: [4.0, 4.0],
        },
        index=[[1, 2], [2023, 2023]],
    )
    expected_output.index.names = ["response_id", asc.data_year]

    # TODO: remove following once the test is updated with all columns
    common_cols = list(set(result.columns).intersection(expected_output.columns))
    result = result[common_cols]
    pdt.assert_frame_equal(result, expected_output, check_like=True)
