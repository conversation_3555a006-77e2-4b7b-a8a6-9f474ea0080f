import pytest
import pandas as pd
import pandas.testing as pdt
import app_real_estate.constants.column_names.asset_characteristics_columns as asc
import app_real_estate.constants.column_names.score_columns as sc
from app_real_estate.models.input_models.r1_table_model import R1TableModel
from app_real_estate.transformation.aggregation.indicator.bc1_2.bc1_2_score_aggregator_property_subtype_response_id import (
    BC1_2ScoreAggregator_PropertySubtype_ResponseId,
)
from app_real_estate.transformation.aggregation.indicator.bc1_2.bc1_2_score_aggregator_response_id import (
    BC1_2ScoreAggregator_ResponseId,
)
from app_real_estate.transformation.aggregation.indicator.bc1_2.bc1_2_score_aggregator_property_subtype_country_responseid import (
    BC1_2ScoreAggregator_PropertySubtype_Country_ResponseId,
)
from app_real_estate.models.score_aggregation_util_models.bc1_2_scored_data import (
    BC1_2ScoredData,
)
from tests.aggregation.agg_test_utils import AggregationTestUtils


@pytest.fixture
def mock_r1_table():
    return pd.DataFrame(
        {
            R1TableModel.RESPONSE_ID: [1, 1, 1],
            R1TableModel.COUNTRY: ["JP", "NL", "NL"],
            R1TableModel.PRT_TYPE: ["OCHI", "OCHI", "TSOT"],
            R1TableModel.R_1_TBL_PGAV: [45.8027, 41.17, 132.74],
        }
    )


@pytest.fixture
def mock_bc1_2_scored_data():
    return pd.DataFrame(
        {
            "response_id": [1, 1, 1, 1, 1],
            asc.data_year: [2023] * 5,
            "property_type_code": [
                "OCHI",
                "OCHI",
                "OCHI",
                "TSOT",
                "TSOT",
            ],
            "country": ["JP", "JP", "NL", "NL", "NL"],
            asc.asset_ownership: [10, 80, 30, 100, 50],
            asc.asset_size_m2: [200, 250, 300, 540, 1000],
            sc.score_bc1_2_coverage: [0.1, 0.2, 0.5, 0.7, 0.5],
        }
    )


def test_bc1_2_parse_dataclass(mock_bc1_2_scored_data):
    bc1_2_scored_data = BC1_2ScoredData.parse_df(
        dataframe=mock_bc1_2_scored_data, verbose=True
    )
    pdt.assert_frame_equal(bc1_2_scored_data, mock_bc1_2_scored_data)


def test_bc1_2_score_aggregation_on_property_subtype_and_response_id(
    mock_bc1_2_scored_data, mock_r1_table
):
    aggregator = BC1_2ScoreAggregator_PropertySubtype_ResponseId(
        mock_bc1_2_scored_data, mock_r1_table
    )
    aggregated_data = aggregator.process()

    data_dict = {
        ("TSOT", 1, 2023): {
            sc.score_bc1_2_coverage: 0.603846,
            sc.asset_size_owned_m2: 1040.0,
            sc.score_bc1_2_fraction: 0.603846,
            sc.score_bc1_2: 5.132692,
            sc.score_bc1_2_percent: 60.3846,
            sc.score_bc1_2_max: 8.5,
            sc.pgav: 132.74,
        },
        ("OCHI", 1, 2023): {
            sc.score_bc1_2_coverage: 0.280645,
            sc.asset_size_owned_m2: 310.0,
            sc.score_bc1_2_fraction: 0.280645,
            sc.score_bc1_2: 2.385484,
            sc.score_bc1_2_percent: 28.0645,
            sc.score_bc1_2_max: 8.5,
            sc.pgav: 86.9727,
        },
    }
    expected_output = AggregationTestUtils.get_multiindex_df(
        data_dict, names=[asc.property_type_code, asc.response_id, asc.data_year]
    )

    pdt.assert_frame_equal(aggregated_data, expected_output, check_like=True)


def test_bc1_2_score_aggregation_on_property_subtype_and_country_and_response_id(
    mock_bc1_2_scored_data, mock_r1_table
):
    aggregator = BC1_2ScoreAggregator_PropertySubtype_Country_ResponseId(
        mock_bc1_2_scored_data, mock_r1_table, report_type="re"
    )
    aggregated_data = aggregator.process()

    data_dict = {
        ("TSOT", "NL", 1, 2023): {
            sc.score_bc1_2_coverage: 0.603846,
            sc.pgav: 132.74,
            sc.asset_size_owned_m2: 1040.0,
            sc.score_bc1_2_fraction: 0.603846,
            sc.score_bc1_2: 5.132692,
            sc.score_bc1_2_percent: 60.3846,
            sc.score_bc1_2_max: 8.5,
        },
        ("OCHI", "JP", 1, 2023): {
            sc.score_bc1_2_coverage: 0.190909,
            sc.pgav: 45.8027,
            sc.asset_size_owned_m2: 220.0,
            sc.score_bc1_2_fraction: 0.190909,
            sc.score_bc1_2: 1.622727,
            sc.score_bc1_2_percent: 19.0909,
            sc.score_bc1_2_max: 8.5,
        },
        ("OCHI", "NL", 1, 2023): {
            sc.score_bc1_2_coverage: 0.5,
            sc.pgav: 41.17,
            sc.asset_size_owned_m2: 90.0,
            sc.score_bc1_2_fraction: 0.5,
            sc.score_bc1_2: 4.25,
            sc.score_bc1_2_percent: 50.0,
            sc.score_bc1_2_max: 8.5,
        },
    }
    expected_output = AggregationTestUtils.get_multiindex_df(
        data_dict,
        names=[
            asc.property_type_code,
            asc.country,
            asc.response_id,
            asc.data_year,
        ],
    )

    pdt.assert_frame_equal(aggregated_data, expected_output, check_like=True)


def test_bc1_2_score_aggregation_on_response_id(mock_bc1_2_scored_data, mock_r1_table):
    aggregator = BC1_2ScoreAggregator_ResponseId(
        mock_bc1_2_scored_data, mock_r1_table, report_type="re"
    )
    aggregated_data = aggregator.process()
    expected_output = pd.DataFrame(
        {
            sc.score_bc1_2: [4.235582],
            sc.pgav: [1.0],
            sc.score_bc1_2_coverage: [0.498304],
            sc.score_bc1_2_fraction: [0.498304],
            sc.score_bc1_2_percent: [49.8304],
            sc.score_bc1_2_max: [8.5],
        },
        index=[[1], [2023]],
    )
    expected_output.index.names = ["response_id", asc.data_year]

    pdt.assert_frame_equal(aggregated_data, expected_output, check_like=True)
