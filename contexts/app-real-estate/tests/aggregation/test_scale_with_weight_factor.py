import pytest
import pandas as pd
from app_real_estate.transformation.aggregation.processor.scale_with_weight_factor import (
    ScaleWithWeightFactor,
)


@pytest.mark.parametrize(
    "data, weight_factor, cols_to_scale, expected",
    [
        # Valid Data and Weight Factor
        (
            pd.DataFrame({"A": [1, 2, 3], "B": [4, 5, 6], "weight": [1, 2, 3]}),
            "weight",
            ["A", "B"],
            pd.DataFrame({"A": [1, 4, 9], "B": [4, 10, 18], "weight": [1, 2, 3]}),
        ),
        # Weight factor column has all ones
        (
            pd.DataFrame({"A": [1, 2, 3], "B": [4, 5, 6], "weight": [1, 1, 1]}),
            "weight",
            ["A", "B"],
            pd.DataFrame({"A": [1, 2, 3], "B": [4, 5, 6], "weight": [1, 1, 1]}),
        ),
        # Weight factor column has all zeros
        (
            pd.DataFrame({"A": [1, 2, 3], "B": [4, 5, 6], "weight": [0, 0, 0]}),
            "weight",
            ["A", "B"],
            pd.DataFrame({"A": [0, 0, 0], "B": [0, 0, 0], "weight": [0, 0, 0]}),
        ),
    ],
)
def test_scale_with_weight_factor(data, weight_factor, cols_to_scale, expected):
    processor = ScaleWithWeightFactor(data, weight_factor, cols_to_scale)
    result = processor.process()
    pd.testing.assert_frame_equal(result, expected)


def test_missing_weight_factor_column():
    data = pd.DataFrame({"A": [1, 2, 3], "B": [4, 5, 6]})
    weight_factor = "weight"
    cols_to_scale = ["A", "B"]
    processor = ScaleWithWeightFactor(data, weight_factor, cols_to_scale)
    with pytest.raises(ValueError, match="Missing required columns: weight"):
        processor.process()
