import pandas as pd
import pytest

from app_real_estate.models.input_models.r1_table_model import R1TableModel
from app_real_estate.transformation.aggregation.certification_aggregation_utils import (
    CertificationAggregationUtils,
)
from app_real_estate.transformation.aggregation.certification_metric_aggregator import (
    CertificationMetricAggregator,
)
import app_real_estate.constants.column_names.asset_characteristics_columns as ac
import app_real_estate.constants.column_names.building_certification_columns as bc


@pytest.fixture
def mock_certifications_data():
    return pd.DataFrame(
        {
            bc.response_id: [1, 1, 1, 1, 2, 2, 1, 1, 1, 1, 2, 2],
            bc.portfolio_asset_id: list(range(1, 13)),
            ac.country: (["FR"] * 3 + ["NL"] + ["FR"] * 2) * 2,
            ac.property_sector: ["Office"] * 12,
            ac.property_type_code: ["OCHI"] * 12,
            bc.type: ["design"] * 6 + ["operational"] * 6,
            bc.certification_id: [1, 1, 1, 2, 1, 1, 3, 3, 3, 4, 3, 3],
            bc.scoring_coverage: [1.0] * 12,
            bc.brand: ["BREEAM"] * 12,
            bc.scheme: ["Design1"] * 3
            + ["Design2"]
            + ["Design1"] * 2
            + ["Ope1"] * 3
            + ["Ope2"]
            + ["Ope1"] * 2,
            bc.outcome: (["Gold"] * 2 + ["Silver"] + ["5 Stars"] + ["Gold"] * 2) * 2,
            bc.age: [2, 4, 5, 4, 0, 2] * 2,
            bc.owned_covered_floor_area: [40, 60, 20, 100, 50, 50] * 2,
            bc.asset_size_m2: [100] * 12,
            bc.asset_ownership: [100] * 12,
        }
    )


@pytest.fixture
def mock_r1_table():
    return pd.DataFrame(
        {
            R1TableModel.RESPONSE_ID: [1, 1, 2, 2],
            R1TableModel.COUNTRY: ["FR", "NL", "FR", "FR"],
            R1TableModel.PRT_TYPE: ["OCHI", "OCHI", "OCHI", "RMFH"],
            R1TableModel.R_1_TBL_AST: [10, 10, 20, 2],
            R1TableModel.R_1_TBL_AREA: [200, 100, 200, 2],
            R1TableModel.R_1_TBL_PGAV: [80, 20, 100, 2],
        }
    )


@pytest.fixture
def mock_property_type_sector_mapping():
    return pd.DataFrame(
        {"PRT_TYPE": ["OCHI", "RMFH"], "PRT_SECTOR": ["Office", "Residential"]}
    )


@pytest.fixture
def certification_metric_aggregator_ctr_sector(
    mock_certifications_data, mock_r1_table, mock_property_type_sector_mapping
):
    return CertificationMetricAggregator(
        certification_data=mock_certifications_data,
        r1_table=mock_r1_table,
        portfolio_level=False,
        property_type_sector_mapping=mock_property_type_sector_mapping,
    )


@pytest.fixture
def certification_metric_aggregator_portfolio(
    mock_certifications_data,
    mock_r1_table,
):
    return CertificationMetricAggregator(
        certification_data=mock_certifications_data,
        r1_table=mock_r1_table,
        portfolio_level=True,
    )


@pytest.fixture
def aggregation_utils_ctr_sector(mock_r1_table, mock_property_type_sector_mapping):
    return CertificationAggregationUtils(
        ["COUNTRY", "PRT_SECTOR"], mock_r1_table, mock_property_type_sector_mapping
    )


@pytest.fixture
def mock_transformed_cert_data(
    certification_metric_aggregator_ctr_sector, mock_certifications_data
):
    return certification_metric_aggregator_ctr_sector.prepare_certification_data(
        mock_certifications_data
    )


def test_prepare_certifications_data_equal_df(
    certification_metric_aggregator_ctr_sector, mock_certifications_data
):
    transformed_data = (
        certification_metric_aggregator_ctr_sector.prepare_certification_data(
            mock_certifications_data
        )
    )

    expected_data = pd.DataFrame(
        {
            "RESPONSE_ID": [1, 1, 1, 1, 2, 2, 1, 1, 1, 1, 2, 2],
            "TYPE": ["NC"] * 6 + ["OPR"] * 6,
            "COUNTRY": (["FR"] * 3 + ["NL"] + ["FR"] * 2) * 2,
            "PRT_SECTOR": ["Office"] * 12,
            "PRT_TYPE": ["OCHI"] * 12,
            "ID": [1, 1, 1, 2, 1, 1, 3, 3, 3, 4, 3, 3],
            "BRAND": ["BREEAM"] * 12,
            "SCHEME": ["Design1"] * 3
            + ["Design2"]
            + ["Design1"] * 2
            + ["Ope1"] * 3
            + ["Ope2"]
            + ["Ope1"] * 2,
            "LEVEL": (["Gold"] * 2 + ["Silver"] + ["5 Stars"] + ["Gold"] * 2) * 2,
            "SCHEME_LEVEL": ["Design1 | Gold"] * 2
            + ["Design1 | Silver"]
            + ["Design2 | 5 Stars"]
            + ["Design1 | Gold"] * 2
            + ["Ope1 | Gold"] * 2
            + ["Ope1 | Silver"]
            + ["Ope2 | 5 Stars"]
            + ["Ope1 | Gold"] * 2,
            bc.portfolio_asset_id: list(range(1, 13)),
            "COV": [40, 60, 20, 100, 50, 50] * 2,
            ac.asset_size_owned_m2: [100.0] * 12,
        }
    )

    pd.testing.assert_frame_equal(transformed_data, expected_data)


def test_prepare_certifications_data_empty_certifications(
    certification_metric_aggregator_ctr_sector,
):
    with pytest.raises(
        ValueError,
        match="Empty certification data",
    ):
        certification_metric_aggregator_ctr_sector.prepare_certification_data(
            pd.DataFrame()
        )


# TODO: Add test with wrong values in the `indicator` column of scoring benchmarks.


def test_add_certified_area_percent_level1(
    aggregation_utils_ctr_sector, mock_transformed_cert_data
):
    level1 = ["COUNTRY", "PRT_SECTOR", "TYPE", "ID", "SCHEME_LEVEL", "BRAND"]
    pcov_result = aggregation_utils_ctr_sector.add_certified_area_percent(
        mock_transformed_cert_data, level1
    )

    expected = pd.DataFrame(
        {
            "RESPONSE_ID": [1, 1, 1, 2] * 2,
            "COUNTRY": (["FR"] * 2 + ["NL"] + ["FR"]) * 2,
            "PRT_SECTOR": ["Office"] * 8,
            "TYPE": ["NC"] * 4 + ["OPR"] * 4,
            "ID": [1, 1, 2, 1, 3, 3, 4, 3],
            "SCHEME_LEVEL": [
                "Design1 | Gold",
                "Design1 | Silver",
                "Design2 | 5 Stars",
                "Design1 | Gold",
                "Ope1 | Gold",
                "Ope1 | Silver",
                "Ope2 | 5 Stars",
                "Ope1 | Gold",
            ],
            "BRAND": ["BREEAM"] * 8,
            "PCOV": [50.0, 10.0, 100.0, 50.0] * 2,
        }
    )
    expected = expected.sort_values(["RESPONSE_ID"] + level1)

    pd.testing.assert_frame_equal(
        pcov_result.reset_index(drop=True), expected.reset_index(drop=True)
    )


def test_add_certified_area_percent_level2(
    aggregation_utils_ctr_sector, mock_transformed_cert_data
):
    level2 = ["COUNTRY", "PRT_SECTOR", "TYPE", "BRAND"]
    pcov_result = aggregation_utils_ctr_sector.add_certified_area_percent(
        mock_transformed_cert_data, level2
    )

    expected = pd.DataFrame(
        {
            "RESPONSE_ID": [1, 1, 2, 1, 1, 2],
            "COUNTRY": ["FR", "NL", "FR"] * 2,
            "PRT_SECTOR": ["Office"] * 6,
            "TYPE": ["NC"] * 3 + ["OPR"] * 3,
            "BRAND": ["BREEAM"] * 6,
        }
    )
    expected["PCOV"] = [60.0, 100.0, 50.0] * 2
    expected = expected.sort_values(["RESPONSE_ID"] + level2)

    pd.testing.assert_frame_equal(
        pcov_result.reset_index(drop=True), expected.reset_index(drop=True)
    )


def test_add_certified_area_percent_level3(
    aggregation_utils_ctr_sector, mock_transformed_cert_data
):
    level3 = ["COUNTRY", "PRT_SECTOR", "TYPE"]
    pcov_result = aggregation_utils_ctr_sector.add_certified_area_percent(
        mock_transformed_cert_data, level3
    )

    expected = pd.DataFrame(
        {
            "RESPONSE_ID": [1, 1, 2, 2, 1, 1, 2, 2],
            "COUNTRY": ["FR", "NL", "FR", "FR"] * 2,
            "PRT_SECTOR": (["Office"] * 3 + ["Residential"]) * 2,
            "TYPE": ["NC"] * 4 + ["OPR"] * 4,
        }
    )
    expected["PCOV"] = [60.0, 100.0, 50.0, 0.0] * 2
    expected = expected.sort_values(["RESPONSE_ID"] + level3)

    pd.testing.assert_frame_equal(
        pcov_result.reset_index(drop=True), expected.reset_index(drop=True)
    )


def test_add_certified_ast_count_sector_ctr_level3(
    certification_metric_aggregator_ctr_sector, mock_transformed_cert_data
):
    level3 = ["COUNTRY", "PRT_SECTOR", "TYPE"]
    ast_count_result = (
        certification_metric_aggregator_ctr_sector.add_certified_asset_count(
            certification_data=mock_transformed_cert_data,
            level=level3,
        )
    )

    expected = pd.DataFrame(
        {
            "RESPONSE_ID": [1, 1, 2] * 2,
            "COUNTRY": ["FR", "NL", "FR"] * 2,
            "PRT_SECTOR": ["Office"] * 6,
            "TYPE": ["NC"] * 3 + ["OPR"] * 3,
        }
    )
    expected["CERTIFIED_AST_COUNT"] = [3, 1, 2] * 2
    expected = expected.sort_values(["RESPONSE_ID"] + level3)

    pd.testing.assert_frame_equal(
        ast_count_result.reset_index(drop=True), expected.reset_index(drop=True)
    )


def test_add_benchmark_certified_area_percentage(
    aggregation_utils_ctr_sector, mock_transformed_cert_data
):
    level3 = ["COUNTRY", "PRT_SECTOR", "TYPE"]
    agg_certs = aggregation_utils_ctr_sector.add_certified_area_percent(
        mock_transformed_cert_data, level3
    )
    # Modify one PCOV value for NC certifications to test the separate
    # calculation of the benchmark for different type of certifications.
    agg_certs.loc[0, "PCOV"] = 40.0

    result = aggregation_utils_ctr_sector.add_benchmark_certified_area_percentage(
        agg_certs, level3
    )

    expected = agg_certs
    expected["BENCH_PCOV"] = [55.0, 100.0, 55.0, 0.0, 45.0, 100.0, 45.0, 0.0]
    expected = expected.sort_values(["RESPONSE_ID"] + level3)

    pd.testing.assert_frame_equal(
        result.reset_index(drop=True), expected.reset_index(drop=True)
    )
