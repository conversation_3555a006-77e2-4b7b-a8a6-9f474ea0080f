import pytest
import pandas as pd
from unittest.mock import Mock, patch, ANY
from app_real_estate.transformation.aggregation.processor.lender_energy_ratings_pre_processor import (
    LenderEnergyRatingsPreProcessor,
)


@pytest.fixture
def mock_energy_ratings_data():
    return pd.DataFrame(
        {
            "survey_year": [2025, 2025, 2025, 2024],
            "portfolio_asset_id": [1, 2, 3, 4],
            "energy_rating": ["A", "B", "C", "A"],
            "rating_system": ["ENERGY_STAR", "BREEAM", "LEED", "ENERGY_STAR"],
        }
    )


@pytest.fixture
def mock_energy_ratings_data_with_size():
    return pd.DataFrame(
        {
            "survey_year": [2025, 2025, 2025],
            "portfolio_asset_id": [1, 2, 3],
            "energy_rating": ["A", "B", "C"],
            "size": [900.0, None, 1400.0],  # Some with size, some without
        }
    )


@pytest.fixture
def mock_asset_level_data():
    return pd.DataFrame(
        {
            "portfolio_asset_id": [1, 2, 3],
            "asset_size_m2": [1000.0, 2000.0, 1500.0],
            "asset_ownership": [100.0, 80.0, 90.0],
            "property_sector": ["Office", "Retail", "Industrial"],
            "property_type_code": ["OFF", "RET", "IND"],
            "country": ["US", "UK", "DE"],
        }
    )


@pytest.fixture
def mock_r1_table():
    return pd.DataFrame(
        {"portfolio_asset_id": [1, 2, 3], "valid_asset": [True, True, True]}
    )


@pytest.fixture
def mock_asset_merger():
    merger = Mock()
    merger.merge.return_value = pd.DataFrame(
        {
            "portfolio_asset_id": [1, 2, 3],
            "energy_rating": ["A", "B", "C"],
            "asset_size_m2": [1000.0, 2000.0, 1500.0],
            "asset_ownership": [100.0, 80.0, 90.0],
            "property_sector": ["Office", "Retail", "Industrial"],
            "property_type_code": ["OFF", "RET", "IND"],
            "country": ["US", "UK", "DE"],
        }
    )
    return merger


@pytest.fixture
def mock_r1_merger():
    merger = Mock()
    merger.merge.return_value = pd.DataFrame(
        {
            "portfolio_asset_id": [1, 2, 3],
            "energy_rating": ["A", "B", "C"],
            "covered_floor_area_m2": [1000.0, 1600.0, 1350.0],
        }
    )
    return merger


class TestLenderEnergyRatingsPreProcessor:

    def test_init_with_default_dependencies(
        self, mock_energy_ratings_data, mock_asset_level_data, mock_r1_table
    ):
        """Test initialization with default dependency classes."""
        processor = LenderEnergyRatingsPreProcessor(
            energy_ratings_data=mock_energy_ratings_data,
            asset_level_data=mock_asset_level_data,
            r1_table=mock_r1_table,
            survey_year=2025,
        )

        assert processor.energy_ratings_data.equals(mock_energy_ratings_data)
        assert processor.asset_level_data.equals(mock_asset_level_data)
        assert processor.r1_table.equals(mock_r1_table)
        assert processor.survey_year == 2025
        assert processor.asset_merger_class is not None
        assert processor.r1_merger_class is not None

    def test_init_with_injected_dependencies(
        self, mock_energy_ratings_data, mock_asset_level_data, mock_r1_table
    ):
        """Test initialization with injected dependency classes."""
        mock_asset_merger_class = Mock()
        mock_r1_merger_class = Mock()

        processor = LenderEnergyRatingsPreProcessor(
            energy_ratings_data=mock_energy_ratings_data,
            asset_level_data=mock_asset_level_data,
            r1_table=mock_r1_table,
            survey_year=2025,
            asset_merger_class=mock_asset_merger_class,
            r1_merger_class=mock_r1_merger_class,
        )

        assert processor.asset_merger_class == mock_asset_merger_class
        assert processor.r1_merger_class == mock_r1_merger_class

    def test_validate_success(
        self, mock_energy_ratings_data, mock_asset_level_data, mock_r1_table
    ):
        """Test successful validation."""
        processor = LenderEnergyRatingsPreProcessor(
            energy_ratings_data=mock_energy_ratings_data,
            asset_level_data=mock_asset_level_data,
            r1_table=mock_r1_table,
            survey_year=2025,
        )

        result = processor.validate()

        # Should filter to only 2025 survey year data
        assert len(result) == 3
        assert all(result["survey_year"] == 2025)
        # Energy ratings don't have year filtering like certifications
        assert "energy_rating" in result.columns

    def test_validate_missing_columns(self, mock_asset_level_data, mock_r1_table):
        """Test validation with missing required columns."""
        incomplete_data = pd.DataFrame(
            {
                "survey_year": [2025],
                # Missing 'portfolio_asset_id'
            }
        )

        processor = LenderEnergyRatingsPreProcessor(
            energy_ratings_data=incomplete_data,
            asset_level_data=mock_asset_level_data,
            r1_table=mock_r1_table,
            survey_year=2025,
        )

        with pytest.raises(
            ValueError, match="Energy ratings data missing required columns"
        ):
            processor.validate()

    def test_validate_no_data_for_survey_year(
        self, mock_asset_level_data, mock_r1_table
    ):
        """Test validation when no data exists for the survey year."""
        data_wrong_year = pd.DataFrame(
            {
                "survey_year": [2024, 2024],
                "portfolio_asset_id": [1, 2],
                "energy_rating": ["A", "B"],
            }
        )

        processor = LenderEnergyRatingsPreProcessor(
            energy_ratings_data=data_wrong_year,
            asset_level_data=mock_asset_level_data,
            r1_table=mock_r1_table,
            survey_year=2025,
        )

        with pytest.raises(
            ValueError, match="No energy ratings data found for survey year 2025"
        ):
            processor.validate()

    @patch(
        "app_real_estate.transformation.metric_calculation.common_operations.CommonOperations.add_covered_floor_area_m2"
    )
    @patch(
        "app_real_estate.transformation.metric_calculation.common_operations.CommonOperations.rename_covered_floor_area_column"
    )
    def test_process_success(
        self,
        mock_rename_covered_floor_area,
        mock_add_covered_floor_area_m2,
        mock_energy_ratings_data,
        mock_asset_level_data,
        mock_r1_table,
        mock_asset_merger,
        mock_r1_merger,
    ):
        """Test successful processing pipeline."""
        # Mock the method calls to return the data with required columns
        mock_rename_covered_floor_area.side_effect = lambda x: x
        mock_add_covered_floor_area_m2.side_effect = lambda x, y: x.assign(
            covered_floor_area_m2=[1000.0, 1600.0, 1350.0]
        )

        mock_asset_merger_class = Mock(return_value=mock_asset_merger)
        mock_r1_merger_class = Mock(return_value=mock_r1_merger)

        processor = LenderEnergyRatingsPreProcessor(
            energy_ratings_data=mock_energy_ratings_data,
            asset_level_data=mock_asset_level_data,
            r1_table=mock_r1_table,
            survey_year=2025,
            asset_merger_class=mock_asset_merger_class,
            r1_merger_class=mock_r1_merger_class,
        )

        with patch("builtins.print"):  # Suppress print statements
            result = processor.process()

        # Verify asset merger was called with "energy_ratings" identifier
        mock_asset_merger_class.assert_called_once_with(
            mock_asset_level_data, ANY, "energy_ratings"
        )
        mock_asset_merger.merge.assert_called_once()

        # Verify processing methods were called
        mock_rename_covered_floor_area.assert_called_once()
        mock_add_covered_floor_area_m2.assert_called_once()

        # Verify R1 merger was called with "energy_ratings" identifier
        mock_r1_merger_class.assert_called_once()
        mock_r1_merger.merge.assert_called_once()

        # Result should be from R1 merger
        assert result.equals(mock_r1_merger.merge.return_value)

    @patch(
        "app_real_estate.transformation.metric_calculation.common_operations.CommonOperations.add_covered_floor_area_m2"
    )
    @patch(
        "app_real_estate.transformation.metric_calculation.common_operations.CommonOperations.rename_covered_floor_area_column"
    )
    def test_process_calculates_covered_floor_area_without_size_column(
        self,
        mock_rename_covered_floor_area,
        mock_add_covered_floor_area_m2,
        mock_energy_ratings_data,
        mock_asset_level_data,
        mock_r1_table,
    ):
        """Test that process correctly calculates covered_floor_area_m2 when no size column."""
        # Create data without 'size' column
        asset_merged_data = pd.DataFrame(
            {
                "portfolio_asset_id": [1, 2],
                "asset_size_m2": [1000.0, 2000.0],
                "asset_ownership": [100.0, 80.0],
                "property_sector": ["Office", "Retail"],
                "property_type_code": ["OFF", "RET"],
                "country": ["US", "UK"],
            }
        )

        mock_asset_merger = Mock()
        mock_asset_merger.merge.return_value = asset_merged_data.copy()
        mock_asset_merger_class = Mock(return_value=mock_asset_merger)

        # Mock the processing methods
        mock_rename_covered_floor_area.side_effect = lambda x: x
        mock_add_covered_floor_area_m2.side_effect = lambda x, y: x.assign(
            covered_floor_area_m2=[1000.0, 1600.0]
        )

        mock_r1_merger = Mock()
        mock_r1_merger.merge.return_value = pd.DataFrame({"result": ["success"]})
        mock_r1_merger_class = Mock(return_value=mock_r1_merger)

        processor = LenderEnergyRatingsPreProcessor(
            energy_ratings_data=mock_energy_ratings_data,
            asset_level_data=mock_asset_level_data,
            r1_table=mock_r1_table,
            survey_year=2025,
            asset_merger_class=mock_asset_merger_class,
            r1_merger_class=mock_r1_merger_class,
        )

        with patch("builtins.print"):
            processor.process()

        # Get the data passed to R1 merger
        r1_merger_call_args = mock_r1_merger_class.call_args[0]
        processed_data = r1_merger_call_args[1]

        # Verify covered_floor_area_m2 was calculated correctly
        assert "covered_floor_area_m2" in processed_data.columns
        assert list(processed_data["covered_floor_area_m2"]) == [1000.0, 1600.0]

    @patch(
        "app_real_estate.transformation.metric_calculation.common_operations.CommonOperations.add_covered_floor_area_m2"
    )
    @patch(
        "app_real_estate.transformation.metric_calculation.common_operations.CommonOperations.rename_covered_floor_area_column"
    )
    def test_process_uses_size_column_when_available(
        self,
        mock_rename_covered_floor_area,
        mock_add_covered_floor_area_m2,
        mock_energy_ratings_data_with_size,
        mock_asset_level_data,
        mock_r1_table,
    ):
        """Test that process uses 'size' column when available, falling back to calculated values."""
        # Create data with 'size' column (some values None)
        asset_merged_data = pd.DataFrame(
            {
                "portfolio_asset_id": [1, 2, 3],
                "asset_size_m2": [1000.0, 2000.0, 1500.0],
                "asset_ownership": [100.0, 80.0, 90.0],
                "size": [900.0, None, 1400.0],  # Mix of values and None
                "property_sector": ["Office", "Retail", "Industrial"],
                "property_type_code": ["OFF", "RET", "IND"],
                "country": ["US", "UK", "DE"],
            }
        )

        mock_asset_merger = Mock()
        mock_asset_merger.merge.return_value = asset_merged_data.copy()
        mock_asset_merger_class = Mock(return_value=mock_asset_merger)

        # Mock the processing methods
        mock_rename_covered_floor_area.side_effect = lambda x: x
        mock_add_covered_floor_area_m2.side_effect = lambda x, y: x.assign(
            covered_floor_area_m2=[
                900.0,
                1600.0,
                1400.0,
            ]  # Uses size when available, calculated when not
        )

        mock_r1_merger = Mock()
        mock_r1_merger.merge.return_value = pd.DataFrame({"result": ["success"]})
        mock_r1_merger_class = Mock(return_value=mock_r1_merger)

        processor = LenderEnergyRatingsPreProcessor(
            energy_ratings_data=mock_energy_ratings_data_with_size,
            asset_level_data=mock_asset_level_data,
            r1_table=mock_r1_table,
            survey_year=2025,
            asset_merger_class=mock_asset_merger_class,
            r1_merger_class=mock_r1_merger_class,
        )

        with patch("builtins.print"):
            processor.process()

        # Get the data passed to R1 merger
        r1_merger_call_args = mock_r1_merger_class.call_args[0]
        processed_data = r1_merger_call_args[1]

        # Verify covered_floor_area_m2 uses size when available, calculated when not
        assert "covered_floor_area_m2" in processed_data.columns
        expected_values = [900.0, 1600.0, 1400.0]
        assert list(processed_data["covered_floor_area_m2"]) == expected_values

    @patch(
        "app_real_estate.transformation.metric_calculation.common_operations.CommonOperations.add_covered_floor_area_m2"
    )
    @patch(
        "app_real_estate.transformation.metric_calculation.common_operations.CommonOperations.rename_covered_floor_area_column"
    )
    def test_process_filters_nan_values(
        self,
        mock_rename_covered_floor_area,
        mock_add_covered_floor_area_m2,
        mock_energy_ratings_data,
        mock_asset_level_data,
        mock_r1_table,
    ):
        """Test that process filters out rows with NaN values in required columns."""
        # Create data with some NaN values
        asset_merged_data = pd.DataFrame(
            {
                "portfolio_asset_id": [1, 2, 3],
                "asset_size_m2": [1000.0, 2000.0, 1500.0],
                "asset_ownership": [100.0, 80.0, 90.0],
                "property_sector": [
                    "Office",
                    None,
                    "Industrial",
                ],  # NaN in required column
                "property_type_code": ["OFF", "RET", "IND"],
                "country": ["US", "UK", "DE"],
            }
        )

        mock_asset_merger = Mock()
        mock_asset_merger.merge.return_value = asset_merged_data
        mock_asset_merger_class = Mock(return_value=mock_asset_merger)

        # Mock the processing methods
        mock_rename_covered_floor_area.side_effect = lambda x: x
        mock_add_covered_floor_area_m2.side_effect = lambda x, y: x.assign(
            covered_floor_area_m2=[1000.0, 1600.0, 1350.0]
        )

        mock_r1_merger = Mock()
        mock_r1_merger.merge.return_value = pd.DataFrame({"result": ["filtered"]})
        mock_r1_merger_class = Mock(return_value=mock_r1_merger)

        processor = LenderEnergyRatingsPreProcessor(
            energy_ratings_data=mock_energy_ratings_data,
            asset_level_data=mock_asset_level_data,
            r1_table=mock_r1_table,
            survey_year=2025,
            asset_merger_class=mock_asset_merger_class,
            r1_merger_class=mock_r1_merger_class,
        )

        with patch("builtins.print"):
            processor.process()

        # Get the data passed to R1 merger
        r1_merger_call_args = mock_r1_merger_class.call_args[0]
        processed_data = r1_merger_call_args[1]

        # Should have filtered out the row with NaN property_sector
        assert len(processed_data) == 2
        assert all(processed_data["property_sector"].notna())

    @patch(
        "app_real_estate.transformation.metric_calculation.common_operations.CommonOperations.add_covered_floor_area_m2"
    )
    @patch(
        "app_real_estate.transformation.metric_calculation.common_operations.CommonOperations.rename_covered_floor_area_column"
    )
    def test_process_filters_nan_covered_floor_area(
        self,
        mock_rename_covered_floor_area,
        mock_add_covered_floor_area_m2,
        mock_energy_ratings_data,
        mock_asset_level_data,
        mock_r1_table,
    ):
        """Test that process filters out rows with NaN covered_floor_area_m2."""
        # Create data that will result in NaN covered_floor_area_m2
        asset_merged_data = pd.DataFrame(
            {
                "portfolio_asset_id": [1, 2, 3],
                "asset_size_m2": [
                    1000.0,
                    None,
                    1500.0,
                ],  # NaN will cause NaN in calculation
                "asset_ownership": [100.0, 80.0, 90.0],
                "property_sector": ["Office", "Retail", "Industrial"],
                "property_type_code": ["OFF", "RET", "IND"],
                "country": ["US", "UK", "DE"],
            }
        )

        mock_asset_merger = Mock()
        mock_asset_merger.merge.return_value = asset_merged_data
        mock_asset_merger_class = Mock(return_value=mock_asset_merger)

        # Mock the processing methods - second row gets NaN
        mock_rename_covered_floor_area.side_effect = lambda x: x
        mock_add_covered_floor_area_m2.side_effect = lambda x, y: x.assign(
            covered_floor_area_m2=[1000.0, None, 1350.0]  # Middle value is NaN
        )

        mock_r1_merger = Mock()
        mock_r1_merger.merge.return_value = pd.DataFrame({"result": ["filtered"]})
        mock_r1_merger_class = Mock(return_value=mock_r1_merger)

        processor = LenderEnergyRatingsPreProcessor(
            energy_ratings_data=mock_energy_ratings_data,
            asset_level_data=mock_asset_level_data,
            r1_table=mock_r1_table,
            survey_year=2025,
            asset_merger_class=mock_asset_merger_class,
            r1_merger_class=mock_r1_merger_class,
        )

        with patch("builtins.print"):
            processor.process()

        # Get the data passed to R1 merger
        r1_merger_call_args = mock_r1_merger_class.call_args[0]
        processed_data = r1_merger_call_args[1]

        # Should have filtered out the row with NaN covered_floor_area_m2
        assert len(processed_data) == 2
        assert all(processed_data["covered_floor_area_m2"].notna())
