import pytest
import pandas as pd
import pandas.testing as pdt
import app_real_estate.constants.column_names.asset_characteristics_columns as asc
import app_real_estate.constants.column_names.score_columns as sc
from app_real_estate.models.input_models.r1_table_model import R1TableModel
from app_real_estate.transformation.aggregation.indicator.gh1.gh1_score_aggregator_property_subtype_response_id import (
    GH1ScoreAggregator_PropertySubtype_ResponseId,
)
from app_real_estate.transformation.aggregation.indicator.gh1.gh1_score_aggregator_response_id import (
    GH1ScoreAggregator_ResponseId,
)
from app_real_estate.transformation.aggregation.indicator.gh1.gh1_score_aggregator_property_subtype_country_response_id import (
    GH1ScoreAggregator_PropertySubtype_Country_ResponseId,
)
from app_real_estate.models.score_aggregation_util_models.gh1_scored_data import (
    GH1ScoredData,
)
from tests.aggregation.agg_test_utils import AggregationTestUtils


@pytest.fixture
def mock_r1_table():
    return pd.DataFrame(
        {
            R1TableModel.RESPONSE_ID: [1, 1, 1],
            R1TableModel.COUNTRY: ["JP", "NL", "NL"],
            R1TableModel.PRT_TYPE: ["OCHI", "OCHI", "TSDC"],
            R1TableModel.R_1_TBL_PGAV: [45.8027, 41.17, 132.74],
        }
    )


@pytest.fixture
def mock_gh1_scored_data():
    return pd.DataFrame(
        {
            "response_id": [1, 1, 1, 1, 1],
            asc.data_year: [2023] * 5,
            "property_type_code": [
                "OCHI",
                "OCHI",
                "OCHI",
                "TSDC",
                "TSDC",
            ],
            "country": ["JP", "JP", "NL", "NL", "NL"],
            asc.asset_ownership: [10, 80, 30, 100, 50],
            asc.asset_size_m2: [200, 250, 300, 540, 1000],
            sc.score_gh1_ghg_area_time_cov_p_s12: [0.1, 0.2, 0.5, 0.4, 0.5],
            sc.score_gh1_ghg_area_time_cov_p_s3: [0.1, 0.2, 0.5, 0.7, 0.5],
            sc.score_gh1_ghg_area_time_cov_p: [0.1, 0.2, 0.5, 0.7, 0.5],
            sc.score_gh1_ghg_lfl_percent_change_s12: [0.1, 0.2, 0.5, 0.7, 0.5],
            sc.score_gh1_ghg_lfl_percent_change_s3: [0.1, 0.2, 0.5, 0.7, 0.5],
            sc.score_gh1_ghg_lfl_percent_change: [0.1, 0.2, 0.5, 0.7, 0.5],
        }
    )


def test_gh1_parse_dataclass(mock_gh1_scored_data):
    gh1_scored_data = GH1ScoredData.parse_df(
        dataframe=mock_gh1_scored_data, verbose=True
    )
    pdt.assert_frame_equal(gh1_scored_data, mock_gh1_scored_data)


def test_gh1_score_aggregation_on_property_subtype_and_response_id(
    mock_gh1_scored_data, mock_r1_table
):
    aggregator = GH1ScoreAggregator_PropertySubtype_ResponseId(
        mock_gh1_scored_data, mock_r1_table
    )
    aggregated_data = aggregator.process()

    data_dict = {
        ("TSDC", 1, 2023): {
            sc.score_gh1_ghg_area_time_cov_p_s12: 0.448077,
            sc.score_gh1_ghg_area_time_cov_p_s3: 0.603846,
            sc.score_gh1_ghg_area_time_cov_p: 0.603846,
            sc.score_gh1_ghg_lfl_percent_change_s12: 0.603846,
            sc.score_gh1_ghg_lfl_percent_change_s3: 0.603846,
            sc.score_gh1_ghg_lfl_percent_change: 0.603846,
            sc.score_gh1: 4.226923,
            sc.score_gh1_fraction: 0.603846,
            sc.score_gh1_percent: 60.3846,
            sc.score_gh1_max: 7.0,
            sc.pgav: 132.74,
        },
        ("OCHI", 1, 2023): {
            sc.score_gh1_ghg_area_time_cov_p_s12: 0.337222,
            sc.score_gh1_ghg_area_time_cov_p_s3: 0.337222,
            sc.score_gh1_ghg_area_time_cov_p: 0.337222,
            sc.score_gh1_ghg_lfl_percent_change_s12: 0.337222,
            sc.score_gh1_ghg_lfl_percent_change_s3: 0.337222,
            sc.score_gh1_ghg_lfl_percent_change: 0.337222,
            sc.score_gh1: 2.3605577,
            sc.score_gh1_fraction: 0.337222,
            sc.score_gh1_percent: 33.7222,
            sc.score_gh1_max: 7.0,
            sc.pgav: 86.9727,
        },
    }
    expected_output = AggregationTestUtils.get_multiindex_df(
        data_dict, names=[asc.property_type_code, asc.response_id, asc.data_year]
    )

    # TODO: remove following once the test is updated with all columns
    common_cols = list(
        set(aggregated_data.columns).intersection(expected_output.columns)
    )
    aggregated_data = aggregated_data[common_cols]
    pdt.assert_frame_equal(aggregated_data, expected_output, check_like=True)


def test_gh1_score_aggregation_on_property_subtype_and_country_and_response_id(
    mock_gh1_scored_data, mock_r1_table
):
    aggregator = GH1ScoreAggregator_PropertySubtype_Country_ResponseId(
        mock_gh1_scored_data, mock_r1_table
    )
    aggregated_data = aggregator.process()

    data_dict = {
        ("TSDC", "NL", 1, 2023): {
            sc.score_gh1_ghg_area_time_cov_p_s12: 0.448077,
            sc.score_gh1_ghg_area_time_cov_p_s3: 0.603846,
            sc.score_gh1_ghg_area_time_cov_p: 0.603846,
            sc.score_gh1_ghg_lfl_percent_change_s12: 0.603846,
            sc.score_gh1_ghg_lfl_percent_change_s3: 0.603846,
            sc.score_gh1_ghg_lfl_percent_change: 0.603846,
            sc.pgav: 132.74,
            sc.asset_size_owned_m2: 1040.0,
            sc.score_gh1: 4.226923,
            sc.score_gh1_fraction: 0.603846,
            sc.score_gh1_percent: 60.3846,
            sc.score_gh1_max: 7.0,
        },
        ("OCHI", "JP", 1, 2023): {
            sc.score_gh1_ghg_area_time_cov_p_s12: 0.190909,
            sc.score_gh1_ghg_area_time_cov_p_s3: 0.190909,
            sc.score_gh1_ghg_area_time_cov_p: 0.190909,
            sc.score_gh1_ghg_lfl_percent_change_s12: 0.190909,
            sc.score_gh1_ghg_lfl_percent_change_s3: 0.190909,
            sc.score_gh1_ghg_lfl_percent_change: 0.190909,
            sc.pgav: 45.8027,
            sc.asset_size_owned_m2: 220.0,
            sc.score_gh1: 1.336364,
            sc.score_gh1_fraction: 0.190909,
            sc.score_gh1_percent: 19.0909,
            sc.score_gh1_max: 7.0,
        },
        ("OCHI", "NL", 1, 2023): {
            sc.score_gh1_ghg_area_time_cov_p_s12: 0.5,
            sc.score_gh1_ghg_area_time_cov_p_s3: 0.5,
            sc.score_gh1_ghg_area_time_cov_p: 0.5,
            sc.score_gh1_ghg_lfl_percent_change_s12: 0.5,
            sc.score_gh1_ghg_lfl_percent_change_s3: 0.5,
            sc.score_gh1_ghg_lfl_percent_change: 0.5,
            sc.pgav: 41.17,
            sc.asset_size_owned_m2: 90.0,
            sc.score_gh1: 3.5,
            sc.score_gh1_fraction: 0.5,
            sc.score_gh1_percent: 50.0,
            sc.score_gh1_max: 7.0,
        },
    }
    expected_output = AggregationTestUtils.get_multiindex_df(
        data_dict,
        names=[
            asc.property_type_code,
            asc.country,
            asc.response_id,
            asc.data_year,
        ],
    )
    # TODO: remove following once the test is updated with all columns
    common_cols = list(
        set(aggregated_data.columns).intersection(expected_output.columns)
    )
    aggregated_data = aggregated_data[common_cols]
    pdt.assert_frame_equal(aggregated_data, expected_output, check_like=True)


def test_gh1_score_aggregation_on_response_id(mock_gh1_scored_data, mock_r1_table):
    aggregator = GH1ScoreAggregator_ResponseId(mock_gh1_scored_data, mock_r1_table)
    aggregated_data = aggregator.process()
    expected_output = pd.DataFrame(
        {
            sc.score_gh1: [3.488127],
            sc.pgav: [1.0],
            sc.score_gh1_ghg_area_time_cov_p_s12: [0.404195],
            sc.score_gh1_ghg_area_time_cov_p_s3: [0.498304],
            sc.score_gh1_ghg_area_time_cov_p: [0.498304],
            sc.score_gh1_ghg_lfl_percent_change_s12: [0.498304],
            sc.score_gh1_ghg_lfl_percent_change_s3: [0.498304],
            sc.score_gh1_ghg_lfl_percent_change: [0.498304],
            sc.score_gh1_fraction: [0.498304],
            sc.score_gh1_percent: [49.8304],
            sc.score_gh1_max: [7.0],
        },
        index=[[1], [2023]],
    )
    expected_output.index.names = ["response_id", asc.data_year]
    # TODO: remove following once the test is updated with all columns
    common_cols = list(
        set(aggregated_data.columns).intersection(expected_output.columns)
    )
    aggregated_data = aggregated_data[common_cols]
    pdt.assert_frame_equal(aggregated_data, expected_output, check_like=True)
