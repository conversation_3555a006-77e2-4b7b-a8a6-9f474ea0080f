import pytest
import pandas as pd
from app_real_estate.transformation.aggregation.validator.validator import (
    Validator,
)


# Test cases for validate_missing_column
@pytest.mark.parametrize(
    "data, required_columns, expected_error",
    [
        (
            pd.DataFrame(columns=["A", "B", "C"]),
            ["A", "B", "C"],
            None,
        ),  # All required columns present
        (
            pd.DataFrame(columns=["A", "B", "C", "D", "E"]),
            ["A", "B", "C"],
            None,
        ),  # All required columns plus additional columns
        (
            pd.DataFrame(columns=["A", "B"]),
            ["A", "B", "C"],
            "Missing required columns: C",
        ),  # Missing one required column
        (
            pd.DataFrame(columns=["A"]),
            ["A", "B", "C"],
            "Missing required columns: B, C",
        ),  # Missing multiple required columns
        (
            pd.DataFrame(columns=["D", "E", "F"]),
            ["A", "B", "C"],
            "Missing required columns: A, B, C",
        ),  # No required columns present
    ],
)
def test_validate_missing_column(data, required_columns, expected_error):
    if expected_error is None:
        # Should not raise an error if all required columns are present
        Validator.validate_missing_column(data, required_columns)
    else:
        # Should raise an error if any required columns are missing
        with pytest.raises(ValueError, match=expected_error):
            Validator.validate_missing_column(data, required_columns)


# Test cases for validate_exception_column
@pytest.mark.parametrize(
    "data, exclude_columns, expected_error",
    [
        (
            pd.DataFrame(columns=["A", "B", "C"]),
            ["X", "Y", "Z"],
            None,
        ),  # No exception columns present
        (
            pd.DataFrame(columns=["A", "B", "C", "D", "E"]),
            ["X", "Y", "Z"],
            None,
        ),  # No exception columns present with additional columns
        (
            pd.DataFrame(columns=["A", "B", "X"]),
            ["X", "Y", "Z"],
            "these columns should not be in data: X",
        ),  # One exception column present
        (
            pd.DataFrame(columns=["A", "X", "Y"]),
            ["X", "Y", "Z"],
            "these columns should not be in data: X, Y",
        ),  # Multiple exception columns present
        (
            pd.DataFrame(columns=["X", "Y", "Z"]),
            ["X", "Y", "Z"],
            "these columns should not be in data: X, Y, Z",
        ),  # All exception columns present
    ],
)
def test_validate_exception_column(data, exclude_columns, expected_error):
    if expected_error is None:
        # Should not raise an error if no exception columns are present
        Validator.validate_exception_column(data, exclude_columns)
    else:
        # Should raise an error if any exception columns are present
        with pytest.raises(ValueError, match=expected_error):
            Validator.validate_exception_column(data, exclude_columns)
