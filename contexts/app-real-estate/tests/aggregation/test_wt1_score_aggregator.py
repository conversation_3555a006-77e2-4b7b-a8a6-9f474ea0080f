import pytest
import pandas as pd
import pandas.testing as pdt
import app_real_estate.constants.column_names.asset_characteristics_columns as asc
import app_real_estate.constants.column_names.score_columns as sc
from app_real_estate.models.input_models.r1_table_model import R1TableModel
from app_real_estate.transformation.aggregation.indicator.wt1.wt1_score_aggregator_response_id import (
    WT1ScoreAggregator_ResponseId,
)
from app_real_estate.transformation.aggregation.indicator.wt1.wt1_score_aggregator_property_subtype_response_id import (
    WT1ScoreAggregator_PropertySubtype_ResponseId,
)
from app_real_estate.transformation.aggregation.indicator.wt1.wt1_score_aggregator_property_subtype_country_response_id import (
    WT1ScoreAggregator_PropertySubtype_Country_ResponseId,
)
from app_real_estate.models.score_aggregation_util_models.wt1_scored_data import (
    WT1ScoredData,
)
from tests.aggregation.agg_test_utils import AggregationTestUtils


@pytest.fixture
def mock_r1_table():
    return pd.DataFrame(
        {
            R1TableModel.RESPONSE_ID: [1, 1, 1],
            R1TableModel.COUNTRY: ["JP", "NL", "JP"],
            R1TableModel.PRT_TYPE: ["TSLS", "TSLS", "OCHI"],
            R1TableModel.R_1_TBL_PGAV: [7.5003, 41.17, 38.3024],
        }
    )


@pytest.fixture
def mock_wt1_scored_data():
    return pd.DataFrame(
        {
            "response_id": [1, 1, 1],
            asc.data_year: [2023] * 3,
            asc.property_type_code: ["TSLS", "OCHI", "TSLS"],
            "country": ["JP", "JP", "NL"],
            asc.asset_ownership: [10, 80, 30],
            asc.asset_size_m2: [200, 250, 300],
            sc.score_wt1_wat_area_time_cov_p_lc: [0.1, 0.2, 0.3],
            sc.score_wt1_wat_area_time_cov_p_tc: [0.4, 0.5, 0.6],
            sc.score_wt1_wat_area_time_cov_p: [0.7, 0.8, 0.9],
            sc.score_wt1_wat_lfl_percent_change_lc: [0.5, 0.6, 0.7],
            sc.score_wt1_wat_lfl_percent_change_tc: [0.8, 0.9, 1.0],
            sc.score_wt1_wat_lfl_percent_change: [0.9, 0.5, 0.5],
            sc.score_wt1_wat_rec_ons: [1, 0, 1],
            sc.score_wt1_wat_rec_percent_change: [0.5, 0.6, 0.7],
            sc.score_wt1_wat_rec_performance: [0.8, 0.9, 0.3],
        }
    )


def test_wt1_parse_dataclass(mock_wt1_scored_data):
    mock_wt1_scored_data = WT1ScoredData.parse_df(
        dataframe=mock_wt1_scored_data, verbose=True
    )
    pdt.assert_frame_equal(mock_wt1_scored_data, mock_wt1_scored_data)


def test_wt1_score_aggregation_on_property_subtype_and_response_id(
    mock_wt1_scored_data, mock_r1_table
):
    aggregator = WT1ScoreAggregator_PropertySubtype_ResponseId(
        mock_wt1_scored_data, r1_table=mock_r1_table
    )
    result = aggregator.process()

    data_dict = {
        ("OCHI", 1, 2023): {
            sc.score_wt1_wat_area_time_cov_p_lc: 0.2,
            sc.score_wt1_wat_area_time_cov_p_tc: 0.5,
            sc.score_wt1_wat_area_time_cov_p: 0.8,
            sc.score_wt1_wat_lfl_percent_change_lc: 0.6,
            sc.score_wt1_wat_lfl_percent_change_tc: 0.9,
            sc.score_wt1_wat_lfl_percent_change: 0.5,
            sc.score_wt1_wat_rec_ons: 0.0,
            sc.score_wt1_wat_rec_percent_change: 0.6,
            sc.score_wt1_wat_rec_performance: 0.9,
            sc.score_wt1: 4.875,
            sc.score_wt1_fraction: 0.696428,
            sc.score_wt1_max: 7.0,
            sc.score_wt1_percent: 69.6428,
            sc.pgav: 38.3024,
        },
        ("TSLS", 1, 2023): {
            sc.score_wt1_wat_area_time_cov_p_lc: 0.269179,
            sc.score_wt1_wat_area_time_cov_p_tc: 0.569179,
            sc.score_wt1_wat_area_time_cov_p: 0.869179,
            sc.score_wt1_wat_lfl_percent_change_lc: 0.669179,
            sc.score_wt1_wat_lfl_percent_change_tc: 0.969179,
            sc.score_wt1_wat_lfl_percent_change: 0.561641,
            sc.score_wt1_wat_rec_ons: 1.0,
            sc.score_wt1_wat_rec_percent_change: 0.6691791,
            sc.score_wt1_wat_rec_performance: 0.3770521,
            sc.score_wt1: 5.1327790,
            sc.score_wt1_fraction: 0.733255,
            sc.score_wt1_percent: 73.3255,
            sc.score_wt1_max: 7.0,
            sc.pgav: 48.6703,
        },
    }

    expected_output = AggregationTestUtils.get_multiindex_df(
        data_dict,
        names=[asc.property_type_code, "response_id", asc.data_year],
    )

    # TODO: remove following once the test is updated with all columns
    common_cols = list(set(result.columns).intersection(expected_output.columns))
    result = result[common_cols]
    pdt.assert_frame_equal(result, expected_output, check_like=True)


def test_wt1_score_aggregation_on_property_subtype_and_country_and_response_id(
    mock_wt1_scored_data, mock_r1_table
):
    aggregator = WT1ScoreAggregator_PropertySubtype_Country_ResponseId(
        mock_wt1_scored_data, mock_r1_table
    )
    result = aggregator.process()

    data_dict = {
        ("OCHI", "JP", 1, 2023): {
            sc.pgav: 38.3024,
            sc.score_wt1_wat_area_time_cov_p_lc: 0.2,
            sc.score_wt1_wat_area_time_cov_p_tc: 0.5,
            sc.score_wt1_wat_area_time_cov_p: 0.8,
            sc.score_wt1_wat_lfl_percent_change_lc: 0.6,
            sc.score_wt1_wat_lfl_percent_change_tc: 0.9,
            sc.score_wt1_wat_lfl_percent_change: 0.5,
            sc.score_wt1_wat_rec_ons: 0.0,
            sc.score_wt1_wat_rec_percent_change: 0.6,
            sc.score_wt1_wat_rec_performance: 0.9,
            sc.score_wt1: 4.875,
            sc.asset_size_owned_m2: 200.0,
            sc.score_wt1_fraction: 0.6964285,
            sc.score_wt1_percent: 69.64285,
            sc.score_wt1_max: 7.0,
        },
        ("TSLS", "JP", 1, 2023): {
            sc.pgav: 7.5003,
            sc.score_wt1_wat_area_time_cov_p_lc: 0.1,
            sc.score_wt1_wat_area_time_cov_p_tc: 0.4,
            sc.score_wt1_wat_area_time_cov_p: 0.7,
            sc.score_wt1_wat_lfl_percent_change_lc: 0.5,
            sc.score_wt1_wat_lfl_percent_change_tc: 0.8,
            sc.score_wt1_wat_lfl_percent_change: 0.9,
            sc.score_wt1_wat_rec_ons: 1.0,
            sc.score_wt1_wat_rec_percent_change: 0.5,
            sc.score_wt1_wat_rec_performance: 0.8,
            sc.score_wt1: 5.44999,
            sc.asset_size_owned_m2: 20.0,
            sc.score_wt1_fraction: 0.778571,
            sc.score_wt1_percent: 77.8571,
            sc.score_wt1_max: 7.0,
        },
        ("TSLS", "NL", 1, 2023): {
            sc.pgav: 41.17,
            sc.score_wt1_wat_area_time_cov_p_lc: 0.3,
            sc.score_wt1_wat_area_time_cov_p_tc: 0.6,
            sc.score_wt1_wat_area_time_cov_p: 0.9,
            sc.score_wt1_wat_lfl_percent_change_lc: 0.7,
            sc.score_wt1_wat_lfl_percent_change_tc: 1.0,
            sc.score_wt1_wat_lfl_percent_change: 0.5,
            sc.score_wt1_wat_rec_ons: 1.0,
            sc.score_wt1_wat_rec_percent_change: 0.7,
            sc.score_wt1_wat_rec_performance: 0.3,
            sc.score_wt1: 5.07499,
            sc.asset_size_owned_m2: 90.0,
            sc.score_wt1_fraction: 0.7249999,
            sc.score_wt1_percent: 72.4999,
            sc.score_wt1_max: 7.0,
        },
    }

    expected_output = AggregationTestUtils.get_multiindex_df(
        data_dict,
        names=[asc.property_type_code, "country", "response_id", asc.data_year],
    )

    # TODO: remove following once the test is updated with all columns
    common_cols = list(set(result.columns).intersection(expected_output.columns))
    result = result[common_cols]
    pdt.assert_frame_equal(result, expected_output, check_like=True)


def test_wt1_score_aggregation_to_portfolio_level(mock_wt1_scored_data, mock_r1_table):
    aggregator = WT1ScoreAggregator_ResponseId(mock_wt1_scored_data, mock_r1_table)
    result = aggregator.process()

    expected_output = pd.DataFrame(
        {
            sc.score_wt1: 5.01926,
            sc.score_wt1_wat_area_time_cov_p_lc: 0.238713,
            sc.score_wt1_wat_area_time_cov_p_tc: 0.538713,
            sc.score_wt1_wat_area_time_cov_p: 0.838713,
            sc.score_wt1_wat_lfl_percent_change_lc: 0.638713,
            sc.score_wt1_wat_lfl_percent_change_tc: 0.938713,
            sc.score_wt1_wat_lfl_percent_change: 0.534495,
            sc.score_wt1_wat_rec_ons: 0.559604,
            sc.score_wt1_wat_rec_percent_change: 0.638713,
            sc.score_wt1_wat_rec_performance: 0.607356,
            sc.pgav: 1.0,
            sc.score_wt1_fraction: 0.717037,
            sc.score_wt1_percent: 71.7037,
            sc.score_wt1_max: 7.0,
        },
        index=[[1], [2023]],
    )
    expected_output.index.names = ["response_id", asc.data_year]

    # TODO: remove following once the test is updated with all columns
    common_cols = list(set(result.columns).intersection(expected_output.columns))
    result = result[common_cols]
    pdt.assert_frame_equal(result, expected_output, check_like=True)
