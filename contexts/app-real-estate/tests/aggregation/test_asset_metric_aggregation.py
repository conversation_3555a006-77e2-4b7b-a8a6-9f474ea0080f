import pytest
import pandas as pd
import numpy as np
import pandas.testing as pdt
import app_real_estate.constants.column_names.asset_characteristics_columns as asc
import app_real_estate.constants.column_names.score_columns as sc
import app_real_estate.constants.column_names.energy_columns as ec
import app_real_estate.constants.column_names.water_columns as wc
import app_real_estate.constants.column_names.waste_columns as wsc
import app_real_estate.constants.column_names.ghg_columns as gc
from app_real_estate.constants.aggregation_recipes.asset_metric_aggregation_recipe import (
    ASSET_METRIC_AGGREGATION_RECIPE,
    COLUMNS_SCALED_PER_AREA_OWNERSHIP,
    COLUMNS_TO_WEIGHT_SUM_PER_OWNERSHIP,
    COLUMNS_NORMALISED_DIFFERENT_WEIGHTS,
    BENCHMARK_COLUMNS_MEAN,
)
from app_real_estate.models.input_models.r1_table_model import R1TableModel
from app_real_estate.transformation.aggregation.asset_metric_aggregator import (
    AssetMetricAggregator,
)
from app_real_estate.transformation.metric_calculation.coverage_calculations import (
    CoverageCalculations,
)


@pytest.fixture
def mock_r1_table():
    return pd.DataFrame(
        {
            R1TableModel.RESPONSE_ID: [1, 1, 1],
            R1TableModel.COUNTRY: ["USA", "UK", "Canada"],
            R1TableModel.PRT_TYPE: ["OCHI", "OCHI", "TSOT"],
            R1TableModel.R_1_TBL_PGAV: [100012004.0, 981248.0, 30600504.0],
        }
    )


def get_multiindex_df(data_dict: dict, names: list[str]):
    df = pd.DataFrame.from_dict(data_dict, orient="index")
    df.index = pd.MultiIndex.from_tuples(df.index, names=names)
    return df


@pytest.fixture
def mock_data_for_aggregation():
    return pd.DataFrame(
        {
            asc.response_id: [1, 1, 1, 1],
            asc.data_year: [2023] * 4,
            asc.portfolio_asset_id: [1, 2, 3, 4],
            asc.country: ["USA", "UK", "Canada", "USA"],
            asc.property_sector: [
                "OCHI",
                "OCHI",
                "TSOT",
                "OCHI",
            ],
            asc.asset_size_m2: [100, 200, 300, 100],
            asc.asset_size_owned_m2: [10, 160, 90, 75],
            asc.asset_ownership: [10, 80, 30, 75],
            asc.asset_ownership_fraction: [0.1, 0.8, 0.3, 0.75],
            ec.asset_vacancy_energy_intensity: [10, 18, np.nan, np.nan],
            wc.asset_vacancy_water_intensity: [10, 18, np.nan, np.nan],
            gc.asset_vacancy_ghg_intensity: [10, 18, np.nan, np.nan],
            ec.asset_size_energy_intensity_m2: [10, 160, np.nan, np.nan],
            wc.asset_size_water_intensity_m2: [10, 160, np.nan, np.nan],
            gc.asset_size_ghg_intensity_m2: [10, 160, np.nan, np.nan],
            ec.asset_size_energy_intensity_sqft: [10, 160, np.nan, np.nan],
            wc.asset_size_water_intensity_sqft: [10, 160, np.nan, np.nan],
            gc.asset_size_ghg_intensity_sqft: [10, 160, np.nan, np.nan],
            ec.en_lfl_outlier_status: ["none", "none", "none", "accepted"],
            ec.en_int_outlier_status: ["none", "none", "none", "accepted"],
            wc.wat_lfl_outlier_status: ["none", "none", "none", "accepted"],
            wc.wat_int_outlier_status: ["none", "none", "none", "accepted"],
            gc.ghg_lfl_outlier_status: ["none", "none", "none", "accepted"],
            gc.ghg_int_outlier_status: ["none", "none", "none", "accepted"],
            wsc.was_int_outlier_status: ["none", "none", "none", "accepted"],
            ec.en_area_time_cov_p_lc: [100, 90, np.nan, 80],
            ec.en_area_time_cov_p_tc: [100, 90, np.nan, 80],
            gc.ghg_area_time_cov_p_s12: [100, 90, np.nan, 80],
            gc.ghg_area_time_cov_p_s3: [100, 90, np.nan, 80],
            wc.wat_area_time_cov_p_lc: [100, 90, np.nan, 80],
            wc.wat_area_time_cov_p_tc: [100, 90, np.nan, 80],
            wsc.was_area_cov_p_lc: [100, 90, np.nan, 80],
            wsc.was_area_cov_p_tc: [100, 90, np.nan, 80],
            sc.en_area_time_weight_lc_owned: [365 * x for x in [100, 200, 300, 100]],
            sc.en_area_time_weight_tc_owned: [365 * x for x in [100, 200, 300, 100]],
            sc.ghg_area_time_weight_s12_owned: [365 * x for x in [100, 200, 300, 100]],
            sc.ghg_area_time_weight_s3_owned: [365 * x for x in [100, 200, 300, 100]],
            sc.wat_area_time_weight_lc_owned: [365 * x for x in [100, 200, 300, 100]],
            sc.wat_area_time_weight_tc_owned: [365 * x for x in [100, 200, 300, 100]],
            sc.was_area_weight_lc_owned: [365 * x for x in [100, 200, 300, 100]],
            sc.was_area_weight_tc_owned: [365 * x for x in [100, 200, 300, 100]],
            ec.en_lfl_abs_change_lc_agg: [-10, -20, -10, np.nan],
            ec.en_lfl_abs_change_tc_agg: [-10, -20, -10, np.nan],
            wc.wat_lfl_abs_change_lc_agg: [-10, -20, -10, np.nan],
            wc.wat_lfl_abs_change_tc_agg: [-10, -20, -10, np.nan],
            gc.ghg_lfl_abs_change_s12_agg: [-10, -20, -10, np.nan],
            gc.ghg_lfl_abs_change_s3_agg: [-10, -20, -10, np.nan],
            ec.en_lfl_abs_change_lc_accepted: [-10, -20, -10, np.nan],
            ec.en_lfl_abs_change_tc_accepted: [-10, -20, -10, np.nan],
            wc.wat_lfl_abs_change_lc_accepted: [-10, -20, -10, np.nan],
            wc.wat_lfl_abs_change_tc_accepted: [-10, -20, -10, np.nan],
            gc.ghg_lfl_abs_change_s12_accepted: [-10, -20, -10, np.nan],
            gc.ghg_lfl_abs_change_s3_accepted: [-10, -20, -10, np.nan],
            ec.en_efficiency_int_kwh_m2_accepted: [10, 20, 10, np.nan],
            wc.wat_scored_int_m3_m2_accepted: [10, 20, 10, np.nan],
            gc.ghg_scored_int_ton_m2_accepted: [10, 20, 10, np.nan],
            ec.energy_efficiency_score_eligible: [True, True, False, False],
            ec.en_int_eligible: [True, True, False, False],
            wc.wat_int_eligible: [True, True, False, False],
            gc.ghg_int_eligible: [True, True, False, False],
            ec.en_lfl_abs_change_agg: [-10, -20, -10, np.nan],
            wc.wat_lfl_abs_change_agg: [-10, -20, -10, np.nan],
            gc.ghg_lfl_abs_change_agg: [-10, -20, -10, np.nan],
            ec.en_lfl_abs_change_accepted: [-10, -20, -10, np.nan],
            ec.en_lfl_abs_change_accepted_mwh: [-10, -20, -10, np.nan],
            wc.wat_lfl_abs_change_accepted: [-10, -20, -10, np.nan],
            gc.ghg_lfl_abs_change_accepted: [-10, -20, -10, np.nan],
            ec.en_ren_ons_con: [10, 20, 10, np.nan],
            ec.en_ren_ons_exp: [10, 20, 10, np.nan],
            ec.en_ren_ons_tpt: [10, 20, 10, np.nan],
            ec.en_ren_ofs_pbl: [10, 20, 10, np.nan],
            ec.en_ren_ofs_pbt: [10, 20, 10, np.nan],
            wc.wat_rec_ons_reu: [10, 20, 10, np.nan],
            wc.wat_rec_ons_cap: [10, 20, 10, np.nan],
            wc.wat_rec_ons_ext: [10, 20, 10, np.nan],
            wc.wat_rec_ofs_pur: [10, 20, 10, np.nan],
            wsc.was_abs_lf: [10, 20, 10, np.nan],
            wsc.was_abs_in: [10, 20, 10, np.nan],
            wsc.was_abs_ru: [10, 20, 10, np.nan],
            wsc.was_abs_wte: [10, 20, 10, np.nan],
            wsc.was_abs_rec: [10, 20, 10, np.nan],
            wsc.was_abs_oth: [10, 20, 10, np.nan],
            ec.en_abs_mwh: [10, 20, 10, np.nan],
            ec.en_abs: [10, 20, 10, np.nan],
            ec.en_abs_ly: [10, 20, 10, np.nan],
            ec.en_abs_f_kwh: [10, 20, 10, np.nan],
            ec.en_abs_e_kwh: [10, 20, 10, np.nan],
            ec.en_abs_d_kwh: [10, 20, 10, np.nan],
            ec.en_abs_f_mwh: [10, 20, 10, np.nan],
            ec.en_abs_e_mwh: [10, 20, 10, np.nan],
            ec.en_abs_d_mwh: [10, 20, 10, np.nan],
            gc.ghg_abs: [10, 20, 10, np.nan],
            gc.ghg_abs_net: [10, 20, 10, np.nan],
            wc.wat_abs: [10, 20, 10, np.nan],
            wc.wat_abs_ly: [10, 20, 10, np.nan],
            wsc.was_abs: [10, 20, 10, np.nan],
            wsc.was_abs_ly: [10, 20, 10, np.nan],
            wsc.was_abs_haz: [10, 20, 10, np.nan],
            wsc.was_abs_nhaz: [10, 20, 10, np.nan],
            ec.en_ren_abs_mwh: [10, 20, 10, np.nan],
            ec.en_ren_abs: [10, 20, 10, np.nan],
            ec.en_ren_abs_ly: [10, 20, 10, np.nan],
            ec.en_ren_abs_consumed_mwh: [10, 20, 10, np.nan],
            gc.ghg_abs_offset: [10, 20, 10, np.nan],
            wc.wat_rec_abs: [10, 20, 10, np.nan],
            wc.wat_rec_abs_ly: [10, 20, 10, np.nan],
            wsc.was_abs_div: [10, 20, 10, np.nan],
            wsc.was_abs_div_ly: [10, 20, 10, np.nan],
            ec.en_abs_nopr_ev: [10, 20, 10, np.nan],
            gc.ghg_abs_s1: [10, 20, 10, np.nan],
            gc.ghg_abs_s2_lb: [10, 20, 10, np.nan],
            gc.ghg_abs_s2_mb: [10, 20, 10, np.nan],
            gc.ghg_abs_s3: [10, 20, 10, np.nan],
            ec.en_lfl_abs_lc_ly_accepted: [2, 2, 2, np.nan],
            ec.en_lfl_abs_tc_ly_accepted: [2, 2, 2, np.nan],
            wc.wat_lfl_abs_lc_ly_accepted: [2, 2, 2, np.nan],
            wc.wat_lfl_abs_tc_ly_accepted: [2, 2, 2, np.nan],
            gc.ghg_lfl_abs_s12_ly_accepted: [2, 2, 2, np.nan],
            gc.ghg_lfl_abs_s3_ly_accepted: [2, 2, 2, np.nan],
            ec.en_lfl_abs_lc_ly_bench: [2, 2, 2, np.nan],
            ec.en_lfl_abs_tc_ly_bench: [2, 2, 2, np.nan],
            wc.wat_lfl_abs_lc_ly_bench: [2, 2, 2, np.nan],
            wc.wat_lfl_abs_tc_ly_bench: [2, 2, 2, np.nan],
            gc.ghg_lfl_abs_s12_ly_bench: [2, 2, 2, np.nan],
            gc.ghg_lfl_abs_s3_ly_bench: [2, 2, 2, np.nan],
            # ec.en_lfl_abs_ly_accepted: [2, 2, 2, np.nan],
            # wc.wat_lfl_abs_ly_accepted: [2, 2, 2, np.nan],
            # gc.ghg_lfl_abs_ly_accepted: [2, 2, 2, np.nan],
            sc.en_area_time_weight_owned: [365 * x for x in [100, 200, 300, 100]],
            sc.ghg_area_time_weight_owned: [365 * x for x in [100, 200, 300, 100]],
            sc.wat_area_time_weight_owned: [365 * x for x in [100, 200, 300, 100]],
            ec.en_area_time_cov_p: [100, 90, np.nan, 80],
            gc.ghg_area_time_cov_p: [100, 90, np.nan, 80],
            wc.wat_area_time_cov_p: [100, 90, np.nan, 80],
            wsc.was_area_cov_p: [100, 90, np.nan, 80],
            # sc.en_area_weight_lc_owned: [10, 160, 90, 75],
            # sc.en_area_weight_tc_owned: [10, 160, 90, 75],
            # sc.ghg_area_weight_s12_owned: [10, 160, 90, 75],
            # sc.ghg_area_weight_s3_owned: [10, 160, 90, 75],
            # sc.wat_area_weight_lc_owned: [10, 160, 90, 75],
            # sc.wat_area_weight_tc_owned: [10, 160, 90, 75],
            # wc.wat_scored_int_dm3_m2: [],
            # ec.en_efficiency_int_kwh_sqft: [],
            # wc.wat_scored_int_m3_sqft: [],
            # wc.wat_scored_int_dm3_sqft: [],
            # gc.ghg_scored_int_ton_sqft: [],
            # ec.en_lfl_eligible_for_aggregation: [],
            # gc.ghg_lfl_eligible_for_aggregation: [],
            # wc.wat_lfl_eligible_for_aggregation: [],
            # ec.en_lfl_area_m2: [],
            # ec.en_lfl_area_m2_lc: [],
            # ec.en_lfl_area_m2_tc: [],
            # gc.ghg_lfl_area_m2: [],
            # gc.ghg_lfl_area_m2_s12: [],
            # gc.ghg_lfl_area_m2_s3: [],
            # wc.wat_lfl_area_m2: [],
            # wc.wat_lfl_area_m2_lc: [],
            # wc.wat_lfl_area_m2_tc: [],
            # ec.en_lfl_area_sqft: [],
            # ec.en_lfl_area_sqft_lc: [],
            # ec.en_lfl_area_sqft_tc: [],
            # gc.ghg_lfl_area_sqft: [],
            # gc.ghg_lfl_area_sqft_s12: [],
            # gc.ghg_lfl_area_sqft_s3: [],
            # wc.wat_lfl_area_sqft: [],
            # wc.wat_lfl_area_sqft_lc: [],
            # wc.wat_lfl_area_sqft_tc: [],
            # ec.en_lfl_area: [],
            # ec.en_lfl_area_lc: [],
            # ec.en_lfl_area_tc: [],
            # gc.ghg_lfl_area: [],
            # gc.ghg_lfl_area_s12: [],
            # gc.ghg_lfl_area_s3: [],
            # wc.wat_lfl_area: [],
            # wc.wat_lfl_area_lc: [],
            # wc.wat_lfl_area_tc: [],
            # ec.en_area_p_lc: [],
            # ec.en_area_p_tc: [],
            # gc.ghg_area_p_s12: [],
            # gc.ghg_area_p_s3: [],
            # wc.wat_area_p_lc: [],
            # wc.wat_area_p_tc: [],
            # wsc.was_area_p_lc: [],
            # wsc.was_area_p_tc: [],
            # asc.asset_size_owned_sqft: [],
            # ec.en_area_cov_p: [],
            # gc.ghg_area_cov_p: [],
            # wc.wat_area_cov_p: [],
            # asc.asset_size_sqft: [],
            ec.energy_efficiency_area_m2: [10, 160, 0, 0],
        }
    )


def test_aggregate_to_property_sector_country_and_response_id(
    mock_data_for_aggregation,
):
    mock_data_for_aggregation = (
        CoverageCalculations().add_coverage_weighted_per_control(
            mock_data_for_aggregation
        )
    )
    asset_metric_agg_recipe = ASSET_METRIC_AGGREGATION_RECIPE.copy()
    asset_metric_agg_recipe = {
        k: asset_metric_agg_recipe[k]
        for k in set(asset_metric_agg_recipe.keys()).intersection(
            mock_data_for_aggregation.columns
        )
    }

    weighted_mean_area_list = COLUMNS_SCALED_PER_AREA_OWNERSHIP
    weighted_mean_area_list = [
        c
        for c in set(weighted_mean_area_list).intersection(
            mock_data_for_aggregation.columns
        )
    ]
    weighted_sum_ownership_list = COLUMNS_TO_WEIGHT_SUM_PER_OWNERSHIP
    weighted_sum_ownership_list = [
        c
        for c in set(weighted_sum_ownership_list).intersection(
            mock_data_for_aggregation.columns
        )
    ]

    weighted_mean_other_frame = COLUMNS_NORMALISED_DIFFERENT_WEIGHTS
    weighted_mean_other_frame = weighted_mean_other_frame.loc[
        weighted_mean_other_frame.base_column.isin(mock_data_for_aggregation.columns)
    ]
    weighted_mean_other_frame = weighted_mean_other_frame.loc[
        weighted_mean_other_frame.normaliser_column.isin(
            mock_data_for_aggregation.columns
        )
    ]

    benchmark_mean_list = BENCHMARK_COLUMNS_MEAN
    benchmark_mean_list = {
        k: benchmark_mean_list[k]
        for k in set(benchmark_mean_list.keys()).intersection(
            set(weighted_mean_other_frame.new_column).union(
                asset_metric_agg_recipe.keys()
            )
        )
    }

    am = AssetMetricAggregator(
        asset_metric_data=mock_data_for_aggregation,
        agg_keys=[asc.property_sector, asc.country, asc.response_id],
        add_benchmarks=True,
        asset_metric_agg_recipe=asset_metric_agg_recipe,
        weighted_mean_area_list=weighted_mean_area_list,
        weighted_sum_ownership_list=weighted_sum_ownership_list,
        weighted_mean_other_frame=weighted_mean_other_frame,
        benchmark_mean_list=benchmark_mean_list,
    )
    result = am.aggregate()

    agg_output = get_multiindex_df(
        {
            ("TSOT", "Canada", 1, 2023): {
                sc.pgav: 30600504.0,
                asc.asset_size_m2: 300,
                ec.asset_vacancy_energy_intensity: np.nan,
                wc.asset_vacancy_water_intensity: np.nan,
                gc.asset_vacancy_ghg_intensity: np.nan,
                sc.asset_size_owned_m2: 90.0,
                ec.asset_size_energy_intensity_m2: 0.0,
                wc.asset_size_water_intensity_m2: 0.0,
                gc.asset_size_ghg_intensity_m2: 0.0,
                ec.en_lfl_p_accepted: -10.0,
                ec.en_ren_rate: 100.0,
                ec.en_ren_rate_ly: 100.0,
                ec.en_ren_ons_con_rate: 100.0,
                ec.en_ren_ons_exp_rate: 100.0,
                ec.en_ren_ons_tpt_rate: 100.0,
                ec.en_ren_ofs_pbl_rate: 100.0,
                ec.en_ren_ofs_pbt_rate: 100.0,
                gc.ghg_lfl_p_accepted: -10.0,
                wc.wat_lfl_p_accepted: -10.0,
                wc.wat_rec_rate: 100.0,
                wc.wat_rec_rate_ly: 100.0,
                wc.wat_rec_ons_reu_rate: 100.0,
                wc.wat_rec_ons_cap_rate: 100.0,
                wc.wat_rec_ons_ext_rate: 100.0,
                wc.wat_rec_ofs_pur_rate: 100.0,
                wsc.was_pabs_div_ly: 100.0,
                wsc.was_pabs_lf: 100.0,
                wsc.was_pabs_in: 100.0,
                wsc.was_pabs_ru: 100.0,
                wsc.was_pabs_wte: 100.0,
                wsc.was_pabs_rec: 100.0,
                wsc.was_pabs_oth: 100.0,
                ec.floor_area_percent_energy_intensity: 0.0,
                wc.floor_area_percent_water_intensity: 0.0,
                gc.floor_area_percent_ghg_intensity: 0.0,
                ec.en_area_time_cov_p_lc_agg: 0.0,
                ec.en_area_time_cov_p_tc_agg: 0.0,
                gc.ghg_area_time_cov_p_s12_agg: 0.0,
                gc.ghg_area_time_cov_p_s3_agg: 0.0,
                wc.wat_area_time_cov_p_lc_agg: 0.0,
                wc.wat_area_time_cov_p_tc_agg: 0.0,
                wsc.was_area_cov_p_lc_agg: 0.0,
                wsc.was_area_cov_p_tc_agg: 0.0,
                sc.en_area_time_weight_lc_owned: 300 * 365,
                sc.en_area_time_weight_tc_owned: 300 * 365,
                sc.ghg_area_time_weight_s12_owned: 300 * 365,
                sc.ghg_area_time_weight_s3_owned: 300 * 365,
                sc.wat_area_time_weight_lc_owned: 300 * 365,
                sc.wat_area_time_weight_tc_owned: 300 * 365,
                sc.was_area_weight_lc_owned: 300 * 365,
                sc.was_area_weight_tc_owned: 300 * 365,
                ec.en_lfl_percent_change_lc_bench: -500.0,
                ec.en_lfl_percent_change_tc_bench: -500.0,
                wc.wat_lfl_percent_change_lc_bench: -500.0,
                wc.wat_lfl_percent_change_tc_bench: -500.0,
                gc.ghg_lfl_percent_change_s12_bench: -500.0,
                gc.ghg_lfl_percent_change_s3_bench: -500.0,
                ec.en_lfl_percent_change_lc_accepted: -500.0,
                ec.en_lfl_percent_change_tc_accepted: -500.0,
                wc.wat_lfl_percent_change_lc_accepted: -500.0,
                wc.wat_lfl_percent_change_tc_accepted: -500.0,
                gc.ghg_lfl_percent_change_s12_accepted: -500.0,
                gc.ghg_lfl_percent_change_s3_accepted: -500.0,
                wsc.was_pabs_div: 100.0,
                ec.en_efficiency_int_kwh_m2_accepted: 10.0,
                wc.wat_scored_int_m3_m2_accepted: 10.0,
                gc.ghg_scored_int_ton_m2_accepted: 10.0,
                ec.energy_efficiency_score_eligible: 0,
                ec.en_int_eligible: 0,
                wc.wat_int_eligible: 0,
                gc.ghg_int_eligible: 0,
                ec.en_lfl_abs_lc_ly_accepted: 0.6,
                ec.en_lfl_abs_tc_ly_accepted: 0.6,
                wc.wat_lfl_abs_lc_ly_accepted: 0.6,
                wc.wat_lfl_abs_tc_ly_accepted: 0.6,
                gc.ghg_lfl_abs_s12_ly_accepted: 0.6,
                gc.ghg_lfl_abs_s3_ly_accepted: 0.6,
                ec.en_lfl_abs_lc_ly_bench: 0.6,
                ec.en_lfl_abs_tc_ly_bench: 0.6,
                wc.wat_lfl_abs_lc_ly_bench: 0.6,
                wc.wat_lfl_abs_tc_ly_bench: 0.6,
                gc.ghg_lfl_abs_s12_ly_bench: 0.6,
                gc.ghg_lfl_abs_s3_ly_bench: 0.6,
                ec.en_lfl_abs_change_agg: -3.0,
                wc.wat_lfl_abs_change_agg: -3.0,
                gc.ghg_lfl_abs_change_agg: -3.0,
                ec.en_lfl_abs_change_accepted: -3.0,
                ec.en_lfl_abs_change_accepted_mwh: -3.0,
                wc.wat_lfl_abs_change_accepted: -3.0,
                gc.ghg_lfl_abs_change_accepted: -3.0,
                ec.en_ren_ons_con: 3.0,
                ec.en_ren_ons_exp: 3.0,
                ec.en_ren_ons_tpt: 3.0,
                ec.en_ren_ofs_pbl: 3.0,
                ec.en_ren_ofs_pbt: 3.0,
                ec.en_abs_mwh: 3.0,
                ec.en_abs: 3.0,
                ec.en_abs_ly: 3.0,
                ec.en_abs_f_kwh: 3.0,
                ec.en_abs_e_kwh: 3.0,
                ec.en_abs_d_kwh: 3.0,
                ec.en_abs_f_mwh: 3.0,
                ec.en_abs_e_mwh: 3.0,
                ec.en_abs_d_mwh: 3.0,
                gc.ghg_abs: 3.0,
                gc.ghg_abs_net: 3.0,
                wc.wat_abs: 3.0,
                wc.wat_abs_ly: 3.0,
                wsc.was_abs: 3.0,
                wsc.was_abs_ly: 3.0,
                wsc.was_abs_haz: 3.0,
                wsc.was_abs_nhaz: 3.0,
                ec.en_ren_abs_mwh: 3.0,
                ec.en_ren_abs: 3.0,
                ec.en_ren_abs_ly: 3.0,
                ec.en_ren_abs_consumed_mwh: 3.0,
                gc.ghg_abs_offset: 3.0,
                wc.wat_rec_abs: 3.0,
                wc.wat_rec_abs_ly: 3.0,
                wsc.was_abs_div: 3.0,
                wsc.was_abs_div_ly: 3.0,
                ec.en_abs_nopr_ev: 3.0,
                gc.ghg_abs_s1: 3.0,
                gc.ghg_abs_s2_lb: 3.0,
                gc.ghg_abs_s2_mb: 3.0,
                gc.ghg_abs_s3: 3.0,
                ec.en_area_time_cov_p: 0.0,
                gc.ghg_area_time_cov_p: 0.0,
                wc.wat_area_time_cov_p: 0.0,
                wsc.was_area_cov_p: 0.0,
                ec.energy_efficiency_area_p: 0.0,
                sc.en_area_time_weight_owned: 300 * 365,
                sc.ghg_area_time_weight_owned: 300 * 365,
                sc.wat_area_time_weight_owned: 300 * 365,
                # BENCHMARKS
                ec.en_ren_rate_agg_benchmark: 100.0,
                ec.en_ren_rate_ly_agg_benchmark: 100.0,
                ec.en_ren_ons_con_rate_benchmark: 100.0,
                ec.en_ren_ons_exp_rate_benchmark: 100.0,
                ec.en_ren_ons_tpt_rate_benchmark: 100.0,
                ec.en_ren_ofs_pbl_rate_benchmark: 100.0,
                ec.en_ren_ofs_pbt_rate_benchmark: 100.0,
                wc.wat_rec_rate_agg_benchmark: 100.0,
                wc.wat_rec_rate_ly_agg_benchmark: 100.0,
                wc.wat_rec_ons_reu_rate_benchmark: 100.0,
                wc.wat_rec_ons_cap_rate_benchmark: 100.0,
                wc.wat_rec_ons_ext_rate_benchmark: 100.0,
                wc.wat_rec_ofs_pur_rate_benchmark: 100.0,
                wsc.was_pabs_div_ly_benchmark: 100.0,
                wsc.was_pabs_lf_benchmark: 100.0,
                wsc.was_pabs_in_benchmark: 100.0,
                wsc.was_pabs_ru_benchmark: 100.0,
                wsc.was_pabs_wte_benchmark: 100.0,
                wsc.was_pabs_rec_benchmark: 100.0,
                wsc.was_pabs_oth_benchmark: 100.0,
                ec.en_area_time_cov_p_lc_agg_benchmark: 0.0,
                ec.en_area_time_cov_p_tc_agg_benchmark: 0.0,
                gc.ghg_area_time_cov_p_s12_agg_benchmark: 0.0,
                gc.ghg_area_time_cov_p_s3_agg_benchmark: 0.0,
                wc.wat_area_time_cov_p_lc_agg_benchmark: 0.0,
                wc.wat_area_time_cov_p_tc_agg_benchmark: 0.0,
                wsc.was_area_cov_p_lc_agg_benchmark: 0.0,
                wsc.was_area_cov_p_tc_agg_benchmark: 0.0,
                ec.en_lfl_percent_change_lc_benchmark: -500.0,
                ec.en_lfl_percent_change_tc_benchmark: -500.0,
                wc.wat_lfl_percent_change_lc_benchmark: -500.0,
                wc.wat_lfl_percent_change_tc_benchmark: -500.0,
                gc.ghg_lfl_percent_change_s12_benchmark: -500.0,
                gc.ghg_lfl_percent_change_s3_benchmark: -500.0,
                wsc.was_pabs_div_benchmark: 100.0,
            },
            ("OCHI", "UK", 1, 2023): {
                sc.pgav: 981248.0,
                asc.asset_size_m2: 200,
                ec.asset_vacancy_energy_intensity: 18.0,
                wc.asset_vacancy_water_intensity: 18.0,
                gc.asset_vacancy_ghg_intensity: 18.0,
                sc.asset_size_owned_m2: 160.0,
                ec.asset_size_energy_intensity_m2: 160.0,
                wc.asset_size_water_intensity_m2: 160.0,
                gc.asset_size_ghg_intensity_m2: 160.0,
                ec.floor_area_percent_energy_intensity: 100,
                wc.floor_area_percent_water_intensity: 100,
                gc.floor_area_percent_ghg_intensity: 100,
                ec.en_lfl_p_accepted: -20.0,
                ec.en_ren_rate: 100.0,
                ec.en_ren_rate_ly: 100.0,
                ec.en_ren_ons_con_rate: 100.0,
                ec.en_ren_ons_exp_rate: 100.0,
                ec.en_ren_ons_tpt_rate: 100.0,
                ec.en_ren_ofs_pbl_rate: 100.0,
                ec.en_ren_ofs_pbt_rate: 100.0,
                gc.ghg_lfl_p_accepted: -20.0,
                wc.wat_lfl_p_accepted: -20.0,
                wc.wat_rec_rate: 100.0,
                wc.wat_rec_rate_ly: 100.0,
                wc.wat_rec_ons_reu_rate: 100.0,
                wc.wat_rec_ons_cap_rate: 100.0,
                wc.wat_rec_ons_ext_rate: 100.0,
                wc.wat_rec_ofs_pur_rate: 100.0,
                wsc.was_pabs_div_ly: 100.0,
                wsc.was_pabs_lf: 100.0,
                wsc.was_pabs_in: 100.0,
                wsc.was_pabs_ru: 100.0,
                wsc.was_pabs_wte: 100.0,
                wsc.was_pabs_rec: 100.0,
                wsc.was_pabs_oth: 100.0,
                ec.en_area_time_cov_p_lc_agg: 90.0,
                ec.en_area_time_cov_p_tc_agg: 90.0,
                gc.ghg_area_time_cov_p_s12_agg: 90.0,
                gc.ghg_area_time_cov_p_s3_agg: 90.0,
                wc.wat_area_time_cov_p_lc_agg: 90.0,
                wc.wat_area_time_cov_p_tc_agg: 90.0,
                wsc.was_area_cov_p_lc_agg: 90.0,
                wsc.was_area_cov_p_tc_agg: 90.0,
                sc.en_area_time_weight_lc_owned: 200 * 365,
                sc.en_area_time_weight_tc_owned: 200 * 365,
                sc.ghg_area_time_weight_s12_owned: 200 * 365,
                sc.ghg_area_time_weight_s3_owned: 200 * 365,
                sc.wat_area_time_weight_lc_owned: 200 * 365,
                sc.wat_area_time_weight_tc_owned: 200 * 365,
                sc.was_area_weight_lc_owned: 200 * 365,
                sc.was_area_weight_tc_owned: 200 * 365,
                ec.en_lfl_percent_change_lc_bench: -1000.0,
                ec.en_lfl_percent_change_tc_bench: -1000.0,
                wc.wat_lfl_percent_change_lc_bench: -1000.0,
                wc.wat_lfl_percent_change_tc_bench: -1000.0,
                gc.ghg_lfl_percent_change_s12_bench: -1000.0,
                gc.ghg_lfl_percent_change_s3_bench: -1000.0,
                ec.en_lfl_percent_change_lc_accepted: -1000.0,
                ec.en_lfl_percent_change_tc_accepted: -1000.0,
                wc.wat_lfl_percent_change_lc_accepted: -1000.0,
                wc.wat_lfl_percent_change_tc_accepted: -1000.0,
                gc.ghg_lfl_percent_change_s12_accepted: -1000.0,
                gc.ghg_lfl_percent_change_s3_accepted: -1000.0,
                wsc.was_pabs_div: 100.0,
                ec.en_efficiency_int_kwh_m2_accepted: 20.0,
                wc.wat_scored_int_m3_m2_accepted: 20.0,
                gc.ghg_scored_int_ton_m2_accepted: 20.0,
                ec.energy_efficiency_score_eligible: 1,
                ec.en_int_eligible: 1,
                wc.wat_int_eligible: 1,
                gc.ghg_int_eligible: 1,
                ec.en_lfl_abs_lc_ly_accepted: 1.6,
                ec.en_lfl_abs_tc_ly_accepted: 1.6,
                wc.wat_lfl_abs_lc_ly_accepted: 1.6,
                wc.wat_lfl_abs_tc_ly_accepted: 1.6,
                gc.ghg_lfl_abs_s12_ly_accepted: 1.6,
                gc.ghg_lfl_abs_s3_ly_accepted: 1.6,
                ec.en_lfl_abs_lc_ly_bench: 1.6,
                ec.en_lfl_abs_tc_ly_bench: 1.6,
                wc.wat_lfl_abs_lc_ly_bench: 1.6,
                wc.wat_lfl_abs_tc_ly_bench: 1.6,
                gc.ghg_lfl_abs_s12_ly_bench: 1.6,
                gc.ghg_lfl_abs_s3_ly_bench: 1.6,
                ec.en_lfl_abs_change_agg: -16.0,
                wc.wat_lfl_abs_change_agg: -16.0,
                gc.ghg_lfl_abs_change_agg: -16.0,
                ec.en_lfl_abs_change_accepted: -16.0,
                ec.en_lfl_abs_change_accepted_mwh: -16.0,
                wc.wat_lfl_abs_change_accepted: -16.0,
                gc.ghg_lfl_abs_change_accepted: -16.0,
                ec.en_ren_ons_con: 16,
                ec.en_ren_ons_exp: 16,
                ec.en_ren_ons_tpt: 16,
                ec.en_ren_ofs_pbl: 16,
                ec.en_ren_ofs_pbt: 16,
                ec.en_abs_mwh: 16,
                ec.en_abs: 16,
                ec.en_abs_ly: 16,
                ec.en_abs_f_kwh: 16,
                ec.en_abs_e_kwh: 16,
                ec.en_abs_d_kwh: 16,
                ec.en_abs_f_mwh: 16,
                ec.en_abs_e_mwh: 16,
                ec.en_abs_d_mwh: 16,
                gc.ghg_abs: 16,
                gc.ghg_abs_net: 16,
                wc.wat_abs: 16,
                wc.wat_abs_ly: 16,
                wsc.was_abs: 16,
                wsc.was_abs_ly: 16,
                wsc.was_abs_haz: 16,
                wsc.was_abs_nhaz: 16,
                ec.en_ren_abs_mwh: 16,
                ec.en_ren_abs: 16,
                ec.en_ren_abs_ly: 16,
                ec.en_ren_abs_consumed_mwh: 16,
                gc.ghg_abs_offset: 16,
                wc.wat_rec_abs: 16,
                wc.wat_rec_abs_ly: 16,
                wsc.was_abs_div: 16,
                wsc.was_abs_div_ly: 16,
                ec.en_abs_nopr_ev: 16,
                gc.ghg_abs_s1: 16,
                gc.ghg_abs_s2_lb: 16,
                gc.ghg_abs_s2_mb: 16,
                gc.ghg_abs_s3: 16,
                ec.en_area_time_cov_p: 90.0,
                gc.ghg_area_time_cov_p: 90.0,
                wc.wat_area_time_cov_p: 90.0,
                wsc.was_area_cov_p: 90.0,
                ec.energy_efficiency_area_p: 100.0,
                sc.en_area_time_weight_owned: 200 * 365,
                sc.ghg_area_time_weight_owned: 200 * 365,
                sc.wat_area_time_weight_owned: 200 * 365,
                # BENCHMARKS
                ec.en_ren_rate_agg_benchmark: 100.0,
                ec.en_ren_rate_ly_agg_benchmark: 100.0,
                ec.en_ren_ons_con_rate_benchmark: 100.0,
                ec.en_ren_ons_exp_rate_benchmark: 100.0,
                ec.en_ren_ons_tpt_rate_benchmark: 100.0,
                ec.en_ren_ofs_pbl_rate_benchmark: 100.0,
                ec.en_ren_ofs_pbt_rate_benchmark: 100.0,
                wc.wat_rec_rate_agg_benchmark: 100.0,
                wc.wat_rec_rate_ly_agg_benchmark: 100.0,
                wc.wat_rec_ons_reu_rate_benchmark: 100.0,
                wc.wat_rec_ons_cap_rate_benchmark: 100.0,
                wc.wat_rec_ons_ext_rate_benchmark: 100.0,
                wc.wat_rec_ofs_pur_rate_benchmark: 100.0,
                wsc.was_pabs_div_ly_benchmark: 100.0,
                wsc.was_pabs_lf_benchmark: 100.0,
                wsc.was_pabs_in_benchmark: 100.0,
                wsc.was_pabs_ru_benchmark: 100.0,
                wsc.was_pabs_wte_benchmark: 100.0,
                wsc.was_pabs_rec_benchmark: 100.0,
                wsc.was_pabs_oth_benchmark: 100.0,
                ec.en_area_time_cov_p_lc_agg_benchmark: 90.0,
                ec.en_area_time_cov_p_tc_agg_benchmark: 90.0,
                gc.ghg_area_time_cov_p_s12_agg_benchmark: 90.0,
                gc.ghg_area_time_cov_p_s3_agg_benchmark: 90.0,
                wc.wat_area_time_cov_p_lc_agg_benchmark: 90.0,
                wc.wat_area_time_cov_p_tc_agg_benchmark: 90.0,
                wsc.was_area_cov_p_lc_agg_benchmark: 90.0,
                wsc.was_area_cov_p_tc_agg_benchmark: 90.0,
                ec.en_lfl_percent_change_lc_benchmark: -1000.0,
                ec.en_lfl_percent_change_tc_benchmark: -1000.0,
                wc.wat_lfl_percent_change_lc_benchmark: -1000.0,
                wc.wat_lfl_percent_change_tc_benchmark: -1000.0,
                gc.ghg_lfl_percent_change_s12_benchmark: -1000.0,
                gc.ghg_lfl_percent_change_s3_benchmark: -1000.0,
                wsc.was_pabs_div_benchmark: 100.0,
            },
            ("OCHI", "USA", 1, 2023): {
                sc.pgav: 100012004.0,
                asc.asset_size_m2: 200,
                ec.asset_vacancy_energy_intensity: 10.0,
                wc.asset_vacancy_water_intensity: 10.0,
                gc.asset_vacancy_ghg_intensity: 10.0,
                sc.asset_size_owned_m2: 85.0,
                ec.asset_size_energy_intensity_m2: 10.0,
                wc.asset_size_water_intensity_m2: 10.0,
                gc.asset_size_ghg_intensity_m2: 10.0,
                ec.floor_area_percent_energy_intensity: 10 / 85 * 100,
                wc.floor_area_percent_water_intensity: 10 / 85 * 100,
                gc.floor_area_percent_ghg_intensity: 10 / 85 * 100,
                ec.en_lfl_p_accepted: -10.0,
                ec.en_ren_rate: 100.0,
                ec.en_ren_rate_ly: 100.0,
                ec.en_ren_ons_con_rate: 100.0,
                ec.en_ren_ons_exp_rate: 100.0,
                ec.en_ren_ons_tpt_rate: 100.0,
                ec.en_ren_ofs_pbl_rate: 100.0,
                ec.en_ren_ofs_pbt_rate: 100.0,
                gc.ghg_lfl_p_accepted: -10.0,
                wc.wat_lfl_p_accepted: -10.0,
                wc.wat_rec_rate: 100.0,
                wc.wat_rec_rate_ly: 100.0,
                wc.wat_rec_ons_reu_rate: 100.0,
                wc.wat_rec_ons_cap_rate: 100.0,
                wc.wat_rec_ons_ext_rate: 100.0,
                wc.wat_rec_ofs_pur_rate: 100.0,
                wsc.was_pabs_div_ly: 100.0,
                wsc.was_pabs_lf: 100.0,
                wsc.was_pabs_in: 100.0,
                wsc.was_pabs_ru: 100.0,
                wsc.was_pabs_wte: 100.0,
                wsc.was_pabs_rec: 100.0,
                wsc.was_pabs_oth: 100.0,
                ec.en_area_time_cov_p_lc_agg: 90,
                ec.en_area_time_cov_p_tc_agg: 90,
                gc.ghg_area_time_cov_p_s12_agg: 90,
                gc.ghg_area_time_cov_p_s3_agg: 90,
                wc.wat_area_time_cov_p_lc_agg: 90,
                wc.wat_area_time_cov_p_tc_agg: 90,
                wsc.was_area_cov_p_lc_agg: 90,
                wsc.was_area_cov_p_tc_agg: 90,
                sc.en_area_time_weight_lc_owned: 200 * 365,
                sc.en_area_time_weight_tc_owned: 200 * 365,
                sc.ghg_area_time_weight_s12_owned: 200 * 365,
                sc.ghg_area_time_weight_s3_owned: 200 * 365,
                sc.wat_area_time_weight_lc_owned: 200 * 365,
                sc.wat_area_time_weight_tc_owned: 200 * 365,
                sc.was_area_weight_lc_owned: 200 * 365,
                sc.was_area_weight_tc_owned: 200 * 365,
                ec.en_lfl_percent_change_lc_bench: -500.0,
                ec.en_lfl_percent_change_tc_bench: -500.0,
                wc.wat_lfl_percent_change_lc_bench: -500.0,
                wc.wat_lfl_percent_change_tc_bench: -500.0,
                gc.ghg_lfl_percent_change_s12_bench: -500.0,
                gc.ghg_lfl_percent_change_s3_bench: -500.0,
                ec.en_lfl_percent_change_lc_accepted: -500.0,
                ec.en_lfl_percent_change_tc_accepted: -500.0,
                wc.wat_lfl_percent_change_lc_accepted: -500.0,
                wc.wat_lfl_percent_change_tc_accepted: -500.0,
                gc.ghg_lfl_percent_change_s12_accepted: -500.0,
                gc.ghg_lfl_percent_change_s3_accepted: -500.0,
                wsc.was_pabs_div: 100.0,
                ec.en_efficiency_int_kwh_m2_accepted: 10.0,
                wc.wat_scored_int_m3_m2_accepted: 10.0,
                gc.ghg_scored_int_ton_m2_accepted: 10.0,
                ec.energy_efficiency_score_eligible: 1,
                ec.en_int_eligible: 1,
                wc.wat_int_eligible: 1,
                gc.ghg_int_eligible: 1,
                ec.en_lfl_abs_lc_ly_accepted: 0.2,
                ec.en_lfl_abs_tc_ly_accepted: 0.2,
                wc.wat_lfl_abs_lc_ly_accepted: 0.2,
                wc.wat_lfl_abs_tc_ly_accepted: 0.2,
                gc.ghg_lfl_abs_s12_ly_accepted: 0.2,
                gc.ghg_lfl_abs_s3_ly_accepted: 0.2,
                ec.en_lfl_abs_lc_ly_bench: 0.2,
                ec.en_lfl_abs_tc_ly_bench: 0.2,
                wc.wat_lfl_abs_lc_ly_bench: 0.2,
                wc.wat_lfl_abs_tc_ly_bench: 0.2,
                gc.ghg_lfl_abs_s12_ly_bench: 0.2,
                gc.ghg_lfl_abs_s3_ly_bench: 0.2,
                ec.en_lfl_abs_change_agg: -1,
                wc.wat_lfl_abs_change_agg: -1,
                gc.ghg_lfl_abs_change_agg: -1,
                ec.en_lfl_abs_change_accepted: -1,
                ec.en_lfl_abs_change_accepted_mwh: -1,
                wc.wat_lfl_abs_change_accepted: -1,
                gc.ghg_lfl_abs_change_accepted: -1,
                ec.en_ren_ons_con: 1,
                ec.en_ren_ons_exp: 1,
                ec.en_ren_ons_tpt: 1,
                ec.en_ren_ofs_pbl: 1,
                ec.en_ren_ofs_pbt: 1,
                ec.en_abs_mwh: 1,
                ec.en_abs: 1,
                ec.en_abs_ly: 1,
                ec.en_abs_f_kwh: 1,
                ec.en_abs_e_kwh: 1,
                ec.en_abs_d_kwh: 1,
                ec.en_abs_f_mwh: 1,
                ec.en_abs_e_mwh: 1,
                ec.en_abs_d_mwh: 1,
                gc.ghg_abs: 1,
                gc.ghg_abs_net: 1,
                wc.wat_abs: 1,
                wc.wat_abs_ly: 1,
                wsc.was_abs: 1,
                wsc.was_abs_ly: 1,
                wsc.was_abs_haz: 1,
                wsc.was_abs_nhaz: 1,
                ec.en_ren_abs_mwh: 1,
                ec.en_ren_abs: 1,
                ec.en_ren_abs_ly: 1,
                ec.en_ren_abs_consumed_mwh: 1,
                gc.ghg_abs_offset: 1,
                wc.wat_rec_abs: 1,
                wc.wat_rec_abs_ly: 1,
                wsc.was_abs_div: 1,
                wsc.was_abs_div_ly: 1,
                ec.en_abs_nopr_ev: 1,
                gc.ghg_abs_s1: 1,
                gc.ghg_abs_s2_lb: 1,
                gc.ghg_abs_s2_mb: 1,
                gc.ghg_abs_s3: 1,
                ec.en_area_time_cov_p: 90.0,
                gc.ghg_area_time_cov_p: 90.0,
                wc.wat_area_time_cov_p: 90.0,
                wsc.was_area_cov_p: 82.35294,
                ec.energy_efficiency_area_p: 10 / 85 * 100,
                sc.en_area_time_weight_owned: 200 * 365,
                sc.ghg_area_time_weight_owned: 200 * 365,
                sc.wat_area_time_weight_owned: 200 * 365,
                # BENCHMARKS
                ec.en_ren_rate_agg_benchmark: 100.0,
                ec.en_ren_rate_ly_agg_benchmark: 100.0,
                ec.en_ren_ons_con_rate_benchmark: 100.0,
                ec.en_ren_ons_exp_rate_benchmark: 100.0,
                ec.en_ren_ons_tpt_rate_benchmark: 100.0,
                ec.en_ren_ofs_pbl_rate_benchmark: 100.0,
                ec.en_ren_ofs_pbt_rate_benchmark: 100.0,
                wc.wat_rec_rate_agg_benchmark: 100.0,
                wc.wat_rec_rate_ly_agg_benchmark: 100.0,
                wc.wat_rec_ons_reu_rate_benchmark: 100.0,
                wc.wat_rec_ons_cap_rate_benchmark: 100.0,
                wc.wat_rec_ons_ext_rate_benchmark: 100.0,
                wc.wat_rec_ofs_pur_rate_benchmark: 100.0,
                wsc.was_pabs_div_ly_benchmark: 100.0,
                wsc.was_pabs_lf_benchmark: 100.0,
                wsc.was_pabs_in_benchmark: 100.0,
                wsc.was_pabs_ru_benchmark: 100.0,
                wsc.was_pabs_wte_benchmark: 100.0,
                wsc.was_pabs_rec_benchmark: 100.0,
                wsc.was_pabs_oth_benchmark: 100.0,
                ec.en_area_time_cov_p_lc_agg_benchmark: 90.0,
                ec.en_area_time_cov_p_tc_agg_benchmark: 90.0,
                gc.ghg_area_time_cov_p_s12_agg_benchmark: 90.0,
                gc.ghg_area_time_cov_p_s3_agg_benchmark: 90.0,
                wc.wat_area_time_cov_p_lc_agg_benchmark: 90.0,
                wc.wat_area_time_cov_p_tc_agg_benchmark: 90.0,
                wsc.was_area_cov_p_lc_agg_benchmark: 90.0,
                wsc.was_area_cov_p_tc_agg_benchmark: 90.0,
                ec.en_lfl_percent_change_lc_benchmark: -500.0,
                ec.en_lfl_percent_change_tc_benchmark: -500.0,
                wc.wat_lfl_percent_change_lc_benchmark: -500.0,
                wc.wat_lfl_percent_change_tc_benchmark: -500.0,
                gc.ghg_lfl_percent_change_s12_benchmark: -500.0,
                gc.ghg_lfl_percent_change_s3_benchmark: -500.0,
                wsc.was_pabs_div_benchmark: 100.0,
            },
        },
        [asc.property_sector, asc.country, asc.response_id, asc.data_year],
    )

    common_cols = list(result.columns.intersection(agg_output.columns))
    pdt.assert_frame_equal(
        result.sort_index(axis=1)[common_cols],
        agg_output.sort_index(axis=1)[common_cols],
        check_like=True,
    )


@pytest.mark.skip("Need to add all columns")
def test_aggregate_to_response_id(mock_data_for_aggregation, mock_r1_table):
    agg_data = AssetMetricAggregator(
        asset_metric_data=mock_data_for_aggregation,
        agg_keys=[asc.property_sector, asc.country, asc.response_id],
    ).aggregate()
    am = AssetMetricAggregator(asset_metric_data=agg_data, agg_keys=[asc.response_id])
    result = am.aggregate()

    agg_output = pd.DataFrame(
        {
            sc.pgav: [131593756.0],
            asc.asset_size_m2: 700,
            ec.asset_vacancy_energy_intensity: 10.0777278,
            wc.asset_vacancy_water_intensity: 10.0777278,
            gc.asset_vacancy_ghg_intensity: 10.0777278,
            sc.asset_size_owned_m2: 335.0,
            ec.asset_size_energy_intensity_m2: 300.0,
            wc.asset_size_water_intensity_m2: 300.0,
            gc.asset_size_ghg_intensity_m2: 300.0,
            ec.floor_area_percent_energy_intensity: 42.85714,
            wc.floor_area_percent_water_intensity: 42.85714,
            gc.floor_area_percent_ghg_intensity: 42.85714,
            ec.en_lfl_p_accepted: -10.074566,
            ec.en_ren_rate: 27.572760,
            ec.en_ren_rate_ly: 13.7863800,
            ec.en_ren_ons_con_rate: 36.4077201,
            ec.en_ren_ons_exp_rate: 11.262199,
            ec.en_ren_ons_tpt_rate: 10.0,
            ec.en_ren_ofs_pbl_rate: 32.135759,
            ec.en_ren_ofs_pbt_rate: 10.194319,
            gc.ghg_lfl_p_accepted: -10.074566,
            wc.wat_lfl_p_accepted: -10.074566,
            wc.wat_rec_rate: 27.572760,
            wc.wat_rec_rate_ly: 13.786380,
            wc.wat_rec_ons_reu_rate: 22.23292029,
            wc.wat_rec_ons_cap_rate: 11.359359,
            wc.wat_rec_ons_ext_rate: 46.3105603,
            wc.wat_rec_ofs_pur_rate: 18.932119,
            wsc.was_pabs_div_ly: 14.9516393,
            wsc.was_pabs_lf: 46.1162408,
            wsc.was_pabs_in: 10.1943195,
            wsc.was_pabs_ru: 11.26219,
            wsc.was_pabs_wte: 10.097159,
            wsc.was_pabs_rec: 3.592279,
            wsc.was_pabs_oth: 18.737800,
            ec.en_area_time_cov_p_lc_agg: 82.4272386,
            ec.en_area_time_cov_p_tc_agg: 82.4272386,
            gc.ghg_area_time_cov_p_s12_agg: 82.4272386,
            gc.ghg_area_time_cov_p_s3_agg: 82.4272386,
            wc.wat_area_time_cov_p_lc_agg: 82.4272386,
            wc.wat_area_time_cov_p_tc_agg: 82.4272386,
            wsc.was_area_cov_p_lc_agg: 82.4272386,
            wsc.was_area_cov_p_tc_agg: 82.4272386,
            ec.en_lfl_percent_change_lc_bench: -10.074566 / 2,
            ec.en_lfl_percent_change_tc_bench: -10.074566 / 2,
            wc.wat_lfl_percent_change_lc_bench: -10.074566 / 2,
            wc.wat_lfl_percent_change_tc_bench: -10.074566 / 2,
            gc.ghg_lfl_percent_change_s12_bench: -10.074566 / 2,
            gc.ghg_lfl_percent_change_s3_bench: -10.074566 / 2,
            ec.en_lfl_percent_change_lc_accepted: -10.074566 / 2,
            ec.en_lfl_percent_change_tc_accepted: -10.074566 / 2,
            wc.wat_lfl_percent_change_lc_accepted: -10.074566 / 2,
            wc.wat_lfl_percent_change_tc_accepted: -10.074566 / 2,
            gc.ghg_lfl_percent_change_s12_accepted: -10.074566 / 2,
            gc.ghg_lfl_percent_change_s3_accepted: -10.074566 / 2,
            wsc.was_pabs_div: 10.074566,
            ec.en_efficiency_int_kwh_m2_accepted: 10.074566,
            wc.wat_scored_int_m3_m2_accepted: 10.074566,
            gc.ghg_scored_int_ton_m2_accepted: 10.074566,
            ec.en_int_eligible: 2,
            wc.wat_int_eligible: 2,
            gc.ghg_int_eligible: 2,
        }
    )

    agg_output.index = [[1], [2023]]
    agg_output.index.names = [asc.response_id, asc.data_year]

    pdt.assert_frame_equal(result.sort_index(axis=1), agg_output.sort_index(axis=1))
