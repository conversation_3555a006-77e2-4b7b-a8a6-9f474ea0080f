import pytest
import pandas as pd
import app_real_estate.constants.column_names.asset_characteristics_columns as asc
from app_real_estate.models.score_aggregation_util_models.en1_scored_data import (
    EN1ScoredData,
)


@pytest.fixture
def mock_asset_metric_data():
    return pd.DataFrame(
        {
            asc.response_id: [1, 2, 3],
            asc.country: ["USA", "USA", "Canada"],
            asc.property_subtype: ["Residential", "Residential", "Commercial"],
            asc.asset_gav: [50006002.0, 981248.0, 30600504.0],
            asc.asset_size_m2: [100, 200, 300],
            asc.asset_ownership: [10, 80, 30],
        }
    )


def test_en1_dataclass_validation(mock_asset_metric_data):
    # check if parse_df is going to faile if wrong dataframe is passed
    # en1 score data class
    with pytest.raises(ValueError):
        EN1ScoredData.parse_df(mock_asset_metric_data)
