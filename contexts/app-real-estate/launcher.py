import signal
import sys
import time
import importlib
from argparse import Namespace


def exit_gracefully(signum, frame):
    r"""
    SIGTERM Handler: https://docs.aws.amazon.com/lambda/latest/operatorguide/static-initialization.html
    Listening for os signals that can be handled,reference: https://docs.aws.amazon.com/lambda/latest/dg/runtimes-extensions-api.html
    Termination Signals: https://www.gnu.org/software/libc/manual/html_node/Termination-Signals.html
    """
    print("[runtime] SIGTERM received")

    print("[runtime] cleaning up")
    # perform actual clean up work here.
    time.sleep(0.2)

    print("[runtime] exiting")
    sys.exit(0)


signal.signal(signal.SIGTERM, exit_gracefully)


def lambda_handler(event, context):

    full_class_name, method_name = event["function"].rsplit(".", 1)
    _, class_name = full_class_name.rsplit(".", 1)

    module = importlib.import_module(full_class_name)

    arguments = Namespace(**event["arguments"])

    cls = getattr(module, class_name)
    instance = cls()

    getattr(instance, method_name)(arguments)

    return {"statusCode": 200}


if __name__ == "__main__":
    lambda_handler(
        {
            "function": "app_real_estate.tasks.PrepareBenchmarkTask.run",
            "arguments": {
                "input_data_location": "s3://gresb-tst-scoring-models/asset_data.parquet",
                "output_location": "s3://gresb-tst-scoring-models/output2",
                "indicator": "en1",
            },
        },
        {},
    )
