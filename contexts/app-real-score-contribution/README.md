# Score Contribution
## Description
Score Contribution is a new GRESB feature that gives Real Estate Participants deeper insights into how individual
assets contribute to their overall GRESB Score. It helps managers identify key areas for improvement and make data-driven
decisions to enhance portfolio performance.

## Running the data pipeline

### Input data
The main input files for asset level scores and portfolio level (country and property subtype level) are read from s3 bucket on prd,
which are the direct output of the scoring models.
The survey_table_data, survey_data, asset_types and locations are parquet files which were saved manually from R sessions.
These files are also saved on s3 for component tests, you can find them here:
`s3://gresb-dev-gold-data/integration_test/score_contribution/test_data/metadata/`
and `s3://gresb-dev-gold-data/integration_test/score_contribution/test_data/yearly_data/2024/input/`

Tasks are added to improve this:
- survey table: https://app.clickup.com/t/86981wf5j
- location: https://app.clickup.com/t/8697ktvr3
- asset types: https://app.clickup.com/t/8697ktvtk

### Running the pipeline locally

#### Caching mechanism

In order to improve readability of the dataframes and to increase performance, only strictly necessary columns are read.
Columns to read is defined in `scoring-models/contexts/app-real-score-contribution/config.json`.
After running the pipeline locally once, the input file is cached automatically. However when you add more columns
to read in the config.json, you need to update the cache by removing the cache file in `scoring-models/shared/gresb-utils/data`.

#### Example command running the script at portfolio level
To run the script locally, you need to give the correct input args. The required input data can
be directly referred to the test data on s3, or you can download them locally and give the local path. An example is given below.

Env vars: # TODO: check which profile to use to read both from prd and write to dev.
```sh
AWS_PROFILE=gresb-dev-developer-break-glass;AWS_REGION=eu-central-1
```
This is required because we are writing data to gold bucket on dev.
```
scoring-models/contexts/app-real-score-contribution/app_real_score_contribution/__main__.py \
s3://gresb-prd-gold-data/main/re/2024/output/aggregated_scores/propertysubtype_country_responseid_scores.parquet \
scoring-models/contexts/app-real-score-contribution/tests/component/test_data/yearly_data/2024/input/survey_table_data.parquet \
scoring-models/contexts/app-real-score-contribution/tests/component/test_data/yearly_data/2024/input/survey_data.parquet \
scoring-models/contexts/app-real-score-contribution/tests/component/test_data/metadata/asset_types.parquet \
scoring-models/contexts/app-real-score-contribution/tests/component/test_data/metadata/locations.parquet \
--survey_year 2024 \
--level portfolio
```
This will produce a file in the directory locally e.g. `scoring-models/contexts/app-real-score-contribution/tests/component/test_data/yearly_data/2024/output/feature-add-component-test-25d2691`
or on the gold data s3 bucket on **DEV**,
under the branch and commit hash combination dir, for example
`s3://gresb-dev-gold-data/feature-add-score-contribution-portfolio-overview-02b6b15/real/v2/portfolio/year=2024/`
For now this is hard-coded to write to the dev bucket, we need to figure out when to upload the data to prd.
The ideal scenario is to work in a consistent runtime, i.e. read and write data from the same runtime, however right now
we only have input data from PRD.

### Running the pipeline in the cloud
This is not possible yet, will be added when we deploy this pipeline as a lambda function or a job in k8s.

## Run unittest tests
```sh
poetry run pytest --ignore=contexts/app-real-score-contribution/tests/component/
```
## Run component tests

The component tests run the data processing pipeline and compare the result with the validated full dataset on prd.
Right now the files are manually copied from prd bucket to the integration test dir in the gold bucket on DEV, we
can think of ways to automate this process in the future: https://app.clickup.com/t/8698f45gd

### Data dir structure
The input data and validation data is stored on the `integration_test` dir in the gold bucket on DEV,
`s3://gresb-dev-gold-data/integration_test/score_contribution/test_data/`.
It is used for both when running locally and in the CI.(When running locally the data will be cached after the first run)

In the yearly data dir, there is input, output and output_validation.
- input: the input data required to run the score contribution pipeline.
- output: the output data from your test, a unique dir will be generated and name by git tag or {branch}-{commit_hash}.
- output_validation: the data to validate the output data.

```sh
    │   ├── test_data
    │   │   ├── metadata
    │   │   │   └── locations.parquet
    │   │   └── yearly_data
    │   │       └── 2024
    │   │           ├── input
    │   │           │   ├── asset_level_scores.csv
    │   │           │   ├── survey_data.parquet
    │   │           │   └── survey_table_data.parquet
    │   │           ├── output
    │   │           │   ├── feature-add-component-test-a6466a0
    │   │           │   │   └── asset
    │   │           │   │       └── real_score_contribution_asset_output_data.csv
    │   │           │   └── feature-add-component-test-f47428f
    │   │           │       └── asset
    │   │           │           └── real_score_contribution_asset_output_data.csv
    │   │           └── output_validation
    │   │               ├── asset
    │   │               │   └── real_score_contribution_asset_test_data.csv
    │   │               └── portfolio
    │   │                   └── real_score_contribution_portfolio_test_data.csv
    │   └── test_score_contribution.py
```


### Run component tests locally
```sh
poetry run pytest contexts/app-real-score-contribution/tests/component/
```

### Running the component tests in the CI
Whenever a commit is pushed on a PR, the component test will be run in the CI. It's excluded from the pre-commit hook.
