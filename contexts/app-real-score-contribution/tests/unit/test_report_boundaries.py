import polars as pl
import pytest
from app_real_score_contribution.report_boundaries import add_reporting_boundaries_data
from app_real_score_contribution.constants import (
    RESPONSE_ID,
    PRT_TYPE_CODE,
    COUNTRY_CODE,
    R_1_TBL_AREA,
    R_1_TBL_PGAV,
    R_1_TBL_AST,
)


@pytest.fixture
def sample_asset_data():
    """Fixture for sample asset data with all response IDs"""
    return pl.DataFrame(
        {
            RESPONSE_ID: ["R1", "R2", "R3"],
            PRT_TYPE_CODE: ["TypeA", "TypeB", "TypeC"],
            COUNTRY_CODE: ["US", "UK", "FR"],
            R_1_TBL_AREA: [500, 600, 700],
            R_1_TBL_PGAV: [50, 60, 70],
            R_1_TBL_AST: [10, 20, 30],
            "extra_col": [100, 200, 300],  # Extra column to ensure it's preserved
        }
    )


@pytest.fixture
def sample_survey_table_data():
    """Fixture for survey_table_data ensuring all response IDs exist"""
    return pl.DataFrame(
        {
            RESPONSE_ID: ["R1", "R2", "R3"],  # Now includes R3
            PRT_TYPE_CODE: ["TypeA", "TypeB", "TypeC"],
            COUNTRY_CODE: ["US", "UK", "FR"],
            R_1_TBL_AREA: [500, 600, 700],
            R_1_TBL_PGAV: [50, 60, 70],
            R_1_TBL_AST: [10, 20, 30],
        }
    )


@pytest.fixture
def sample_survey_table_data_with_missing_pgav():
    """Fixture where R_1_TBL_PGAV has missing values"""
    return pl.DataFrame(
        {
            RESPONSE_ID: ["R1", "R2", "R3", "R4"],  # Now includes R3
            PRT_TYPE_CODE: ["TypeA", "TypeB", "TypeC", "TypeD"],
            COUNTRY_CODE: ["US", "UK", "FR", "DE"],
            R_1_TBL_AST: [10, 20, 30, 40],
            R_1_TBL_AREA: [500, 600, 700, 800],
            R_1_TBL_PGAV: [
                50,
                60,
                70,
                None,
            ],  # Missing pgav values since they are development type of assets.
        }
    )


def test_typical_case(sample_asset_data, sample_survey_table_data):
    """Test normal case where all columns exist and match correctly"""
    result = add_reporting_boundaries_data(sample_asset_data, sample_survey_table_data)

    # Ensure R_1_TBL_AREA and R_1_TBL_PGAV are merged correctly
    assert result[R_1_TBL_AREA].to_list() == [500, 600, 700]
    assert result[R_1_TBL_PGAV].to_list() == [50, 60, 70]
    assert result["extra_col"].to_list() == [100, 200, 300]


def test_missing_gav_values(
    sample_asset_data, sample_survey_table_data_with_missing_pgav
):
    """Test case where R_1_TBL_PGAV has missing values (which is normal for development assets)"""
    result = add_reporting_boundaries_data(
        sample_asset_data, sample_survey_table_data_with_missing_pgav
    )

    # Ensure R_1_TBL_AREA and R_1_TBL_PGAV are merged correctly
    assert result[R_1_TBL_AREA].to_list() == [500, 600, 700]
    assert result[R_1_TBL_PGAV].to_list() == [50, 60, 70]
    assert result["extra_col"].to_list() == [100, 200, 300]


if __name__ == "__main__":
    pytest.main()
