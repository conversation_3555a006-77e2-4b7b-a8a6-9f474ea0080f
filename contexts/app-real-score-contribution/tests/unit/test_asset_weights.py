import polars as pl
import pytest
from app_real_score_contribution.asset_weights import add_asset_weights
from app_real_score_contribution.constants import (
    R_1_TBL_PGAV,
    R_1_TBL_AREA,
    ASSET_SIZE,
    ASSET_OWNERSHIP,
)


@pytest.fixture
def valid_asset_data():
    """Fixture for valid asset data"""
    return pl.DataFrame(
        {
            R_1_TBL_PGAV: [50, 80, 100],  # GAV percentages
            R_1_TBL_AREA: [200, 500, 1000],  # Total area
            ASSET_SIZE: [50, 100, 200],  # Individual asset sizes
            ASSET_OWNERSHIP: [100, 50, 75],  # Ownership percentages
        }
    )


@pytest.fixture
def asset_data_missing_columns():
    """Fixture for data missing required columns"""
    return pl.DataFrame(
        {
            ASSET_SIZE: [50, 100, 200],  # Missing required R_1_TBL_PGAV, R_1_TBL_AREA
            ASSET_OWNERSHIP: [100, 50, 75],
        }
    )


@pytest.fixture
def asset_data_zero_area():
    """Fixture for data containing a zero in total area"""
    return pl.DataFrame(
        {
            R_1_TBL_PGAV: [50, 80, 100],
            R_1_TBL_AREA: [200, 0, 1000],  # Second row has zero area
            ASSET_SIZE: [50, 100, 200],
            ASSET_OWNERSHIP: [100, 50, 75],
        }
    )


@pytest.fixture
def asset_data_outside_valid_range():
    """Fixture for data with values outside valid range"""
    return pl.DataFrame(
        {
            R_1_TBL_PGAV: [50, -10, 200],  # -10% and 200% are invalid GAV values
            R_1_TBL_AREA: [200, 500, 1000],
            ASSET_SIZE: [50, 100, 200],
            ASSET_OWNERSHIP: [100, 50, 75],
        }
    )


@pytest.fixture
def asset_data_with_nans():
    """Fixture for data containing NaN values"""
    return pl.DataFrame(
        {
            R_1_TBL_PGAV: [50, None, 100],
            R_1_TBL_AREA: [200, 500, 1000],
            ASSET_SIZE: [50, 100, 200],
            ASSET_OWNERSHIP: [100, 50, 75],
        }
    )


def test_valid_asset_weights(valid_asset_data):
    """Test normal case with valid data"""
    result = add_asset_weights(valid_asset_data)

    expected_fractions = [
        (50 / 100) * (50) * (100 / 100) / 200,  # = 0.25
        (80 / 100) * (100) * (50 / 100) / 500,  # = 0.08
        (100 / 100) * (200) * (75 / 100) / 1000,  # = 0.15
    ]
    expected_percentages = [f * 100 for f in expected_fractions]

    assert result["asset_weight_frac"].round(4).to_list() == expected_fractions
    assert result["asset_weight_percent"].round(2).to_list() == expected_percentages


def test_missing_columns(asset_data_missing_columns):
    """Test case where required columns are missing"""
    with pytest.raises(ValueError, match="Missing required columns"):
        add_asset_weights(asset_data_missing_columns)


def test_zero_area(asset_data_zero_area):
    """Test case where total area is zero to prevent division by zero"""
    with pytest.raises(ValueError, match="R_1_TBL_AREA cannot have zero values"):
        add_asset_weights(asset_data_zero_area)


def test_outside_valid_range(asset_data_outside_valid_range):
    """Test case where values are outside valid range"""
    with pytest.raises(ValueError, match="Invalid asset weight found"):
        add_asset_weights(asset_data_outside_valid_range)


def test_nan_values(asset_data_with_nans):
    """Test case where values are outside valid range"""
    with pytest.raises(ValueError, match="asset_weight_frac cannot have null values"):
        add_asset_weights(asset_data_with_nans)


if __name__ == "__main__":
    pytest.main()
