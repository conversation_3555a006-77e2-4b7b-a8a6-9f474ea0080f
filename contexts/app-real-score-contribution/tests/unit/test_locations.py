import polars as pl
import pytest

from app_real_score_contribution.constants import (
    COUNTRY_NAME,
    SUB_REGION,
    INTER_REGION,
    REGION,
    SUPER_REGION,
    COUNTRY_CODE,
)
from app_real_score_contribution.locations import add_location_groups


@pytest.fixture
def locations():
    return pl.DataFrame(
        {
            COUNTRY_CODE: ["US", "NL", "FR"],
            COUNTRY_NAME: ["United States", "Netherlands", "France"],
            SUB_REGION: ["Northern America", "Western Europe", "Western Europe"],
            INTER_REGION: ["", "", ""],
            REGION: ["Americas", "Europe", "Europe"],
            SUPER_REGION: ["Global", "Global", "Global"],
        }
    )


def test_typical_case(locations):
    data = pl.DataFrame({COUNTRY_CODE: ["US", "NL", "FR"]})

    result = add_location_groups(data, locations)

    assert result.shape == (3, 6)  # Ensure columns are added
    assert result[COUNTRY_NAME].to_list() == [
        "United States",
        "Netherlands",
        "France",
    ]
    assert result[SUB_REGION].to_list() == [
        "Northern America",
        "Western Europe",
        "Western Europe",
    ]
    assert result[INTER_REGION].to_list() == ["", "", ""]
    assert result[REGION].to_list() == ["Americas", "Europe", "Europe"]
    assert result[SUPER_REGION].to_list() == ["Global", "Global", "Global"]


def test_missing_country(locations):
    data = pl.DataFrame({COUNTRY_CODE: ["US", "DE", "FR"]})

    result = add_location_groups(data, locations)

    assert result.shape == (3, 6)  # Ensure columns are added
    assert result[COUNTRY_NAME].to_list() == ["United States", None, "France"]
    assert result[SUB_REGION].to_list() == ["Northern America", None, "Western Europe"]
    assert result[INTER_REGION].to_list() == ["", None, ""]
    assert result[REGION].to_list() == ["Americas", None, "Europe"]
    assert result[SUPER_REGION].to_list() == ["Global", None, "Global"]


def test_empty_dataframe(locations):
    data = pl.DataFrame({COUNTRY_CODE: []})

    result = add_location_groups(data, locations)

    assert result.shape == (0, 6)  # Ensure all columns are present but empty
