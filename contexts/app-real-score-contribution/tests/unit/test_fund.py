import polars as pl

from app_real_score_contribution.constants import RC_3_A
from app_real_score_contribution.fund import add_fund_characteristics


def test_add_fund_characteristics():
    # Mock asset_data
    asset_data = pl.DataFrame(
        {"response_id": [1, 2, 3], "other_column": ["A", "B", "C"]}
    )

    # Mock survey_data
    survey_data = pl.DataFrame(
        {
            "response_id": [1, 2, 3],
            "company_fund_id": ["F1", "F2", "F3"],
            "fund_name": ["Fund 1", "Fund 2", "Fund 3"],
            RC_3_A: [1, 2, 1],
        }
    )

    # Expected result
    expected = pl.DataFrame(
        {
            "response_id": [1, 2, 3],
            "other_column": ["A", "B", "C"],
            "company_fund_id": ["F1", "F2", "F3"],
            "fund_name": ["Fund 1", "Fund 2", "Fund 3"],
            RC_3_A: [1, 2, 1],
            "area_unit": ["m2", "sqft", "m2"],
        }
    )

    # Run function and assert
    result = add_fund_characteristics(asset_data, survey_data)
    assert result.equals(expected)


def test_add_fund_characteristics_nulls():
    asset_data = pl.DataFrame({"response_id": [1, None], "other_column": ["A", "B"]})
    survey_data = pl.DataFrame(
        {
            "response_id": [1, None],
            "company_fund_id": ["F1", None],
            "fund_name": ["Fund 1", None],
            RC_3_A: [1, None],
        }
    )
    result = add_fund_characteristics(asset_data, survey_data)
    assert len(result) == 2
