import math

import polars as pl
import pytest

from app_real_score_contribution.energy.model import ControlTypePartition
from app_real_score_contribution.energy.weight import (
    add_coverage_weights_per_control,
    add_lfl_weights_per_control,
    add_percent_weights_per_control,
    add_weights_per_control,
    add_lfl_asset_weights,
    get_landlord_controlled_weight,
    get_tenant_controlled_weight,
)


def build_test_df():
    return pl.DataFrame(
        {
            "asset_weight_frac": [0.25],
            "asset_size_base_tenant_m2": [10],
            "asset_size_common_m2": [20],
            "asset_size_shared_m2": [30],
            "asset_size_tenant_landlord_m2": [40],
            "asset_size_tenant_tenant_m2": [50],
            "asset_size_whole_landlord_m2": [0],  # mutually exclusive
            "asset_size_whole_tenant_m2": [0],  # mutually exclusive
            "asset_weight_frac_en_lfl": [0.1],
            "en_lfl_abs_bt_last_year": [5],
            "en_lfl_abs_lc_bc_last_year": [10],
            "en_lfl_abs_lc_bs_last_year": [15],
            "en_lfl_abs_lc_t_last_year": [20],
            "en_lfl_abs_tc_t_last_year": [25],
            "en_lfl_abs_lc_w_last_year": [0],  # mutually exclusive
            "en_lfl_abs_tc_w_last_year": [0],  # mutually exclusive
            "other_landlord_controlled_area_m2": [0.0],
            "other_tenant_controlled_area_m2": [0.0],
            "en_lfl_abs_lc_o_last_year": [10],
            "en_lfl_abs_tc_o_last_year": [5],
        }
    )


def test_add_coverage_weights_per_control():
    df = build_test_df()
    result = add_coverage_weights_per_control(df)
    assert "weight_frac_en_cov_lc" in result.columns
    assert "weight_frac_en_cov_tc" in result.columns
    assert result["weight_frac_en_cov_lc"].to_list()[0] > 0.0


def test_add_lfl_weights_per_control():
    df = build_test_df()
    result = add_lfl_weights_per_control(df)
    assert "weight_frac_en_lfl_lc" in result.columns
    assert "weight_frac_en_lfl_tc" in result.columns
    assert result["weight_frac_en_lfl_tc"].to_list()[0] > 0.0


def test_add_percent_weights_per_control():
    df = build_test_df()
    df = add_coverage_weights_per_control(df)
    df = add_lfl_weights_per_control(df)
    result = add_percent_weights_per_control(df)
    for col in [
        "weight_perc_en_cov_lc",
        "weight_perc_en_cov_tc",
        "weight_perc_en_lfl_lc",
        "weight_perc_en_lfl_tc",
    ]:
        assert col in result.columns
        assert isinstance(result[col].to_list()[0], float)


def test_add_weights_per_control():
    df = build_test_df()
    result = add_weights_per_control(df)
    for col in [
        "weight_frac_en_cov_lc",
        "weight_frac_en_cov_tc",
        "weight_frac_en_lfl_lc",
        "weight_frac_en_lfl_tc",
        "weight_perc_en_cov_lc",
        "weight_perc_en_cov_tc",
        "weight_perc_en_lfl_lc",
        "weight_perc_en_lfl_tc",
    ]:
        assert col in result.columns


@pytest.fixture
def sample_df():
    # two assets in same bucket, only first has an LFL score
    return pl.DataFrame(
        {
            "response_id": [1, 1, 2],
            "country": ["US", "US", "US"],
            "property_type_code": ["A", "A", "B"],
            "asset_size": [100.0, 200.0, 50.0],
            "asset_ownership": [50.0, 25.0, 100.0],
            "r_1_tbl_pgav": [1000.0, 500.0, 200.0],
            # only row0 has an LFL score
            "score_en1_en_lfl_percent_change": [1.2, None, 0.5],
        }
    )


def test_add_lfl_asset_weights_en(sample_df):
    out = add_lfl_asset_weights(sample_df, "en")
    # lfl_asset_area = size * own/100
    # row0: 100 * 50% = 50.0; row1: no score → 0
    # group (1,US,A) → sum = 50.0
    # asset_weight_frac_en_lfl = (PGAV/100 * area) / agg_area
    # row0: (1000/100 * 50) / 50 = (10 * 50)/50 = 10.0
    # row1: no score → 0
    # row2: different bucket (2,US,B): area=50*100/100=50, agg=50, PGAV=200 → (200/100*50)/50=2
    expected_frac = [10.0, 0.0, 2.0]
    assert out["asset_weight_frac_en_lfl"].to_list() == pytest.approx(expected_frac)
    # percent = frac * 100
    expected_perc = [1000.0, 0.0, 200.0]
    assert out["asset_weight_percent_en_lfl"].to_list() == pytest.approx(expected_perc)


def test_unknown_utility():
    df = pl.DataFrame(
        {
            "response_id": [1],
            "country": ["US"],
            "property_type_code": ["A"],
            "asset_size": [100.0],
            "asset_ownership": [50.0],
            "r_1_tbl_pgav": [1000.0],
            "score_x1_x_lfl_percent_change": [1.0],
        }
    )
    with pytest.raises(ValueError):
        add_lfl_asset_weights(df, "foo")


@pytest.mark.parametrize(
    "areas, expect_lc, expect_tc",
    [
        # 1) Base‐tenant only → 100% LC, 0% TC
        (
            ControlTypePartition(
                base_tenant=100.0,
                common=0.0,
                shared=0.0,
                tenant_lc=0.0,
                tenant_tc=0.0,
                whole_lc=0.0,
                whole_tc=0.0,
            ),
            1.0,
            0.0,
        ),
        # 2) Whole‐building LC only → 100% LC
        (
            ControlTypePartition(
                base_tenant=0.0,
                common=0.0,
                shared=0.0,
                tenant_lc=0.0,
                tenant_tc=0.0,
                whole_lc=250.0,
                whole_tc=0.0,
            ),
            1.0,
            0.0,
        ),
        # 3) Whole‐building TC only → 100% TC
        (
            ControlTypePartition(
                base_tenant=0.0,
                common=0.0,
                shared=0.0,
                tenant_lc=0.0,
                tenant_tc=0.0,
                whole_lc=0.0,
                whole_tc=300.0,
            ),
            0.0,
            1.0,
        ),
        # 4) No area reported → both zero
        (
            ControlTypePartition(
                base_tenant=0.0,
                common=0.0,
                shared=0.0,
                tenant_lc=0.0,
                tenant_tc=0.0,
                whole_lc=0.0,
                whole_tc=0.0,
            ),
            0.0,
            0.0,
        ),
        # 5) Base‐tenant with common area → LC = common/(base_tenant), TC = tenant_tc/(base_tenant)
        (
            ControlTypePartition(
                base_tenant=200.0,
                common=50.0,
                shared=50.0,
                tenant_lc=30.0,
                tenant_tc=70.0,
                whole_lc=0.0,
                whole_tc=0.0,
                outdoor_lc=0.0,
                outdoor_tc=0.0,
            ),
            0.58,  # 116/200
            0.42,  # complement
        ),
    ],
)
def test_valid_asset_levels(areas, expect_lc, expect_tc):
    lc = get_landlord_controlled_weight(areas)
    tc = get_tenant_controlled_weight(areas)
    assert 0.0 <= lc <= 1.0
    assert 0.0 <= tc <= 1.0
    assert math.isclose(lc, expect_lc, rel_tol=1e-9)
    assert math.isclose(tc, expect_tc, rel_tol=1e-9)
