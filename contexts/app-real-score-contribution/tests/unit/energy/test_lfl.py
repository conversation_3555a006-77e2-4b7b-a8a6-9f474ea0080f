import polars as pl
import pytest

from app_real_score_contribution.energy.lfl import (
    add_last_year_lfl_consumption,
    add_extra_lfl_consumption,
    add_extra_areas_columns_per_control,
    set_lfl_scores_back_to_missing,
)


def test_add_last_year_lfl_consumption():
    # Sample test data
    df = pl.DataFrame(
        {
            "portfolio_asset_id": [1, 1],
            "building_data_row_id": ["a", "b"],
            "data_year": [2022, 2023],
            "en_lfl_abs_wf": [100, 110],
            "en_lfl_abs_wd": [200, 210],
            "en_lfl_abs_we": [300, 310],
        }
    )

    survey_year = 2024

    result = add_last_year_lfl_consumption(df, survey_year)

    # Check the shape and content
    assert "en_lfl_abs_wf_last_year" in result.columns
    assert "en_lfl_abs_wd_last_year" in result.columns
    assert "en_lfl_abs_we_last_year" in result.columns

    # For the row with building_data_row_id 'b', check values from previous year
    last_year_values = (
        result.filter(pl.col("building_data_row_id") == "b")
        .select(
            [
                "en_lfl_abs_wf_last_year",
                "en_lfl_abs_wd_last_year",
                "en_lfl_abs_we_last_year",
            ]
        )
        .to_dicts()[0]
    )

    assert last_year_values["en_lfl_abs_wf_last_year"] == 100
    assert last_year_values["en_lfl_abs_wd_last_year"] == 200
    assert last_year_values["en_lfl_abs_we_last_year"] == 300


def test_add_extra_lfl_consumption():
    df = pl.DataFrame(
        {
            "whole_building": [True, True, False],
            "tenant_ctrl": [False, True, True],
            "en_lfl_abs_wf_last_year": [10.0, 20.0, None],
            "en_lfl_abs_wd_last_year": [5.0, None, 5.0],
            "en_lfl_abs_we_last_year": [None, 15.0, 5.0],
            "en_lfl_abs_lc_bc_last_year": [1.0, 2.0, 3.0],
            "en_lfl_abs_lc_bs_last_year": [1.0, 2.0, 3.0],
            "en_lfl_abs_lc_t_last_year": [1.0, 2.0, 3.0],
            "en_lfl_abs_tc_t_last_year": [1.0, 2.0, 3.0],
        }
    )

    result = add_extra_lfl_consumption(df)

    assert "en_lfl_abs_w_last_year" in result.columns
    assert "en_lfl_abs_lc_w_last_year" in result.columns
    assert "en_lfl_abs_tc_w_last_year" in result.columns
    assert "en_lfl_abs_bt_last_year" in result.columns

    # Check first row (landlord-controlled)
    assert result[0, "en_lfl_abs_lc_w_last_year"] == 15.0  # 10 + 5
    assert result[0, "en_lfl_abs_tc_w_last_year"] is None

    # Check second row (tenant-controlled)
    assert result[1, "en_lfl_abs_tc_w_last_year"] == 35.0  # 20 + 15
    assert result[1, "en_lfl_abs_lc_w_last_year"] is None

    # Check final summed column
    assert result[0, "en_lfl_abs_bt_last_year"] == 4.0  # 1 + 1 + 1 + 1


def test_add_extra_areas_per_control():
    df = pl.DataFrame(
        {
            "whole_building": [True, True, False, False],
            "tenant_ctrl": [False, True, False, True],
            "asset_size_m2": [1000, 2000, 1500, 1200],
        }
    )

    result = add_extra_areas_columns_per_control(df)

    expected = pl.DataFrame(
        {
            "whole_building": [True, True, False, False],
            "tenant_ctrl": [False, True, False, True],
            "asset_size_m2": [1000, 2000, 1500, 1200],
            "asset_size_whole_landlord_m2": [1000, None, None, None],
            "asset_size_whole_tenant_m2": [None, 2000, None, None],
            "asset_size_base_tenant_m2": [None, None, 1500, 1200],
        }
    )

    assert result.equals(
        expected
    ), "Test failed: Output does not match expected DataFrame"


@pytest.mark.parametrize(
    "utility,original_var,points_var",
    [
        ("en", "score_en1_en_lfl_percent_change", "score_points_en_lfl"),
        ("wat", "score_wt1_wat_lfl_percent_change", "score_points_wat_lfl"),
        ("ghg", "score_gh1_ghg_lfl_percent_change", "score_points_ghg_lfl"),
    ],
)
def test_set_lfl_scores_back_to_missing_typical(utility, original_var, points_var):
    # build a small DataFrame where row 0 is missing original, row 1 is present
    df = pl.DataFrame(
        {
            original_var: [None, 5.0],
            points_var: [10.0, 20.0],
            "unchanged": [100, 200],
        }
    )
    out = set_lfl_scores_back_to_missing(df, utility)

    # Row 0: original was NULL → points should now be NULL
    assert out[points_var][0] is None
    # Row 1: original was not NULL → points preserved
    assert out[points_var][1] == 20.0
    # Other columns untouched
    assert out["unchanged"].to_list() == [100, 200]


def test_set_lfl_scores_back_to_missing_bad_utility():
    df = pl.DataFrame(
        {
            "score_en1_en_lfl_percent_change": [None],
            "score_points_en_lfl": [1.0],
        }
    )
    with pytest.raises(ValueError):
        set_lfl_scores_back_to_missing(df, "FOO")
