import polars as pl
import pytest

from app_real_score_contribution.energy.tableau import (
    clean_lfl_data,
    clean_offsite_scores,
)


def test_clean_lfl_data():
    df = pl.DataFrame(
        {
            "en_lfl_percent_change_lc": [0.10, 0.20, 0.30],
            "en_lfl_percent_change_tc": [0.15, 0.25, 0.35],
            "weight_frac_en_lfl_lc": [0.0, 0.5, 1.0],
            "weight_frac_en_lfl_tc": [1.0, 0.0, 0.2],
            "score_en1_lfl_availability": [42, 84, 126],
        }
    )

    out = clean_lfl_data(df)

    # percent‐change columns renamed
    assert out["en_lfl_p_lc"].to_list() == [0.10, 0.20, 0.30]
    assert out["en_lfl_p_tc"].to_list() == [0.15, 0.25, 0.35]

    # availability_lc only where weight_frac_en_lfl_lc > 0
    assert out["score_en1_lfl_availability_lc"].to_list() == [None, 84, 126]

    # availability_tc only where weight_frac_en_lfl_tc > 0
    assert out["score_en1_lfl_availability_tc"].to_list() == [42, None, 126]

    # original DataFrame unchanged except for additions
    for col in df.columns:
        assert col in out.columns

    # exactly four new columns appended
    for new_col in (
        "en_lfl_p_lc",
        "en_lfl_p_tc",
        "score_en1_lfl_availability_lc",
        "score_en1_lfl_availability_tc",
    ):
        assert new_col in out.columns


@pytest.fixture
def sample():
    return pl.DataFrame(
        {
            "score_points_en_ren_ons": [0.0, 1.0, 0.5],
            "score_points_en_ren_ofs": [0.1, 0.2, 0.3],
            "score_points_potential_en_ren_ofs": [0.4, 0.5, 0.6],
            "score_contribution_en_ren_ofs": [1.1, 1.2, 1.3],
            "score_contribution_potential_en_ren_ofs": [2.1, 2.2, 2.3],
        }
    )


def test_clean_offsite_when_onsite_not_max(sample):
    # rows where score_points_en_ren_ons != 1 must be unchanged
    out = clean_offsite_scores(sample)
    # row0: ons=0 → ofs stays
    assert out["score_points_en_ren_ofs"][0] == pytest.approx(0.1)
    assert out["score_points_potential_en_ren_ofs"][0] == pytest.approx(0.4)
    assert out["score_contribution_en_ren_ofs"][0] == pytest.approx(1.1)
    assert out["score_contribution_potential_en_ren_ofs"][0] == pytest.approx(2.1)


def test_clean_offsite_when_onsite_max(sample):
    out = clean_offsite_scores(sample)
    # row1: ons=1 → all ofs columns become null
    for col in [
        "score_points_en_ren_ofs",
        "score_points_potential_en_ren_ofs",
        "score_contribution_en_ren_ofs",
        "score_contribution_potential_en_ren_ofs",
    ]:
        assert out[col][1] is None


def test_clean_offsite_partial(sample):
    # row2: ons=0.5 → unchanged
    out = clean_offsite_scores(sample)
    assert out["score_points_en_ren_ofs"][2] == pytest.approx(0.3)
    assert out["score_points_potential_en_ren_ofs"][2] == pytest.approx(0.6)
    assert out["score_contribution_en_ren_ofs"][2] == pytest.approx(1.3)
    assert out["score_contribution_potential_en_ren_ofs"][2] == pytest.approx(2.3)
