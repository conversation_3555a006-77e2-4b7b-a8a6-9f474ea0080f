import polars as pl
import pytest

from app_real_score_contribution.energy.utils import (
    null_preserving_row_sum,
    calc_total_scores,
)


def test_row_sum_with_na_if_all_null_all_null():
    df = pl.DataFrame(
        {
            "a": [None, None],
            "b": [None, None],
        }
    )
    # apply helper: new = row_sum(a,b)
    out = df.with_columns(
        null_preserving_row_sum([pl.col("a"), pl.col("b")]).alias("sum_ab")
    )
    # both rows all-null → sum_ab = None
    assert out["sum_ab"].to_list() == [None, None]


def test_row_sum_with_na_if_all_null_mixed_and_both_present():
    df = pl.DataFrame(
        {
            "a": [None, 2, 1],
            "b": [None, 3, None],
        }
    )
    out = df.with_columns(
        null_preserving_row_sum([pl.col("a"), pl.col("b")]).alias("sum_ab")
    )
    # row0: both null  → None
    # row1: 2 + 3      → 5
    # row2: 1 + (null) → 1   (null treated as zero when at least one value present)
    assert out["sum_ab"].to_list() == [None, 5, 1]


@pytest.fixture
def wide_df():
    # two rows: one fully populated, one with some nulls
    return pl.DataFrame(
        {
            "score_points_en_1": [5.0, 3.0],
            "score_points_en_cov_sub": [1.0, None],
            "score_points_en_cov_sub_lc": [0.6, None],
            "score_points_en_cov_sub_tc": [0.4, None],
            "score_points_en_lfl_avail": [None, None],
            "score_points_en_lfl_perf": [2.0, 1.5],
            "score_points_en_lfl_avail_lc": [None, 0.5],
            "score_points_en_lfl_perf_lc": [1.0, None],
            "score_points_en_lfl_avail_tc": [3.0, None],
            "score_points_en_lfl_perf_tc": [None, 0.5],
            "score_points_en_ren_ons": [0.8, None],
            "score_points_en_ren_ofs": [0.5, 0.3],
            "score_points_en_ren_perf": [1.0, 0.2],
        }
    )


def test_calc_total_scores_typical(wide_df):
    df = calc_total_scores(wide_df)

    # 1) copy cov subscores
    assert df["score_points_en_cov"].to_list() == [1.0, None]
    assert df["score_points_en_cov_lc"].to_list() == [0.6, None]
    assert df["score_points_en_cov_tc"].to_list() == [0.4, None]

    # 2) LFL totals:
    # row0: avail=None, perf=2.0 → 2.0
    # row1: avail=None, perf=1.5 → 1.5
    assert df["score_points_en_lfl"].to_list() == [2.0, 1.5]

    # 3) per-control LFL:
    # row0: _lc: perf_lc=1.0 →1.0; _tc: avail_tc=3.0→3.0
    # row1: _lc: avail_lc=0.5→0.5; _tc: perf_tc=0.5→0.5
    assert df["score_points_en_lfl_lc"].to_list() == [1.0, 0.5]
    assert df["score_points_en_lfl_tc"].to_list() == [3.0, 0.5]

    # 4) ren availability = min(sum(ons,ofs),1)
    # row0: sum=0.8+0.5=1.3→clip→1.0
    # row1: sum=null+0.3=0.3→0.3
    assert df["score_points_en_ren_avail"].to_list() == [1.0, 0.3]

    # 5) ren total = ren_avail + ren_perf
    # row0: 1.0 + 1.0 = 2.0
    # row1: 0.3 + 0.2 = 0.5
    assert pytest.approx(df["score_points_en_ren"].to_list(), rel=1e-6) == [2.0, 0.5]


def test_calc_total_scores_all_null_lfl_and_ren(wide_df):
    # force LFL and REN columns all-null on row0 to test propagation of None
    df0 = wide_df.with_columns(
        [
            pl.lit(None).cast(pl.Float64).alias(c)
            for c in [
                "score_points_en_lfl_avail",
                "score_points_en_lfl_perf",
                "score_points_en_lfl_avail_lc",
                "score_points_en_lfl_perf_lc",
                "score_points_en_lfl_avail_tc",
                "score_points_en_lfl_perf_tc",
                "score_points_en_ren_ons",
                "score_points_en_ren_ofs",
            ]
        ]
    )
    df = calc_total_scores(df0)

    # all null → lfl and per-control become None
    assert df["score_points_en_lfl"].to_list() == [None, None]
    assert df["score_points_en_lfl_lc"].to_list() == [None, None]
    assert df["score_points_en_lfl_tc"].to_list() == [None, None]
    # ren_avail: both inputs null for row0 → None; row1 remains as before
    assert df["score_points_en_ren_avail"].to_list() == [None, None]
    # ren total: None + perf → if ren_avail None but score_points_en_ren_perf exists,
    # row_sum should treat perf as the sum
    assert df["score_points_en_ren"].to_list() == [1.0, 0.2]
