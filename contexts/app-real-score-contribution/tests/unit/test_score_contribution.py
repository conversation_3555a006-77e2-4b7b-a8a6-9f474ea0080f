import polars as pl
import pytest

from app_real_score_contribution.score_contribution import (
    calculate_points_and_contributions,
)


@pytest.fixture
def simple_df():
    # f1 has a None to test missing behavior
    return pl.DataFrame({"f1": [0.2, None], "w": [0.5, 0.5]})


@pytest.fixture
def score_map():
    return {
        "fraction_var": ["f1"],
        "points_var": ["score_points_wt_1"],
        "weight_var": ["w"],
        "max_points": [10.0],
    }


def test_calculate_points_and_contributions_no_fill(simple_df, score_map):
    """
    When assign_missing_to_zero=False:
      - None fractions should propagate to all new columns.
    """
    df = calculate_points_and_contributions(
        simple_df, score_map, assign_missing_to_zero=False
    )

    # row 0: f1=0.2  => en_point=2.0, potential=8.0, contrib=2.0*0.5=1.0, contrib_pot=8.0*0.5=4.0
    # row 1: f1=None => all new columns None
    expected = {
        "score_points_wt_1": [2.0, None],
        "score_points_potential_wt_1": [8.0, None],
        "score_contribution_wt_1": [1.0, None],
        "score_contribution_potential_wt_1": [4.0, None],
    }

    for col, vals in expected.items():
        assert df[col].to_list() == vals


def test_calculate_points_and_contributions_fill(simple_df, score_map):
    """
    When assign_missing_to_zero=True:
      - None fractions are replaced by 0.0 before computing.
    """
    df = calculate_points_and_contributions(
        simple_df, score_map, assign_missing_to_zero=True
    )

    # row 0 same as before
    # row 1: f1 filled->0.0 => en_point=0.0, potential=10.0, contrib=0.0, contrib_pot=10.0*0.5=5.0
    expected = {
        "score_points_wt_1": [2.0, 0.0],
        "score_points_potential_wt_1": [8.0, 10.0],
        "score_contribution_wt_1": [1.0, 0.0],
        "score_contribution_potential_wt_1": [4.0, 5.0],
    }

    for col, vals in expected.items():
        got = df[col].to_list()
        # explicit float compare
        assert all(
            (g == v) or (g is None and v is None) for g, v in zip(got, vals)
        ), f"Column {col} was {got}, expected {vals}"
