import re

import pytest
import polars as pl
import random
from app_real_score_contribution.prepare_output import (
    subset_data,
    pivot_scores_longer,
    process_long_data,
)


@pytest.fixture
def columns_to_keep():
    """Fixture providing the columns_to_keep config."""
    return {
        "asset_long": {
            "id_vars": ["response_id", "portfolio_asset_id"],
            "score_vars": ["score_type", "points", "contribution"],
        }
    }


@pytest.fixture
def sample_data(request):
    """
    A fixture that generates a Polars DataFrame with random floats.

    We expect request.param to be a dict like:
        {
            "expected_columns": ["colA", "colB", ...],
            "nrows": 3
        }
    """
    params = getattr(request, "param", {})  # fallback to {}
    expected_columns = params.get("expected_columns", ["default_col"])
    other_cols = ["random_1", "to_be_dropped_1", "to_be_dropped_2"]
    nrows = params.get("nrows", 3)

    data_dict = {}
    for col in expected_columns + other_cols:
        data_dict[col] = [random.random() for _ in range(nrows)]

    return pl.DataFrame(data_dict)


class TestSubsetData:

    def test_subset_data_typical_case(self):
        df = pl.DataFrame(
            {
                "id1": [1, 2],
                "score_points_en_1": [0.8, 0.9],
                "score_points_potential_en_1": [0.2, 0.1],
                "score_contribution_en_1": [0.5, 0.6],
                "score_contribution_potential_en_1": [0.3, 0.3],
                "weight": [0.7, 0.7],
            }
        )

        config = {
            "id_vars": ["id1"],
            "score_vars": ["score_points_en_1"],
            "weight_vars": ["weight"],
        }

        result = subset_data(df, config)

        assert set(result.columns) == {
            "id1",
            "score_points_en_1",
            "score_points_potential_en_1",
            "score_contribution_en_1",
            "score_contribution_potential_en_1",
            "weight",
        }
        assert result.shape == (2, 6)

    def test_subset_data_missing_columns(self):
        df = pl.DataFrame({"id1": [1, 2], "score_points_en_1": [0.8, 0.9]})

        config = {
            "id_vars": ["id1", "id2"],  # id2 is missing
            "score_vars": ["score_points_en_1"],
        }

        with pytest.raises(ValueError, match="Missing expected columns:"):
            subset_data(df, config)


@pytest.fixture
def sample_asset_data():
    """Fixture providing sample data for pivoting tests."""
    return pl.DataFrame(
        {
            "response_id": [1, 2],
            "portfolio_asset_id": [101, 102],
            "asset_weight_percent": [50.0, 75.0],
            "score_points_en_1": [5.0, 6.0],
            "score_points_gh_1": [3.0, 4.0],
            "score_contribution_en_1": [1.5, 1.8],
            "score_contribution_gh_1": [0.9, 1.2],
        }
    )


@pytest.fixture
def sample_config():
    """Fixture providing a sample config for pivoting tests."""
    return {
        "id_vars": ["response_id", "portfolio_asset_id"],
        "score_vars": ["points", "contribution"],
        "weight_vars": ["asset_weight_percent", "asset_weight_percent"],
    }


class TestPivotScoresLonger:
    def test_typical_case(self, sample_asset_data, sample_config):
        """Test pivoting score-related columns into a long format."""
        long_data = pivot_scores_longer(
            sample_asset_data,
            id_vars=sample_config["id_vars"],
            weight_var=sample_config["weight_vars"][0],
            total_score="total_score",
            subscores=["en_1", "gh_1"],
        )

        assert "score_type" in long_data.columns
        assert "points" in long_data.columns
        assert "contribution" in long_data.columns

        # Ensure expected row count (2 indicators per original row)
        expected_rows = (
            sample_asset_data.shape[0] * 2
        )  # 2 indicators (en_1, gh_1) per row
        assert long_data.shape[0] == expected_rows

        # Sort values before comparison
        sorted_points = sorted(long_data["points"].to_list())
        sorted_contributions = sorted(long_data["contribution"].to_list())

        # Expected values (sorted)
        expected_points = sorted([5.0, 3.0, 6.0, 4.0])
        expected_contributions = sorted([1.5, 0.9, 1.8, 1.2])

        assert sorted_points == expected_points
        assert sorted_contributions == expected_contributions

    def test_no_matching_columns(self, sample_config):
        """Test behavior when no matching columns exist for pivoting."""
        df = pl.DataFrame({"random_col": [1, 2, 3]})
        with pytest.raises(
            ValueError,
            match=re.escape(
                "No matching score columns found. Available: ['random_col']"
            ),
        ):
            pivot_scores_longer(
                df,
                id_vars=sample_config["id_vars"],
                weight_var=sample_config["weight_vars"][0],
                total_score="total_score",
                subscores=["en_1", "gh_1"],
            )

    def test_missing_contribution_columns(self, sample_config):
        """Test behavior when contribution columns are missing."""
        df = pl.DataFrame(
            {
                "response_id": [1, 2],
                "portfolio_asset_id": [101, 102],
                "score_points_en_1": [5.0, 6.0],  # No matching score_contribution_en_1
            }
        )
        with pytest.raises(
            pl.exceptions.SchemaFieldNotFoundError, match="asset_weight_percent"
        ):
            pivot_scores_longer(
                df,
                id_vars=sample_config["id_vars"],
                weight_var=sample_config["weight_vars"][0],
                total_score="total_score",
                subscores=["en_1", "gh_1"],
            )


@pytest.fixture
def sample_asset_data_for_testing_prepare_output():
    """Fixture providing a sample asset data DataFrame."""
    return pl.DataFrame(
        {
            "portfolio_asset_id": [101, 102, 103],
            "asset_name": ["Asset A", "Asset B", "Asset C"],
            "response_id": ["R1", "R2", "R3"],
            "country_name": ["US", "UK", "FR"],
            "score_points_en_1": [10, 12, 8],
            "score_points_gh_1": [5, 6, 4],
            "score_points_wt_1": [6, 7, 3],
            "score_points_ws_1": [3, 4, 2],
            "score_contribution_en_1": [1.2, 1.5, 0.9],
            "score_contribution_wt_1": [0.6, 0.7, 0.5],
            "score_contribution_gh_1": [0.6, 0.7, 0.5],
            "score_contribution_ws_1": [0.3, 0.4, 0.2],
            "asset_weight_percent": [50.0, 75.0, 100.0],
        }
    )


@pytest.fixture
def columns_to_keep_2():
    """Fixture providing a dictionary of columns to retain for the long format processing."""
    return {
        "asset_long": {
            "id_vars": ["portfolio_asset_id", "response_id"],
            "score_type_vars": ["score_type"],
            "score_value_vars": ["points", "contribution"],
        }
    }


class TestProcessLongData:
    def test_typical_case(
        self,
        sample_asset_data_for_testing_prepare_output,
        columns_to_keep_2,
        sample_config,
    ):
        """Test processing long data, ensuring points and contribution values are correctly adjusted."""

        processed_data = process_long_data(
            sample_asset_data_for_testing_prepare_output,
            id_vars=sample_config["id_vars"],
            weight_var=sample_config["weight_vars"][0],
            total_score="total_score",
            subscores=["en_1", "gh_1"],
        )

        assert "points" in processed_data.columns
        assert "contribution" in processed_data.columns

        # Ensure 'points' is None where 'score_type' is 'total'
        total_filtered = processed_data.filter(pl.col("score_type") == "total")
        assert total_filtered["points"].to_list() == [None] * total_filtered.shape[0]

        # Ensure 'contribution' is None where 'score_type' is in indicator list
        indicators = ["en_1", "gh_1", "wt_1", "ws_1"]
        indicator_filtered = processed_data.filter(
            pl.col("score_type").is_in(indicators)
        )
