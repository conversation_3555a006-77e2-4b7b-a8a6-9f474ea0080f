import polars as pl
import pytest
from app_real_score_contribution.asset_types import (
    get_ancestor_of_a_generation,
    add_property_type_groups,
)
from app_real_score_contribution.constants import (
    PRT_TYPE_CODE,
    PRT_SECTOR,
    PRT_TYPE,
    PRT_SUBTYPE,
    PRT_TYPE_NAME_PLACEHOLDER,
)

# Mock asset_types DataFrame
asset_types = pl.DataFrame(
    {
        PRT_TYPE_CODE: ["A", "B", "C", "D", "E"],
        "parent_code": [None, "A", "B", "C", "D"],
        "generation": [1, 2, 3, 4, 5],
    }
)


class TestGetAncestorOfAGeneration:
    def test_ancestor_at_generation_1(self):
        asset_type_codes = pl.DataFrame({PRT_TYPE_CODE: ["C", "B", "A"]})
        target_gen = 1
        result = get_ancestor_of_a_generation(asset_type_codes, target_gen, asset_types)
        assert result.to_list() == ["A", "A", "A"]

    def test_some_codes_at_target_generation(self):
        asset_type_codes = pl.DataFrame({PRT_TYPE_CODE: ["A", "B", "C"]})
        target_gen = 2
        result = get_ancestor_of_a_generation(asset_type_codes, target_gen, asset_types)
        assert result.to_list() == [None, "B", "B"]

    def test_no_valid_ancestors(self):
        asset_type_codes = pl.DataFrame({PRT_TYPE_CODE: ["A", "B", "C"]})
        target_gen = 6
        result = get_ancestor_of_a_generation(asset_type_codes, target_gen, asset_types)
        assert result.to_list() == [None, None, None]

    def test_asset_type_codes_with_nulls(self):
        asset_type_codes = pl.DataFrame({PRT_TYPE_CODE: ["C", None, "B"]})
        target_gen = 2
        result = get_ancestor_of_a_generation(asset_type_codes, target_gen, asset_types)
        assert result.to_list() == ["B", None, "B"]

    def test_empty_asset_type_codes(self):
        asset_type_codes = pl.DataFrame({PRT_TYPE_CODE: []})
        target_gen = 1
        result = get_ancestor_of_a_generation(asset_type_codes, target_gen, asset_types)
        assert result.to_list() == []

    def test_invalid_asset_type_codes(self):
        asset_type_codes = pl.DataFrame({PRT_TYPE_CODE: ["X", "Y", "Z"]})
        target_gen = 1
        with pytest.raises(
            ValueError, match="Invalid asset type codes found: \\['X', 'Y', 'Z'\\]"
        ):
            get_ancestor_of_a_generation(asset_type_codes, target_gen, asset_types)

    def test_mixed_valid_and_invalid_codes(self):
        asset_type_codes = pl.DataFrame({PRT_TYPE_CODE: ["C", "X", "B"]})
        target_gen = 1
        with pytest.raises(
            ValueError, match="Invalid asset type codes found: \\['X'\\]"
        ):
            get_ancestor_of_a_generation(asset_type_codes, target_gen, asset_types)


@pytest.fixture
def asset_types_with_name():
    return pl.DataFrame(
        {
            PRT_TYPE_CODE: ["A", "B", "C", "D", "E"],
            PRT_TYPE_NAME_PLACEHOLDER: [
                "Sector A",
                "Type B",
                "Subtype C",
                "Subtype D",
                "Subtype E",
            ],
            "parent_code": [None, "A", "B", "C", "D"],
            "generation": [1, 2, 3, 4, 5],
        }
    )


class TestAddPropertyTypeGroups:

    def test_typical_case(self, asset_types_with_name):
        data = pl.DataFrame({PRT_TYPE_CODE: ["C", "B", "A"]})

        result = add_property_type_groups(data, asset_types_with_name)

        assert result[PRT_SECTOR].to_list() == ["Sector A", "Sector A", "Sector A"]
        assert result[PRT_TYPE].to_list() == ["Type B", "Type B", "Sector A"]
        assert result[PRT_SUBTYPE].to_list() == ["Subtype C", "Type B", "Sector A"]

    def test_missing_property_type(self, asset_types_with_name):
        data = pl.DataFrame({PRT_TYPE_CODE: ["X", "Y", "Z"]})

        with pytest.raises(
            ValueError, match="Invalid asset type codes found: \['X', 'Y', 'Z'\]"
        ):
            add_property_type_groups(data, asset_types_with_name)

    def test_mixed_valid_and_invalid_codes(self, asset_types_with_name):
        data = pl.DataFrame({PRT_TYPE_CODE: ["C", "X", "B"]})

        with pytest.raises(ValueError, match="Invalid asset type codes found: \['X'\]"):
            add_property_type_groups(data, asset_types_with_name)

    def test_empty_dataframe(self, asset_types_with_name):
        data = pl.DataFrame({PRT_TYPE_CODE: []})
        result = add_property_type_groups(data, asset_types_with_name)
        assert result.shape == (0, 4)  # Ensure all columns are present but empty

    def test_inconsistent_asset_types(self, asset_types_with_name):
        # Inconsistent or incomplete asset_types_with_name
        incomplete_asset_types = asset_types_with_name.filter(pl.col("generation") <= 2)

        data = pl.DataFrame({PRT_TYPE_CODE: ["C", "B", "A"]})

        with pytest.raises(ValueError, match="Invalid asset type codes found: \['C'\]"):
            add_property_type_groups(data, incomplete_asset_types)


if __name__ == "__main__":
    pytest.main()
