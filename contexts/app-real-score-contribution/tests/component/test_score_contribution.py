import os

import polars as pl
import pytest
from typing import Dict, Any

from app_real_score_contribution.config_types import ConfigType
from app_real_score_contribution.pipeline import process_pipeline
from app_real_score_contribution.prepare_output import generate_all_variable_names
from gresb_utils.file_io import load_polars_from_file, write_data
from app_real_score_contribution.utils import load_config
from gresb_utils.git_utils import get_git_output_run
from gresb_utils.validation import compare_polars_df_with_file
import logging

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
handler = logging.StreamHandler()
handler.setFormatter(logging.Formatter("%(asctime)s - %(levelname)s - %(message)s"))
logger.addHandler(handler)

S3_BASE_PATH = "s3://gresb-dev-gold-data/integration_test/score_contribution/test_data"


@pytest.fixture
def test_data() -> Dict[str, Any]:
    """
    Fixture providing paths to test data files for each year and a shared metadata folder.
    """
    git_version = get_git_output_run()
    path = S3_BASE_PATH
    return {
        "metadata": {
            "locations": f"{path}/metadata/locations.parquet",
            "asset_types": f"{path}/metadata/asset_types.parquet",
        },
        "yearly_data": {
            "2024": {
                "input": {
                    "asset_data": f"{path}/yearly_data/2024/input/asset_level_scores.parquet",
                    "portfolio_data": f"{path}/yearly_data/2024/input/propertysubtype_country_responseid_scores.parquet",
                    "survey_data": f"{path}/yearly_data/2024/input/survey_data.parquet",
                    "survey_table_data": f"{path}/yearly_data/2024/input/survey_table_data.parquet",
                },
                # locations of the files to compare the output with, the are the actual production data.
                "output_validation": {
                    "asset": f"{path}/yearly_data/2024/output_validation/asset/real_score_contribution_asset_data.parquet",
                    "portfolio": f"{path}/yearly_data/2024/output_validation/portfolio/real_score_contribution_portfolio_data.parquet",
                },
                # locations to write the output to, each run will be stored in a unique path
                "output": {
                    "asset": f"{path}/yearly_data/2024/output/{git_version}/asset/real_score_contribution_asset_output_data.csv",
                    "portfolio": f"{path}/yearly_data/2024/output/{git_version}/portfolio/real_score_contribution_portfolio_output_data.csv",
                },
            },
            # Optional: More years like "2025", "2026", etc.
        },
    }


@pytest.fixture
def get_config() -> ConfigType:
    return load_config(f"{os.path.dirname(os.path.dirname(os.path.dirname(__file__)))}")


def test_process_pipeline_multiple_levels(test_data, get_config):
    """
    Test the complete data pipeline for each year, verifying both 'asset' and 'portfolio' levels.
    """

    # 1. Load shared metadata
    locations_df = load_polars_from_file(
        test_data["metadata"]["locations"],
        file_type="parquet",
        columns=[
            col
            for cols in get_config["columns_to_read"]["locations"].values()
            for col in cols
        ],
    )
    asset_types_df = load_polars_from_file(
        test_data["metadata"]["asset_types"],
        file_type="parquet",
        columns=[
            col
            for cols in get_config["columns_to_read"]["asset_types"].values()
            for col in cols
        ],
    )

    # 2. Loop through each year
    for year, paths in test_data["yearly_data"].items():
        survey_data_df = load_polars_from_file(
            paths["input"]["survey_data"],
            file_type="parquet",
            columns=[
                col
                for cols in get_config["columns_to_read"]["survey_data"].values()
                for col in cols
            ],
        )
        survey_table_data_df = load_polars_from_file(
            paths["input"]["survey_table_data"],
            file_type="parquet",
            columns=[
                col
                for cols in get_config["columns_to_read"]["survey_table_data"].values()
                for col in cols
            ],
        )

        # 3. Test each level
        for level_name in ["asset", "portfolio"]:
            # Load input data for this year
            main_df = load_polars_from_file(
                paths["input"][f"{level_name}_data"], file_type="parquet"
            )
            # Skip if there's no matching output_validation file for that level
            if level_name not in paths["output_validation"]:
                logger.warning("Skipping level: %s", level_name)
                continue

            # 3.1 Run pipeline
            output_wide, output_long = process_pipeline(
                df=main_df,
                survey_table_data=survey_table_data_df,
                survey_data=survey_data_df,
                asset_types=asset_types_df,
                locations=locations_df,
                survey_year=int(year),
                column_mappings=get_config["column_mappings"],
                required_columns=get_config["required_columns"],
                columns_to_read=get_config["columns_to_read"],
                column_types=get_config["column_types"],
                level=level_name,
                config=get_config,
            )

            # 3.2 Compare frames
            result = compare_polars_df_with_file(
                output_wide,
                path=f"{paths['output_validation'][level_name]}",
                # This is required because for asset level, there are more columns on production than produced by this pipeline
                # because they also contain the columns for energy, ghg, water and waste tabs.
                columns=generate_all_variable_names(
                    get_config["columns_to_keep"][level_name]
                ),
                allow_float_tolerance=True,
                sort=True,
                sort_on=get_config["columns_to_keep"][level_name]["id_vars"],
                csv_schema_overrides={
                    "score_points_wat_cov_lc": pl.Float64,
                    "score_points_potential_wat_cov_lc": pl.Float64,
                    "asset_ownership": pl.Float64,
                    "wat_area_time_cov_p_lc": pl.Float64,
                },
                # compare 1:1 to the production data.
                is_subset=False,
            )
            # The result should be empty if the data matches
            try:
                assert result == []
                logger.info(
                    f"[For Data Year={year}, Level={level_name}] component test succeeded! "
                )
            except AssertionError:
                # Persist the result to a place on s3
                logger.error(
                    f"[For Data Year={year}, Level={level_name}] component test failed, columns: {result} do not match!"
                )
                mismatch_df = pl.DataFrame(
                    {
                        "column": [r["column"] for r in result],
                        "status": [r["status"] for r in result],
                        "details": [r["details"] for r in result],
                        "mismatches": [
                            r["mismatches"].to_dicts() for r in result
                        ],  # Store mismatches as list of dicts
                    }
                )
                write_data(
                    mismatch_df,
                    os.path.join(
                        os.path.dirname(paths["output"][level_name]),
                        "failed_output.csv",
                    ),
                )
                raise ValueError(
                    f"Validation failed for year={year}, level={level_name}"
                )
            # No matter match or not, persist the component tests result to a place on s3
            finally:
                write_data(
                    output_wide,
                    test_data["yearly_data"][year]["output"][level_name],
                )


if __name__ == "__main__":
    pytest.main(["-v", "--tb=short"])
