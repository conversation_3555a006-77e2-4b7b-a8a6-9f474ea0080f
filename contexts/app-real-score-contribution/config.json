{"columns_to_read": {"asset": {"id_vars": ["data_year", "company_fund_id", "response_id", "property_type_code", "country", "portfolio_asset_id", "asset_name", "asset_ownership", "asset_size", "asset_size_m2", "asset_size_sqft", "building_data_row_id"], "value_vars": ["score_en1", "score_gh1", "score_wt1", "score_ws1", "en_area_p_lc", "en_area_p_tc", "en_lfl_abs_wf", "en_lfl_abs_wd", "en_lfl_abs_we", "whole_building", "tenant_ctrl", "en_lfl_abs_lc_bc_ly", "en_lfl_abs_lc_bs_ly", "en_lfl_abs_lc_t_ly", "en_lfl_abs_tc_t_ly", "asset_size_common_m2", "asset_size_shared_m2", "asset_size_tenant_landlord_m2", "asset_size_tenant_tenant_m2", "en_lfl_abs_lc_o_ly", "en_lfl_abs_tc_o_ly", "en_lfl_percent_change_lc", "en_lfl_percent_change_tc", "score_en1_lfl_availability", "score_en1_en_area_time_cov_p", "score_en1_en_area_time_cov_p_lc", "score_en1_en_area_time_cov_p_tc", "score_en1_en_lfl_percent_change", "score_en1_en_lfl_percent_change_lc", "score_en1_en_lfl_percent_change_tc", "score_en1_en_ren_performance", "score_en1_en_ren_ons", "score_en1_en_ren_ofs"]}, "portfolio": {"id_vars": ["data_year", "response_id", "country", "property_type_code"], "value_vars": ["SCORE.F_EN_1", "SCORE.F_ANS_EN_1_TBL.AGGR_COV", "SCORE.F_ANS_EN_1_TBL.AGGR_LFL", "SCORE.F_ANS_EN_1_TBL.REN", "SCORE.F_GH_1", "SCORE.F_ANS_GH_1_TBL.AGGR_COV", "SCORE.F_ANS_GH_1_TBL.AGGR_LFL", "SCORE.F_WT_1", "SCORE.F_ANS_WT_1_TBL.AGGR_COV", "SCORE.F_ANS_WT_1_TBL.AGGR_LFL", "SCORE.F_ANS_WT_1_TBL.REC", "SCORE.F_WS_1", "SCORE.F_ANS_WS_1_TBL.MAIN", "SCORE.F_ANS_WS_1_TBL.AGGR", "R_1_TBL_AST"]}, "survey_table_data": {"id_vars": ["RESPONSE_ID", "COUNTRY", "PRT_TYPE", "R_1_TBL_AREA", "R_1_TBL_PGAV", "R_1_TBL_AST"]}, "survey_data": {"id_vars": ["RESPONSE_ID", "FUND_ID", "FUND_NAME", "RC_3_A"]}, "asset_types": {"id_vars": ["code", "name", "parent_code", "generation"]}, "locations": {"id_vars": ["a2_code", "name", "sub_region_name", "intermediate_region_name", "region_name", "super_region_name"]}}, "required_columns": {"asset": ["response_id", "data_year", "asset_size", "asset_ownership", "country", "score_en1", "score_gh1", "score_wt1", "score_ws1", "property_type_code"], "portfolio": ["score.f_en_1", "score.f_ans_en_1_tbl.aggr_cov", "score.f_ans_en_1_tbl.aggr_lfl", "score.f_ans_en_1_tbl.ren", "score.f_gh_1", "score.f_ans_gh_1_tbl.aggr_cov", "score.f_ans_gh_1_tbl.aggr_lfl", "score.f_wt_1", "score.f_ans_wt_1_tbl.aggr_cov", "score.f_ans_wt_1_tbl.aggr_lfl", "score.f_ans_wt_1_tbl.rec", "score.f_ws_1", "score.f_ans_ws_1_tbl.main", "score.f_ans_ws_1_tbl.aggr", "country"], "survey_table_data": ["r_1_tbl_area", "r_1_tbl_pgav", "prt_type", "response_id", "country"], "survey_data": ["response_id", "fund_id", "fund_name", "rc_3_a"], "asset_types": ["code", "name"], "locations": ["a2_code", "name", "sub_region_name", "intermediate_region_name", "region_name", "super_region_name"]}, "column_mappings": {"survey_data": {"fund_id": "company_fund_id"}, "survey_table_data": {"prt_type": "property_type_code"}, "asset": {"score_en1": "score_points_en_1", "score_gh1": "score_points_gh_1", "score_wt1": "score_points_wt_1", "score_ws1": "score_points_ws_1", "en_lfl_abs_lc_bc_ly": "en_lfl_abs_lc_bc_last_year", "en_lfl_abs_lc_bs_ly": "en_lfl_abs_lc_bs_last_year", "en_lfl_abs_lc_t_ly": "en_lfl_abs_lc_t_last_year", "en_lfl_abs_tc_t_ly": "en_lfl_abs_tc_t_last_year", "en_lfl_abs_lc_o_ly": "en_lfl_abs_lc_o_last_year", "en_lfl_abs_tc_o_ly": "en_lfl_abs_tc_o_last_year"}, "asset_types": {"code": "property_type_code", "name": "property_type_name"}, "locations": {"a2_code": "country", "name": "country_name", "sub_region_name": "sub_region", "intermediate_region_name": "inter_region", "region_name": "region", "super_region_name": "super_region"}}, "column_types": {"survey_table_data": {"r_1_tbl_area": "float", "r_1_tbl_pgav": "float"}, "portfolio": {}}, "score_map": {"portfolio": {"fraction_var": ["score.f_en_1", "score.f_ans_en_1_tbl.aggr_cov", "score.f_ans_en_1_tbl.aggr_lfl", "score.f_ans_en_1_tbl.ren", "score.f_gh_1", "score.f_ans_gh_1_tbl.aggr_cov", "score.f_ans_gh_1_tbl.aggr_lfl", "score.f_wt_1", "score.f_ans_wt_1_tbl.aggr_cov", "score.f_ans_wt_1_tbl.aggr_lfl", "score.f_ans_wt_1_tbl.rec", "score.f_ws_1", "score.f_ans_ws_1_tbl.main", "score.f_ans_ws_1_tbl.aggr"], "points_var": ["score_points_en_1", "score_points_en_cov", "score_points_en_lfl", "score_points_en_ren", "score_points_gh_1", "score_points_ghg_cov", "score_points_ghg_lfl", "score_points_wt_1", "score_points_wat_cov", "score_points_wat_lfl", "score_points_wat_rec", "score_points_ws_1", "score_points_was_cov", "score_points_was_div"], "weight_var": ["portfolio_weight_frac", "portfolio_weight_frac", "portfolio_weight_frac", "portfolio_weight_frac", "portfolio_weight_frac", "portfolio_weight_frac", "portfolio_weight_frac", "portfolio_weight_frac", "portfolio_weight_frac", "portfolio_weight_frac", "portfolio_weight_frac", "portfolio_weight_frac", "portfolio_weight_frac", "portfolio_weight_frac"], "max_points": [14, 8.5, 2.5, 3, 7, 5, 2, 7, 4, 2, 1, 4, 2, 2]}, "asset": {"fraction_var": ["score_en_1_frac", "score_gh_1_frac", "score_wt_1_frac", "score_ws_1_frac"], "points_var": ["score_points_en_1", "score_points_gh_1", "score_points_wt_1", "score_points_ws_1"], "weight_var": ["asset_weight_frac", "asset_weight_frac", "asset_weight_frac", "asset_weight_frac"], "max_points": [14, 7, 7, 4]}, "energy": {"fraction_var": ["score_en1_en_area_time_cov_p", "score_en1_en_area_time_cov_p_lc", "score_en1_en_area_time_cov_p_tc", "score_en1_en_lfl_percent_change", "score_en1_en_lfl_percent_change_lc", "score_en1_en_lfl_percent_change_tc", "score_en1_lfl_availability", "score_en1_lfl_availability_lc", "score_en1_lfl_availability_tc", "score_en1_en_ren_performance", "score_en1_en_ren_ons", "score_en1_en_ren_ofs"], "points_var": ["score_points_en_cov_sub", "score_points_en_cov_sub_lc", "score_points_en_cov_sub_tc", "score_points_en_lfl_perf", "score_points_en_lfl_perf_lc", "score_points_en_lfl_perf_tc", "score_points_en_lfl_avail", "score_points_en_lfl_avail_lc", "score_points_en_lfl_avail_tc", "score_points_en_ren_perf", "score_points_en_ren_ons", "score_points_en_ren_ofs"], "weight_var": ["asset_weight_frac", "weight_frac_en_cov_lc", "weight_frac_en_cov_tc", "asset_weight_frac_en_lfl", "weight_frac_en_lfl_lc", "weight_frac_en_lfl_tc", "asset_weight_frac_en_lfl", "asset_weight_frac_en_lfl", "asset_weight_frac_en_lfl", "asset_weight_frac", "asset_weight_frac", "asset_weight_frac"], "max_points": [8.5, 8.5, 8.5, 2, 2, 2, 0.5, 0.5, 0.5, 2, 1, 0.5]}}, "total_score_map": {"portfolio": {"fraction_var": ["score_total"], "points_var": ["score_points_total"], "weight_var": ["portfolio_weight_frac"], "max_points": [32]}, "asset": {"fraction_var": ["score_total", "score_en_1_frac", "score_en_cov", "score_en_cov_lc", "score_en_cov_tc", "score_en_lfl", "score_en_lfl_lc", "score_en_lfl_tc", "score_en_ren", "score_en_ren_avail"], "points_var": ["score_points_total", "score_points_en_1", "score_points_en_cov", "score_points_en_cov_lc", "score_points_en_cov_tc", "score_points_en_lfl", "score_points_en_lfl_lc", "score_points_en_lfl_tc", "score_points_en_ren", "score_points_en_ren_avail"], "weight_var": ["asset_weight_frac", "asset_weight_frac", "asset_weight_frac", "weight_frac_en_cov_lc", "weight_frac_en_cov_tc", "asset_weight_frac_en_lfl", "weight_frac_en_lfl_lc", "weight_frac_en_lfl_tc", "asset_weight_frac", "asset_weight_frac"], "max_points": [32, 14, 8.5, 8.5, 8.5, 2.5, 2.5, 2.5, 3, 1]}}, "columns_to_keep": {"asset": {"id_vars": ["portfolio_asset_id", "asset_name", "response_id", "company_fund_id", "fund_name", "country_name", "sub_region", "region", "property_sector", "property_type", "property_subtype", "asset_size_m2", "asset_size_sqft", "area_unit", "asset_ownership", "en_area_p_lc", "en_area_p_tc", "asset_weight_percent", "asset_weight_percent_en_lfl"], "score_vars": ["score_points_en_1", "score_points_gh_1", "score_points_wt_1", "score_points_ws_1", "score_points_total", "score_points_en_cov", "score_points_en_lfl", "score_points_en_ren"]}, "asset_long": {"id_vars": ["response_id", "portfolio_asset_id"], "weight_vars": ["asset_weight_percent"], "score_vars": ["score_type", "points", "contribution"]}, "portfolio": {"id_vars": ["response_id", "company_fund_id", "fund_name", "country_name", "sub_region", "region", "property_subtype", "property_type", "property_sector", "r_1_tbl_ast", "area_m2", "area_sqft", "area_unit", "r_1_tbl_pgav"], "score_vars": ["score_points_en_1", "score_points_en_cov", "score_points_en_lfl", "score_points_en_ren", "score_points_gh_1", "score_points_ghg_cov", "score_points_ghg_lfl", "score_points_wt_1", "score_points_wat_cov", "score_points_wat_lfl", "score_points_wat_rec", "score_points_ws_1", "score_points_was_cov", "score_points_was_div", "score_points_total"]}, "portfolio_long": {"id_vars": ["response_id", "country_name", "property_subtype"], "weight_vars": ["r_1_tbl_pgav"], "score_vars": ["score_type", "points", "contribution"]}}, "conversion": {"sqft_to_sqm": 0.092903}}