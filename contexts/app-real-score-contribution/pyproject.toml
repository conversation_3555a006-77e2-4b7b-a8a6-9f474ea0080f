[tool.poetry]
name = "app-real-score-contribution"
version = "0.1.0"
description = ""
authors = ["yangcao <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.13"
polars = "^1.23.0"
click = "^8.1.8"
smart-open = "^7.1.0"
python-dotenv = "^1.0.1"
gresb_utils = { path = "../../shared/gresb-utils", develop = true }


[tool.poetry.group.dev.dependencies]
pytest = "^8.3.4"
pre-commit = "^3.6.2"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
