import polars as pl
import logging

from polars.exceptions import ColumnNotFoundError

from app_real_score_contribution.enums import PolarsDtype

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
handler = logging.StreamHandler()
handler.setFormatter(logging.Formatter("%(asctime)s - %(levelname)s - %(message)s"))
logger.addHandler(handler)


def validate_columns(df: pl.DataFrame, required_columns: list[str]) -> None:
    """
    Validate that the DataFrame contains all required columns.
    df: pl.DataFrame: The DataFrame to validate.
    required_columns: List[str]: List of required column names.
    """
    missing = [col for col in required_columns if col not in df.columns]
    if missing:
        raise ValueError(f"Missing required columns: {missing}")


def cast_column_type(df: pl.DataFrame, type_mapping: dict[str, str]) -> pl.DataFrame:
    """
    Cast columns to specified Polars data types.

    Args:
        df (pl.DataFrame): The DataFrame to cast.
        type_mapping (dict[str, str]): Column name to type mapping (e.g., {"column_name": "float"}).

    Returns:
        pl.DataFrame: The DataFrame with casted column types.
    """
    try:
        cast_exprs = []
        for column, dtype_str in type_mapping.items():
            polars_dtype = PolarsDtype.from_string(dtype_str)
            cast_exprs.append(pl.col(column).cast(polars_dtype))

        return df.with_columns(cast_exprs)  # Apply all casting in one go (efficient)

    except ColumnNotFoundError as e:
        logger.error(f"Column '{column}' not found in DataFrame: {e}")
        raise
    except ValueError as e:
        logger.error(f"Invalid data type '{dtype_str}' for column '{column}': {e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected Error casting columns: {e}")
        raise


def validate_and_rename(
    name: str,
    df: pl.DataFrame,
    required_cols: list[str],
    column_mappings: dict[str, str],
    column_types: dict[str, str],
) -> pl.DataFrame:
    """
    - Drop columns with empty names.
    - Rename all columns to lowercase.
    - Validate required columns.
    - Rename columns using column_mappings.
    - Cast columns using column_types.

    Args:
        name (str): Name of the DataFrame.
        df (pl.DataFrame): Input Polars DataFrame.
        required_cols (List[str]): List of required columns for gresb_utils.
        column_mappings (Dict[str, str]): Column mappings for renaming.
        column_types (Dict[str, str]): Column types for casting.

    Returns:
        pl.DataFrame: Processed and validated DataFrame.
    """
    # Convert all column names to lowercase
    df = df.rename({column: column.lower() for column in df.columns if column})
    # Validate if all required columns are present
    validate_columns(df, required_cols)
    # Rename columns using column_mappings
    try:
        df = df.rename(column_mappings)
    except Exception as e:
        logger.error(
            f"Error renaming DataFrame: {name} with error: {e}, column_mappings: {column_mappings}"
        )
        raise
    # Cast columns using column_types, normally this is only needed for csv files not for parquet.
    df = cast_column_type(df, column_types)
    return df
