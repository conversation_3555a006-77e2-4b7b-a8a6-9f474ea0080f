import polars as pl


def calculate_points_and_contributions(
    data: pl.DataFrame, score_map: dict, assign_missing_to_zero: bool = False
) -> pl.DataFrame:
    """
    Adds points and contribution columns based on fraction columns to the DataFrame.

    For each mapping (fraction_var, points_var, weight_var, max_points) in score_map:
      - Optionally, assign missing values in the fraction_var column to zero.
      - Compute score points: fraction_var * max_points.
      - Compute potential: max_points - score points.
      - Compute contribution: score points multiplied by weight_var.
      - Compute contribution potential: potential multiplied by weight_var.

    These computed columns are then combined horizontally and appended to the original DataFrame.

    Args:
        data (pl.DataFrame): The input DataFrame.
        score_map (dict): A dictionary with keys:
            - "fraction_var": list of old score column names.
            - "points_var": list of new score column names.
            - "weight_var": list of weight column names.
            - "max_points": list of maximum point values (numeric).
        assign_missing_to_zero (bool): Whether to replace missing values with zero for the fraction_var column.

    Returns:
        pl.DataFrame: The updated DataFrame with new score columns appended.
    """

    for fraction_var, points_var, weight_var, max_points in zip(
        score_map["fraction_var"],
        score_map["points_var"],
        score_map["weight_var"],
        score_map["max_points"],
    ):
        # Only need to do this with LFL data, they can be NA, but we need them to be zero to calculate the actual points.
        frac = (
            pl.col(fraction_var).fill_null(0)
            if assign_missing_to_zero
            else pl.col(fraction_var)
        )
        exprs = [
            # calculate asset score points from fractions
            (frac * max_points).alias(points_var),
            # calculate potential points by subtracting asset score points from max points
            (max_points - frac * max_points).alias(
                points_var.replace("points", "points_potential")
            ),
            # calculate points contribution and potential points contribution by multiplying asset score points with weight
            ((frac * max_points) * pl.col(weight_var)).alias(
                points_var.replace("points", "contribution")
            ),
            # calculate potential points contribution by multiplying potential points with weight
            ((max_points - frac * max_points) * pl.col(weight_var)).alias(
                points_var.replace("points", "contribution_potential")
            ),
        ]
        data = data.with_columns(exprs)

    return data


def sum_all_performance_points(df: pl.DataFrame) -> pl.DataFrame:
    """
    Adds a column with the sum of all score points from the performance indicators.
    This is required because it's missing from the asset level scores.

    Ideally all total scores needed for REAL score contribution will be added to
    the output of the scoring model in the future, so we don't need to have these
    separate "total" score calculations here. The indicator vars have to be
    hard-coded here because the BC indicator scores should not be included in the "total".

    """
    indicator_vars = [
        "score_points_en_1",
        "score_points_gh_1",
        "score_points_wt_1",
        "score_points_ws_1",
    ]

    return df.with_columns(
        pl.sum_horizontal(indicator_vars).alias("score_points_total")
    )


def compute_fractions(
    df: pl.DataFrame,
    source_cols: list[str],
    denominators: list[str],
    dest_cols: list[str] | None = None,
    *,
    drop_source: bool = True
) -> pl.DataFrame:
    """
    Divide each source column by a denominator (column name or literal) and
    write the result into dest_cols (or overwrite source_cols if dest_cols is None).

    Args:
        df: Input DataFrame.
        source_cols: Names of columns to divide.
        denominators: Either a list of floats or a list of column names; must
            be the same length as source_cols.
        dest_cols: Names for the new columns. If None, source_cols will be
            overwritten.
        drop_source: If True and dest_cols is given, drop the original source_cols.
    """
    if dest_cols is None:
        dest_cols = list(source_cols)
    if not (len(source_cols) == len(denominators) == len(dest_cols)):
        raise ValueError(
            "source_cols, denominators, and dest_cols must all be same length"
        )

    exprs = []
    for src, denom, dst in zip(source_cols, denominators, dest_cols):
        rhs = pl.col(src) / (pl.col(denom) if isinstance(denom, str) else denom)
        exprs.append(rhs.alias(dst))

    out = df.with_columns(exprs)
    if drop_source and dest_cols is not source_cols:
        out = out.drop(source_cols)
    return out
