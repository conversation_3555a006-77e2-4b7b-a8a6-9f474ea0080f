from __future__ import annotations

import polars as pl
from polars import DataFrame
import logging

from app_real_score_contribution.area import add_areas
from app_real_score_contribution.asset_types import add_property_type_groups
from app_real_score_contribution.energy.lfl import (
    add_last_year_lfl_consumption,
    add_extra_areas_columns_per_control,
    add_extra_lfl_consumption,
    set_lfl_scores_back_to_missing,
)
from app_real_score_contribution.energy.tableau import (
    clean_lfl_data,
    clean_offsite_scores,
)
from app_real_score_contribution.energy.utils import (
    calc_total_scores as calc_total_scores_energy,
)
from app_real_score_contribution.energy.weight import (
    add_lfl_asset_weights,
    add_weights_per_control,
)
from app_real_score_contribution.portfolio_weights import add_portfolio_weights
from app_real_score_contribution.prepare_output import subset_data, process_long_data
from app_real_score_contribution.config_types import ConfigType
from app_real_score_contribution.score_contribution import (
    calculate_points_and_contributions,
    sum_all_performance_points,
    compute_fractions,
)
from app_real_score_contribution.validation import validate_and_rename
from app_real_score_contribution.locations import add_location_groups
from app_real_score_contribution.fund import add_fund_characteristics
from app_real_score_contribution.report_boundaries import add_reporting_boundaries_data
from app_real_score_contribution.asset_weights import (
    add_asset_weights,
    calc_fractional_scores_per_indicator,
)
from app_real_score_contribution.utils import get_current_year_data

from dotenv import load_dotenv


load_dotenv()

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
handler = logging.StreamHandler()
handler.setFormatter(logging.Formatter("%(asctime)s - %(levelname)s - %(message)s"))
logger.addHandler(handler)


def process_pipeline(
    df: pl.DataFrame,
    survey_table_data: pl.DataFrame,
    survey_data: pl.DataFrame,
    asset_types: pl.DataFrame,
    locations: pl.DataFrame,
    survey_year: int,
    column_mappings: dict[str, dict[str, str]],
    required_columns: dict[str, list[str]],
    columns_to_read: dict[str, dict[str, list[str]]],
    column_types: dict[str, dict[str, str]],
    level: str,  # "asset" or "portfolio"
    config: ConfigType,
) -> tuple[DataFrame, DataFrame]:
    """
    Unified data processing pipeline for asset or portfolio level.
    df: pl.DataFrame: The main DataFrame, either asset level or portfolio level.
    survey_table_data: pl.DataFrame: The survey table DataFrame.
    survey_data: pl.DataFrame: The survey data DataFrame.
    asset_types: pl.DataFrame: The asset types DataFrame
    locations: pl.DataFrame: The locations DataFrame.
    survey_year: int: The survey year.
    column_mappings: dict[str, str]: Column mappings for renaming.
    required_columns: dict[str, list[str]]: Required columns for gresb_utils.
    columns_to_read: dict[str, dict[str, list[str]]]: extract ID columns from this for sorting.
    column_types: dict[str, dict[str, str]]: Column types for casting.
    level: str: Processing level ("asset" or "portfolio").

    return tuple[DataFrame, DataFrame]: Processed DataFrames (wide and long format).
    """
    try:
        # Step 1: clean and validate all DataFrames
        dataframes: dict[
            str,
            tuple[
                pl.DataFrame,
                list[str],
                dict[str, str],
                dict[str, str],
                dict[str, list[str]],
            ],
        ] = {
            "df": (
                df,
                required_columns[level],
                column_mappings.get(level, {}),
                column_types.get(level, {}),
                columns_to_read.get(level, {}),
            ),
            "survey_table_data": (
                survey_table_data,
                required_columns["survey_table_data"],
                column_mappings.get("survey_table_data", {}),
                column_types.get("survey_table_data", {}),
                columns_to_read.get("survey_table_data", {}),
            ),
            "survey_data": (
                survey_data,
                required_columns["survey_data"],
                column_mappings.get("survey_data", {}),
                column_types.get("survey_data", {}),
                columns_to_read.get("survey_data", {}),
            ),
            "locations": (
                locations,
                required_columns["locations"],
                column_mappings.get("locations", {}),
                column_types.get("locations", {}),
                columns_to_read.get("locations", {}),
            ),
            "asset_types": (
                asset_types,
                required_columns["asset_types"],
                column_mappings.get("asset_types", {}),
                column_types.get("asset_types", {}),
                columns_to_read.get("asset_types", {}),
            ),
        }

        # Step 1: Sort dataframes on id columns for query efficiency and readability
        sorted_dataframes = {
            name: (
                (
                    df.sort(sort_columns_dict["id_vars"])
                    if "id_vars" in sort_columns_dict
                    and set(sort_columns_dict["id_vars"]).issubset(df.columns)
                    else df
                ),
                req_cols,
                column_mapping,
                column_types,
                sort_columns_dict,
            )
            for name, (
                df,
                req_cols,
                column_mapping,
                column_types,
                sort_columns_dict,
            ) in dataframes.items()
        }

        # Step 2: Apply validation and renaming after sorting
        processed_dfs = {
            name: validate_and_rename(name, df, req_cols, column_mapping, column_types)
            for name, (
                df,
                req_cols,
                column_mapping,
                column_types,
                _,
            ) in sorted_dataframes.items()
        }
        df = processed_dfs["df"]
        survey_table_data = processed_dfs["survey_table_data"]
        survey_data = processed_dfs["survey_data"]
        locations = processed_dfs["locations"]
        asset_types = processed_dfs["asset_types"]

        if level == "asset":
            # specific for energy tab
            df = add_last_year_lfl_consumption(df, survey_year)
        df = get_current_year_data(df, survey_year)
        df = add_property_type_groups(df, asset_types)
        df = add_location_groups(df, locations)
        df = add_fund_characteristics(df, survey_data)
        df = add_reporting_boundaries_data(df, survey_table_data)

        if level == "asset":
            # Required for asset overview tab:
            df = add_asset_weights(df)
            # This is specially required for asset overview tab, since the variable names are different, missing fractional scores.
            df = calc_fractional_scores_per_indicator(df, config["score_map"][level])
            # Required for energy tab
            df = add_lfl_asset_weights(df, "en")
            df = add_extra_areas_columns_per_control(df)
            df = add_extra_lfl_consumption(df)
            df = add_weights_per_control(df)
            df = clean_lfl_data(df)

        elif level == "portfolio":
            df = add_areas(df)
            df = add_portfolio_weights(df)

        else:
            raise ValueError(
                f"Invalid level: {level}. Expected 'asset' or 'portfolio'."
            )

        # Calculate points and contributions based on fractions. Required for both asset and portfolio overview.
        # Keep the score_map separate for asset level and energy specific ones,
        # because calc_fractional_scores_per_indicator function is only applicable for asset overview
        # it will complain the points var of energy tab for example score_points_en_cov_sub doesn't exit in the dataframe, which is
        # only calculated by the line below in calculate_points_and_contributions
        df = calculate_points_and_contributions(
            df,
            score_map={
                k: config["score_map"][level][k]
                + (config["score_map"]["energy"][k] if level == "asset" else [])
                for k in config["score_map"][level]
            },
            assign_missing_to_zero=True,
        )

        if level == "asset":
            # # specific to the energy tab #
            df = calc_total_scores_energy(df)

        # repeat this for total separately because we needed to calculate the total points from the indicators first
        df = sum_all_performance_points(df)
        # total_score_map includes both vars for the asset overview page and energy page.
        df = compute_fractions(
            df,
            source_cols=config["total_score_map"][level]["points_var"],
            denominators=config["total_score_map"][level]["max_points"],
            dest_cols=config["total_score_map"][level]["fraction_var"],
            drop_source=True,
        )

        df = calculate_points_and_contributions(
            df, score_map=config["total_score_map"][level]
        )
        if level == "asset":
            # specific to the energy tab
            df = clean_offsite_scores(df)
            df = set_lfl_scores_back_to_missing(df, "en")

        wide_df = subset_data(df, config["columns_to_keep"][level])
        long_df = process_long_data(
            df,
            id_vars=config["columns_to_keep"][f"{level}_long"]["id_vars"],
            # There should be only one weight var, it's either GAV or floor area (more granular than portfolio level).
            weight_var=config["columns_to_keep"][f"{level}_long"]["weight_vars"][0],
            total_score="total",
            subscores=["en_1", "gh_1", "wt_1", "ws_1"],
            clean_scores=True,
        )

        return wide_df, long_df

    except Exception as err:
        logger.exception("Unexpected error in the pipeline")
        raise RuntimeError(f"Unexpected error in the pipeline: {err}") from err
