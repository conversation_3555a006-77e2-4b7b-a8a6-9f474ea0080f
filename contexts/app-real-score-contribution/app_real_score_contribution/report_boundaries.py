import polars as pl
from app_real_score_contribution.constants import (
    RESPONSE_ID,
    PRT_TYPE_CODE,
    COUNTRY_CODE,
    R_1_TBL_AREA,
    R_1_TBL_PGAV,
    R_1_TBL_AST,
)


def add_reporting_boundaries_data(
    main_df: pl.DataFrame, survey_table_data: pl.DataFrame
) -> pl.DataFrame:
    """
    Adds reporting boundaries data to the main_df DataFrame (could be asset or portfolio level)
    based on survey_data.

    Args:
        main_df (pl.DataFrame): The main asset data DataFrame.
        survey_table_data (pl.DataFrame): A DataFrame containing 'survey_table_data'

    Returns:
        pl.DataFrame: The updated main_df DataFrame with reporting boundaries data joined.
    """
    rb_data = survey_table_data.filter(
        # Filter out rows where R_1_TBL_PGAV is not null, in theory this is not required, since main_df already
        # filtered out the assets that are development assets which do not have R_1_TBL_PGAV
        pl.col(R_1_TBL_PGAV).is_not_null()
    ).select(
        [
            RESPONSE_ID,
            PRT_TYPE_CODE,
            COUNTRY_CODE,
            R_1_TBL_AREA,
            R_1_TBL_PGAV,
            R_1_TBL_AST,
        ]
    )

    # Find the shared columns between main_df and rb_data
    shared_cols = list(set(main_df.columns) & set(rb_data.columns))

    return main_df.join(
        # Required due to:
        # polars.exceptions.SchemaError: datatypes of join keys don't match - `r_1_tbl_ast`: f64 on left does not match `r_1_tbl_ast`: i32 on right
        # Note r_1_tbl_ast only exists on portfolio level data, not on asset level.
        rb_data.with_columns(pl.col("r_1_tbl_ast").cast(pl.Int64)),
        on=shared_cols,
        how="left",
    )
