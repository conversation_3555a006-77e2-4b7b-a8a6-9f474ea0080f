import polars as pl
from app_real_score_contribution.constants import (
    COUNTRY_NAME,
    SUB_REGION,
    INTER_REGION,
    REGION,
    SUPER_REGION,
    COUNTRY_CODE,
)


def add_location_groups(data: pl.DataFrame, locations: pl.DataFrame) -> pl.DataFrame:
    """
    Adds location-related columns to the data based on locations mapping.

    Args:
        data (pl.DataFrame): The main DataFrame containing 'country'.
        locations (pl.DataFrame): The locations DataFrame containing country codes and related information.

    Returns:
        pl.DataFrame: DataFrame with added location-related columns.
    """
    # Early return for empty DataFrame
    if data.is_empty():
        return pl.DataFrame(
            {
                COUNTRY_CODE: [],
                COUNTRY_NAME: [],
                SUB_REGION: [],
                INTER_REGION: [],
                REGION: [],
                SUPER_REGION: [],
            }
        )

    joined_data = data.join(locations, on=COUNTRY_CODE, how="left")
    # Only keep the columns we need
    cols = [COUNTRY_NAME, SUB_REGION, INTER_REGION, REGION, SUPER_REGION]
    data = data.with_columns([joined_data[col] for col in cols])

    return data
