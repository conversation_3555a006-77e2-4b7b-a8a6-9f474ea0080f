import polars as pl

from app_real_score_contribution.constants import R_1_TBL_PGAV, PORTFOLIO_WEIGHT_FRAC


def add_portfolio_weights(data: pl.DataFrame) -> pl.DataFrame:
    """
    Adds a 'portfolio_weight_frac' column to the DataFrame,
    calculated as r_1_tbl_pgav divided by 100.

    Args:
        data (pl.DataFrame): The input DataFrame.

    Returns:
        pl.DataFrame: The DataFrame with the new column added.
    """
    return data.with_columns((pl.col(R_1_TBL_PGAV) / 100).alias(PORTFOLIO_WEIGHT_FRAC))
