import polars as pl
from app_real_score_contribution import constants  # Ensure this module is available


def add_areas(df: pl.DataFrame) -> pl.DataFrame:
    """
    Adds area_m2 and area_sqft columns to the DataFrame based on the area_unit.

    - If area_unit is "m2", then area_m2 equals r_1_tbl_area.
      Otherwise, area_m2 is calculated by converting from sqft to sqm.

    - If area_unit is "sqft", then area_sqft equals r_1_tbl_area.
      Otherwise, area_sqft is calculated by converting from sqm to sqft.

    Args:
        df (pl.DataFrame): The input DataFrame.

    Returns:
        pl.DataFrame: The DataFrame with added area_m2 and area_sqft columns.
    """
    df = df.with_columns(
        [
            pl.when(pl.col("area_unit") == "m2")
            .then(pl.col("r_1_tbl_area"))
            .otherwise(pl.col("r_1_tbl_area") * constants.SQFT_TO_SQM)
            .alias("area_m2"),
            pl.when(pl.col("area_unit") == "sqft")
            .then(pl.col("r_1_tbl_area"))
            .otherwise(pl.col("r_1_tbl_area") / constants.SQFT_TO_SQM)
            .alias("area_sqft"),
        ]
    )
    return df
