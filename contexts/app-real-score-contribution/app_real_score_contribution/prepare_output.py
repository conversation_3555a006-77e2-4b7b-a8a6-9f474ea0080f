import polars as pl
import re
from typing import List, Optional


def generate_all_variable_names(column_config: dict[str, list[str]]) -> List[str]:

    score_vars = column_config.get("score_vars", [])
    potential_vars = [s.replace("points", "points_potential") for s in score_vars]
    contribution_vars = [s.replace("points", "contribution") for s in score_vars]
    contribution_potential_vars = [
        s.replace("points", "contribution_potential") for s in score_vars
    ]

    return (
        column_config.get("id_vars", [])
        + score_vars
        + potential_vars
        + contribution_vars
        + contribution_potential_vars
        + column_config.get("weight_vars", [])
    )


def subset_data(
    data_frame: pl.DataFrame, column_config: dict[str, list[str]]
) -> pl.DataFrame:
    """
    Subset the DataFrame based on explicit and derived column names.
    TODO: this should not be needed anymore, remove when complete all the tabs: energy, ghg, water, waste and BC.
    Right now it's still required due to some of the columns in the asset file not being used in the final output yet (only asset overview tab is complete).
    https://app.clickup.com/t/8698fj0xf

    Args:
        data_frame (pl.DataFrame): The full input DataFrame.
        column_config (Dict[str, list[str]]): Dict with:
            - id_vars: List of identifier columns.
            - score_vars: List of base score columns (points).
            - weight_vars: Optional list of weight columns.

    Returns:
        pl.DataFrame: Subset of input with required columns.
    """
    # Extract required columns
    final_columns = generate_all_variable_names(column_config)

    # Validate existence
    missing = [col for col in final_columns if col not in data_frame.columns]
    if missing:
        raise ValueError(f"Missing expected columns: {set(missing)}")

    return data_frame.select(final_columns)


def pivot_scores_longer(
    data: pl.DataFrame,
    id_vars: list[str],
    weight_var: str,
    total_score: str,
    subscores: list[str],
) -> pl.DataFrame:
    """
    Pivot score columns to longer format using Polars `unpivot`for Tableau.

    Args:
        data: Input DataFrame in wide format
        id_vars: List of identifier columns
        weight_var: Column name for weight variable
        total_score: The name of the total score, default is just string "total"
        subscores: List of subscore column names, e.g., ["en_1", "gh_1", "wt_1", "ws_1"]

    Returns:
        pl.DataFrame: A long-format DataFrame with 'response_id', 'portfolio_asset_id',
                      'score_type', 'points', 'contribution'.
    """

    # Define regex pattern to match either points or contribution column
    score_type_regex = r"(points|contribution)"
    total_score_potential = f"potential_{total_score}"
    subscore_regex = f"{'|'.join([total_score] + subscores + [total_score_potential])}"
    # Get all the columns which are variables (either a score or a contribution)
    var_pattern = re.compile(rf"^score_({score_type_regex})_({subscore_regex})$")
    pivot_vars = [col for col in data.columns if var_pattern.match(col)]
    if not pivot_vars:
        raise ValueError(f"No matching score columns found. Available: {data.columns}")
    # These are the columns that will be used as index columns which stay the same after unpivoting
    id_cols = id_vars + [weight_var]

    # Unpivot selected columns into long format
    long_data = data.unpivot(
        index=id_cols,
        on=pivot_vars,
        variable_name="score_variable",
        value_name="score_value",
    )

    # Extract `score_type` (points/contribution) and `indicator` (e.g., en_1, gh_1)
    long_data = long_data.with_columns(
        pl.col("score_variable")
        .str.extract(r"^score_(points|contribution)_", group_index=1)
        .alias("score_type"),
        pl.col("score_variable")
        .str.extract(rf"_({subscore_regex})$", group_index=1)
        .alias("indicator"),
    ).drop("score_variable")

    # Pivot back to separate `points` and `contribution`
    long_data = long_data.pivot(
        values="score_value", index=id_cols + ["indicator"], on="score_type"
    ).rename({"indicator": "score_type"})

    return long_data


def process_long_data(
    data: pl.DataFrame,
    id_vars: List[str],
    weight_var: str,
    total_score: str,
    subscores: List[str],
    add_metric: bool = False,
    metric: Optional[str] = None,
    add_control_types: bool = False,
    control_types: Optional[List[str]] = None,
    clean_scores: bool = False,
    percent_area_under_control: str = "en_area_p",
) -> pl.DataFrame:
    """
    This function-of-functions pivots the wide data longer, so that we can create
    multiple stacked bar charts in a single worksheet in tableau. The resulting
    tables are tuned for the specific visualizations in tableau, and should not
    be used for any kind of analysis outside of these specific charts.

    Args:
        data: Input DataFrame in wide format
        id_vars: List of identifier columns
        weight_var: Column name for weight variable
        total_score: Name of the total score column
        subscores: List of subscore column names
        add_metric: Whether to add metric columns
        metric: Name of the metric column to add
        add_control_types: Whether to add control type columns
        control_types: List of control type names
        clean_scores: Whether to clean score values
        percent_area_under_control: Column name for percent area under control

    Returns:
        DataFrame in long format for visualization
    """
    scores = pivot_scores_longer(
        data,
        id_vars=id_vars,
        weight_var=weight_var,
        total_score=total_score,
        subscores=subscores,
    )

    if add_metric:
        if not metric:
            raise ValueError("Metric name must be provided when add_metric is True")
        metrics = data.select(["portfolio_asset_id", metric])
        scores = scores.join(metrics, on="portfolio_asset_id")

    if add_control_types:
        if not control_types:
            raise ValueError(
                "Control types must be provided when add_control_types is True"
            )

        # If we need control types as an id column, it also needs to be present in
        # the overall scores dataset
        scores = scores.with_columns(pl.lit("all").alias("control"))

        scores_by_control = pivot_scores_by_control(
            data, id_vars, total_score, subscores, control_types
        )
        weights_by_control = pivot_weights_by_control(
            data, id_vars, weight_var, total_score, control_types
        )
        scores_by_control = scores_by_control.join(
            weights_by_control, on=["portfolio_asset_id", "control"]
        )

        if add_metric and metric:
            metrics_by_control = pivot_metrics_by_control(
                data, id_vars, metric, control_types
            )
            scores_by_control = scores_by_control.join(
                metrics_by_control, on=["portfolio_asset_id", "control"]
            )

        scores = pl.concat([scores, scores_by_control])
        areas_by_control = pivot_areas_by_control(
            data, id_vars, control_types, percent_area_under_control
        )
        scores = clean_per_control_scores(scores, areas_by_control)

    if clean_scores:
        scores = remove_unneeded_scores(scores, subscores, total_score)

    # Fix the name of the weight column for LFL data to ensure that the schema for
    # all long-format asset tables are the same. Not strictly necessary, but makes the table name cleaner.
    # Only relevant for en, gh, wt tabs.
    if re.search(r"asset_weight_percent.*lfl", weight_var):
        scores = scores.rename({weight_var: "asset_weight_percent"})

    return scores


def remove_unneeded_scores(
    data: pl.DataFrame, point_scores: List[str], contribution_score: str
) -> pl.DataFrame:
    """
    Remove unneeded score columns from the DataFrame for Tableau visualization.

    Args:
        data: DataFrame with scores in long format
        point_scores: List of point score column names to be assigned to None
        contribution_score: Name of the contribution score column to be assigned to None

    Returns:
        DataFrame with cleaned score values
    """
    # For score breakdown in points -- show per indicator + potential, but not total
    data = data.with_columns(
        pl.when(pl.col("score_type") == contribution_score)
        .then(None)
        .otherwise(pl.col("points"))
        .alias("points")
    )

    # For score contribution -- show only contribution as a total + potential, but
    # not the contribution per indicator
    data = data.with_columns(
        pl.when(pl.col("score_type").is_in(point_scores))
        .then(None)
        .otherwise(pl.col("contribution"))
        .alias("contribution")
    )

    return data


def pivot_scores_by_control(
    data: pl.DataFrame,
    id_vars: List[str],
    total_score: str,
    subscores: List[str],
    control_types: List[str],
) -> pl.DataFrame:
    score_type_regex = r"points|contribution"
    total_score_potential = f"potential_{total_score}"
    subscore_regex = f"{'|'.join([total_score] + subscores + [total_score_potential])}"
    control_regex = f"{'|'.join(control_types)}"

    var_pattern = re.compile(
        rf"^score_({score_type_regex})_({subscore_regex})_({control_regex})$"
    )
    pivot_vars = [col for col in data.columns if var_pattern.match(col)]
    long_data = data.unpivot(
        index=id_vars,
        on=pivot_vars,
        variable_name="score_variable",
        value_name="score_value",
    )

    return (
        long_data.with_columns(
            [
                pl.col("score_variable")
                .str.extract(r"^score_(points|contribution)_", 1)
                .alias(".value"),
                pl.col("score_variable")
                .str.extract(rf"_({control_regex})$", 1)
                .alias("control"),
            ]
        )
        .drop("score_variable")
        .pivot(values="score_value", index=id_vars + ["control"], on=".value")
    )


def pivot_weights_by_control(
    data: pl.DataFrame,
    id_vars: List[str],
    weight_var: str,
    total_score: str,
    control_types: List[str],
) -> pl.DataFrame:
    control_regex = f"{'|'.join(control_types)}"
    var_pattern = re.compile(rf"^weight_perc_{total_score}_({control_regex})$")
    pivot_vars = [col for col in data.columns if var_pattern.match(col)]

    long_data = data.unpivot(
        index=id_vars,
        on=pivot_vars,
        variable_name="score_variable",
        value_name=weight_var,
    )

    return long_data.with_columns(
        pl.col("score_variable")
        .str.extract(rf"_({control_regex})$", 1)
        .alias("control")
    ).drop("score_variable")


def pivot_areas_by_control(
    data: pl.DataFrame,
    id_vars: List[str],
    control_types: List[str],
    percent_area_under_control: str,
) -> pl.DataFrame:
    control_regex = f"{'|'.join(control_types)}"
    var_pattern = re.compile(rf"^{percent_area_under_control}_({control_regex})$")
    pivot_vars = [col for col in data.columns if var_pattern.match(col)]

    long_data = data.unpivot(
        index=id_vars,
        on=pivot_vars,
        variable_name="score_variable",
        value_name="area_p",
    )

    return long_data.with_columns(
        pl.col("score_variable")
        .str.extract(rf"_({control_regex})$", 1)
        .alias("control")
    ).drop("score_variable")


def pivot_metrics_by_control(
    data: pl.DataFrame, id_vars: List[str], metric: str, control_types: List[str]
) -> pl.DataFrame:
    control_regex = f"{'|'.join(control_types)}"
    var_pattern = re.compile(rf"^{metric}_({control_regex})$")
    pivot_vars = [col for col in data.columns if var_pattern.match(col)]

    long_data = data.unpivot(
        index=id_vars, on=pivot_vars, variable_name="metric_variable", value_name=metric
    )

    return long_data.with_columns(
        pl.col("metric_variable")
        .str.extract(rf"_({control_regex})$", 1)
        .alias("control")
    ).drop("metric_variable")


def clean_per_control_scores(
    scores: pl.DataFrame, areas_per_control: pl.DataFrame
) -> pl.DataFrame:
    df = scores.join(
        areas_per_control, on=["portfolio_asset_id", "control"], how="left"
    )

    # Filter out rows with 0% area
    df = df.filter(pl.col("area_p") != 0)

    # Remove "all" control if 100% already exists
    full_control_assets = df.filter(pl.col("area_p") == 100)[
        "portfolio_asset_id"
    ].unique()

    df = df.filter(
        ~(
            (pl.col("control") == "all")
            & pl.col("portfolio_asset_id").is_in(full_control_assets)
        )
    )

    return df.drop("area_p")
