import polars as pl

from app_real_score_contribution.energy.model import ControlTypePartition


def add_coverage_weights_per_control(df: pl.DataFrame) -> pl.DataFrame:
    struct_cols = [
        "asset_size_base_tenant_m2",  # not in asset level scores, created by add_extra_areas_columns_per_control
        "asset_size_common_m2",
        "asset_size_shared_m2",
        "asset_size_tenant_landlord_m2",
        "asset_size_tenant_tenant_m2",
        "asset_size_whole_landlord_m2",  # not in asset level scores, created by add_extra_areas_columns_per_control
        "asset_size_whole_tenant_m2",  # not in asset level scores, created by add_extra_areas_columns_per_control
    ]
    st = pl.struct([pl.col(c) for c in struct_cols])
    return df.with_columns(
        [
            (
                pl.col("asset_weight_frac")
                * st.map_elements(
                    lambda r: get_landlord_controlled_weight(
                        ControlTypePartition.from_coverage_row(r)
                    )
                )
                # the weight (in fraction) of the asset's landlord-controlled area for energy data coverage scoring
            ).alias("weight_frac_en_cov_lc"),
            (
                pl.col("asset_weight_frac")
                * st.map_elements(
                    lambda r: get_tenant_controlled_weight(
                        ControlTypePartition.from_coverage_row(r)
                    )
                )
                # the weight (in fraction) of the asset's tenant-controlled area for energy data coverage scoring
            ).alias("weight_frac_en_cov_tc"),
        ]
    )


def add_lfl_weights_per_control(df: pl.DataFrame) -> pl.DataFrame:
    lfl_cols = [
        "en_lfl_abs_bt_last_year",
        "en_lfl_abs_lc_bc_last_year",
        "en_lfl_abs_lc_bs_last_year",
        "en_lfl_abs_lc_t_last_year",
        "en_lfl_abs_tc_t_last_year",
        "en_lfl_abs_lc_w_last_year",
        "en_lfl_abs_tc_w_last_year",
        "en_lfl_abs_lc_o_last_year",
        "en_lfl_abs_tc_o_last_year",
    ]
    st = pl.struct([pl.col(c) for c in lfl_cols])
    return df.with_columns(
        [
            (
                pl.col("asset_weight_frac_en_lfl")
                * st.map_elements(
                    lambda r: get_landlord_controlled_weight(
                        ControlTypePartition.from_lfl_row(r)
                    )
                )
            ).alias("weight_frac_en_lfl_lc"),
            (
                pl.col("asset_weight_frac_en_lfl")
                * st.map_elements(
                    lambda r: get_tenant_controlled_weight(
                        ControlTypePartition.from_lfl_row(r)
                    )
                )
            ).alias("weight_frac_en_lfl_tc"),
        ]
    )


def add_percent_weights_per_control(df: pl.DataFrame) -> pl.DataFrame:
    """
    Converts fractional control weights into percentages.

    Returns:
        pl.DataFrame: DataFrame with percentage versions of the four control weights.
    """
    frac_cols = [
        "weight_frac_en_cov_lc",  # created in add_coverage_weights_per_control
        "weight_frac_en_cov_tc",  # created in add_coverage_weights_per_control
        "weight_frac_en_lfl_lc",  # created in add_lfl_weights_per_control
        "weight_frac_en_lfl_tc",  # created in add_lfl_weights_per_control
    ]
    return df.with_columns(
        [
            (pl.col(f).cast(pl.Float64) * 100).alias(f.replace("frac", "perc"))
            for f in frac_cols
        ]
    )


def add_weights_per_control(df: pl.DataFrame) -> pl.DataFrame:
    """
    Applies all control weight transformations (coverage, LFL, and percentage versions).

    Returns:
        pl.DataFrame: Transformed DataFrame with all control weights added.
    """

    df = add_coverage_weights_per_control(df)
    df = add_lfl_weights_per_control(df)
    df = add_percent_weights_per_control(df)
    return df


def add_lfl_asset_weights(df: pl.DataFrame, utility: str) -> pl.DataFrame:
    """
    For each asset & utility, re‐compute “last‐year‐like‐for-like” (LFL) weights
    only for assets that have an LFL score, based on percentage of GAV within the bucket and their ownership‐adjusted area.

    LFL weights per asset are different from the generic asset weights, since not
    all assets are eligible for LFL. For each utility, we have to re-calculate the
    LFL-eligible area per country/subtype bucket, and use that as the area
    normalization.

    Args:
        df: A DataFrame containing at least these columns:
            - response_id, country, property_type_code
            - asset_size (float), asset_ownership (pct as float)
            - r_1_tbl_pgav (float)
            - score_<ind>1_<utility>_lfl_percent_change
        utility: One of "en", "wat", or "ghg".

    Returns:
        A new DataFrame with two extra columns:
          * asset_weight_frac_<utility>_lfl
          * asset_weight_percent_<utility>_lfl
    """

    abbrev = {"en": "en", "wat": "wt", "ghg": "gh"}.get(utility)
    if abbrev is None:
        raise ValueError(f"unknown utility '{utility}'")
    score_col = f"score_{abbrev}1_{utility}_lfl_percent_change"

    # who has an LFL score?
    has_lfl = pl.col(score_col).is_not_null()

    # 1) compute per‐asset LFL‐eligible area = asset_size * ownership/100, else 0
    df = df.with_columns(
        [
            pl.when(has_lfl)
            .then(pl.col("asset_size") * pl.col("asset_ownership") / 100.0)
            .otherwise(0.0)
            .alias("lfl_eligible_asset_area")
        ]
    )

    # 2) sum‐up those areas by "response_id","country","property_type_code"
    grp = (
        df.filter(has_lfl)
        .group_by(["response_id", "country", "property_type_code"])
        .agg(
            pl.col("lfl_eligible_asset_area").sum().alias("lfl_eligible_asset_area_agg")
        )
    )
    df = df.join(grp, on=["response_id", "country", "property_type_code"], how="left")

    # 3) compute the asset level fractional weight for Like-for-Like (LFL) scoring, which is the contribution of this asset
    # within the bucket(country/property_type_code).
    wcol = f"asset_weight_frac_{utility}_lfl"
    df = df.with_columns(
        [
            pl.when(has_lfl)
            .then(
                (pl.col("r_1_tbl_pgav") / 100.0 * pl.col("lfl_eligible_asset_area"))
                / pl.col("lfl_eligible_asset_area_agg")
            )
            .otherwise(0.0)
            .alias(wcol)
        ]
    )

    # 4) percentage version
    pcol = wcol.replace("frac", "percent")
    df = df.with_columns([(pl.col(wcol) * 100.0).alias(pcol)])

    # 5) drop intermediates
    return df.drop(["lfl_eligible_asset_area", "lfl_eligible_asset_area_agg"])


def get_landlord_controlled_weight(weights: ControlTypePartition) -> float:
    """Proportion of total area under landlord control, per GRESB rules."""
    return weights.weight_lc()


def get_tenant_controlled_weight(weights: ControlTypePartition) -> float:
    """Proportion of total area under tenant control, per GRESB rules."""
    return weights.weight_tc()
