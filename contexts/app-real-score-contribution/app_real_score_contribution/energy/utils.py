import polars as pl


def null_preserving_row_sum(exprs: list[pl.Expr]) -> pl.Expr:
    """
    Returns a row-wise sum across the given expressions, preserving null semantics:
    if all values in a row are null, the result is null; otherwise, nulls are ignored in summation.

    The function is named `null_preserving_row_sum` to reflect <PERSON><PERSON>' underlying use of
    Apache Arrow's null system. While Polars represents missing values as `None` in Python,
    these are internally stored as `null` using a validity bitmap. Therefore, 'null' is the
    more technically accurate term in this context.
    """
    not_null_count = pl.sum_horizontal([e.is_not_null().cast(pl.Int32) for e in exprs])
    row_sum = pl.sum_horizontal(exprs)
    return pl.when(not_null_count > 0).then(row_sum).otherwise(None)


def calc_total_scores(df: pl.DataFrame) -> pl.DataFrame:
    """
      Compute all the final “total” and per-control subscores for energy.

      Hopefully we can simplify this in the future by adding total subscores per
      control to the output of the scoring model

    - Copies the “cov” subscores for display
    - Sums availability + performance for LFL totals (and per-control)
    - Clamps the ren_avail sum at 1, then sums ren_avail + ren_perf.

      Args:
          df: input wide DataFrame with at least the following columns:
            - score_en1
            - score_points_en_cov_sub, score_points_en_cov_sub_lc, score_points_en_cov_sub_tc
            - score_points_en_lfl_avail, score_points_en_lfl_perf
            - score_points_en_lfl_avail_lc, score_points_en_lfl_perf_lc
            - score_points_en_lfl_avail_tc, score_points_en_lfl_perf_tc
            - score_points_en_ren_ons, score_points_en_ren_ofs, score_points_en_ren_perf

      Returns:
          A new DataFrame with nine additional columns:
            score_points_en_cov,
            score_points_en_cov_lc,
            score_points_en_cov_tc,
            score_points_en_lfl,
            score_points_en_lfl_lc,
            score_points_en_lfl_tc,
            score_points_en_ren_avail,
            score_points_en_ren
    """
    df = df.with_columns(
        [
            # 1) copy cov subscores for Tableau
            # TLDR: There are no subscores for the data coverage, so copy into another column so
            # that the score and score contribution can be displayed + colored separately
            # in tableau.
            # We need to have one row for each subscore points and one row for contribution,
            # For lfl, we have performance and availability subscores, but for data coverage, the only subscore is
            # the score itself. The reason that you can't just use one row with the score point is that,
            # we need different color for score points and contribution.
            pl.col("score_points_en_cov_sub").alias("score_points_en_cov"),
            pl.col("score_points_en_cov_sub_lc").alias("score_points_en_cov_lc"),
            pl.col("score_points_en_cov_sub_tc").alias("score_points_en_cov_tc"),
            # 2) LFL totals (null if both inputs null)
            null_preserving_row_sum(
                [
                    pl.col("score_points_en_lfl_avail"),
                    pl.col("score_points_en_lfl_perf"),
                ]
            ).alias("score_points_en_lfl"),
            # 3) fake “total” LFL per control
            # Since for LFL, there is different granularity: percent change is scored per control, but data availability is scored per asset.
            # Assign a fake "total" LFL score per control so that the score
            # potential per control is calculated correctly
            null_preserving_row_sum(
                [
                    pl.col("score_points_en_lfl_avail_lc"),
                    pl.col("score_points_en_lfl_perf_lc"),
                ]
            ).alias("score_points_en_lfl_lc"),
            null_preserving_row_sum(
                [
                    pl.col("score_points_en_lfl_avail_tc"),
                    pl.col("score_points_en_lfl_perf_tc"),
                ]
            ).alias("score_points_en_lfl_tc"),
            # 4) ren availability = min(rowSum(ons, ofs), 1)
            null_preserving_row_sum(
                [
                    pl.col("score_points_en_ren_ons"),
                    pl.col("score_points_en_ren_ofs"),
                ]
            )
            .clip(upper_bound=1)
            .alias("score_points_en_ren_avail"),
        ]
    )

    # 5) ren total = ren_avail + ren_perf. It needs to be in a separate step because score_points_en_ren_avail is not available
    # in the original df, and is calculated on the fly. polars evaluates the expressions in a list in parallel.
    return df.with_columns(
        null_preserving_row_sum(
            [
                pl.col("score_points_en_ren_avail"),
                pl.col("score_points_en_ren_perf"),
            ]
        ).alias("score_points_en_ren")
    )
