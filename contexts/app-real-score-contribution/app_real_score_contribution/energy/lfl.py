import polars as pl

from app_real_score_contribution.energy.utils import null_preserving_row_sum


def add_last_year_lfl_consumption(data: pl.DataFrame, survey_year: int) -> pl.DataFrame:
    """
    Add last year's LFL consumption data to the main DataFrame by pivoting wider.

    Args:
        data (pl.DataFrame): Input asset-level data.
        survey_year (int): Current survey year.

    Returns:
        pl.DataFrame: DataFrame with additional columns for last year's LFL values.
    """
    current_year = survey_year - 1

    id_vars = ["building_data_row_id", "portfolio_asset_id", "data_year"]
    lfl_abs_vars = ["en_lfl_abs_wf", "en_lfl_abs_wd", "en_lfl_abs_we"]

    # Step 1: Prepare data for pivot
    lfl_data = data.select(id_vars + lfl_abs_vars)
    lfl_data = lfl_data.with_columns(
        pl.when(pl.col("data_year") == current_year)
        .then(pl.lit("current_year"))
        .otherwise(pl.lit("last_year"))
        .alias("data_year")
    )

    # Step 2: Pivot wider to get current_year and last_year side-by-side
    lfl_data_wide = lfl_data.pivot(
        values=lfl_abs_vars + ["building_data_row_id"],
        index=["portfolio_asset_id"],
        on=["data_year"],
    )

    # Step 3: Join with original data on asset_id + building_id
    data_with_last_year_lfl = data.join(
        lfl_data_wide.select(
            [
                "portfolio_asset_id",
                "building_data_row_id_current_year",
                "en_lfl_abs_wf_last_year",
                "en_lfl_abs_wd_last_year",
                "en_lfl_abs_we_last_year",
            ]
        ),
        left_on=["portfolio_asset_id", "building_data_row_id"],
        right_on=["portfolio_asset_id", "building_data_row_id_current_year"],
        how="left",
    )

    return data_with_last_year_lfl


def add_extra_lfl_consumption(data: pl.DataFrame) -> pl.DataFrame:
    """
    Add extra LFL consumption data to the DataFrame.
    This includes landlord-controlled and tenant-controlled full-building entries,
    as well as building-type breakdown LFL metrics.
    """
    # 1. Calculate total last-year energy consumption from multiple columns
    lfl_energy_cols = [  # These columns were added in the previous function: add_last_year_lfl_consumption
        "en_lfl_abs_wf_last_year",
        "en_lfl_abs_wd_last_year",
        "en_lfl_abs_we_last_year",
    ]
    data = data.with_columns(
        [
            null_preserving_row_sum([pl.col(col) for col in lfl_energy_cols]).alias(
                "en_lfl_abs_w_last_year"
            )
        ]
    )

    # 2. Assign energy to landlord-controlled full-building entries
    is_landlord_full_building = (pl.col("whole_building") == True) & (
        pl.col("tenant_ctrl") == False
    )
    data = data.with_columns(
        pl.when(is_landlord_full_building)
        .then(pl.col("en_lfl_abs_w_last_year"))
        .otherwise(None)
        .alias("en_lfl_abs_lc_w_last_year")
    )

    # 3. Assign energy to tenant-controlled full-building entries
    is_tenant_full_building = (pl.col("whole_building") == True) & (
        pl.col("tenant_ctrl") == True
    )
    data = data.with_columns(
        pl.when(is_tenant_full_building)
        .then(pl.col("en_lfl_abs_w_last_year"))
        .otherwise(None)
        .alias("en_lfl_abs_tc_w_last_year")
    )

    # 4. Aggregate building-type breakdown LFL metrics. These columns come from the asset level scores.
    breakdown_cols = [
        "en_lfl_abs_lc_bc_last_year",
        "en_lfl_abs_lc_bs_last_year",
        "en_lfl_abs_lc_t_last_year",
        "en_lfl_abs_tc_t_last_year",
    ]
    data = data.with_columns(
        # Building Type breakdown: sum of various area-based energy components
        null_preserving_row_sum([pl.col(col) for col in breakdown_cols]).alias(
            "en_lfl_abs_bt_last_year"
        )
    )

    return data


def add_extra_areas_columns_per_control(data: pl.DataFrame) -> pl.DataFrame:
    """
    Add extra areas per control type to the DataFrame.
    This includes landlord-controlled and tenant-controlled full-building entries,
    as well as base building tenant area.
    Create a table showing an example input of this function:
    +---------------------+---------------------+---------------------+
    | whole_building      | tenant_ctrl         | asset_size_m2       |
    +---------------------+---------------------+---------------------+
    | True                | False               | 1000                |
    | True                | True                | 2000                |
    | False               | False               | 1500                |
    | False               | True                | 1200                |
    +---------------------+---------------------+---------------------+

    Create a table showing the result of this function:
    +---------------------+---------------------+---------------------+
    | whole_building      | tenant_ctrl         | asset_size_m2       |
    +---------------------+---------------------+---------------------+
    | True                | False               | 1000                |
    | True                | True                | 2000                |
    | False               | False               | 1500                |
    | False               | True                | 1200                |
    +---------------------+---------------------+---------------------+
    | asset_size_whole_landlord_m2 | asset_size_whole_tenant_m2 | asset_size_base_tenant_m2 |
    +---------------------+---------------------+---------------------+
    | 1000                | None                | None                |
    | None                | 2000                | None                |
    | None                | None                | 1500                |
    | None                | None                | 1200                |
    +---------------------+---------------------+---------------------+
    Args:
        data (pl.DataFrame): Input asset-level data.
    Returns:
        pl.DataFrame: DataFrame with additional columns for areas per control type.
    """

    return data.with_columns(
        [
            pl.when(
                (pl.col("whole_building") == True) & (pl.col("tenant_ctrl") == False)
            )
            .then(pl.col("asset_size_m2"))
            .otherwise(None)
            .alias("asset_size_whole_landlord_m2"),
            pl.when(
                (pl.col("whole_building") == True) & (pl.col("tenant_ctrl") == True)
            )
            .then(pl.col("asset_size_m2"))
            .otherwise(None)
            .alias("asset_size_whole_tenant_m2"),
            pl.when(pl.col("whole_building") == False)
            .then(pl.col("asset_size_m2"))
            .otherwise(None)
            .alias("asset_size_base_tenant_m2"),
        ]
    )


def set_lfl_scores_back_to_missing(df: pl.DataFrame, utility: str) -> pl.DataFrame:
    """
    For assets that weren’t LFL-eligible (original LFL percent-change is NULL),
    set their LFL points score back to NULL (while leaving potentials & contributions at 0).
    Undo what was done in assign_missing_scores_to_zero

    Args:
        df: Input Polars DataFrame.
        utility: One of "en", "wat", or "ghg".

    Returns:
        A new DataFrame with the `score_points_{utility}_lfl` column
        nullified wherever the original LFL percent-change was missing.
    """
    indicator_abbreviation_map = {"en": "en", "wat": "wt", "ghg": "gh"}
    try:
        indicator_abb = indicator_abbreviation_map[utility]
    except KeyError:
        raise ValueError(
            f"Unknown utility {utility!r}, expected one of {list(indicator_abbreviation_map)}"
        )

    # The 1 is just the convention used in the scoring model.
    original_var = f"score_{indicator_abb}1_{utility}_lfl_percent_change"
    points_var = f"score_points_{utility}_lfl"

    if original_var not in df.columns or points_var not in df.columns:
        raise KeyError(
            f"Required column(s) missing: {original_var!r} or {points_var!r}"
        )

    return df.with_columns(
        pl.when(pl.col(original_var).is_null())
        .then(pl.lit(None).cast(pl.Float64))
        .otherwise(pl.col(points_var))
        .alias(points_var)
    )
