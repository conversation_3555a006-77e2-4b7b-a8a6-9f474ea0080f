import math
from dataclasses import dataclass


@dataclass
class ControlTypePartition:
    """
    Partition of an asset's floor area (or last-year LFL consumption) into control-type buckets.

    Fields:
      base_tenant:    the asset size for assets that was reported on base_tenant-building and tenant area level.
      common:         common/shared services & corridors (landlord-controlled)
      shared:         other shared spaces (ambiguous)
      tenant_lc:      tenant space under landlord control
      tenant_tc:      tenant space under tenant control
      whole_lc:       whole-building landlord-controlled
      whole_tc:       whole-building tenant-controlled
      outdoor_lc:     outdoor landlord-controlled consumption
      outdoor_tc:     outdoor tenant-controlled consumption
    """

    base_tenant: float
    common: float
    shared: float
    tenant_lc: float
    tenant_tc: float
    whole_lc: float
    whole_tc: float
    outdoor_lc: float = 0.0
    outdoor_tc: float = 0.0

    @classmethod
    def from_coverage_row(cls, row: dict) -> "ControlTypePartition":
        return cls(
            base_tenant=row["asset_size_base_tenant_m2"],
            common=row["asset_size_common_m2"],
            shared=row["asset_size_shared_m2"],
            tenant_lc=row["asset_size_tenant_landlord_m2"],
            tenant_tc=row["asset_size_tenant_tenant_m2"],
            whole_lc=row["asset_size_whole_landlord_m2"],
            whole_tc=row["asset_size_whole_tenant_m2"],
        )

    @classmethod
    def from_lfl_row(cls, row: dict) -> "ControlTypePartition":
        return cls(
            # this is the energy consumption (abs) for the assets that are eligible for lfl
            base_tenant=row["en_lfl_abs_bt_last_year"],
            common=row["en_lfl_abs_lc_bc_last_year"],
            shared=row["en_lfl_abs_lc_bs_last_year"],
            tenant_lc=row["en_lfl_abs_lc_t_last_year"],
            tenant_tc=row["en_lfl_abs_tc_t_last_year"],
            whole_lc=row["en_lfl_abs_lc_w_last_year"],
            whole_tc=row["en_lfl_abs_tc_w_last_year"],
            outdoor_lc=row.get("en_lfl_abs_lc_o_last_year", 0.0),
            outdoor_tc=row.get("en_lfl_abs_tc_o_last_year", 0.0),
        )

    def _sanitize(self) -> dict[str, float]:
        """
        Return a dict of all nine fields, replacing None or NaN with 0.0.
        """
        sanitized = {}
        for field_name, value in vars(self).items():
            if value is None or (isinstance(value, float) and math.isnan(value)):
                sanitized[field_name] = 0.0
            else:
                sanitized[field_name] = value
        return sanitized

    def _compute_control_weight(self, control: str) -> float:
        """
        Generic control-type weight calculation.

        Args:
          control: either "lc" for landlord-controlled or "tc" for tenant-controlled.

        Returns:
          A float in [0,1] giving the share of the asset’s floor area in that control type.
        """
        vals = self._sanitize()
        base_tenant = vals["base_tenant"]
        common_area = vals["common"]
        shared_area = vals["shared"]
        tenant_lc = vals["tenant_lc"]
        tenant_tc = vals["tenant_tc"]
        whole_lc = vals["whole_lc"]
        whole_tc = vals["whole_tc"]
        outdoor_lc = vals["outdoor_lc"]
        outdoor_tc = vals["outdoor_tc"]

        # Fractions of the tenant area
        total_tenant_space_weight = tenant_lc + tenant_tc
        frac_tenant_lc = (
            (tenant_lc / total_tenant_space_weight)
            if total_tenant_space_weight
            else 0.0
        )
        frac_tenant_tc = (
            (tenant_tc / total_tenant_space_weight)
            if total_tenant_space_weight
            else 0.0
        )

        if control == "lc":
            if not tenant_tc > 0:
                # base_tenant = it's a way of reporting (vs. whole building).
                # base tenant = base building (common area + shared service) + tenant space (landlord controlled + tenant controlled)
                # This is the fraction of landlord controlled tenant area in the building that reports in base_tenant way.
                frac_base_tenant_lc = 1.0
            elif (common_area + shared_area) > 0:
                frac_base_tenant_lc = 0.4 + 0.6 * frac_tenant_lc
            else:
                frac_base_tenant_lc = frac_tenant_lc
            numerator = base_tenant * frac_base_tenant_lc + whole_lc + outdoor_lc

        elif control == "tc":
            if not tenant_tc > 0:
                frac_base_tenant_tc = 0.0
            elif (common_area + shared_area) > 0:
                frac_base_tenant_tc = 0.6 * frac_tenant_tc
            else:
                frac_base_tenant_tc = frac_tenant_tc
            numerator = base_tenant * frac_base_tenant_tc + whole_tc + outdoor_tc

        else:
            raise ValueError(f"control must be 'lc' or 'tc', got {control!r}")
        # actually base_tenant, whole_lc and whole_tc are just asset size, you could only have one of those > 0, on asset level.
        # (on country and property subtype level you could have them > 0 at the same time)
        denominator = base_tenant + whole_lc + whole_tc + outdoor_lc + outdoor_tc
        return numerator / denominator if denominator else 0.0

    def weight_lc(self) -> float:
        """Landlord-controlled weight (0–1)."""
        return self._compute_control_weight("lc")

    def weight_tc(self) -> float:
        """Tenant-controlled weight (0–1)."""
        return self._compute_control_weight("tc")
