import polars as pl


def clean_lfl_data(df: pl.DataFrame) -> pl.DataFrame:
    """
    Clean up like-for-like fields for Tableau.

    - Rename `% change` columns to a uniform `en_lfl_p_*` pattern.
    - For each control type, only carry through the availability score when
      that control type was actually eligible (weight_frac_en_lfl_* > 0).
      Otherwise set it to null so Tableau won’t plot it.

    Args:
        df: wide Polars DataFrame with at least these columns:
            - en_lfl_percent_change_lc, en_lfl_percent_change_tc
            - weight_frac_en_lfl_lc, weight_frac_en_lfl_tc
            - score_en1_lfl_availability

    Returns:
        A new DataFrame with four new columns:
            - en_lfl_p_lc, en_lfl_p_tc
            - score_en1_lfl_availability_lc, score_en1_lfl_availability_tc
    """
    return df.with_columns(
        [
            # rename percent-change, these two columns come from asset level score
            pl.col("en_lfl_percent_change_lc").alias("en_lfl_p_lc"),
            pl.col("en_lfl_percent_change_tc").alias("en_lfl_p_tc"),
            # Add LFL availability per control so it shows up in the bar charts both at
            # the whole building and per control level, but only add them if there is LFL
            # eligibility in that control type
            pl.when(pl.col("weight_frac_en_lfl_lc") == 0.0)
            .then(None)
            .otherwise(pl.col("score_en1_lfl_availability"))
            .alias("score_en1_lfl_availability_lc"),
            pl.when(pl.col("weight_frac_en_lfl_tc") == 0.0)
            .then(None)
            .otherwise(pl.col("score_en1_lfl_availability"))
            .alias("score_en1_lfl_availability_tc"),
        ]
    )


def clean_offsite_scores(df: pl.DataFrame) -> pl.DataFrame:
    """
    When onsite-renewable availability is maxed (==1), hide offsite-renewable
    scores by setting them to null, so that Tableau won’t plot them alongside onsite.

    In tableau we want to show either onsite or offsite, not both, since there is
    a cap in the scoring model on the sum of these scores. To hide offsite when
    onsite is present in the charts, just set the offsite scores to missing.

    Operates on these four columns, all keyed off `score_points_en_ren_ons == 1`:
      - score_points_en_ren_ofs
      - score_points_potential_en_ren_ofs
      - score_contribution_en_ren_ofs
      - score_contribution_potential_en_ren_ofs

    Args:
        df: any DataFrame containing the above columns plus `score_points_en_ren_ons`.

    Returns:
        A new DataFrame in which, for each row where `score_points_en_ren_ons == 1`,
        the four “ofs” columns are replaced with null.
    """
    mask = pl.col("score_points_en_ren_ons") == 1
    return df.with_columns(
        [
            pl.when(mask)
            .then(None)
            .otherwise(pl.col("score_points_en_ren_ofs"))
            .alias("score_points_en_ren_ofs"),
            pl.when(mask)
            .then(None)
            .otherwise(pl.col("score_points_potential_en_ren_ofs"))
            .alias("score_points_potential_en_ren_ofs"),
            pl.when(mask)
            .then(None)
            .otherwise(pl.col("score_contribution_en_ren_ofs"))
            .alias("score_contribution_en_ren_ofs"),
            pl.when(mask)
            .then(None)
            .otherwise(pl.col("score_contribution_potential_en_ren_ofs"))
            .alias("score_contribution_potential_en_ren_ofs"),
        ]
    )
