import polars as pl
from app_real_score_contribution.constants import (
    R_1_TBL_PGAV,
    R_1_TBL_AREA,
    ASSET_SIZE,
    ASSET_OWNERSHIP,
    ASSET_WEIGHT_FRAC,
    ASSET_WEIGHT_PERCENT,
)
from app_real_score_contribution.validation import validate_columns


def add_asset_weights(asset_data: pl.DataFrame) -> pl.DataFrame:
    """
    Computes asset weight fraction and percentage for each asset.

    Args:
        asset_data (pl.DataFrame): DataFrame containing:
            - r_1_tbl_pgav: Gross Asset Value in percentage.
            - r_1_tbl_area: Total area.
            - asset_size: Size of the individual asset.
            - asset_ownership: Ownership percentage.

    Returns:
        pl.DataFrame: Updated DataFrame with 'asset_weight_frac' and 'asset_weight_percent'.
    """
    # Ensure necessary columns exist, these two columns were added along the pipeline and not available at the beginning
    required_columns = [R_1_TBL_PGAV, R_1_TBL_AREA]
    validate_columns(asset_data, required_columns)

    # Make sure R_1_TBL_AREA is not zero to avoid having infinite value
    zero_area = asset_data.filter(pl.col(R_1_TBL_AREA) == 0)
    if not zero_area.is_empty():
        raise ValueError(f"R_1_TBL_AREA cannot have zero values")
    # Compute asset weight fraction
    asset_data = asset_data.with_columns(
        (
            (pl.col(R_1_TBL_PGAV) / 100)
            * pl.col(ASSET_SIZE)
            * (pl.col(ASSET_OWNERSHIP) / 100)
            / pl.col(R_1_TBL_AREA)
        ).alias(ASSET_WEIGHT_FRAC)
    )

    # Compute asset weight percentage
    asset_data = asset_data.with_columns(
        (pl.col(ASSET_WEIGHT_FRAC) * 100).alias(ASSET_WEIGHT_PERCENT)
    )

    # Validation: Ensure values are within valid bounds
    if asset_data[ASSET_WEIGHT_FRAC].is_null().any():
        raise ValueError(f"{ASSET_WEIGHT_FRAC} cannot have null values")

    invalid_rows = asset_data.filter(
        (pl.col(ASSET_WEIGHT_FRAC).round(4) < 0)
        | (pl.col(ASSET_WEIGHT_FRAC).round(4) > 1)
        | (pl.col(ASSET_WEIGHT_PERCENT).round(2) < 0)
        | (pl.col(ASSET_WEIGHT_PERCENT).round(2) > 100)
    )

    if not invalid_rows.is_empty():
        raise ValueError(f"Invalid asset weight found:\n{invalid_rows}")

    return asset_data


def calc_fractional_scores_per_indicator(
    data: pl.DataFrame, score_map: dict
) -> pl.DataFrame:
    """
    Converts point-based scores to fractional scores by dividing each column in 'points_var' by its corresponding 'max_points'.
    This is a specific function just to handle the strange naming of the indicator scores, score_en1 starts in points, but we replace it with fractions. And rename the points columns to score_points_en_1

    Args:
        data (pl.DataFrame): Input dataframe containing raw scores.
        score_map (dict): Mapping of old score columns to their respective max points.

    Returns:
        pl.DataFrame: Updated dataframe with fractional scores.
    """
    transformations = {
        fraction_var: (pl.col(points_var) / max_point).alias(fraction_var)
        for points_var, max_point, fraction_var in zip(
            score_map["points_var"], score_map["max_points"], score_map["fraction_var"]
        )
    }

    # Apply transformations and return updated dataframe
    return data.with_columns(**transformations)
