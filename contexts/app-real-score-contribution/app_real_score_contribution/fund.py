import polars as pl
from app_real_score_contribution.constants import (
    RESPONSE_ID,
    COMPANY_FUND_ID,
    FUND_NAME,
    RC_3_A,
)


def add_fund_characteristics(
    df: pl.DataFrame,
    survey_data: pl.DataFrame,
) -> pl.DataFrame:
    """
    Adds fund characteristics to the data based on survey_data.
    df: pl.DataFrame: The main DataFrame containing 'response_id'
    survey_data: pl.DataFrame: The survey table DataFrame containing 'response_id', 'fund_id', 'fund_name', 'rc_3_a'.
    """
    try:
        fund_data = survey_data[RESPONSE_ID, COMPANY_FUND_ID, FUND_NAME, RC_3_A]
        df = df.join(fund_data, on=RESPONSE_ID, how="left")
        df = df.with_columns(
            pl.when(pl.col(RC_3_A) == 1)
            .then(pl.lit("m2"))
            .otherwise(pl.lit("sqft"))
            .alias("area_unit")
        )
        return df
    except Exception as e:
        raise RuntimeError(f"Error adding fund characteristics: {e}") from e
