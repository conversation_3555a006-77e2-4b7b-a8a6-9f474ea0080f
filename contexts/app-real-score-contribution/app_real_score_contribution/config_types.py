from typing import TypedDict


# Then define a top-level config typed-dict
class ConfigType(TypedDict, total=False):
    score_map: dict[str, dict[str, list[str]]]
    total_score_map: dict[str, dict[str, list[str]]]
    column_mappings: dict[str, dict[str, str]]
    conversion: dict[str, float]
    required_columns: dict[str, list[str]]
    columns_to_read: dict[str, dict[str, list[str]]]
    column_types: dict[str, dict[str, str]]
    columns_to_keep: dict[str, dict[str, list[str]]]
