import polars as pl
from app_real_score_contribution.constants import (
    PRT_SECTOR,
    PRT_SECTOR_CODE,
    PRT_TYPE,
    PRT_TYPE_CODE,
    PRT_SUBTYPE,
    PRT_TYPE_NAME_PLACEHOLDER,
)


def get_ancestor_of_a_generation(
    asset_type_codes: pl.DataFrame, target_gen: int, asset_types: pl.DataFrame
) -> pl.Series:
    """
    Get the ancestors that match a given generation for a given list of asset_type_codes.
    For each value of that list, get the ancestor of it that matches the given generation.

    Asset types are organized in a parent-child tree where each asset type,
    except the 1st generation ones, have a single parent. The number of ancestors
    for any asset type defines their generation. For example; an asset type with
    2 ancestors is a 3rd generation asset type. This function takes a set of
    asset type codes and returns the ancestors of those asset types of a given
    generation.
    Asset types with no ancestors of the given generation are returned as NA.

    Args:
        asset_type_codes (pl.DataFrame): A Polars DataFrame of single column containing the asset type codes to be processed.
        target_gen (int): Desired generation level.
        asset_types (pl.DataFrame): Asset types DataFrame with columns 'code', 'parent_code', and 'generation'.

    Returns:
        pl.Series: A Polars Series of asset type codes for the specified generation.

    Example:
        asset_type_codes = pl.DataFrame({"property_type_code": ["C", "B", "A"]})
        target_gen = 1
        asset_types = pl.DataFrame({
            "code": ["A", "B", "C", "D", "E"],
            "parent_code": [None, "A", "B", "C", "D"],
            "generation": [1, 2, 3, 4, 5]
        })

        result = get_ancestor_of_a_generation(asset_type_codes, target_gen, asset_types)

        Input asset_type_codes:
        ┌─────────────────────┐
        │ property_type_code  │
        │ ---                 │
        │ str                 │
        ├─────────────────────┤
        │ C                   │
        │ B                   │
        │ A                   │
        └─────────────────────┘

        Output:
        ┌─────────────────────┐
        │ property_type_code  │
        │ ---                 │
        │ str                 │
        ├─────────────────────┤
        │ A                   │
        │ A                   │
        │ None                │
        └─────────────────────┘
    """
    # If input is empty, return an empty Series
    if asset_type_codes.is_empty():
        return pl.Series(name=PRT_TYPE_CODE, values=[], dtype=pl.Utf8)

    # Prepare asset_types DataFrame as a lookup table
    lookup = asset_types.select([PRT_TYPE_CODE, "parent_code", "generation"])

    # Check for invalid asset_type_codes (not present in asset_types)
    invalid_codes = asset_type_codes.filter(
        ~pl.col("property_type_code").is_in(lookup[PRT_TYPE_CODE])
    )
    if invalid_codes.shape[0] > 0:
        raise ValueError(
            f"Invalid asset type codes found: {invalid_codes['property_type_code'].to_list()}"
        )

    # Start with the current asset_type_codes
    current_codes = asset_type_codes

    # Determine the generations of the current asset level
    asset_type_codes_with_generation = current_codes.join(
        lookup, on=PRT_TYPE_CODE, how="left"
    )

    # Replace codes with None if they belong to a generation "older" than target_gen (in number it's lower)
    asset_type_codes_with_generation = asset_type_codes_with_generation.with_columns(
        pl.when(pl.col("generation") < target_gen)
        .then(None)
        .otherwise(pl.col(PRT_TYPE_CODE))
        .alias(PRT_TYPE_CODE)  # Replace the existing column
    )

    # Identify rows where generation > target_gen and is not None
    is_younger_gen_mask = (pl.col("generation").is_not_null()) & (
        pl.col("generation") > target_gen
    )
    # Iteratively update only rows where the generation is greater than target_gen
    while asset_type_codes_with_generation.filter(is_younger_gen_mask).shape[0] > 0:
        # Add the updated parent_code column conditionally, ensuring the same length
        updated_codes = asset_type_codes_with_generation.with_columns(
            pl.when(is_younger_gen_mask)
            .then(
                asset_type_codes_with_generation.join(
                    lookup, on=PRT_TYPE_CODE, how="left"
                )["parent_code"]
            )
            .otherwise(pl.col(PRT_TYPE_CODE))
            .alias(PRT_TYPE_CODE)
        )

        # Update generations for the next iteration
        asset_type_codes_with_generation = updated_codes.select(
            pl.col(PRT_TYPE_CODE)
        ).join(lookup, on=PRT_TYPE_CODE, how="left")

    return asset_type_codes_with_generation[PRT_TYPE_CODE]


def add_property_type_groups(
    data: pl.DataFrame, asset_types: pl.DataFrame
) -> pl.DataFrame:
    """
    Adds property sector, type, and subtype columns to the data based on asset_types.

    Args:
        data (pl.DataFrame): The main DataFrame containing 'property_type_code'.
        asset_types (pl.DataFrame): The asset_types DataFrame containing 'code' and 'name'.

    Returns:
        pl.DataFrame: DataFrame with added 'property_sector', 'property_type', and 'property_subtype'.
    """
    # Early return for empty DataFrame
    if data.is_empty():
        return pl.DataFrame(
            {
                PRT_TYPE_CODE: [],
                PRT_SECTOR: [],
                PRT_TYPE: [],
                PRT_SUBTYPE: [],
            }
        )
    # Step 1: Add property sector (1st generation)
    sector_codes = get_ancestor_of_a_generation(
        data.select(pl.col(PRT_TYPE_CODE)), 1, asset_types
    )
    sector_df = (
        sector_codes.to_frame()
        .join(
            asset_types,
            on=PRT_TYPE_CODE,
            how="left",
        )
        .rename({PRT_TYPE_NAME_PLACEHOLDER: PRT_SECTOR})
        .select(pl.col(PRT_SECTOR))
    )

    # Step 2: Add property type (2nd generation)
    type_codes = get_ancestor_of_a_generation(
        data.select(pl.col(PRT_TYPE_CODE)), 2, asset_types
    )

    # Fill in NAs in type_codes with corresponding sector_codes where applicable. This might happen because for some
    # asset types, the sector, property type, and property subtype are the same, so they only appear once in the asset_type
    # table.
    type_codes_filled = (
        pl.DataFrame(
            {
                PRT_TYPE_CODE: type_codes,
                PRT_SECTOR_CODE: sector_codes,
            }
        )
        .with_columns(
            pl.when(pl.col(PRT_TYPE_CODE).is_null())
            .then(pl.col(PRT_SECTOR_CODE))
            .otherwise(pl.col(PRT_TYPE_CODE))
            .alias("type_codes_filled")
        )
        .select(pl.col("type_codes_filled"))
    )

    type_df = (
        type_codes_filled.join(
            asset_types.rename(
                {"property_type_code": "type_codes_filled"}
            ),  # Temp rename for clarity during the join
            on="type_codes_filled",  # Join on type codes
            how="left",
        )
        .rename({PRT_TYPE_NAME_PLACEHOLDER: PRT_TYPE})
        .select(pl.col(PRT_TYPE))
    )

    # Step 3: Add property_subtype
    subtype_df = (
        data.select(pl.col(PRT_TYPE_CODE))
        .join(
            asset_types,
            on=PRT_TYPE_CODE,
            how="left",
        )
        .rename({PRT_TYPE_NAME_PLACEHOLDER: PRT_SUBTYPE})
        .select(pl.col(PRT_SUBTYPE))
    )

    # Combine the columns into the original DataFrame
    result = data.with_columns(
        [
            sector_df[PRT_SECTOR],
            type_df[PRT_TYPE],
            subtype_df[PRT_SUBTYPE],
        ]
    )

    # Validate that there are no missing values in the added columns
    if (
        result.select([PRT_SECTOR, PRT_TYPE, PRT_SUBTYPE]).null_count().pipe(sum).item()
        > 0
    ):
        raise ValueError("Missing property sector, type, or subtype values.")

    return result
