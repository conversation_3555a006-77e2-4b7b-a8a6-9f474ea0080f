from enum import Enum
import polars as pl


class PolarsDtype(Enum):
    """Enum mapping string type names to Polars data types."""

    INT = pl.Int64
    FLOAT = pl.Float64
    STRING = pl.Utf8
    BOOLEAN = pl.Boolean
    DATETIME = pl.Datetime
    DATE = pl.Date
    TIME = pl.Time

    @classmethod
    def from_string(cls, dtype_str: str):
        """Converts a string representation to a Polars data type."""
        try:
            return cls[dtype_str.upper()].value
        except KeyError:
            raise ValueError(f"Unsupported data type: {dtype_str}")
