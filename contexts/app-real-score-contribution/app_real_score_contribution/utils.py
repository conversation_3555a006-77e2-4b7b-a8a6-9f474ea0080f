import json

import polars as pl

from app_real_score_contribution.config_types import ConfigType


def get_current_year_data(df: pl.DataFrame, survey_year: int) -> pl.DataFrame:
    """
    Filters rows where 'data_year' equals 'survey_year - 1'.
    """
    try:
        data_year = survey_year - 1
        return df.filter(pl.col("data_year") == data_year)
    except Exception as e:
        raise RuntimeError(f"Error filtering data for the current year: {e}") from e


def load_config(config_dir) -> ConfigType:
    try:
        with open(f"{config_dir}/config.json", "r") as f:
            config = json.load(f)
            return config
    except FileNotFoundError as err:
        raise RuntimeError("Configuration file 'config.json' not found.") from err
    except json.JSONDecodeError as e:
        raise RuntimeError(f"Error decoding 'config.json': {e}") from e
