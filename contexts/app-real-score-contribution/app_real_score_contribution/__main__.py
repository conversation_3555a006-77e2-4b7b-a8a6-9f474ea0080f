import os
import click

from config import GOLD_DATA_BUCKET
from gresb_utils.file_io import load_polars_from_file, write_data
from app_real_score_contribution.pipeline import process_pipeline
from app_real_score_contribution.utils import load_config
from gresb_utils.git_utils import get_git_output_run


@click.command()
@click.argument(
    "main_data",
    type=click.Path(),
    default=f"s3://{GOLD_DATA_BUCKET}/main/re/2024/output/asset_level_scores.parquet",
)
@click.argument("survey_table_data", type=click.Path())
@click.argument("survey_data", type=click.Path())
@click.argument("asset_types_data", type=click.Path())
@click.argument("locations_data", type=click.Path())
@click.argument("output_path", type=click.Path(), required=False)
@click.option("--survey_year", type=int)
@click.option(
    "--level",
    type=click.Choice(["asset", "portfolio"]),
    required=True,
    help="Processing level.",
)
def main(
    main_data: str,
    survey_table_data: str,
    survey_data: str,
    asset_types_data: str,
    locations_data: str,
    output_path: str,
    survey_year: int,
    level: str,
):
    """
    CLI to run the unified data processing pipeline for asset or portfolio level.
    """
    try:
        # Load configuration
        config = load_config(
            os.path.dirname(os.path.dirname(os.path.realpath(__file__)))
        )

        main_df = load_polars_from_file(
            main_data,
            "parquet",
            columns=[
                col
                for cols in config["columns_to_read"][level].values()
                for col in cols
            ],
        )
        survey_table_data_df = load_polars_from_file(
            survey_table_data,
            "parquet",
            columns=[
                col
                for cols in config["columns_to_read"]["survey_table_data"].values()
                for col in cols
            ],
        )
        asset_types_df = load_polars_from_file(
            asset_types_data,
            "parquet",
            columns=[
                col
                for cols in config["columns_to_read"]["asset_types"].values()
                for col in cols
            ],
        )
        locations_df = load_polars_from_file(
            locations_data,
            "parquet",
            columns=[
                col
                for cols in config["columns_to_read"]["locations"].values()
                for col in cols
            ],
        )
        survey_data_df = load_polars_from_file(
            survey_data,
            "parquet",
            columns=[
                col
                for cols in config["columns_to_read"]["survey_data"].values()
                for col in cols
            ],
        )

        # Log all the input for the pipeline
        click.secho("Running pipeline with the following inputs:", fg="blue")
        click.secho(f"Asset data: {main_data}", fg="blue")
        click.secho(f"Survey table data: {survey_table_data}", fg="blue")
        click.secho(f"Survey data: {survey_data}", fg="blue")
        click.secho(f"Locations data: {locations_data}", fg="blue")
        click.secho(f"Output path: {output_path}", fg="blue")
        click.secho(f"Survey year: {survey_year}", fg="blue")
        click.secho(f"Level: {level}", fg="blue")
        click.secho(f"Configuration: {config}", fg="blue")
        # Process pipeline
        outputs = process_pipeline(
            df=main_df,
            survey_table_data=survey_table_data_df,
            survey_data=survey_data_df,
            asset_types=asset_types_df,
            locations=locations_df,
            survey_year=survey_year,
            column_mappings=config["column_mappings"],
            required_columns=config["required_columns"],
            columns_to_read=config["columns_to_read"],
            column_types=config["column_types"],
            level=level,  # "asset" or "portfolio",
            config=config,
        )
        # TODO: fix current_branch when running in container or production https://app.clickup.com/t/24594449/PRD-1822
        # locations to write the output to, each version (git tag or {branch}-{commit_hash}) will be stored in a unique path
        git_version = get_git_output_run()
        # TODO: this is only used for output_validation for now, since we still have to get some data from prd.
        output_path = output_path or f"s3://{GOLD_DATA_BUCKET}/{git_version}/real/v2"
        write_data(
            outputs[0],  # Source of truth
            f"{output_path}/{level}/year={survey_year}/real_score_contribution_{level}_overview_data.parquet",
        )
        write_data(
            outputs[1],
            f"{output_path}/{level}_long/year={survey_year}/real_score_contribution_{level}_overview_long_data.parquet",
        )

        click.secho("Pipeline executed successfully.", fg="green")
    except ValueError as e:
        click.secho(f"ValueError: {e}", fg="yellow", err=True)
    except RuntimeError as e:
        click.secho(f"RuntimeError: {e}", fg="red", err=True)
    except Exception as e:
        click.secho(f"Unexpected error: {e}", fg="red", err=True)


if __name__ == "__main__":
    main()
