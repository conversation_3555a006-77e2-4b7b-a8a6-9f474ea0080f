import os
import subprocess
import logging

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
handler = logging.StreamHandler()
handler.setFormatter(logging.Formatter("%(asctime)s - %(levelname)s - %(message)s"))
logger.addHandler(handler)


def replace_slash_with_dash(name: str) -> str:
    """Replaces slashes in branch names with dashes."""
    return name.replace("/", "-")


def get_git_output(command: list) -> str:
    """Runs a Git command and returns the output as a string."""
    # Validate that this is a Git command
    if not command or command[0] != "git":
        logger.error(f"Invalid Git command: {command}")
        return "unknown"
    try:
        return (
            subprocess.check_output(command, stderr=subprocess.DEVNULL)
            .decode("utf-8")
            .strip()
        )
    except subprocess.CalledProcessError as e:
        logger.debug(
            f"Git command failed: {' '.join(command)} - {e.stderr.decode('utf-8') if e.stderr else ''}"
        )
        return "unknown"


def get_current_branch(replace_slash=True) -> str:
    """Gets the current Git branch name, handling both local Git and GitHub Actions."""
    branch = os.getenv("GITHUB_REF_NAME") or get_git_output(
        ["git", "rev-parse", "--abbrev-ref", "HEAD"]
    )

    if branch != "unknown" and replace_slash:
        return replace_slash_with_dash(branch)
    return branch


def get_current_commit_hash(short=False) -> str:
    """Gets the current Git commit hash, handling both local Git and GitHub Actions.

    Args:
        short (bool): If True, returns the short version of the commit hash.

    Returns:
        str: The current commit hash.
    """
    commit_hash = os.getenv("GITHUB_SHA") or get_git_output(
        ["git", "rev-parse", "HEAD"]
    )
    return commit_hash[:7] if short else commit_hash


def get_git_output_run() -> str:
    """Determine the best identifier for an output run based on Git state."""

    # Check if we are on a tag
    tag = get_git_output(["git", "describe", "--tags", "--exact-match"])
    if tag != "unknown":
        return tag  # Use version tag if present

    # Otherwise, get branch name and commit hash
    branch = get_current_branch(replace_slash=True)
    commit_hash = get_current_commit_hash(short=True)
    logger.info(f"Branch: {branch}, Commit Hash: {commit_hash}")

    return f"{branch}-{commit_hash}"  # Feature branch format
