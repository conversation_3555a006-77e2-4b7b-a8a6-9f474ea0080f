import polars as pl
from typing import Optional
import time  # noqa: F401
import os
import logging
import boto3
import tempfile

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
handler = logging.StreamHandler()
handler.setFormatter(logging.Formatter("%(asctime)s - %(levelname)s - %(message)s"))
logger.addHandler(handler)


def load_file(
    path: str,
    file_type: str,
    csv_schema_overrides: Optional[dict[str, str]],
    columns: Optional[list[str]],
) -> pl.DataFrame:
    """
    Load a file (CSV or Parquet) into a Polars DataFrame with schema validation.

    Args:
        path (str): Path to the file.
        file_type (str): "parquet" or "csv".
        csv_schema_overrides (Optional[dict[str, str]]): Schema overrides for CSV.
        columns (Optional[List[str]]): List of columns to load.

    Returns:
        pl.DataFrame: The loaded DataFrame.
    """
    if file_type == "parquet":
        lazy_df = pl.scan_parquet(path)
    elif file_type == "csv":
        lazy_df = pl.scan_csv(
            path, schema_overrides=csv_schema_overrides, null_values="NA"
        )
    else:
        raise ValueError(f"Unsupported file type: {file_type}")

    # Validate requested columns
    available_cols = set(lazy_df.collect_schema())
    if columns:
        missing_cols = [col for col in columns if col not in available_cols]
        if missing_cols:
            raise ValueError(f"Requested columns {missing_cols} not found in: {path}.")

    return lazy_df.select(columns).collect() if columns else lazy_df.collect()


def load_polars_from_file(
    file_path: str,
    file_type: Optional[str] = None,
    from_local_cache: bool = True,
    csv_schema_overrides: Optional[dict[str, str]] = None,
    columns: Optional[list[str]] = None,
) -> pl.DataFrame:
    """
    Load data from a file into a Polars DataFrame, first trying local cache
    and falling back to S3 if necessary.

    Args:
        file_path (str): Path to the file (local or S3).
        file_type (Optional[str]): Explicit file type ("parquet", "csv", etc.).
            If not provided, inferred from file extension.
        from_local_cache (bool): Whether to load data from the local cache.
            Disable when running in the cloud.
        csv_schema_overrides (Optional[dict[str, str]]): Optional schema overrides
            for CSV columns.
        columns (Optional[list[str]]): Optional list of columns to load.

    Returns:
        pl.DataFrame: Loaded Polars DataFrame.
    """
    try:
        # Infer file type if not explicitly provided
        if not file_type:
            _, ext = os.path.splitext(file_path)
            file_type = ext.lower().lstrip(".")

        # Validate supported file types
        if file_type not in {"csv", "parquet"}:
            raise ValueError(f"Unsupported file type: {file_type}")

        # Construct local cache path
        cache_path = os.path.join(
            "/",
            *os.path.dirname(__file__).split("/")[:-1],
            "data",
            os.path.basename(file_path),
        )

        # Try loading from local cache
        if from_local_cache:
            try:
                logger.info(f"Trying to load file from local cache: {cache_path}")
                return load_file(cache_path, file_type, csv_schema_overrides, columns)
            except FileNotFoundError:
                logger.warning(
                    f"File not found in cache location: {cache_path}. Loading from S3 or provided local path."
                )

        # Load from actual file location
        logger.info(f"Loading file from path (S3 or local): {file_path}")
        start_time = time.time()
        df = load_file(file_path, file_type, csv_schema_overrides, columns)
        end_time = time.time()
        logger.info(f"File loaded in {end_time - start_time:.2f} seconds")

        # Save to local cache
        os.makedirs(os.path.dirname(cache_path), exist_ok=True)
        if file_type == "csv":
            df.write_csv(cache_path)
        else:
            df.write_parquet(cache_path)
        logger.info(f"File cached locally at: {cache_path}")

        return df

    except ValueError as e:
        logger.error(f"Invalid data request: {e}")
        raise
    except Exception as e:
        raise RuntimeError(f"Error loading data from {file_path}: {e}") from e


def write_data(dataframe: pl.DataFrame, path: str, file_format: str = "parquet"):
    """
    A function that can upload data to a specified path, either on S3 or locally.

    Args:
        dataframe (pl.DataFrame): The Polars DataFrame to be written.
        path (str): The destination path (local or S3).
        file_format (str): The format to write the data in ("parquet", "csv", etc.). Default is "parquet".

    Returns:
        None
    """
    # Define file type handlers
    file_handlers = {
        "parquet": lambda df, p: df.write_parquet(p),
        "csv": lambda df, p: df.write_csv(p),
    }
    try:
        write_function = file_handlers[file_format]
    except KeyError:
        raise ValueError(f"Unsupported file format: {file_format}")

    try:
        logger.info(f"Writing data to {path} in {file_format} format")
        write_function(dataframe, path)
        logger.info(f"Data successfully written to {path}")
    except FileNotFoundError:
        dir_path = os.path.dirname(path)
        logger.warning(f"Directory missing for {path}, creating it: {dir_path}")
        os.makedirs(dir_path, exist_ok=True)

        try:
            write_function(dataframe, path)
            logger.info(f"Data successfully written to {path} after creating directory")
        except Exception as e:
            raise RuntimeError(
                f"Failed to write data to {path} even after creating directory: {e}"
            ) from e
    except Exception as e:
        raise RuntimeError(f"Error writing data to {path}: {e}") from e


def download_and_read_excel(
    s3_path: str, sheet_name: Optional[str] = None
) -> pl.DataFrame:
    """
    Download Excel file from S3 and read it with Polars.

    Args:
        s3_path (str): S3 path to the Excel file (e.g., "s3://bucket/path/file.xlsx")
        sheet_name (Optional[str]): Name of the sheet to read. If None, reads the first sheet.

    Returns:
        pl.DataFrame: The loaded Excel data as a Polars DataFrame.

    Raises:
        ValueError: If the S3 path is invalid or file cannot be downloaded.
        RuntimeError: If there's an error reading the Excel file.
    """
    try:
        # Parse S3 path
        if s3_path.startswith("s3://"):
            s3_path = s3_path[5:]  # Remove s3:// prefix
        bucket, key = s3_path.split("/", 1)

        # Create S3 client
        s3_client = boto3.client("s3")

        # Download file to temporary location
        with tempfile.NamedTemporaryFile(suffix=".xlsx", delete=False) as tmp_file:
            logger.info(f"Downloading Excel file from S3: s3://{bucket}/{key}")
            s3_client.download_file(bucket, key, tmp_file.name)
            tmp_path = tmp_file.name

        try:
            # Read Excel file
            if sheet_name:
                logger.info(f"Reading sheet '{sheet_name}' from Excel file")
                df = pl.read_excel(tmp_path, sheet_name=sheet_name)
            else:
                logger.info("Reading first sheet from Excel file")
                df = pl.read_excel(tmp_path)

            logger.info(f"Successfully loaded {len(df)} rows from Excel file")
            return df
        finally:
            # Clean up temporary file
            os.unlink(tmp_path)

    except ValueError as e:
        logger.error(f"Invalid S3 path or download error: {e}")
        raise
    except Exception as e:
        logger.error(f"Error reading Excel file from S3: {e}")
        raise RuntimeError(f"Error reading Excel file from {s3_path}: {e}") from e
