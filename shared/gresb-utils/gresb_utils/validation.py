import polars as pl
from typing import Optional, List, Dict, Any
import logging
from gresb_utils.file_io import (
    load_polars_from_file,
)

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
handler = logging.StreamHandler()
handler.setFormatter(logging.Formatter("%(asctime)s - %(levelname)s - %(message)s"))
logger.addHandler(handler)


def compare_polars_df_with_file(
    dataframe: pl.DataFrame,
    path: str,
    columns: Optional[List[str]] = None,
    *,
    allow_float_tolerance: bool = False,
    sort: bool = False,
    sort_on: Optional[List[str]] = None,
    file_type: Optional[str] = None,
    from_local_cache: bool = True,
    csv_schema_overrides: Optional[Dict[str, str]] = None,
    is_subset: bool = True,
    subset_on: list[str] = None,
    column_mapping: dict[str, str] = None,
) -> List[Dict[str, Any]]:
    """
    Compare columns of a Polars DataFrame with another DataFrame loaded from a CSV or Parquet file.
    Uses `load_polars_from_file` for consistent file loading.

    Args:
        dataframe (pl.DataFrame): The DataFrame to compare.
        path (str): Path to the input file (local or S3) containing the reference DataFrame.
        columns (List[str]): List of column names to compare. When left None, all columns need to exist and no extra columns can exist.
        allow_float_tolerance (bool, optional): Compare float columns within 1e-4 tolerance.
        sort (bool, optional): If True, sort both DataFrames before comparison.
        sort_on (List[str], optional): Columns to sort on. If None, defaults to `columns`.
        file_type (str, optional): 'csv', 'parquet', etc. If None, inferred from `path`.
        from_local_cache (bool, optional): Whether to load data from local cache first.
        csv_schema_overrides (Dict[str, str], optional): Optional schema overrides for the loaded DataFrame from csv.
        is_subset (bool, optional): If True, means the ref_df is a subset (in rows) of the dataframe. If False, the two DataFrames must match exactly.
        subset_on (list, optional): columns to right join on the reference dataframe if is_subset is True
        column_mapping (Dict[str, str], optional): Column mapping to rename the columns of the reference DataFrame

    Returns:
        List[Dict[str, Any]]: A list of dictionaries describing comparison results.

    """

    # 1. Load reference DataFrame using your shared loader
    columns_to_load = columns
    if column_mapping is not None and columns is not None:
        # Map the columns to the columns names in the dict
        # reverse the column_mapping so that we can map the columns to the reference DataFrame
        column_mapping_re = {v: k for k, v in column_mapping.items()}
        columns_to_load = [column_mapping_re.get(col, col) for col in columns]
    ref_df = load_polars_from_file(
        file_path=path,
        file_type=file_type,
        from_local_cache=from_local_cache,
        csv_schema_overrides=csv_schema_overrides,
        columns=columns_to_load,
    )
    # 2. Determine columns to compare, if not present, then the columns must match exactly the reference DataFrame
    # rename ref dataframe according to column_mapping
    if column_mapping is not None:
        ref_df = ref_df.rename(column_mapping)
    columns = columns or ref_df.columns

    # 2. If is_subset is True, join the two dataframes on the subset_on columns
    if is_subset:
        if subset_on is None:
            raise ValueError("subset_on must be provided if is_subset is True")
        results = []
        for col in subset_on:
            # Check if the column that we need to join to create the subset of the rows exists in both DataFrames
            if col not in dataframe.columns or col not in ref_df.columns:
                logger.warning(f"Column '{col}' is missing in one of the DataFrames.")
                results.append(
                    {
                        "column": col,
                        "status": "missing",
                        "details": "Column missing in one of the DataFrames",
                    }
                )
                continue

        dataframe = dataframe.join(ref_df, how="right", on=subset_on)
    # 3. Sort if requested
    if sort:
        if sort_on is None:
            sort_on = columns  # fallback: sort on columns
        dataframe = dataframe.sort(sort_on)
        ref_df = ref_df.sort(sort_on)

    results = []

    # 4. Column-by-column comparison
    for col in columns:
        # Check if column exists in both DataFrames
        if col not in dataframe.columns or col not in ref_df.columns:
            logger.warning(f"Column '{col}' is missing in one of the DataFrames.")
            results.append(
                {
                    "column": col,
                    "status": "missing",
                    "details": "Column missing in one of the DataFrames",
                }
            )
            continue

        col_df = dataframe[col]
        col_ref = ref_df[col]
        # Compare float columns within tolerance if requested
        if (
            allow_float_tolerance
            and dataframe[col].dtype == pl.Float64
            and ref_df[col].dtype == pl.Float64
        ):
            difference = (col_df - col_ref).abs()
            # Mismatch if:
            #   - One is null and the other is not
            #   - Or the difference >= 1e-4
            is_mismatch = (
                (col_df.is_null() & col_ref.is_not_null())
                | (col_df.is_not_null() & col_ref.is_null())
                | (difference.ge(1e-4))
            )
            if is_mismatch.any():
                mismatches = pl.DataFrame(
                    {
                        f"{col}_dataframe": col_df,
                        f"{col}_ref_df": col_ref,
                    }
                ).filter(is_mismatch)

                results.append(
                    {
                        "column": col,
                        "status": "mismatch",
                        "details": "Float difference >= 1e-4 or null mismatch",
                        "mismatches": mismatches,
                    }
                )
        else:
            # Exact match required
            if not col_df.equals(col_ref):
                is_mismatch = (
                    (col_df.is_null() & col_ref.is_not_null())
                    | (col_df.is_not_null() & col_ref.is_null())
                    | (col_df != col_ref)
                )

                mismatches = pl.DataFrame(
                    {
                        f"{col}_dataframe": col_df,
                        f"{col}_ref_df": col_ref,
                    }
                ).filter(is_mismatch)

                results.append(
                    {
                        "column": col,
                        "status": "mismatch",
                        "details": "Exact mismatch",
                        "mismatches": mismatches,
                    }
                )

    # 5. Print results to stdout for clarity
    for result in results:
        logger.info(
            f"\nColumn: {result['column']}\n"
            f"Status: {result['status']}\n"
            f"Details: {result['details']}"
        )
        if "mismatches" in result:
            logger.error("Mismatched rows:")
            logger.error(str(result["mismatches"]))

    return results
