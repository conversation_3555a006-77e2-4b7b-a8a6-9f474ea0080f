import logging
import logging.config
import inspect


def setup_logger(caller_name: str = None) -> logging.Logger:
    # If no caller_name is provided, use inspect to find the calling module
    if caller_name is None:
        frame = inspect.currentframe()
        outer_frames = inspect.getouterframes(frame)

        # Getting the name of the calling module
        # outer_frames[1] is the caller of setup_logger
        caller_frame = outer_frames[1]
        caller_name = caller_frame.frame.f_globals["__name__"]

    # Set up logging
    logging.config.fileConfig("logging.ini", disable_existing_loggers=False)

    # Get a logger with the specified caller_name
    logger = logging.getLogger(caller_name)

    return logger
