# This file is automatically @generated by Poetry 1.8.5 and should not be changed by hand.

[[package]]
name = "polars"
version = "1.23.0"
description = "Blazingly fast DataFrame library"
optional = false
python-versions = ">=3.9"
files = [
    {file = "polars-1.23.0-cp39-abi3-macosx_10_12_x86_64.whl", hash = "sha256:d9d344958cb9bfe1bd42c1af910cb000c0dfa2892b849f2cd35649e0bcd10fab"},
    {file = "polars-1.23.0-cp39-abi3-macosx_11_0_arm64.whl", hash = "sha256:37ca01c31909ead40f32eb275b578c576bdd2e93730a07f3a38688b1c84f6ca8"},
    {file = "polars-1.23.0-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:3cb0b944ddfc13eefa3b8c244cba39790226fd11c861cbba9db2a1cbe033387a"},
    {file = "polars-1.23.0-cp39-abi3-manylinux_2_24_aarch64.whl", hash = "sha256:655d296078c99cd3f5cefc3ec1f1447676a64f0d7dcd2f89d919f19bf84fa53f"},
    {file = "polars-1.23.0-cp39-abi3-win_amd64.whl", hash = "sha256:b774f04d77ed4153d9539e546db5ad6ff4757c79dfe981e22400eb59299c5493"},
    {file = "polars-1.23.0-cp39-abi3-win_arm64.whl", hash = "sha256:701df6381665c56da18704305119ce73983b1bead3378fbc8fac7ffd24817e93"},
    {file = "polars-1.23.0.tar.gz", hash = "sha256:4305e87e4c48bc4ae8401a055fb5431c4c0c4e88855e648927269f31e6d338f0"},
]

[package.extras]
adbc = ["adbc-driver-manager[dbapi]", "adbc-driver-sqlite[dbapi]"]
all = ["polars[async,cloudpickle,database,deltalake,excel,fsspec,graph,iceberg,numpy,pandas,plot,pyarrow,pydantic,style,timezone]"]
async = ["gevent"]
calamine = ["fastexcel (>=0.9)"]
cloudpickle = ["cloudpickle"]
connectorx = ["connectorx (>=0.3.2)"]
database = ["polars[adbc,connectorx,sqlalchemy]"]
deltalake = ["deltalake (>=0.19.0)"]
excel = ["polars[calamine,openpyxl,xlsx2csv,xlsxwriter]"]
fsspec = ["fsspec"]
gpu = ["cudf-polars-cu12"]
graph = ["matplotlib"]
iceberg = ["pyiceberg (>=0.5.0)"]
numpy = ["numpy (>=1.16.0)"]
openpyxl = ["openpyxl (>=3.0.0)"]
pandas = ["pandas", "polars[pyarrow]"]
plot = ["altair (>=5.4.0)"]
polars-cloud = ["polars-cloud (>=0.0.1a1)"]
pyarrow = ["pyarrow (>=7.0.0)"]
pydantic = ["pydantic"]
sqlalchemy = ["polars[pandas]", "sqlalchemy"]
style = ["great-tables (>=0.8.0)"]
timezone = ["tzdata"]
xlsx2csv = ["xlsx2csv (>=0.8.0)"]
xlsxwriter = ["xlsxwriter"]

[metadata]
lock-version = "2.0"
python-versions = "^3.13"
content-hash = "14cf05e116552af6344f37d8f9901d4126c6d149d8a3a81ddffd088e6ab9c3b7"
