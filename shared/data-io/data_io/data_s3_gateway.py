from typing import List

import aiobotocore
import pandas as pd
import s3fs
import boto3
import botocore
from data_io.s3_file import S3File
from data_io.s3_folder import S3Folder
from config import AWS_PROFILE


class DataS3Gateway:
    """
    Responsible for managing the interaction with a Amazon S3 Bucket. It facilitates
    the import and export of data from and to S3. As a gateway, it provides a clean
    interface for other parts of the application to interact with S3 data sources.
    """

    def __init__(
        self, file_system=None, profile_name: str | None = AWS_PROFILE
    ) -> None:
        """
        Initializes a DataS3Gateway with the specified AWS profile.

        Parameters:
        file_system: Optional S3FileSystem instance. If None, a new one will be created.
        profile_name: Optional AWS profile name to use for authentication.
                     If provided, creates a new boto3 session with this profile.
        """
        self.profile_name = profile_name

        if file_system is None:
            # Create a new aiobotocore AioSession with the specified profile for the s3 fs
            aio_session = aiobotocore.session.AioSession(profile=profile_name)
            # Create a new s3fs instance with the aiobotocore AioSession
            self.fs = s3fs.S3FileSystem(anon=False, session=aio_session)
            # Create and store a boto3 session for boto3 client/resource creation
            self._session = boto3.Session(profile_name=self.profile_name)
        else:
            self.fs = file_system
            self._session = boto3.Session()

    def _bucket_exists(self, bucket: str) -> bool:
        """
        Checks whether the specified bucket exists in the GRESB S3 file system.
        This function requires having an open connection to the GRESB S3 environment.

        Parameters:
        bucket (str): String describing the name of the bucket.

        Returns:
        bool: A boolean indicating whether the bucket exists or not.
        """
        s3_resource = self._session.resource("s3")
        return s3_resource.Bucket(bucket) in s3_resource.buckets.all()

    def _file_exists(self, s3_file: S3File) -> bool:
        """
        Checks whether the specified file exists in the GRESB S3 file system.
        This function requires having an open connection to the GRESB S3 environment.

        Parameters:
        s3_file (S3File): An S3File object containing the bucket name and the absolute path to the file.
        Returns:
        bool: A boolean indicating whether the file exists.
        """
        s3_client = self._session.client("s3")

        try:
            bucket = s3_file.bucket_name
            file_path = f"{s3_file.base_filename}.{s3_file.format}"
            s3_client.head_object(Bucket=bucket, Key=file_path)
            return True
        except botocore.exceptions.ClientError as e:
            if e.response["Error"]["Code"] == "404":
                return False
            else:
                raise Exception(f"_file_exists: Error browsing S3: {str(e)}.")

    def export_data(self, data: pd.DataFrame, s3_file: S3File) -> None:
        """
        Exports data to a file on Amazon S3.

        Parameters:
        data (pd.DataFrame): the data to export.
        s3_file (S3File): s3File object that contains bucket, filename, and file format.
        """
        if not self._bucket_exists(s3_file.bucket_name):
            raise ValueError("export_data: S3File bucket name does not exist.")

        self._write_data(data, s3_file)

    def import_data(self, s3_file: S3File) -> pd.DataFrame:
        """
        Imports data from a file on Amazon S3.

        Parameters:
        s3_file (S3File): s3File object that contains bucket, filename, and file format.

        Returns:
        pd.DataFrame: A dataframe containing data from the specified file.
        """
        if not self._bucket_exists(s3_file.bucket_name):
            raise ValueError(
                f"import_data: S3File bucket name does not exist: {s3_file.bucket_name}"
            )

        if not self._file_exists(s3_file):
            raise ValueError(
                f"import_data: S3File path does not exist: {s3_file.full_path}"
            )

        return self._read_data(s3_file)

    def _read_data(self, s3_file: S3File) -> pd.DataFrame:
        """
        Reads data from a file on Amazon S3.

        Parameters:
        s3_file (S3File): s3File object that contains bucket, filename, and file format.

        Returns:
        pd.DataFrame: A dataframe containing data from the specified file.
        """
        # The following list is exerpt from default pandas 'process as NaN' list with 'NA' removed
        # since it is a country code of Namibia. If we will encounter other strings that should be
        # parced as NaN, we should add them here.
        to_be_parsed_as_na = [
            " ",
            "#N/A",
            "<NA>",
            "N/A",
            "NULL",
            "NaN",
            "None",
            "n/a",
            "nan",
            "null",
        ]
        try:
            with self.fs.open(s3_file.full_path) as f:
                if s3_file.format == "json":
                    return pd.read_json(f, lines=True)
                elif s3_file.format == "parquet":
                    return pd.read_parquet(f)
                elif s3_file.format == "csv":
                    return pd.read_csv(
                        f, na_values=to_be_parsed_as_na, low_memory=False
                    )
                else:
                    raise ValueError(
                        f"_read_data: Unsupported file format: {s3_file.format}"
                    )
        except Exception as e:
            raise ValueError(f"_read_data: Error reading data from S3: {str(e)}")

    def _write_data(self, data: pd.DataFrame, s3_file: S3File) -> None:
        """
        Writes data to a file on Amazon S3.

        Parameters:
        data (pd.DataFrame): the data to write.
        s3_file (S3File): s3File object that contains bucket, filename, and file format.
        """
        try:
            if s3_file.format == "json":
                formatted_data = data.to_json(
                    orient="records", lines=True, date_format="iso"
                ).encode()
            elif s3_file.format == "parquet":
                formatted_data = data.to_parquet(None, index=False)
            elif s3_file.format == "csv":
                formatted_data = data.to_csv(index=False).encode()
            else:
                raise ValueError(
                    f"_write_data: Unsupported file format: {s3_file.format}"
                )

            with self.fs.open(s3_file.full_path, "wb") as file:
                file.write(formatted_data)

        except Exception as e:
            raise ValueError(f"_write_data: Error writing data to S3: {str(e)}")

    def _list(self, s3_folder: S3Folder) -> List[str]:
        """
        Lists all files in a specified S3 folder.

        Args:
            s3_folder (S3Folder): An instance representing the target S3 folder.
                                The `full_path` attribute should provide the full path to the folder in S3.

        Returns:
            List[str]: A list of S3 URIs (strings) for each file in the specified folder.
                    Each URI follows the format "s3://bucket/path/to/file".

        Notes:
            This method filters items to only include files (not directories) within the specified folder.
        """
        items = self.fs.ls(s3_folder.full_path)
        return [f"s3://{item}" for item in items if self.fs.isfile(item)]
