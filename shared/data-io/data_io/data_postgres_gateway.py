import pandas as pd
import psycopg2
import os


class DataPostgresGateway:
    """
    Responsible for managing the interaction with a PostgreSQL database.
    It handles the creation of a PostgreSQL connection and facilitates the
    import of data from the database. As a gateway, it encapsulates
    database-specific operations and provides a clean interface for other parts
    of the application to interact with PostgreSQL data sources.
    """

    def __init__(self, host=None, dbname=None, user=None, password=None, port=None):
        self.host = host or os.environ.get("DEFAULT_CON_HOSTNAME")
        self.dbname = dbname or os.environ.get("DEFAULT_CON_DBNAME")
        self.user = user or os.environ.get("DEFAULT_CON_USERNAME")
        self.password = password or os.environ.get("DEFAULT_CON_PASSWORD")
        self.port = port or os.environ.get("DEFAULT_CON_PORT")

    def import_db_data(
        self, query: str, params: dict = None, fetch_size: int = 1000
    ) -> pd.DataFrame:
        """
        Queries data from the portal database using a query.

        Parameters:
        query (str): A string containing the SQL query.
        params (dict): A dictionary of parameters to sanitize the query.
        fetch_size (int): Number of rows to fetch at a time. Defaults to 1000.

        Returns:
        pd.DataFrame: A DataFrame containing the result of the query.
        """
        if query is None:
            raise ValueError("import_db_data: Query is required.")

        conn = self.__create_connection()
        try:
            with conn.cursor() as cur:
                if params:
                    query = psycopg2.sql.SQL(query).format(
                        *[psycopg2.sql.Identifier(k) for k in params.keys()]
                    )
                cur.execute(query, params)
                columns = [desc[0] for desc in cur.description]
                results = []
                while True:
                    rows = cur.fetchmany(fetch_size)
                    if not rows:
                        break
                    results.extend(rows)
                return pd.DataFrame(results, columns=columns)
        finally:
            conn.close()

    def __create_connection(self) -> psycopg2.extensions.connection:
        """
        Creates a connection with the portal database in order to query it outside of this class.

        Parameters:

        Returns:
        psycopg2.extensions.connection: A psycopg2 connection object enabling queries to the portal DB.
        """
        # possibility of extracting to outside of this class.
        return psycopg2.connect(
            host=self.host,
            dbname=self.dbname,
            user=self.user,
            password=self.password,
            port=self.port,
        )
