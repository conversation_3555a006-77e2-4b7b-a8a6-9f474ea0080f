class DataQueryTemplateGenerator:
    """
    Class with static methods that helps to create query templates to get data from
    databases.
    """

    @staticmethod
    def get_energy_ratings_query(snapshot_ids: list) -> str:
        """
        Generate SQL query to get energy ratings data.

        Parameters:
        snapshot_ids (list): a list containing the relevant assets' snapshot ids.

        Returns:
        str: A string in sql query format allowing to import energy ratings data.
        """
        return f"""
            SELECT
                aer.id AS asset_energy_rating_id,
                aer.snapshot_id,
                ra.id AS response_id,
                aer.portfolio_asset_id,
                ps.company_fund_id,
                ps.year AS survey_year,
                aer.energy_rating_id,
                er.name,
                aer.size AS covered_floor_area
            FROM snapshot_asset_energy_ratings aer
                INNER JOIN energy_ratings er
                ON er.id = aer.energy_rating_id
                INNER JOIN portfolio_snapshots ps
                ON ps.id = aer.snapshot_id
                INNER JOIN reit_analyses ra
                ON ra.company_fund_id = ps.company_fund_id
                AND ra.survey_date = ps.year::varchar
                INNER JOIN surveys s
                ON s.id = ra.our_survey_id
                INNER JOIN survey_series ss
                ON ss.id = s.series_id
            WHERE NOT ra.soft_deleted
                AND ss.subtype = 'real_estate'
                AND ps.id IN {tuple(snapshot_ids)}
        """

    @staticmethod
    def get_certification_table_query() -> str:
        return "SELECT id AS certification_id, brand, scheme, scoring_status FROM certifications"

    @staticmethod
    def get_energy_ratings_table_query() -> str:
        return "SELECT id AS energy_rating_id, name FROM energy_ratings"

    @staticmethod
    def get_survey_table_text_values_query(
        response_ids: list, table_prefix: str
    ) -> str:
        return f"""
            SELECT
                analysis_id AS response_id,
                variable,
                value
            FROM text_values tv
                INNER JOIN reit_analyses ra ON ra.id = tv.analysis_id
            WHERE ra.id IN {tuple(response_ids)}
                AND value IS NOT null
                AND variable LIKE '{table_prefix + "%"}'
        """

    @staticmethod
    def get_ashrae_thresholds_table_query():
        return """
        SELECT
            id,
            property_type_code,
            climate_zone,
            threshold AS ashrae_intensity_threshold
        FROM asset_portal_ashrae_energy_use_intensity_thresholds
        """

    @staticmethod
    def get_weather_stations_table_query():
        return "SELECT id, climate_zone FROM asset_portal_ashrae_weather_stations"
