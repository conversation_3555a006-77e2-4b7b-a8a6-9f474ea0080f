from dataclasses import dataclass


@dataclass
class S3File:
    """
    Responsible for defining s3 file attributes and full path property.
    """

    bucket_name: str
    base_filename: str
    format: str

    @property
    def full_path(self) -> str:
        """
        Returns the full path of the file including bucket, base filename, and file format.
        """
        return f"{self.bucket_name}/{self.base_filename}.{self.format}"

    @classmethod
    def from_uri(cls, uri: str):
        """
        Creates an S3File object from an S3 URI.

        Args:
            uri (str): S3 URI in the format 's3://bucket_name/base_filename.format'.

        Returns:
            S3File: An S3File instance.
        """
        if not uri.startswith("s3://"):
            raise ValueError("from_uri: Invalid S3 URI format")

        path = uri[5:]
        bucket_name, _, file_info = path.partition("/")
        base_filename, _, file_format = file_info.rpartition(".")

        if not bucket_name or not base_filename or not file_format:
            raise ValueError("from_uri: Incomplete S3 URI")

        return cls(
            bucket_name=bucket_name, base_filename=base_filename, format=file_format
        )
