from dataclasses import dataclass


@dataclass
class S3Folder:
    """
    Responsible for defining s3 folder attributes and full path property.
    """

    bucket_name: str
    folder: str

    @property
    def full_path(self) -> str:
        """
        Returns the full path of the folder including bucket and folder.
        """
        return f"{self.bucket_name}/{self.folder}"

    @classmethod
    def from_uri(cls, uri: str):
        """
        Creates an S3Folder object from an S3 URI.

        Args:
            uri (str): S3 URI in the format 's3://bucket_name/folder'.

        Returns:
            S3Folder: An S3Folder instance.
        """
        if not uri.startswith("s3://"):
            raise ValueError("from_uri: Invalid S3 URI format")

        path = uri[5:]
        bucket_name, _, folder = path.partition("/")

        if not bucket_name or not folder:
            raise ValueError("from_uri: Incomplete S3 URI")

        return cls(bucket_name=bucket_name, folder=folder)
