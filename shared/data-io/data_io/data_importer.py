import os
import pandas as pd
from urllib.parse import urlparse
from typing import Dict, Optional
from data_io.s3_folder import S3Folder
from data_io.data_s3_gateway import DataS3Gateway
from data_io.s3_file import S3File
from gresb_utils.setup_logging import setup_logger


class DataImporter:
    """
    DataImporter is responsible for loading data from either local files or from S3, based on the prefix in the URL.
    It supports multiple formats like CSV, Parquet, and JSON, and uses caching when a caching location is provided.
    """

    def __init__(
        self, file_system=None, caching_location: Optional[str] = None
    ) -> None:
        """
        Initializes the DataImporter with an optional file system and caching location.

        :param file_system: The file system to use for loading from S3. Defaults to s3fs.S3FileSystem.
        :param caching_location: Optional directory path for caching files locally.
        """

        self.caching_location = caching_location
        self.s3_gateway = DataS3Gateway(file_system=file_system)
        self.logger = setup_logger()

    def import_data(self, url: str) -> pd.DataFrame:
        """
        Imports data from a given URL, which could be a local file or an S3 path.
        The file format is inferred from the file extension and loaded accordingly.

        :param url: The path to the file, either local or in S3.
        :return: A pandas DataFrame containing the loaded data.
        """
        self.logger.info(f"Importing data from: {url}")
        return (
            self._import_s3_data(url)
            if url.startswith("s3://")
            else self._import_local_data(url)
        )

    def import_folder_data(self, url: str) -> Dict[str, pd.DataFrame]:
        """
        Imports all files within a folder specified by the URL, either local or in S3.
        Each file is loaded based on its format and stored in a dictionary.

        :param url: The URL to the folder, either local or in S3.
        :return: A dictionary with file names as keys and pandas DataFrames as values.
        """
        self.logger.info(f"Importing folder data from: {url}")
        return (
            self._import_s3_folder_data(url)
            if url.startswith("s3://")
            else self._import_local_folder_data(url)
        )

    def _import_s3_data(self, s3_url: str) -> pd.DataFrame:
        """
        Helper method to import data from a single S3 file.

        :param s3_url: The full S3 URL (e.g., 's3://bucket_name/path/to/file.format').
        :return: A pandas DataFrame containing the loaded data.
        """
        parsed_url = urlparse(s3_url)
        bucket_name = parsed_url.netloc
        base_filename, file_format = os.path.splitext(parsed_url.path.lstrip("/"))
        file_format = file_format.lstrip(".")

        s3_file = S3File(
            bucket_name=bucket_name, base_filename=base_filename, format=file_format
        )

        if self.caching_location:
            cached_file_path = os.path.join(
                self.caching_location, f"{base_filename}.{file_format}"
            )
            if os.path.exists(cached_file_path):
                return self._import_local_data(cached_file_path)

            df = self.s3_gateway.import_data(s3_file)
            self._cache_data(df, cached_file_path, file_format)
            return df

        return self.s3_gateway.import_data(s3_file)

    def _import_local_data(self, file_path: str) -> pd.DataFrame:
        """
        Helper method to import data from a local file based on its extension.

        :param file_path: The local file path.
        :return: A pandas DataFrame containing the loaded data.
        """
        file_format = file_path.rsplit(".", 1)[-1].lower()

        if file_format == "csv":
            return pd.read_csv(file_path)
        elif file_format == "parquet":
            return pd.read_parquet(file_path)
        elif file_format == "json":
            return pd.read_json(file_path)
        else:
            raise ValueError(f"Unsupported file format: {file_format}")

    def _import_local_folder_data(self, folder_path: str) -> Dict[str, pd.DataFrame]:
        """
        Helper method to import data from all files in a local folder.

        :param folder_path: The path to the local folder.
        :return: A dictionary with file names as keys and pandas DataFrames as values.
        """
        dfs = {}
        for filename in os.listdir(folder_path):
            file_path = os.path.join(folder_path, filename)
            file_key, _ = os.path.splitext(filename)
            try:
                dfs[file_key] = self._import_local_data(file_path)
            except ValueError as e:
                self.logger.warning(f"Skipping unsupported file '{filename}': {e}")
        return dfs

    def _import_s3_folder_data(self, uri: str) -> Dict[str, pd.DataFrame]:
        """
        Helper method to import data from all files in an S3 folder.

        :param uri: The S3 folder URI.
        :return: A dictionary with file names as keys and pandas DataFrames as values.
        """
        s3folder = S3Folder.from_uri(uri)
        dfs = {}

        for file_path in self.s3_gateway._list(s3folder):
            filename = os.path.basename(file_path)
            file_key, _ = os.path.splitext(filename)
            try:
                dfs[file_key] = self._import_s3_data(file_path)
            except ValueError as e:
                self.logger.warning(
                    f"Skipping unsupported file '{filename}' in S3: {e}"
                )

        return dfs

    def _cache_data(self, df: pd.DataFrame, file_path: str, file_format: str) -> None:
        """
        Caches a DataFrame locally in the specified format.

        :param df: The DataFrame to cache.
        :param file_path: The local file path for caching.
        :param file_format: The format of the file (csv, parquet, json).
        """
        if file_format == "csv":
            df.to_csv(file_path, index=False)
        elif file_format == "parquet":
            df.to_parquet(file_path)
        elif file_format == "json":
            df.to_json(file_path, orient="records", lines=True)
        else:
            raise ValueError(f"Unsupported cache file format: {file_format}")
