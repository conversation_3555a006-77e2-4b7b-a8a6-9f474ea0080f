import os
import pandas as pd
from data_io.data_s3_gateway import DataS3Gateway
from data_io.s3_file import S3File
from typing import Dict
from gresb_utils.setup_logging import setup_logger


class DataExporter:
    """
    A class to export data in parquet format to either local filesystem or S3,
    based on the location provided. If the location starts with 's3://', the
    data is saved to an S3 bucket using the DataS3Gateway. Otherwise, it is saved locally.
    """

    def __init__(self):
        """
        Initialize the DataExporter class. Uses DataS3Gateway for S3 operations.
        """
        self.s3_gateway = DataS3Gateway()
        self.logger = setup_logger()

    def save_dict_to_parquet(
        self, data_dict: Dict[str, pd.DataFrame], location: str, sub_path: str = None
    ) -> None:
        """
        Save a dictionary of DataFrames to parquet files either locally or on S3.

        Args:
            data_dict (dict): A dictionary where the key is the filename, and the value is a pandas DataFrame.
            location (str): Location to save the files. Starts with 's3://' for S3, otherwise local path.
            sub_path (str, optional): Optional sub-path to append to the location.
        """
        if not location.endswith("/"):
            location += "/"

        if sub_path:
            if not sub_path.endswith("/"):
                sub_path += "/"
            location = os.path.join(location, sub_path)

        if location.startswith("s3://"):
            for filename, df in data_dict.items():
                s3_file = S3File.from_uri(f"{location}{filename}.parquet")
                self.logger.info(f"Writing data to {location}{filename}.parquet")
                self.s3_gateway.export_data(df, s3_file)
        else:
            os.makedirs(location, exist_ok=True)
            for filename, df in data_dict.items():
                file_path = os.path.join(location, f"{filename}.parquet")
                self.logger.info(f"Writing data to {file_path}")
                df.to_parquet(file_path)
