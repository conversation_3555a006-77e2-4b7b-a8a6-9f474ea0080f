[tool.poetry]
name = "data-io"
version = "0.1.0"
description = "A package providing helper functions to query and export data from Portal DB or S3."
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.13"
numpy = "2.2.0"
pandas = "2.2.3"
s3fs = "2024.5.0"
botocore = "1.34.106"
boto3 = "1.34.106"
pytest = "^8.3.4"
sql = "2022.4.0"
mypy = "^1.9.0"
pre-commit-hooks = "^4.6.0"
pyarrow = "^18.1.0"
fastparquet = "^2024.2.0"
psycopg2-binary = "^2.9.9"
pytest-mock = "^3.14.0"
python-dotenv = "^1.0.0"
tqdm = "^4.66.5"
black = "^24.8.0"
autopep8 = "^2.3.1"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
