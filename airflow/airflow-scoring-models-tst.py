from airflow import DAG
from airflow.operators.python import PythonOperator
from datetime import datetime, timedelta
import boto3
import json
from botocore.config import Config

# Define Lambda function name and region
LAMBDA_FUNCTION_NAME = "airflow-scoring-model-test"
AWS_REGION = "eu-central-1"

# Configure Boto3 with a proper timeout
config = Config(read_timeout=600, connect_timeout=30)


# Function to invoke Lambda and check the response
def invoke_lambda(function, arguments, **kwargs):
    lambda_client = boto3.client("lambda", region_name=AWS_REGION, config=config)

    logical_date = kwargs["execution_date"]

    # Build the output location dynamically
    year = logical_date.year
    month = f"{logical_date.month:02d}"
    day = f"{logical_date.day:02d}"

    # Can't use fstring to replace dates, as the string is full of dates, use simple replace
    arguments_as_json = json.dumps(arguments)
    arguments_with_year_month_date_path = arguments_as_json.replace(
        "{date}", f"year={year}/month={month}/day={day}"
    )
    arguments_with_year_month_date_path_as_dict = json.loads(
        arguments_with_year_month_date_path
    )

    payload = {
        "function": function,
        "arguments": arguments_with_year_month_date_path_as_dict,
    }

    response = lambda_client.invoke(
        FunctionName=LAMBDA_FUNCTION_NAME,
        InvocationType="RequestResponse",
        Payload=json.dumps(payload),
    )

    response_payload = json.load(response["Payload"])
    if response.get("StatusCode") == 200 and "FunctionError" not in response:
        print("Lambda invocation successful.")
        print(f"Response: {response_payload}")
    else:
        error_message = response_payload.get("errorMessage", "Unknown error occurred.")
        raise Exception(f"Lambda invocation failed: {error_message}")


default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
}

# Define the DAG
with DAG(
    "invoke_scoring_models",
    default_args=default_args,
    description="A DAG to invoke a scoring models AWS Lambda function",
    schedule_interval=None,  # Run manually
    start_date=datetime(2023, 1, 1),
    catchup=False,
) as dag:

    prepare_en1 = PythonOperator(
        task_id="prepare_en1",
        python_callable=invoke_lambda,
        execution_timeout=timedelta(minutes=12),  # Allow enough time for Lambda
        op_kwargs={
            "function": "app_real_estate.tasks.PrepareBenchmarkTask.run",
            "arguments": {
                "input_data_location": "s3://gresb-tst-scoring-models/asset_data.parquet",
                "output_location": "s3://gresb-tst-scoring-models/output2/{date}/",
                "indicator": "en1",
            },
        },
    )

    benchmark_en1 = PythonOperator(
        task_id="benchmark_en1",
        python_callable=invoke_lambda,
        op_kwargs={
            "function": "app_real_estate.tasks.BenchmarkTask.run",
            "arguments": {
                "input_data_location": "s3://gresb-tst-scoring-models/output2/{date}/task=prepare_data/indicator=en1/asset_data_cy.parquet",
                "output_location": "s3://gresb-tst-scoring-models/output2/{date}/",
                "indicator": "en1",
                "extra_dataframes": [
                    "s3://gresb-tst-scoring-models/output2/{date}/task=prepare_data/indicator=en1/descriptions.parquet"
                ],
            },
        },
    )

    score_en1 = PythonOperator(
        task_id="score_en1",
        python_callable=invoke_lambda,
        op_kwargs={
            "function": "app_real_estate.tasks.IndicatorScorerTask.run",
            "arguments": {
                "asset_data_cy": "s3://gresb-tst-scoring-models/output2/{date}/task=prepare_data/indicator=en1/asset_data_cy.parquet",
                "output_location": "s3://gresb-tst-scoring-models/output2/{date}/",
                "indicator": "en1",
                "memberships_folder": "s3://gresb-tst-scoring-models/output2/{date}/task=benchmark/indicator=en1/sub_type=memberships/",
            },
        },
    )

    prepare_gh1 = PythonOperator(
        task_id="prepare_gh1",
        python_callable=invoke_lambda,
        op_kwargs={
            "function": "app_real_estate.tasks.PrepareBenchmarkTask.run",
            "arguments": {
                "input_data_location": "s3://gresb-tst-scoring-models/asset_data.parquet",
                "output_location": "s3://gresb-tst-scoring-models/output2/{date}/",
                "indicator": "gh1",
            },
        },
    )

    benchmark_gh1 = PythonOperator(
        task_id="benchmark_gh1",
        python_callable=invoke_lambda,
        op_kwargs={
            "function": "app_real_estate.tasks.BenchmarkTask.run",
            "arguments": {
                "input_data_location": "s3://gresb-tst-scoring-models/output2/{date}/task=prepare_data/indicator=gh1/asset_data_cy.parquet",
                "output_location": "s3://gresb-tst-scoring-models/output2/{date}/",
                "indicator": "gh1",
                "extra_dataframes": [
                    "s3://gresb-tst-scoring-models/output2/{date}/task=prepare_data/indicator=gh1/descriptions.parquet"
                ],
            },
        },
    )

    score_gh1 = PythonOperator(
        task_id="score_gh1",
        python_callable=invoke_lambda,
        op_kwargs={
            "function": "app_real_estate.tasks.IndicatorScorerTask.run",
            "arguments": {
                "asset_data_cy": "s3://gresb-tst-scoring-models/output2/{date}/task=prepare_data/indicator=gh1/asset_data_cy.parquet",
                "output_location": "s3://gresb-tst-scoring-models/output2/{date}/",
                "indicator": "gh1",
                "memberships_folder": "s3://gresb-tst-scoring-models/output2/{date}/task=benchmark/indicator=gh1/sub_type=memberships/",
            },
        },
    )

    prepare_ws1 = PythonOperator(
        task_id="prepare_ws1",
        python_callable=invoke_lambda,
        op_kwargs={
            "function": "app_real_estate.tasks.PrepareBenchmarkTask.run",
            "arguments": {
                "input_data_location": "s3://gresb-tst-scoring-models/asset_data.parquet",
                "output_location": "s3://gresb-tst-scoring-models/output2/{date}/",
                "indicator": "ws1",
            },
        },
    )

    benchmark_ws1 = PythonOperator(
        task_id="benchmark_ws1",
        python_callable=invoke_lambda,
        op_kwargs={
            "function": "app_real_estate.tasks.BenchmarkTask.run",
            "arguments": {
                "input_data_location": "s3://gresb-tst-scoring-models/output2/{date}/task=prepare_data/indicator=ws1/asset_data_cy.parquet",
                "output_location": "s3://gresb-tst-scoring-models/output2/{date}/",
                "indicator": "ws1",
            },
        },
    )

    score_ws1 = PythonOperator(
        task_id="score_ws1",
        python_callable=invoke_lambda,
        op_kwargs={
            "function": "app_real_estate.tasks.IndicatorScorerTask.run",
            "arguments": {
                "asset_data_cy": "s3://gresb-tst-scoring-models/output2/{date}/task=prepare_data/indicator=ws1/asset_data_cy.parquet",
                "output_location": "s3://gresb-tst-scoring-models/output2/{date}/",
                "indicator": "ws1",
                "memberships_folder": "s3://gresb-tst-scoring-models/output2/{date}/task=benchmark/indicator=ws1/sub_type=memberships/",
            },
        },
    )

    prepare_wt1 = PythonOperator(
        task_id="prepare_wt1",
        python_callable=invoke_lambda,
        op_kwargs={
            "function": "app_real_estate.tasks.PrepareBenchmarkTask.run",
            "arguments": {
                "input_data_location": "s3://gresb-tst-scoring-models/asset_data.parquet",
                "output_location": "s3://gresb-tst-scoring-models/output2/{date}/",
                "indicator": "wt1",
            },
        },
    )

    benchmark_wt1 = PythonOperator(
        task_id="benchmark_wt1",
        python_callable=invoke_lambda,
        op_kwargs={
            "function": "app_real_estate.tasks.BenchmarkTask.run",
            "arguments": {
                "input_data_location": "s3://gresb-tst-scoring-models/output2/{date}/task=prepare_data/indicator=wt1/asset_data_cy.parquet",
                "output_location": "s3://gresb-tst-scoring-models/output2/{date}/",
                "indicator": "wt1",
                "extra_dataframes": [
                    "s3://gresb-tst-scoring-models/output2/{date}/task=prepare_data/indicator=wt1/descriptions.parquet"
                ],
            },
        },
    )

    score_wt1 = PythonOperator(
        task_id="score_wt1",
        python_callable=invoke_lambda,
        op_kwargs={
            "function": "app_real_estate.tasks.IndicatorScorerTask.run",
            "arguments": {
                "asset_data_cy": "s3://gresb-tst-scoring-models/output2/{date}/task=prepare_data/indicator=wt1/asset_data_cy.parquet",
                "output_location": "s3://gresb-tst-scoring-models/output2/{date}/",
                "indicator": "wt1",
                "memberships_folder": "s3://gresb-tst-scoring-models/output2/{date}/task=benchmark/indicator=wt1/sub_type=memberships/",
            },
        },
    )

    # Define task dependencies
    prepare_en1 >> benchmark_en1 >> score_en1
    prepare_gh1 >> benchmark_gh1 >> score_gh1
    prepare_ws1 >> benchmark_ws1 >> score_ws1
    prepare_wt1 >> benchmark_wt1 >> score_wt1
