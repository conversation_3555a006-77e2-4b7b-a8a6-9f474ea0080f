import pandas as pd
from infra_dev_asset.schemas.mappings import response_id


def prepare_response_data_per_section(
    df: pd.DataFrame,
    indicator_variables: list,
    indicator_variables_other: list,
) -> pd.DataFrame:
    """
    Prepares the response data for the specified indicator variables.

    This function processes the DataFrame to include the specified indicator variables.
    If other indicator variables are validated, the data in corresponding columns is
    modified according to validation status.

    Parameters:
    df (pd.DataFrame): The DataFrame containing the response data.
    indicator_variables (list): A list of indicator variable names.
    indicator_variables_other (list): A list of 'other' indicator variable names (if applicable).
    include_other_vld (bool, optional): Indicates whether to include other validation variables (default is False).

    Returns:
    pd.DataFrame: A DataFrame with the processed response data for the specified indicators.
    """

    include_other = indicator_variables_other is not None
    if include_other:
        cols = indicator_variables + indicator_variables_other
    else:
        cols = indicator_variables
    indicator_data = df[cols].copy()
    indicator_data.insert(0, response_id, df[response_id])

    indicator_data = indicator_data.fillna(0)
    return indicator_data
