import pandas as pd
from infra_dev_asset.schemas.mappings import response_id


def check_indicator_score_output(
    indicator_score_table: pd.DataFrame, required_columns
) -> pd.DataFrame:
    """
    Validates the indicator score output table by checking for required columns
    and ensuring score values are within the valid range [0, 1].

    Parameters:
    indicator_score_table (pd.DataFrame): The DataFrame containing the indicator score data.
    required_columns (list): The list of required columns that must be present in the DataFrame.

    Returns:
    pd.DataFrame: The validated indicator score table.

    Raises:
    AssertionError: If the required columns are not present or if the score values are outside the valid range.
    """

    check_required_columns(indicator_score_table, required_columns)
    numeric_columns = list(
        indicator_score_table.columns.difference([response_id]).values
    )
    check_output_scores(indicator_score_table, numeric_columns)

    return indicator_score_table


def check_required_columns(
    indicator_score_table: pd.DataFrame, required_columns: list
) -> None:
    """
    Checks if the required columns are present in the DataFrame.

    Parameters:
    indicator_score_table (pd.DataFrame): The DataFrame to be checked for required columns.
    required_columns (list): The list of required columns.

    Raises:
    AssertionError: If any of the required columns are missing from the DataFrame.
    """

    req_cols_exist = [col in indicator_score_table.columns for col in required_columns]
    missing_cols = [
        required_columns[i]
        for i in range(len(required_columns))
        if not req_cols_exist[i]
    ]
    assert all(req_cols_exist), f"Following columns required: {', '.join(missing_cols)}"


def check_output_scores(
    indicator_score_table: pd.DataFrame, numeric_columns: list
) -> None:
    """
    Checks if the score values are within the valid range [0, 1] and identifies any missing values.

    Parameters:
    indicator_score_table (pd.DataFrame): The DataFrame containing the score data.
    numeric_columns (list): The list of columns to be checked for valid score values.

    Raises:
    AssertionError: If any score values are greater than 1 or less than 0.
    """

    invalid_numeric_cells = (indicator_score_table[numeric_columns] > 1).any().any()
    if invalid_numeric_cells:
        invalid_df = indicator_score_table[
            (indicator_score_table[numeric_columns] > 1).any(axis=1)
        ]
        assert (
            not invalid_numeric_cells
        ), f"Some values are greater than 1.00 \n{invalid_df}"

    invalid_numeric_cells = (indicator_score_table[numeric_columns] < 0).any().any()
    if invalid_numeric_cells:
        invalid_df = indicator_score_table[
            (indicator_score_table[numeric_columns] < 0).any(axis=1)
        ]
        assert (
            not invalid_numeric_cells
        ), f"Some values are less than 0.00 \n{invalid_df}"

    missing_numeric_cells = indicator_score_table[numeric_columns].isnull().any().any()
    if missing_numeric_cells:
        print(f"Warning: There are some missing values \n{missing_numeric_cells}")
