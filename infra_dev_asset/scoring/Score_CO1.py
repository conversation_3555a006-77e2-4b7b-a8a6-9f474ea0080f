import pandas as pd
import numpy as np

from infra_dev_asset.schemas.mappings import response_id
from infra_dev_asset.scoring.CheckIndicatorOutput import check_indicator_score_output
from infra_dev_asset.scoring.ScoreBase import ScoreBase


class ScoreCO1(ScoreBase):
    """
    A class used to calculate scores for the CO1 indicator (Contractor engagement).
    Indicator consists of radiobuttons and percentages with items weighted
    uniformly.
    Below are standard attributes with values specific to the class.

    Attributes
    indicator_name (str): Indicator name used for creating score column.
    indicator_variables (list): A dictionary defining 'primary' and 'other' indicator
    variable names.
    weight_distribution (dict): A dictionary defining weights distributions for each
    section.
    debugging_mode (bool, optional): In the debugging mode intermediate scores are saved
    to final DataFrame. Default is False.
    base_score (ScoreBase): An instance of the ScoreBase class initialized with
    indicator variables and weight distribution.
    include_other_vld (bool): Indicates whether to include other valid indicator
    variables.
    materiality (bool): Indicates whether materiality should be considered.
    diminish (bool): Transform fraction scores based on the diminishing increase in
    scoring curve.

    Methods
    __repr__():  Provides a string representation of the ScoreCO1 instance.
    __init__(df: pd.DataFrame): Initializes the ScoreCO1 instance with the given
    DataFrame.
    get_indicator_variables(): Retrieves the list of indicator variables.
    calculate_score(df: pd.DataFrame): Calculates the score for the CO1 indicator and
    returns a DataFrame.
    """

    def __init__(self, debugging_mode=False):
        """
        Initializes the ScoreCO1 instance with default attributes.
        """
        self.indicator_name = "CO_1"
        self.indicator_variables = self.get_indicator_variables()
        self.weight_distribution = self.assign_weight_distribution()
        self.debugging_mode = debugging_mode
        self.base_score = ScoreBase(
            self.indicator_name,
            self.indicator_variables,
            self.weight_distribution,
        )

    def __repr__(self):
        """
        Provides a string representation of the ScoreCO1 instance.
        """
        return (
            f"ScoreCO1(indicator_name={self.indicator_name}, "
            f"indicator_variables={self.indicator_variables}, "
            f"weight_distribution={self.weight_distribution}, "
            f"debugging_mode={self.debugging_mode}, "
            f"include_other_vld={self.include_other_vld}, "
            f"materiality={self.materiality}, "
            f"diminish={self.diminish})"
        )

    def get_indicator_variables(self) -> dict:
        """
        Retrieves the list of indicator variables.

        Returns:
        dict: A dictionary defining primary and other indicator variable names.
        """
        indicator_variables_dict = {
            "percentage_1": {
                "section_name": f"{self.indicator_name}_1",
                "primary": [
                    f"{self.indicator_name}_A1_A1_{option}"
                    for option in ["PROF", "ESG"]
                ],
                "other": None,
            },
            "percentage_2": {
                "section_name": f"{self.indicator_name}_2",
                "checkbox_1": {
                    "section_name": f"{self.indicator_name}_2_1",
                    "primary": [
                        f"{self.indicator_name}_A1_B1_{i}" for i in range(1, 3)
                    ],
                    "other": None,
                },
                "checkbox_2": {
                    "section_name": f"{self.indicator_name}_2_2",
                    "primary": [
                        f"{self.indicator_name}_A1_B1_A1_{i}" for i in range(1, 3)
                    ],
                    "other": [f"{self.indicator_name}_A1_B1_A1_3"],
                },
            },
        }
        return indicator_variables_dict

    def assign_weight_distribution(self) -> dict:
        """
        Assigns weights distributions for indicator variables.

        Returns:
        dict: A dictionary defining weights distributions.
        """
        weights_distributions_dict = {
            "percentage_1": {
                "section": 1 / 2,
                "primary": np.repeat(1 / 2, 2),
                "other": None,
            },
            "percentage_2": {
                "section": 1 / 2,
                "checkbox_1": {
                    "section": 2 / 3,
                    "primary": np.array([2 / 3, 3 / 3]),
                    "other": None,
                },
                "checkbox_2": {
                    "section": 1 / 3,
                    "primary": np.array([3 / 3, 2 / 3]),
                    "other": np.array([2 / 3]),
                },
            },
        }
        return weights_distributions_dict

    def calculate_score(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculates the score for the CO1 indicator and returns a DataFrame.

        Parameters:
        df (pd.DataFrame): The DataFrame containing the response data.

        Returns:
        pd.DataFrame: A DataFrame with response IDs and their corresponding scores.
        """
        # TODO: this function is too long. Find a way to refactor it.
        score_coverage = self.base_score.calculate_coverage_score(
            df=df, section_item="percentage_1"
        )
        score_coverage_weight = self.weight_distribution["percentage_1"]["section"]

        cleaned_df = self.clean_variables_based_on_coverage(
            df=df, section_item="percentage_2.checkbox_1"
        )

        score_percentage_2_checkbox_1 = self.base_score.prepare_section_score_checkbox(
            section_item="percentage_2.checkbox_1", df=cleaned_df
        )
        weight_percentage_2_checkbox_1 = self.weight_distribution["percentage_2"][
            "checkbox_1"
        ]["section"]

        score_percentage_2_checkbox_2 = self.base_score.prepare_section_score_checkbox(
            section_item="percentage_2.checkbox_2", df=cleaned_df
        )
        weight_percentage_2_checkbox_2 = self.weight_distribution["percentage_2"][
            "checkbox_2"
        ]["section"]

        score_checkbox = (
            score_percentage_2_checkbox_1 * weight_percentage_2_checkbox_1
        ) + (score_percentage_2_checkbox_2 * weight_percentage_2_checkbox_2)
        score_checkbox_weight = self.weight_distribution["percentage_2"]["section"]

        score_indicator = (score_coverage * score_coverage_weight) + (
            score_checkbox * score_checkbox_weight
        )
        indicator_score_name = f"SCORE.F_{self.indicator_name}"
        indicator_score_table = pd.DataFrame(
            {
                response_id: df[response_id],
                indicator_score_name: score_indicator,
            }
        )

        if self.debugging_mode == True:
            percentage_1_name = self.base_score.get_section_score_name("percentage_1")
            indicator_score_table[percentage_1_name] = score_coverage
            percentage_2_name = self.base_score.get_section_score_name("percentage_2")
            indicator_score_table[percentage_2_name] = score_checkbox
            checkbox_1_name = self.base_score.get_section_score_name(
                "percentage_2.checkbox_1"
            )
            indicator_score_table[checkbox_1_name] = score_percentage_2_checkbox_1
            checkbox_2_name = self.base_score.get_section_score_name(
                "percentage_2.checkbox_2"
            )
            indicator_score_table[checkbox_2_name] = score_percentage_2_checkbox_2

        check_indicator_score_output(
            indicator_score_table, indicator_score_table.columns
        )
        return indicator_score_table
