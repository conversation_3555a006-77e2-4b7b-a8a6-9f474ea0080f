import pandas as pd
import numpy as np

from infra_dev_asset.schemas.mappings import response_id
from infra_dev_asset.scoring.CheckIndicatorOutput import check_indicator_score_output
from infra_dev_asset.scoring.ScoreBase import ScoreBase


class ScoreSE3(ScoreBase):
    """
    A class used to calculate scores for the SE3 indicator (Supply chain engagement
    program).
    Indicator consists of one checkbox section.

    Attributes:
    indicator_name (str): Indicator name used for creating score column.
    indicator_variables (dict): A dictionary defining primary and 'other' indicator
    variable names.
    weight_distribution (dict): A dictionary defining weights distributions for each
    section.
    debugging_mode (bool, optional): In the debugging mode intermediate scores are saved
    to final DataFrame. Default is False.
    base_score (ScoreBase): An instance of the ScoreBase class initialized with
    indicator variables and weight distribution.
    include_other_vld (False): Inherited default value from ScoreBase. Indicates whether
    to include 'other' validation data.
    materiality (False): Inherited default value from ScoreBase. Indicates whether
    materiality is considered.
    diminish (False): Inherited default value from ScoreBase. Indicates whether to apply
    diminishing returns.

    Methods:
    __init__(): Initializes the ScoreSE3 instance with default attributes.
    __repr__(): Provides a string representation of the ScoreSE3 instance.
    get_indicator_variables(): Retrieves the list of indicator variables.
    assign_weight_distribution(): Assigns weights distributions for indicator variables.
    calculate_score(df: pd.DataFrame): Calculates the score for the SE3 indicator and
    returns a DataFrame.
    """

    def __init__(self, debugging_mode=False):
        """
        Initializes the ScoreSE3 instance with default attributes.
        """
        self.indicator_name = "SE_3"
        self.indicator_variables = self.get_indicator_variables()
        self.weight_distribution = self.assign_weight_distribution()
        self.debugging_mode = debugging_mode
        self.base_score = ScoreBase(
            self.indicator_name,
            self.indicator_variables,
            self.weight_distribution,
        )

    def __repr__(self):
        """
        Provides a string representation of the ScoreSE3 instance.
        """
        return (
            f"ScoreSE3(indicator_name={self.indicator_name}, "
            f"indicator_variables={self.indicator_variables}, "
            f"weight_distribution={self.weight_distribution}, "
            f"debugging_mode={self.debugging_mode}, "
            f"include_other_vld={self.include_other_vld}, "
            f"materiality={self.materiality}, "
            f"diminish={self.diminish})"
        )

    def get_indicator_variables(self) -> dict:
        """
        Retrieves the list of indicator variables.

        Returns:
        dict: A dictionary defining primary and other indicator variable names.
        """
        checkbox_1_name = f"{self.indicator_name}"

        indicator_variables_dict = {
            "checkbox_1": {
                "section_name": f"{checkbox_1_name}_1",
                "primary": [f"{self.indicator_name}_A1_{i}" for i in range(1, 10)],
                "other": [f"{self.indicator_name}_A1_10"],
            },
            "checkbox_2": {
                "section_name": f"{checkbox_1_name}_2",
                "primary": [f"{self.indicator_name}_A1_{i}" for i in range(21, 30)],
                "other": [f"{self.indicator_name}_A1_30"],
            },
        }
        return indicator_variables_dict

    def assign_weight_distribution(self) -> dict:
        """
        Assigns weights distributions for indicator variables.

        Returns:
        dict: A dictionary defining weights distributions.
        """
        weights_distributions_dict = {
            "checkbox_1": {
                "section": 1 / 2,
                "primary": np.repeat(1 / 8, 9),
                "other": [1 / 8],
            },
            "checkbox_2": {
                "section": 1 / 2,
                "primary": np.repeat(1 / 8, 9),
                "other": [1 / 8],
            },
        }
        return weights_distributions_dict

    def calculate_score(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculates the score for the SE2 indicator and returns a DataFrame.

        Parameters:
        df (pd.DataFrame): The DataFrame containing the response data.

        Returns:
        pd.DataFrame: A DataFrame with response IDs and their corresponding scores.
        """
        score_checkbox_1 = self.base_score.prepare_section_score_checkbox(
            section_item="checkbox_1", df=df
        )
        weight_checkbox_1 = self.weight_distribution["checkbox_1"]["section"]

        score_checkbox_2 = self.base_score.prepare_section_score_checkbox(
            section_item="checkbox_2", df=df
        )
        weight_checkbox_2 = self.weight_distribution["checkbox_2"]["section"]

        score_indicator = (score_checkbox_1 * weight_checkbox_1) + (
            score_checkbox_2 * weight_checkbox_2
        )
        indicator_score_name = f"SCORE.F_{self.indicator_name}"

        indicator_score_table = pd.DataFrame(
            {
                response_id: df[response_id],
                indicator_score_name: score_indicator,
            }
        )

        if self.debugging_mode == True:
            checkbox_1_name = self.base_score.get_section_score_name("checkbox_1")
            indicator_score_table[checkbox_1_name] = score_checkbox_1
            checkbox_2_name = self.base_score.get_section_score_name("checkbox_2")
            indicator_score_table[checkbox_2_name] = score_checkbox_2

        check_indicator_score_output(
            indicator_score_table, indicator_score_table.columns
        )
        return indicator_score_table
