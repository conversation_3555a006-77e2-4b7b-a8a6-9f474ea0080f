import pandas as pd
import numpy as np

from infra_dev_asset.schemas.mappings import vld_status_table
from infra_dev_asset.scoring.GetWeights import get_weights
from infra_dev_asset.scoring.GetWeightsMateriality import get_weights_materiality
from infra_dev_asset.schemas.mappings import response_id


class ScoreBase:
    """
    A base class for scoring indicators.

    Attributes:
    indicator_name (str): Indicator name for the score column.
    indicator_variables (dict): A dictionary of lists containing primary and secondary
    indicator variable names.
    weights_per_indicator (dict): A dictionary of arrays containing the weights for each
    indicator variable.
    include_other_vld (bool): Indicates whether to include other valid indicator
    variables.
    materiality (bool): Indicates whether materiality should be considered.
    diminish (bool): Transform fraction scores based on the diminishing increase in
    scoring curve.
    """

    def __init__(
        self,
        indicator_name,
        indicator_variables,
        weight_distribution=None,
        include_other_vld=False,
        materiality=False,
        diminish=False,
    ):
        """
        Initializes the ScoreBase instance.
        """
        self.indicator_name = (indicator_name,)
        self.indicator_variables = indicator_variables
        self.weight_distribution = weight_distribution
        self.include_other_vld = include_other_vld
        self.materiality = materiality
        self.diminish = diminish

    def prepare_response_data_per_section(
        self,
        df: pd.DataFrame,
        section_variables: list,
        section_variables_other: list,
    ) -> pd.DataFrame:
        """
        Prepares the response data for the specified section variables.

        This function processes the DataFrame to include the specified section variables.
        If other section variables are validated, the data in corresponding columns is
        modified according to validation status.

        Parameters:
        df (pd.DataFrame): The DataFrame containing the response data.
        section_variables (list): A list of section variable names.
        section_variables_other (list): A list of 'other' section variable names (if applicable).

        Returns:
        pd.DataFrame: A DataFrame with the processed response data for the specified indicators.
        """
        # note: If no response answered a particular variable, it won't be present in the survey data.
        existing_variables = [var for var in section_variables if var in df.columns]
        section_data = df[existing_variables].copy()
        section_data.insert(0, response_id, df[response_id])

        if section_variables_other is not None:
            if set(section_variables_other).issubset(set(df.columns)):
                # Check carefully! Perhaps it's not always RE2.
                validation_str = "RE2"
                section_data_other = self.add_points_based_on_validation_status(
                    df, section_variables_other, validation_str
                )
                section_data = section_data.merge(section_data_other)

        section_data = section_data.fillna(0)
        return section_data

    def get_section_item_in_a_dict(
        self, indicator_dict: dict, section_item: str
    ) -> dict:
        """
        Get a section in a indicator variables or weight distribution dictionaries.

        Parameters:
        indicator_dict (dict): Dictionary containing indicator data. It can be
        `indicator_variables` or `weight_distribution`.
        section_item (str): The key(s) to identify the section item to extract from
        `indicator_dict`. If `indicator_dict` is a nested dictionary, you can access the
        desired section by adding `.`, for example "checkbox_1.checkbox_1_1".

        Returns:
        dict: Dictionary containing the `section_item` data.
        """
        keys = section_item.split(".")
        section_data = indicator_dict
        for key in keys:
            section_data = section_data[key]
        return section_data

    def prepare_section_score_checkbox(
        self, section_item: str, df: pd.DataFrame, **kwargs: dict
    ) -> pd.DataFrame:
        """
        Calculates the score for a checkbox section item. Wrapper around
        calculate_checkbox_score, with data preparation and weight calculations added.

        Parameters:
        section_item (str): The key(s) to identify the section item in
        `indicator_variables`. If `indicator_variables` has nested dictionaries, you can
        access the desired section by adding `.`, for example "checkbox_1.checkbox_1_1".
        df (pd.DataFrame): The DataFrame containing the response data.
        **kwargs:
            Additional keyword arguments:
            - codebook_survey (pd.DataFrame): A DataFrame providing additional survey
            metadata.
            - esg_topics_table (pd.DataFrame): A DataFrame providing ESG topics
            information.

        Returns:
        np.array: An array containing the calculated scores for the checkbox section item.
        """
        indicator_variables = self.get_section_item_in_a_dict(
            self.indicator_variables, section_item
        )
        section_variables = indicator_variables["primary"]
        section_variables_other = indicator_variables["other"]
        weight_data = self.get_section_item_in_a_dict(
            self.weight_distribution, section_item
        )

        # TODO: make proper materiality flag here. Current one is global, does not work per section.
        if isinstance(weight_data["primary"], str):
            weights = get_weights_materiality(
                df,
                kwargs.get("codebook_survey"),
                kwargs.get("esg_topics_table"),
                section_variables,
                section_variables_other,
            )
        else:
            weights = get_weights(
                df, section_variables, section_variables_other, weight_data
            )

        section_data = self.prepare_response_data_per_section(
            df, section_variables, section_variables_other
        )

        score_checkbox = self.calculate_checkbox_score(section_data, weights)

        return score_checkbox

    def calculate_evidence_score(
        self, df: pd.DataFrame, section_item: str, validation_str="RE3"
    ) -> pd.DataFrame:
        """
        Calculates the score for the evidence indicator.

        Parameters:
        df (pd.DataFrame): The DataFrame containing the response data.
        section_item (str): Name of the section items, for example "evidence_1".
        validation_str (str, optional): Lookup key for evidence score table. Default is "RE3".

        Returns:
        np.array: An array containing the calculated scores for the evidence indicator.
        """

        evidence_variables = self.indicator_variables[section_item]["variable"]
        if not isinstance(evidence_variables, list):
            evidence_variables = [evidence_variables]
        evidence_weights = self.weight_distribution[section_item]
        score_evidence_per_variable = self.validation_to_score(
            df, evidence_variables, validation_str
        )
        score_evidence = np.sum(score_evidence_per_variable * evidence_weights, axis=1)
        return score_evidence

    def calculate_checkbox_score(
        self,
        section_data: pd.DataFrame,
        weights: pd.DataFrame,
    ) -> pd.DataFrame:
        """
        Calculate the checkbox score for the given section data and weights.

        Parameters:
        section_data (pd.DataFrame): The DataFrame containing the section data for the
        responses.
        weights (pd.DataFrame): The DataFrame containing the weights for each section
        variable.

        Returns:
        pd.DataFrame:
            A DataFrame with the calculated checkbox scores.
        """
        cols = section_data.columns

        # Ensure same column order for section_data and weights
        section_data_ordered = section_data[cols].drop(response_id, axis=1)
        weights_ordered = weights[cols].drop(response_id, axis=1)

        # Calculate the checkbox score
        score_checkbox = np.sum(
            section_data_ordered.values * weights_ordered.values,
            axis=1,
        )

        score_checkbox = np.minimum(score_checkbox, 1)
        return score_checkbox

    def calculate_radiobutton_score(
        self, section_item: str, section_data: pd.DataFrame
    ) -> pd.Series:
        """
        Calculate the score for a radiobutton field in a DataFrame.

        This function evaluates the specified `section_variable` column in the
        `section_data` DataFrame by mapping values to weights (in this case "weight"
        means "score").

        Parameters:
        section_data (pd.DataFrame): A DataFrame containing the data for the section.
        section_variable (str): The column name in the DataFrame to be evaluated.

        Returns:
        pd.Series: A Series containing the scores.
        """
        section_variable = self.indicator_variables[section_item]["variable"]
        values = self.weight_distribution[section_item]["values"]
        weights = self.weight_distribution[section_item]["weights"]
        if section_variable in section_data:
            score_radiobutton = section_data[section_variable].copy().fillna(0)
            score_radiobutton = score_radiobutton.map(dict(zip(values, weights)))
        else:
            print(
                f"Warning: column {section_variable} not found in data, all scores are 0"
            )
            score_radiobutton = pd.Series(np.zeros(len(section_data)))
        return score_radiobutton

    def calculate_aligned_guidelines_score(
        self, section_item: str, df: pd.DataFrame
    ) -> pd.Series:
        """
        Calculate the score for an aligned guidance field in a DataFrame.

        Parameters:
        section_data (pd.DataFrame): A DataFrame containing the data for the section.
        section_item (str): The section name in the DataFrame to be evaluated.

        Returns:
        pd.Series: A Series containing the scores.
        """
        guideline_var = self.indicator_variables[section_item]["variable"]
        guideline_other_var = self.indicator_variables[section_item]["other"]
        score_column = self.indicator_variables[section_item]["section_name"]

        df[score_column] = (
            np.where(
                (df[guideline_var].notna())
                & (df[guideline_var].str.upper() != "OTHER"),
                1,
                0,
            )
            if guideline_var in df
            else 0
        )

        if (guideline_other_var is not None) & (guideline_other_var in df.columns):
            guideline_other_validation_score = (
                self.validation_to_score(df, [guideline_other_var], "RE2")
                .loc[:, guideline_other_var]
                .values
            )
            df[score_column] = df[score_column] + guideline_other_validation_score

        return df[score_column]

    def calculate_coverage_score(self, df: pd.DataFrame, section_item: str) -> list:
        """
        Calculate the coverage score based if the entity has training and development
        for employees or contractors.
        Note: this function only works if the section_item is composed by 2 variables.

        Parameters:
        df (pd.DataFrame): The DataFrame containing the response data.
        section_item (str): The section name in the DataFrame to be evaluated.

        Returns:
        list: a list with calculated coverage score for all responses.
        """
        # TODO: improve this to work on multiple variables and not only when the item
        # has 2 variables.
        coverage_variables = self.indicator_variables[section_item]["primary"]
        coverage_var_1 = coverage_variables[0]
        coverage_var_2 = coverage_variables[1]

        weight_var_1 = self.weight_distribution[section_item]["primary"][0]
        weight_var_2 = self.weight_distribution[section_item]["primary"][1]

        # Training and development for employees - linear score based on input percentage
        score_coverage_1 = np.where(
            df[coverage_var_1].isna(), 0, df[coverage_var_1] / 100
        )
        score_coverage_2 = np.where(
            df[coverage_var_2].isna(), 0, df[coverage_var_2] / 100
        )

        score_coverage = (
            score_coverage_1 * weight_var_1 + score_coverage_2 * weight_var_2
        )
        return score_coverage

    def clean_variables_based_on_coverage(
        self, df: pd.DataFrame, section_item: str
    ) -> pd.DataFrame:
        """
        Clean checkbox variables related to employee/contractor satisfaction,
        `[EM,CO]_1_A1_B1_1` and `[EM,CO]_1_A1_B1_2`. The checkboxes are scored only
        when the percentage coverage is bigger than zero.

        Parameters:
        df (pd.DataFrame): The DataFrame containing the response data. This should
        contain employee/contractor satisfaction variables.
        section_item (str): The section name in the DataFrame to be evaluated.

        Returns:
        pd.DataFrame: A DataFrame with employee/contractor satisfaction variables
        cleaned.
        """
        # this validation is important, otherwise the columns will be created with nan
        # values
        indicator_variables = self.get_section_item_in_a_dict(
            self.indicator_variables, section_item
        )
        variables = indicator_variables["primary"]
        missing_columns = [col for col in variables if col not in df.columns]

        df_copy = df.copy()
        # Employee satisfaction
        for variable in variables:
            if not variable in missing_columns:
                has_survey_coverage = (df_copy[f"{variable}_PCOV"] > 0) & (
                    df_copy[f"{variable}_PCOV"].notna()
                )
                df_copy.loc[~has_survey_coverage, variable] = np.nan
        return df_copy

    def calculate_performance_by_section(
        self,
        df: pd.DataFrame,
        section: str,
    ) -> list:
        """
        Calculate the performance score for each section in indicator variables based on
        specified criteria.

        Parameters:
        df (pd.DataFrame): The DataFrame containing sector data.
        section (str): The dictionary section to be evaluated.

        Returns:
        list: a list with calculated performance score.
        """
        if section not in self.indicator_variables:
            raise ValueError(f"No configuration found for row_name: {section}")
        variables = self.indicator_variables[section]

        if section not in self.weight_distribution:
            raise ValueError(f"No weights found for row_name: {section}")
        weights = self.weight_distribution[section]["primary"].reshape(-1, 1)

        columns = variables["primary"]
        df_variables = df[columns].copy()

        # Standard scoring by presence
        df_score = df_variables.notna().astype(int)

        # Special scoring for data coverage dropdown
        for col in df_variables.columns:
            if "Full coverage (100%)" in df_variables[col].unique():
                df_score[col] = df_variables[col].apply(
                    lambda x: 1 if x == "Full coverage (100%)" else 0
                )

        scores = df_score.values @ weights
        return scores.flatten()

    def validation_to_score(
        self, df: pd.DataFrame, vld_col: list, code: str
    ) -> pd.DataFrame:
        """
        Maps a validation status to a score for a particular column of a DataFrame.

        Parameters:
        ----------
        df (pd.DataFrame): The input DataFrame including the column names of the related
        indicator variables.
        vld_col (list): Column name(s) that contain the validation statuses to be
        scored.
        code (str): The validation code that maps the validation status to a score.
            Should match one of the ["RE1", "RE2", "RE3", "RE4", "RE5"].

        Returns:
        ----------
        pd.DataFrame: The scores associated with the validation statuses for a given
        code.
        """
        # Load validation statuses
        vld_lookup = pd.DataFrame(vld_status_table[code])

        # Map validation statuses to scores
        def map_scores(col):
            return col.map(vld_lookup.set_index("value")["score"])

        if isinstance(vld_col, list):
            scores = pd.DataFrame()
            for vld_col_i in vld_col:
                scores[vld_col_i] = (
                    df[[vld_col_i]].apply(map_scores)
                    if vld_col_i in df
                    else pd.Series(np.zeros(len(df)))
                )
        else:
            scores = (
                df[vld_col].apply(map_scores)
                if vld_col in df
                else pd.Series(np.zeros(len(df)))
            )
        return scores

    def add_points_based_on_validation_status(
        self, df: pd.DataFrame, section_variables_other: list, validation_str="RE2"
    ) -> pd.DataFrame:
        """
        Calculates validation scores for "other" variables in the DataFrame.

        Parameters:
        df (pd.DataFrame): The DataFrame containing the response data.
        section_variables_other (list): List of other variables to apply validation
        scores to.
        validation_str (str, optional): The validation string to use. Defaults to "RE2".

        Returns:
        pd.DataFrame: DataFrame with validation scores applied to specified variables.
        """
        cols = section_variables_other + [response_id]
        df_out = df[cols].copy()
        for other_cb_var in section_variables_other:
            other_vld_var = f"{other_cb_var}_OTHER_VLD"
            other_vld = self.validation_to_score(df, [other_vld_var], validation_str)
            df_out[other_cb_var] = np.where(
                df_out[other_cb_var] != 1, 0, other_vld[other_vld_var]
            )
        return df_out

    def get_section_score_name(self, section_identifier: str) -> str:
        """
        Assigns the name of the score variable for a given section_identifier

        Parameters:
        section_identifier (str): Identifier from specfic child indicator class setup.
        For example, "checkbox_1". If it is a nested dictionary, you can access the
        desired section by adding `.`, for example "checkbox_1.checkbox_1_1".

        Returns:
        str: Section score variable name
        """
        section_data = self.get_section_item_in_a_dict(
            self.indicator_variables, section_identifier
        )
        return f"SCORE.F_{section_data['section_name']}"
