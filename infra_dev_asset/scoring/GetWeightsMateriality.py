import pandas as pd
from infra_dev_asset.schemas.mappings import (
    response_id,
    materiality_weights_mapping,
    survey_materiality_key,
)


def get_weights_materiality(
    df: pd.DataFrame,
    codebook_survey: pd.DataFrame,
    esg_topics_table: pd.DataFrame,
    indicator_variables: list,
    indicator_variables_other=None,
    normalize_weights=True,
) -> pd.DataFrame:
    """
    Calculates materiality weights for the given indicator variables based on the survey data and ESG topics table.

    This function maps ESG topic names to the provided set of indicator variable names,
    retrieves materiality weights for the survey data entries, and maps indicator variable
    names to the materiality weights through the ESG topic names.
    It also optionally includes weights for other indicator variables.

    Parameters:
    df (pd.DataFrame): The DataFrame containing the survey data.
    codebook_survey (pd.DataFrame): The DataFrame containing the codebook survey data.
    esg_topics_table (pd.DataFrame): The DataFrame containing the ESG topics data.
    indicator_variables (list): A list of indicator variable names.
    indicator_variables_other (list, optional): A list of 'other' indicator variable names (if applicable).

    Returns:
    pd.DataFrame: A DataFrame with response IDs and their corresponding materiality weights in wide format.
    """
    include_other = indicator_variables_other is not None

    # Maps the ESG topic names to provided set of indicator variable names
    indicator_variable_topic_name = get_indicator_text(
        codebook_survey, indicator_variables
    )

    # Retrieves materiality weights for the survey data entries together with corresponding ESG topic names
    data_materiality_weights = map_materiality_to_numeric(df, esg_topics_table)

    # Maps indicator variable names to the materiality weights by merging by ESG topic name
    data_materiality_weights = pd.merge(
        data_materiality_weights, indicator_variable_topic_name, on="esg_topic_name"
    )[[response_id, "materiality_weight", "indicator_variable"]]

    # Pivots data to wide format
    data_weights_wide = data_materiality_weights.pivot(
        index=response_id, columns="indicator_variable", values="materiality_weight"
    ).reset_index()

    if include_other:
        # Following the guide, assigning medium weight to 'OTHER' variables
        data_weights_wide[indicator_variables_other] = materiality_weights_mapping[
            "medium"
        ]
    weight_sums = data_weights_wide[indicator_variables].sum(axis=1)
    weight_columns = indicator_variables
    if include_other:
        weight_columns = weight_columns + indicator_variables_other

    if normalize_weights:
        data_weights_wide[weight_columns] = data_weights_wide[weight_columns].div(
            weight_sums, axis=0
        )
    return data_weights_wide


def get_indicator_text(
    codebook_survey: pd.DataFrame, indicator_variables: list
) -> pd.DataFrame:
    """
    Gets the indicator text for the specified indicator variables from the codebook survey.

    Parameters:
    codebook_survey (pd.DataFrame): The codebook survey DataFrame containing information about variables.
    indicator_variables (list): A list of indicator variables for which text needs to be retrieved.

    Returns:
    pd.DataFrame: A DataFrame with columns 'indicator_variable' and 'esg_topic_name' containing the text for the specified indicator variables.
    """

    is_correct_variable = codebook_survey["cb_variable"].isin(indicator_variables)
    indicator_text = codebook_survey.loc[is_correct_variable, ["cb_variable", "text"]]
    indicator_text = indicator_text.rename(
        columns={"text": "esg_topic_name", "cb_variable": "indicator_variable"}
    )
    return indicator_text


def map_materiality_to_numeric(
    df: pd.DataFrame, esg_topics_table: pd.DataFrame
) -> pd.DataFrame:
    """
    Maps materiality text values to numeric weights for all responses and attaches corresponding ESG topic names.

    Parameters:
    df (pd.DataFrame): The input DataFrame containing survey data.
    esg_topics_table (pd.DataFrame): The ESG topics table containing the mapping of ESG topics.

    Returns:
    pd.DataFrame: DataFrame with RESPONSE_ID, esg_topic_name, and materiality_weight.
    """

    # Extract materiality data
    materiality_columns = [c for c in df.columns if survey_materiality_key in c]
    materiality_data = df.loc[:, [response_id] + materiality_columns]

    # Reshape data to long format
    materiality_data = materiality_data.melt(
        id_vars=[response_id], var_name="materiality_variable", value_name="str_value"
    )
    materiality_data["esg_id"] = (
        materiality_data["materiality_variable"]
        .str.replace(survey_materiality_key, "")
        .astype(int)
    )
    materiality_data["materiality_weight"] = materiality_data["str_value"].map(
        materiality_weights_mapping
    )

    # Merge materiality data with ESG topics
    materiality_data = pd.merge(
        materiality_data, esg_topics_table, left_on="esg_id", right_on="id"
    )
    materiality_data.rename(columns={"name": "esg_topic_name"}, inplace=True)
    materiality_data = materiality_data[
        [response_id, "esg_topic_name", "materiality_weight"]
    ]

    return materiality_data
