import pandas as pd
import numpy as np

from infra_dev_asset.scoring.CheckIndicatorOutput import check_indicator_score_output
from infra_dev_asset.scoring.ScoreBase import ScoreBase


class ScoreLE3(ScoreBase):
    """
    A class used to calculate scores for the LE3 indicator (Leadership 3: ESG,
    climate-related, Human Capital and/or Health and
    Safety senior decision maker).
    Indicator consists of one checkbox section then radiobuttons with items weighted
    uniformly.
    Below are standard attributes with values specific to the class.

    Attributes
    indicator_name (str): Indicator name used for creating score column.
    indicator_variables (list): A dictionary defining 'primary' and 'other' indicator
    variable names.
    weight_distribution (dict): A dictionary defining weights distributions for each
    section.
    debugging_mode (bool, optional): In the debugging mode intermediate scores are saved
    to final DataFrame. Default is False.
    base_score (ScoreBase): An instance of the ScoreBase class initialized with
    indicator variables and weight distribution.
    include_other_vld (bool): Indicates whether to include other valid indicator
    variables.
    materiality (bool): Indicates whether materiality should be considered.
    diminish (bool): Transform fraction scores based on the diminishing increase in
    scoring curve.

    Methods
    __repr__():  Provides a string representation of the ScoreLE3 instance.
    __init__(df: pd.DataFrame): Initializes the ScoreLE3 instance with the given
    DataFrame.
    get_indicator_variables(): Retrieves the list of indicator variables.
    calculate_score(df: pd.DataFrame): Calculates the score for the LE3 indicator and
    returns a DataFrame.
    """

    def __init__(self, debugging_mode=False):
        """
        Initializes the ScoreLE3 instance with default attributes.
        """
        self.indicator_name = "LE_3"
        self.indicator_variables = self.get_indicator_variables()
        self.weight_distribution = self.assign_weight_distribution()
        self.debugging_mode = debugging_mode
        self.base_score = ScoreBase(
            self.indicator_name,
            self.indicator_variables,
            self.weight_distribution,
        )

    def __repr__(self):
        """
        Provides a string representation of the ScoreLE3 instance.
        """
        return (
            f"ScoreLE3(indicator_name={self.indicator_name}, "
            f"indicator_variables={self.indicator_variables}, "
            f"weight_distribution={self.weight_distribution}, "
            f"debugging_mode={self.debugging_mode}, "
            f"include_other_vld={self.include_other_vld}, "
            f"materiality={self.materiality}, "
            f"diminish={self.diminish})"
        )

    def get_indicator_variables(self) -> dict:
        """
        Retrieves the list of indicator variables.

        Returns:
        dict: A dictionary defining primary and other indicator variable names.
        """
        checkbox_1_variables = [f"{self.indicator_name}_A1_{i}" for i in range(1, 5)]
        indicator_variables_dict = {
            "checkbox_1": {
                "section_name": f"{self.indicator_name}_1",
                "primary": checkbox_1_variables,
                "other": None,
            },
            "radiobutton_1": {
                "section_name": f"{self.indicator_name}_1",
                "primary": f"{self.indicator_name}_A1_1_A",
                "other": [f"{self.indicator_name}_A1_1_A3"],
            },
            "radiobutton_2": {
                "section_name": f"{self.indicator_name}_2",
                "primary": f"{self.indicator_name}_A1_2_A",
                "other": [f"{self.indicator_name}_A1_2_A5"],
            },
            "radiobutton_3": {
                "section_name": f"{self.indicator_name}_3",
                "primary": f"{self.indicator_name}_A1_3_A",
                "other": [f"{self.indicator_name}_A1_3_A5"],
            },
            "radiobutton_4": {
                "section_name": f"{self.indicator_name}_4",
                "primary": f"{self.indicator_name}_A1_4_A",
                "other": [f"{self.indicator_name}_A1_4_A5"],
            },
        }
        return indicator_variables_dict

    def assign_weight_distribution(self) -> dict:
        """
        Assigns weights distributions for indicator variables.

        Returns:
        dict: A dictionary defining weights distributions.
        """
        weights_distributions_dict = {
            "checkbox_1": {
                "section": 1,
                "primary": np.array([3 / 6, 1 / 6, 1 / 6, 1 / 6]),
                "other": None,
            },
            "radiobutton_1": {
                "section": 3 / 6,
                "weights": np.repeat(1, 6),
                "values": np.array([1, 2, 5, 6, 3]),
            },
            "radiobutton_2": {
                "section": 1 / 6,
                "weights": np.repeat(1, 6),
                "values": np.arange(1, 6),
            },
            "radiobutton_3": {
                "section": 1 / 6,
                "weights": np.repeat(1, 6),
                "values": np.arange(1, 6),
            },
            "radiobutton_4": {
                "section": 1 / 6,
                "weights": np.repeat(1, 6),
                "values": np.arange(1, 6),
            },
        }
        return weights_distributions_dict

    def calculate_score(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculates the score for the LE3 indicator (radiobuttons 1-4) and returns a
        DataFrame.

        Parameters:
        df (pd.DataFrame): The DataFrame containing the response data.

        Returns:
        pd.DataFrame: A DataFrame with response IDs and their corresponding scores.
        """

        score_radiobutton_1 = self.calculate_radiobutton_score_LE3(
            df=df,
            radiobutton="radiobutton_1",
            primary_var="LE_3_A1_1_A",
            other_vld_var="LE_3_A1_1_A3_OTHER_VLD",
            value_for_validation_check=3,
        )
        weight_radiobutton_1 = self.weight_distribution["radiobutton_1"]["section"]

        score_radiobutton_2 = self.calculate_radiobutton_score_LE3(
            df=df,
            radiobutton="radiobutton_2",
            primary_var="LE_3_A1_2_A",
            other_vld_var="LE_3_A1_2_A5_OTHER_VLD",
            value_for_validation_check=5,
        )
        weight_radiobutton_2 = self.weight_distribution["radiobutton_2"]["section"]

        score_radiobutton_3 = self.calculate_radiobutton_score_LE3(
            df=df,
            radiobutton="radiobutton_3",
            primary_var="LE_3_A1_3_A",
            other_vld_var="LE_3_A1_3_A5_OTHER_VLD",
            value_for_validation_check=5,
        )
        weight_radiobutton_3 = self.weight_distribution["radiobutton_3"]["section"]

        score_radiobutton_4 = self.calculate_radiobutton_score_LE3(
            df=df,
            radiobutton="radiobutton_4",
            primary_var="LE_3_A1_4_A",
            other_vld_var="LE_3_A1_4_A5_OTHER_VLD",
            value_for_validation_check=5,
        )
        weight_radiobutton_4 = self.weight_distribution["radiobutton_4"]["section"]

        score_indicator = (
            (score_radiobutton_1 * weight_radiobutton_1)
            + (score_radiobutton_2 * weight_radiobutton_2)
            + (score_radiobutton_3 * weight_radiobutton_3)
            + (score_radiobutton_4 * weight_radiobutton_4)
        )
        indicator_score_name = f"SCORE.F_{self.indicator_name}"

        indicator_score_table = pd.DataFrame(
            {
                "RESPONSE_ID": df["RESPONSE_ID"],
                indicator_score_name: score_indicator,
            }
        )

        if self.debugging_mode == True:
            radiobutton_1_name = self.base_score.get_section_score_name("radiobutton_1")
            indicator_score_table[radiobutton_1_name] = score_radiobutton_1
            radiobutton_2_name = self.base_score.get_section_score_name("radiobutton_2")
            indicator_score_table[radiobutton_2_name] = score_radiobutton_2
            radiobutton_3_name = self.base_score.get_section_score_name("radiobutton_3")
            indicator_score_table[radiobutton_3_name] = score_radiobutton_3
            radiobutton_4_name = self.base_score.get_section_score_name("radiobutton_4")
            indicator_score_table[radiobutton_4_name] = score_radiobutton_4

        check_indicator_score_output(
            indicator_score_table, indicator_score_table.columns
        )
        return indicator_score_table

    def calculate_radiobutton_score_LE3(
        self,
        df: pd.DataFrame,
        radiobutton: str,
        primary_var: str,
        other_vld_var: str,
        value_for_validation_check: int,
    ) -> np.array:
        """
        Calculate the score for a LE3 radiobutton fields in a DataFrame.

        Parameters:
        df (pd.DataFrame): The DataFrame containing the response data.
        radiobutton (str): Radiobutton label. It can be: radiobutton_1, radiobutton_2,
        radiobutton_3, and radiobutton_4.
        primary_var (str): The variable to be scored.
        other_vld_var (str): The other validation variable related to the primary
        variable.
        value_for_validation_check (int): An integer that indicates if "other" option
        was chosen and, if so, it will be needed to check the VLD variable.

        Return:
        np.array: An array with the scores for the specified radiobutton.
        """
        # TODO: maybe we can bring this verification to validation_to_score
        if other_vld_var in df.columns:
            validation_score_df = self.base_score.validation_to_score(
                df, [other_vld_var], "RE4"
            )
            validation_score = validation_score_df[other_vld_var].to_list()
        else:
            # if there is no answer for the variable, there is no score.
            validation_score = 0

        primary_score = self.map_weights_from_response_values(
            df[primary_var].tolist(),
            self.weight_distribution[radiobutton]["values"],
            self.weight_distribution[radiobutton]["weights"],
        )

        score_radiobutton = np.where(
            df[primary_var] == value_for_validation_check,
            validation_score,
            primary_score,
        )
        score_radiobutton[np.isnan(df[primary_var])] = 0

        return score_radiobutton

    def map_weights_from_response_values(
        self, values: list, weights_value: list, weights_weight: list
    ) -> np.array:
        """
        Map the weights of a list of values.

        Parameters:
        values (list): List containing options chosen by participants for a
        indicator/question. This comes from response data: response_data[var].to_list().
        weights_value (list): Values from weight_distribution.
        weights_weight (list): Weights from weight_distribution.

        Returns:
        np.array: A array containing respective weights for all values in values list.
        """
        mapping_dict = dict(zip(weights_value, weights_weight[: len(weights_value)]))
        # get(value, np.nan): If value is found in mapping_dict, the corresponding value
        # from the dictionary is returned. Otherwise, np.nan is returned.
        mapped_list = [mapping_dict.get(value, np.nan) for value in values]
        return mapped_list
