import pandas as pd
import numpy as np
from infra_dev_asset.schemas.mappings import response_id
from infra_dev_asset.scoring.CheckIndicatorOutput import check_indicator_score_output
from infra_dev_asset.scoring.ScoreBase import ScoreBase


class ScoreLE2(ScoreBase):
    """
    A class used to calculate scores for the LE2 indicator (Leadership 2: ESG objectives).
    Indicator consists of two sections with one checkbox each. Sections
    are weighted and indicator is further modified according to evidence presented.

    Attributes:
    indicator_name (str): Indicator name used for creating score column.
    indicator_variables (dict): A dictionary defining primary and 'other' indicator variable names for checkbox_1, checkbox_2, and evidence.
    weight_distribution (dict): A dictionary defining weights distributions for each section.
    debugging_mode (bool, optional): In the debugging mode intermediate scores are saved to final DataFrame. Default is False.
    base_score (ScoreBase): An instance of the ScoreBase class initialized with indicator variables and weight distribution.
    include_other_vld (False): Inherited default value from ScoreBase. Indicates whether to include 'other' validation data.
    materiality (False): Inherited default value from ScoreBase. Indicates whether materiality is considered.
    diminish (False): Inherited default value from ScoreBase. Indicates whether to apply diminishing returns.

    Methods:
    __init__(): Initializes the ScoreLE2 instance with default attributes.
    __repr__(): Provides a string representation of the ScoreLE2 instance.
    get_indicator_variables(): Retrieves the list of indicator variables.
    assign_weight_distribution(): Assigns weights distributions for indicator variables.
    calculate_score(df: pd.DataFrame): Calculates the score for the LE2 indicator (indicator, checkbox_1, checkbox_2, evidence) and returns a DataFrame.
    """

    def __init__(self, debugging_mode=False):
        """
        Initializes the ScoreLE2 instance with default attributes.
        """
        self.indicator_name = "LE_2"
        self.indicator_variables = self.get_indicator_variables()
        self.weight_distribution = self.assign_weight_distribution()
        self.debugging_mode = debugging_mode
        self.base_score = ScoreBase(
            self.indicator_name,
            self.indicator_variables,
            self.weight_distribution,
        )

    def __repr__(self):
        """
        Provides a string representation of the ScoreLE2 instance.
        """
        return (
            f"ScoreLE2(indicator_name={self.indicator_name}, "
            f"indicator_variables={self.indicator_variables}, "
            f"weight_distribution={self.weight_distribution}, "
            f"debugging_mode={self.debugging_mode}, "
            f"include_other_vld={self.base_score.include_other_vld}, "
            f"materiality={self.base_score.materiality}, "
            f"diminish={self.base_score.diminish})"
        )

    def get_indicator_variables(self):
        """
        Retrieves the dictionary of indicator variables.

        Returns:
        dict:
            A dictionary defining 'primary' and other 'indicator' variable names for checkbox_1, checkbox_2, and the corresponding evidence variables.
        """

        # There seems to be an error in codebook excel sheet, LE_2_A1_1_1 should be LE_2_A1_1 (section score)
        indicator_variables_dict = {
            "checkbox_1": {
                "section_name": f"{self.indicator_name}_1",
                "primary": [f"{self.indicator_name}_A1_1_{i}" for i in range(2, 5)],
                "other": None,
            },
            "checkbox_2": {
                "section_name": f"{self.indicator_name}_2",
                "primary": [f"{self.indicator_name}_A1_2_{i}" for i in range(1, 3)],
                "other": None,
            },
            "evidence_1": {
                "section_name": f"{self.indicator_name}_3",
                "variable": [
                    f"{self.indicator_name}_A1_A{i}_EVD_VLD" for i in range(1, 3)
                ],
                "other": None,
            },
        }
        return indicator_variables_dict

    def assign_weight_distribution(self):
        """
        Assigns weights distributions for indicator variables.

        Returns:
        dict:
            A dictionary defining weights distributions for checkbox_1, checkbox_2, and the corresponding evidence weights.
        """

        # Following weights are for indicator variables including potentially incorrect LE_2_A1_1_1.
        # We believe 4/5 is a section weight (this way scores match with R model).
        weight_distribution_dict = {
            "checkbox_1": {
                "section": 4.0 / 5,
                "primary": np.array([1.0 / 3, 1.0 / 3, 1.0 / 3]),
                "other": None,
            },
            "checkbox_2": {
                "section": 1.0 / 5,
                "primary": np.array([1.0 / 2, 1.0 / 2]),
                "other": None,
            },
            "evidence_1": [1.0, 3.0 / 4],
        }
        return weight_distribution_dict

    def calculate_score(self, df: pd.DataFrame):
        """
        Calculates the score for the LE2 indicator and returns a DataFrame.
        score = (score_checkbox_1 + score_checkbox_2) * score_evidence

        Parameters:
        df (pd.DataFrame): The DataFrame containing the response data.

        Returns:
        pd.DataFrame: A DataFrame with response IDs and their corresponding scores.
        """

        section_item = "checkbox_1"
        score_checkbox_1 = self.base_score.prepare_section_score_checkbox(
            section_item, df
        )
        weight_checkbox_1 = self.weight_distribution["checkbox_1"]["section"]

        section_item = "checkbox_2"
        score_checkbox_2 = self.base_score.prepare_section_score_checkbox(
            section_item, df
        )
        weight_checkbox_2 = self.weight_distribution["checkbox_2"]["section"]

        # If there will be more validation_str values in the future, perhaps it's worth
        # to make a separate class attribute for it.
        section_item = "evidence_1"
        score_evidence_1 = self.base_score.calculate_evidence_score(
            df, section_item, validation_str="RE3"
        )

        score_indicator = (
            score_checkbox_1 * weight_checkbox_1 + score_checkbox_2 * weight_checkbox_2
        ) * score_evidence_1

        indicator_score_name = f"SCORE.F_{self.indicator_name}"
        indicator_score_table = pd.DataFrame(
            {
                response_id: df[response_id],
                indicator_score_name: score_indicator,
            }
        )

        if self.debugging_mode == True:
            checkbox_1_name = self.base_score.get_section_score_name("checkbox_1")
            indicator_score_table[checkbox_1_name] = score_checkbox_1
            checkbox_2_name = self.base_score.get_section_score_name("checkbox_2")
            indicator_score_table[checkbox_2_name] = score_checkbox_2
            evidence_1_name = self.base_score.get_section_score_name("evidence_1")
            indicator_score_table[evidence_1_name] = score_evidence_1

        check_indicator_score_output(
            indicator_score_table, indicator_score_table.columns
        )
        return indicator_score_table
