import pandas as pd

from infra_dev_asset.schemas.mappings import response_id
from infra_dev_asset.scoring.Score_LE2 import ScoreLE2
from infra_dev_asset.scoring.Score_LE3 import ScoreLE3
from infra_dev_asset.scoring.Score_LE4 import ScoreLE4
from infra_dev_asset.scoring.Score_PO1 import ScorePO1
from infra_dev_asset.scoring.Score_PO2 import ScorePO2
from infra_dev_asset.scoring.Score_PO3 import ScorePO3
from infra_dev_asset.scoring.Score_SE1 import ScoreSE1
from infra_dev_asset.scoring.Score_SE2 import ScoreSE2
from infra_dev_asset.scoring.Score_SE3 import ScoreSE3
from infra_dev_asset.scoring.Score_RP1 import ScoreRP1
from infra_dev_asset.scoring.Score_RM1 import ScoreRM1
from infra_dev_asset.scoring.Score_RM21 import ScoreRM21
from infra_dev_asset.scoring.Score_RM22 import ScoreRM22
from infra_dev_asset.scoring.Score_RM23 import ScoreRM23
from infra_dev_asset.scoring.Score_RM31 import ScoreRM31
from infra_dev_asset.scoring.Score_RM32 import ScoreRM32
from infra_dev_asset.scoring.Score_RP2 import ScoreRP21
from infra_dev_asset.scoring.Score_GH1 import ScoreGH1
from infra_dev_asset.scoring.Score_GH2 import ScoreGH2
from infra_dev_asset.scoring.Score_MA1 import ScoreMA1
from infra_dev_asset.scoring.Score_EM1 import ScoreEM1
from infra_dev_asset.scoring.Score_EM2 import ScoreEM2
from infra_dev_asset.scoring.Score_RM4 import ScoreRM4
from infra_dev_asset.scoring.Score_RM51 import ScoreRM51
from infra_dev_asset.scoring.Score_RM52 import ScoreRM52
from infra_dev_asset.scoring.Score_RM53 import ScoreRM53
from infra_dev_asset.scoring.Score_RM54 import ScoreRM54
from infra_dev_asset.scoring.Score_RM55 import ScoreRM55
from infra_dev_asset.scoring.Score_RM56 import ScoreRM56
from infra_dev_asset.scoring.Score_CO1 import ScoreCO1
from infra_dev_asset.scoring.Score_CO2 import ScoreCO2
from infra_dev_asset.scoring.Score_HS1 import ScoreHS1
from infra_dev_asset.scoring.Score_HS2 import ScoreHS2
from infra_dev_asset.scoring.Score_HS3 import ScoreHS3


def calculate_fractional_scores_per_indicator(
    dev_asset_data: pd.DataFrame,
    codebook_survey: pd.DataFrame,
    esg_topics_table: pd.DataFrame,
) -> pd.DataFrame:
    """
    Calculate scores for all indicators and merge them into a single DataFrame.

    Parameters:
    dev_asset_data (pd.DataFrame): Data containing development asset data.
    codebook_survey (pd.DataFrame): Data containing codebook survey data.
    esg_topics_table (pd.DataFrame): Data containing ESG topics data.

    Returns:
    pd.DataFrame: Data with calculated scores for all indicators.
    """
    scores_per_indicator_fractional = dev_asset_data[response_id].copy()

    indicator_dict = {
        "LE_2": ScoreLE2(),
        "LE_3": ScoreLE3(),
        "LE_4": ScoreLE4(),
        "PO_1": ScorePO1(),
        "PO_2": ScorePO2(),
        "PO_3": ScorePO3(),
        "SE_1": ScoreSE1(),
        "SE_2": ScoreSE2(),
        "SE_3": ScoreSE3(),
        "RP_1": ScoreRP1(),
        "RP_2": ScoreRP21(),
        "GH_1": ScoreGH1(),
        "GH_2": ScoreGH2(),
        "MA_1": ScoreMA1(),
        "EM_1": ScoreEM1(),
        "EM_2": ScoreEM2(),
        "RM_1": ScoreRM1(),
        "RM_2.1": ScoreRM21(),
        "RM_2.2": ScoreRM22(),
        "RM_2.3": ScoreRM23(),
        "RM_3.1": ScoreRM31(),
        "RM_3.2": ScoreRM32(),
        "RM_4": ScoreRM4(),
        "RM_5.1": ScoreRM51(),
        "RM_5.2": ScoreRM52(),
        "RM_5.3": ScoreRM53(),
        "RM_5.4": ScoreRM54(),
        "RM_5.5": ScoreRM55(),
        "RM_5.6": ScoreRM56(),
        "CO_1": ScoreCO1(),
        "CO_2": ScoreCO2(),
        "HS_1": ScoreHS1(),
        "HS_2": ScoreHS2(),
        "HS_3": ScoreHS3(),
    }

    for indicator_name in indicator_dict.keys():
        indicator = indicator_dict[indicator_name]
        if isinstance(indicator, (ScoreRM21, ScoreRM22)):
            score = indicator.calculate_score(
                dev_asset_data, codebook_survey, esg_topics_table
            )
        else:
            score = indicator.calculate_score(dev_asset_data)
        scores_per_indicator_fractional = pd.merge(
            scores_per_indicator_fractional, score, on=response_id, how="inner"
        )

    scores_per_indicator_fractional = scores_per_indicator_fractional.set_index(
        response_id
    )
    scores_per_indicator_fractional = scores_per_indicator_fractional.rename(
        columns=lambda x: x.replace("SCORE.F_", "")
    )

    return scores_per_indicator_fractional
