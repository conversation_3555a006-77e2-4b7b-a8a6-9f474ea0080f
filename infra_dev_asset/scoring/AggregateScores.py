import pandas as pd
from infra_dev_asset.scoring.GetWeightsMateriality import get_weights_materiality
from infra_dev_asset.schemas.mappings import response_id


def extract_scoring_details(codebook_overview_indicators: pd.DataFrame) -> pd.DataFrame:
    """
    Extracts and processes scoring details from a codebook overview DataFrame.

    Parameters:
    codebook_overview_indicators (pd.DataFrame): A DataFrame containing indicator details.

    Returns:
    pd.DataFrame: A processed DataFrame with scoring details for development modules.

    The function performs the following steps:
    1. Filters the DataFrame to include only development modules with a score greater than 0 (scored indicators).
    2. Splits the 'aggregation' column into 'esg_topic_name' and 'aspect_name'.
    3. Formats the 'indicator_name' column to match the required format.
    4. Extracts materiality topic from the 'r_spec' column.
    5. Converts the 'component' column values to uppercase.
    """
    is_development_module = codebook_overview_indicators["component"] == "dm"
    is_scored = codebook_overview_indicators["score"] > 0
    scoring_details = codebook_overview_indicators.loc[
        is_development_module & is_scored, :
    ]
    # Aggregation keys are stored in a single column in the following format: ['SOC', 'CO'].
    # Moving them to two separate columns:
    scoring_details.loc[:, ["esg_topic_name"]] = scoring_details["aggregation"].apply(
        lambda x: x[0]
    )
    scoring_details.loc[:, ["aspect_name"]] = scoring_details["aggregation"].apply(
        lambda x: x[1]
    )
    scoring_details = scoring_details.drop(columns=["aggregation"])
    # Same as indicator column, but question number is separated by underscore to match
    # formatting for score names.
    scoring_details.loc[:, "indicator_name"] = scoring_details["indicator"].apply(
        lambda indicator: indicator[:2] + "_" + indicator[2:]
    )

    scoring_details.loc[:, "r_spec"] = scoring_details.loc[:, "r_spec"].str.replace(
        "materiality:", ""
    )
    scoring_details = scoring_details.rename(columns={"r_spec": "materiality"})
    scoring_details["component"] = scoring_details["component"].str.upper()
    return scoring_details


def get_materiality_weights(
    data: pd.DataFrame, scoring_details: pd.DataFrame, esg_topics_table: pd.DataFrame
) -> pd.DataFrame:
    """
    Prepares materiality weights for each response.

    Parameters:
    data (pd.DataFrame): A DataFrame containing the response data.
    scoring_details (pd.DataFrame): A DataFrame containing details about the scoring indicators.

    Returns:
    pd.DataFrame: A DataFrame containing materiality weights for all indicators which are affected
    by materiality (columns) and all responses (rows).
    """
    is_material = -scoring_details["materiality"].isna()

    scoring_details_mat = scoring_details.loc[is_material, :].copy()
    scoring_details_mat = scoring_details_mat.rename(
        columns={"materiality": "text", "score_var": "cb_variable"}
    )
    materiality_weights = get_weights_materiality(
        data,
        scoring_details_mat,
        esg_topics_table,
        scoring_details_mat["cb_variable"].to_list(),
        normalize_weights=False,
    )
    materiality_weights = materiality_weights.set_index(response_id)
    materiality_weights = materiality_weights.rename(
        columns=lambda x: x.replace("SCORE_", "")
    )
    return materiality_weights


def get_original_maximum_scores_per_indicator(
    data: pd.DataFrame, number_of_responses: int, scoring_details: pd.DataFrame
) -> pd.DataFrame:
    """
    Calculates the original maximum scores for each indicator.

    Parameters:
    data (pd.DataFrame): A DataFrame containing the response data.
    number_of_responses (int): Number of rows in a response dataframe.
    scoring_details (pd.DataFrame): A DataFrame containing details about the scoring indicators.

    Returns:
    pd.DataFrame: A DataFrame containing the maximum scores per indicator (columns)
    and responses (rows). All rows are identical.
    """
    max_scores_original = scoring_details.loc[:, ["score"]].T
    max_scores_original = max_scores_original.rename(
        columns=scoring_details.loc[:, "indicator_name"].to_dict()
    )
    max_scores_original = pd.concat(
        [max_scores_original] * number_of_responses, ignore_index=True
    ).set_index(data[response_id])
    return max_scores_original


def rescale_max_scores_with_dynamic_materiality(
    scoring_details: pd.DataFrame,
    materiality_weights: pd.DataFrame,
    max_scores_original: pd.DataFrame,
) -> pd.DataFrame:
    """
    Rescales maximum scores using dynamic materiality weights.

    Parameters:
    scoring_details (pd.DataFrame): A DataFrame containing details about the scoring indicators.
    materiality_weights (pd.DataFrame): A DataFrame containing weights for material indicators.
    max_scores_original (pd.DataFrame): A DataFrame containing the original maximum scores for each indicator.

    Returns:
    pd.DataFrame: A DataFrame containing the rescaled maximum scores adjusted for dynamic materiality.
    """
    is_not_material = scoring_details["materiality"].isna()
    is_material = -is_not_material
    indicator_names = scoring_details.loc[:, "indicator_name"]
    indic_mat = indicator_names[is_material]
    indic_other = indicator_names[is_not_material]

    max_scores_mat_scaled = materiality_weights[indic_mat].multiply(
        max_scores_original[indic_mat]
    )
    max_scores_all_scaled = pd.concat(
        [max_scores_mat_scaled, max_scores_original[indic_other]], axis=1
    )
    total_score_per_response = max_scores_all_scaled.sum(axis=1)

    total_score_ori = scoring_details["score"].sum()
    dynamic_weight = total_score_ori / total_score_per_response

    max_scores_materiality = max_scores_all_scaled.multiply(dynamic_weight, axis=0)
    return max_scores_materiality


def prepare_score_columns(
    max_scores: pd.DataFrame, scores: pd.DataFrame, fractional_scores=True
) -> tuple:
    """
    Prepares different types of score columns based on maximum scores and given scores.

    Parameters:
    max_scores (pd.DataFrame): A DataFrame containing the maximum possible scores for each indicator.
    scores (pd.DataFrame): A DataFrame containing the actual scores for each indicator.
    fractional_scores (bool): A flag indicating whether to calculate fractional scores (default is True).

    Returns:
    tuple: A tuple containing three DataFrames:
        - fractional_scores (pd.DataFrame): The fractional scores if calculated, else an equivalent copy of point_scores.
        - point_scores (pd.DataFrame): The point scores based on the input scores and maximum scores.
        - percent_scores (pd.DataFrame): The percent scores calculated from fractional scores.

    The function performs the following steps:
    1. If fractional_scores flag is True, it calculates the point scores by multiplying the fractional scores by the maximum scores.
    2. If fractional_scores flag is False, it calculates the fractional scores by dividing the point scores by the maximum scores.
    3. Calculates the percent scores by converting fractional scores to percentage.
    """
    if fractional_scores:
        fractional_scores = scores.copy()
        point_scores = fractional_scores.multiply(max_scores)
    else:
        point_scores = scores.copy()
        fractional_scores = point_scores.div(max_scores)
    percent_scores = fractional_scores.copy() * 100
    return fractional_scores, point_scores, percent_scores


def merge_score_columns(
    max_scores: pd.DataFrame,
    fractional_scores: pd.DataFrame,
    point_scores: pd.DataFrame,
    percent_scores: pd.DataFrame,
) -> pd.DataFrame:
    """
    Merges different types of score columns into a single DataFrame with renamed columns.

    Parameters:
    max_scores (pd.DataFrame): A DataFrame containing the maximum scores for each indicator.
    fractional_scores (pd.DataFrame): A DataFrame containing the fractional scores for each indicator.
    point_scores (pd.DataFrame): A DataFrame containing the point scores for each indicator.
    percent_scores (pd.DataFrame): A DataFrame containing the percent scores for each indicator.

    Returns:
    pd.DataFrame: A DataFrame containing all types of scores with appropriately renamed columns.

    The function performs the following steps:
    1. Renames the columns in each score DataFrame to indicate the type of score and whether it pertains to ESG topics.
    2. Concatenates the renamed DataFrames into a single DataFrame.
    """
    esg_topics = ["GOV", "ENV", "SOC"]
    max_scores_renamed = max_scores.rename(
        columns=lambda x: "SCORE.MAX_DM." + x if x in esg_topics else "SCORE.MAX_" + x
    )
    fractional_scores_renamed = fractional_scores.rename(
        columns=lambda x: "SCORE.F_DM." + x if x in esg_topics else "SCORE.F_" + x
    )
    point_scores_renamed = point_scores.rename(
        columns=lambda x: "SCORE_DM." + x if x in esg_topics else "SCORE_" + x
    )
    percent_scores_renamed = percent_scores.rename(
        columns=lambda x: "SCORE.P_DM." + x if x in esg_topics else "SCORE.P_" + x
    )
    scores = pd.concat(
        [
            fractional_scores_renamed,
            max_scores_renamed,
            point_scores_renamed,
            percent_scores_renamed,
        ],
        axis=1,
    )
    return scores


def aggregate_scores(
    scoring_details: pd.DataFrame,
    score_columns: pd.DataFrame,
    aggregate_by="aspect_name",
) -> pd.DataFrame:
    """
    Aggregates scores based on a specified key.

    Parameters:
    scoring_details (pd.DataFrame): A DataFrame containing details about the scoring indicators.
    score_columns (pd.DataFrame): A DataFrame containing the scores to be aggregated.
    aggregate_by (str): The column name by which to aggregate the scores (default is "aspect_name").

    Returns:
    pd.DataFrame: A DataFrame with the aggregated scores.

    The function performs the following steps:
    1. Transposes the score_columns DataFrame.
    2. Joins the transposed scores with the aggregation keys from scoring_details.
    3. Aggregates the scores by summing them based on the specified aggregation key.
    4. Transposes the aggregated DataFrame back to the original orientation and sets the index.
    """
    score_columns_wide = score_columns.T
    aggregation_keys = scoring_details[["indicator_name", aggregate_by]].set_index(
        "indicator_name"
    )
    score_columns_wide = score_columns_wide.join(aggregation_keys)
    aggregated_columns = score_columns_wide.groupby(aggregate_by).sum().T
    aggregated_columns = aggregated_columns.set_index(score_columns.index)
    return aggregated_columns


def get_all_aggregated_scores(
    scoring_details: pd.DataFrame,
    scores_per_indicator_max: pd.DataFrame,
    scores_per_indicator_points: pd.DataFrame,
    aggregate_by="aspect_name",
) -> pd.DataFrame:
    """
    Aggregates and prepares all score columns based on a specified key.

    Parameters:
    scoring_details (pd.DataFrame): A DataFrame containing details about the scoring indicators.
    scores_per_indicator_max (pd.DataFrame): A DataFrame containing the maximum possible scores per indicator.
    scores_per_indicator_points (pd.DataFrame): A DataFrame containing the actual point scores per indicator.
    aggregate_by (str): The column name by which to aggregate the scores (default is "aspect_name").

    Returns:
    pd.DataFrame: A DataFrame with merged and aggregated scores.

    The function performs the following steps:
    1. Aggregates the point scores based on the specified key.
    2. Aggregates the maximum scores based on the specified key.
    3. Prepares fractional, point, and percent scores from the aggregated scores.
    4. Merges all score columns into a single DataFrame.
    """

    agg_point_scores = aggregate_scores(
        scoring_details, scores_per_indicator_points, aggregate_by=aggregate_by
    )
    agg_max_scores = aggregate_scores(
        scoring_details, scores_per_indicator_max, aggregate_by=aggregate_by
    )
    agg_frac_scores, agg_point_scores, agg_percent_scores = prepare_score_columns(
        agg_max_scores, agg_point_scores, fractional_scores=False
    )
    scores_merged = merge_score_columns(
        agg_max_scores, agg_frac_scores, agg_point_scores, agg_percent_scores
    )
    return scores_merged


def calculate_aggregated_scores(
    data: pd.DataFrame,
    scores_per_indicator_fractional: pd.DataFrame,
    codebook_overview_indicators: pd.DataFrame,
    esg_topics_table: pd.DataFrame,
) -> pd.DataFrame:
    """
    Calculates maximum and point scores per indicator and aggregates them per aspect (e.g. 'LE'),
    ESG topic name (e.g. 'GOV'), and component ('DM').

    Parameters:
    data (pd.DataFrame): A DataFrame containing the response data.
    scores_per_indicator_fractional (pd.DataFrame): A DataFrame containing fractional scores per indicator.
    codebook_overview_indicators (pd.DataFrame): A DataFrame containing details about the scoring indicators.
    esg_topics_table (pd.DataFrame): A DataFrame containing ESG topics.

    Returns:
    pd.DataFrame: A DataFrame with calculated and aggregated scores across different dimensions.

    """

    number_of_responses = data.shape[0]

    scoring_details = extract_scoring_details(codebook_overview_indicators)
    # Will be used for ensuring the correct column order for matrix operations.
    indicator_columns = scoring_details["indicator_name"]
    materiality_weights = get_materiality_weights(
        data, scoring_details, esg_topics_table
    )
    original_max_scores = get_original_maximum_scores_per_indicator(
        data, number_of_responses, scoring_details
    )
    scores_per_indicator_max = rescale_max_scores_with_dynamic_materiality(
        scoring_details, materiality_weights, original_max_scores
    )
    scores_per_indicator_max = scores_per_indicator_max[indicator_columns]

    (
        scores_per_indicator_fractional,
        scores_per_indicator_points,
        score_per_indicator_percent,
    ) = prepare_score_columns(
        scores_per_indicator_max,
        scores_per_indicator_fractional,
        fractional_scores=True,
    )

    scores_per_indicator = merge_score_columns(
        scores_per_indicator_max,
        scores_per_indicator_fractional,
        scores_per_indicator_points,
        score_per_indicator_percent,
    )

    aggregation_types = ["aspect_name", "esg_topic_name", "component"]
    aggregated_scores = []
    for agg_type in aggregation_types:
        scores = get_all_aggregated_scores(
            scoring_details,
            scores_per_indicator_max,
            scores_per_indicator_points,
            aggregate_by=agg_type,
        )
        aggregated_scores.append(scores)
    scores = pd.concat([scores_per_indicator] + aggregated_scores, axis=1)
    return scores
