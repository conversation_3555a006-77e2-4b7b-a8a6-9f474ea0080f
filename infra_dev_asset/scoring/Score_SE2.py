import pandas as pd
import numpy as np

from infra_dev_asset.schemas.mappings import response_id
from infra_dev_asset.scoring.CheckIndicatorOutput import check_indicator_score_output
from infra_dev_asset.scoring.ScoreBase import ScoreBase


class ScoreSE2(ScoreBase):
    """
    A class used to calculate scores for the SE2 indicator (Supply chain engagement
    program).
    Indicator consists of one checkbox section.

    Attributes:
    indicator_name (str): Indicator name used for creating score column.
    indicator_variables (dict): A dictionary defining primary and 'other' indicator
    variable names.
    weight_distribution (dict): A dictionary defining weights distributions for each
    section.
    debugging_mode (bool, optional): In the debugging mode intermediate scores are saved
    to final DataFrame. Default is False.
    base_score (ScoreBase): An instance of the ScoreBase class initialized with
    indicator variables and weight distribution.
    include_other_vld (False): Inherited default value from ScoreBase. Indicates whether
    to include 'other' validation data.
    materiality (False): Inherited default value from ScoreBase. Indicates whether
    materiality is considered.
    diminish (False): Inherited default value from ScoreBase. Indicates whether to apply
    diminishing returns.

    Methods:
    __init__(): Initializes the ScoreSE2 instance with default attributes.
    __repr__(): Provides a string representation of the ScoreSE2 instance.
    get_indicator_variables(): Retrieves the list of indicator variables.
    assign_weight_distribution(): Assigns weights distributions for indicator variables.
    calculate_score(df: pd.DataFrame): Calculates the score for the SE2 indicator and
    returns a DataFrame.
    """

    def __init__(self, debugging_mode=False):
        """
        Initializes the ScoreSE2 instance with default attributes.
        """
        self.indicator_name = "SE_2"
        self.indicator_variables = self.get_indicator_variables()
        self.weight_distribution = self.assign_weight_distribution()
        self.debugging_mode = debugging_mode
        self.base_score = ScoreBase(
            self.indicator_name,
            self.indicator_variables,
            self.weight_distribution,
        )

    def __repr__(self):
        """
        Provides a string representation of the ScoreSE2 instance.
        """
        return (
            f"ScoreSE2(indicator_name={self.indicator_name}, "
            f"indicator_variables={self.indicator_variables}, "
            f"weight_distribution={self.weight_distribution}, "
            f"debugging_mode={self.debugging_mode}, "
            f"include_other_vld={self.include_other_vld}, "
            f"materiality={self.materiality}, "
            f"diminish={self.diminish})"
        )

    def get_indicator_variables(self) -> dict:
        """
        Retrieves the list of indicator variables.

        Returns:
        dict: A dictionary defining primary and other indicator variable names.
        """
        checkbox_1_name = f"{self.indicator_name}"

        indicator_variables_dict = {
            "checkbox_1": {
                "section_name": f"{checkbox_1_name}_1",
                "primary": [f"{self.indicator_name}_A1_{i}" for i in range(1, 9)],
                "other": None,
            },
            "checkbox_2": {
                "section_name": f"{checkbox_1_name}_2",
                "primary": [f"{self.indicator_name}_A1_{i}" for i in range(11, 22)],
                "other": [f"{self.indicator_name}_A1_22"],
            },
            "checkbox_3": {
                "section_name": f"{checkbox_1_name}_3",
                "primary": [f"{self.indicator_name}_A1_{i}" for i in range(31, 33)],
                "other": [f"{self.indicator_name}_A1_33"],
            },
        }

        return indicator_variables_dict

    def assign_weight_distribution(self) -> dict:
        """
        Assigns weights distributions for indicator variables.

        Returns:
        dict: A dictionary defining weights distributions.
        """
        weights_distributions_dict = {
            "checkbox_1": {
                "section": 1 / 3,
                "primary": np.repeat(1 / 6, 8),
                "other": None,
            },
            "checkbox_2": {
                "section": 1 / 3,
                "primary": np.repeat(1 / 7, 11),
                "other": [1 / 7],
            },
            "checkbox_3": {
                "section": 1 / 3,
                "primary": np.repeat(1 / 2, 2),
                "other": [1 / 2],
            },
        }
        return weights_distributions_dict

    def calculate_score(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculates the score for the SE2 indicator and returns a DataFrame.

        Parameters:
        df (pd.DataFrame): The DataFrame containing the response data.

        Returns:
        pd.DataFrame: A DataFrame with response IDs and their corresponding scores.
        """
        score_checkbox_1 = self.base_score.prepare_section_score_checkbox(
            section_item="checkbox_1", df=df
        )
        weight_checkbox_1 = self.weight_distribution["checkbox_1"]["section"]

        score_checkbox_2 = self.base_score.prepare_section_score_checkbox(
            section_item="checkbox_2", df=df
        )
        weight_checkbox_2 = self.weight_distribution["checkbox_2"]["section"]

        score_checkbox_3 = self.base_score.prepare_section_score_checkbox(
            section_item="checkbox_3", df=df
        )
        weight_checkbox_3 = self.weight_distribution["checkbox_3"]["section"]

        score_indicator = (
            (score_checkbox_1 * weight_checkbox_1)
            + (score_checkbox_2 * weight_checkbox_2)
            + (score_checkbox_3 * weight_checkbox_3)
        )
        indicator_score_name = f"SCORE.F_{self.indicator_name}"

        indicator_score_table = pd.DataFrame(
            {
                response_id: df[response_id],
                indicator_score_name: score_indicator,
            }
        )

        if self.debugging_mode == True:
            checkbox_1_name = self.base_score.get_section_score_name("checkbox_1")
            indicator_score_table[checkbox_1_name] = score_checkbox_1
            checkbox_2_name = self.base_score.get_section_score_name("checkbox_2")
            indicator_score_table[checkbox_2_name] = score_checkbox_2
            checkbox_3_name = self.base_score.get_section_score_name("checkbox_3")
            indicator_score_table[checkbox_3_name] = score_checkbox_3

        check_indicator_score_output(
            indicator_score_table, indicator_score_table.columns
        )
        return indicator_score_table
