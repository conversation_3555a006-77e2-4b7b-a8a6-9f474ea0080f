import pandas as pd
import numpy as np
from infra_dev_asset.schemas.mappings import response_id


def get_weights(
    df: pd.DataFrame,
    section_variables: list,
    section_variables_other: list,
    weights: dict,
) -> pd.DataFrame:
    """
    Generates a DataFrame of weights for the given section variables based on the
    provided weights per variable.

    This function replicates the provided weights for each variable across all
    responses in the DataFrame.

    Parameters:
    df (pd.DataFrame): The DataFrame containing the response data.
    section_variables (list): A list of section variable names for which weights need
    to be generated.
    section_variables_other (list): A list of 'other' section variable names.
    weights (dict): A dictionary of arrays containing the weights for each indicator
    variable.

    Returns:
    pd.DataFrame: A DataFrame with RESPONSE_ID and their corresponding weights for each
    section variable.
    """

    include_other = section_variables_other is not None
    if include_other:
        weights_per_section = np.append(weights["primary"], weights["other"])
        section_variables = section_variables + section_variables_other
    else:
        weights_per_section = weights["primary"]

    number_of_responses = len(df)
    weights_df = pd.DataFrame(
        [weights_per_section] * number_of_responses, columns=section_variables
    )
    weights_df.insert(0, response_id, df[response_id])
    return weights_df
