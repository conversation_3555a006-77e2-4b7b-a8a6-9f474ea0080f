import pandas as pd
import numpy as np

from infra_dev_asset.schemas.mappings import response_id
from infra_dev_asset.scoring.CheckIndicatorOutput import check_indicator_score_output
from infra_dev_asset.scoring.ScoreBase import ScoreBase


class ScoreRP1(ScoreBase):
    """
    A class used to calculate scores for the RP1 indicator
    (Reporting 1: ESG reporting).
    Indicator consists of six subindicators, each including complex 2-level combination of checkboxes,
    radiobuttons, guideline scores and evidence weights.

    Attributes:
    indicator_name (str): Indicator name used for creating score column.
    indicator_variables (dict): A dictionary defining primary and 'other' indicator variable names for checkbox_1, checkbox_2, and checkbox_3.
    weight_distribution (dict): A dictionary defining weights distributions for each section.
    debugging_mode (bool, optional): In the debugging mode intermediate scores are saved to final DataFrame. Default is False.
    base_score (ScoreBase): An instance of the ScoreBase class initialized with indicator variables and weight distribution.
    include_other_vld (False): Inherited default value from ScoreBase. Indicates whether to include 'other' validation data.
    materiality (False): Inherited default value from ScoreBase. Indicates whether materiality is considered.
    diminish (False): Inherited default value from ScoreBase. Indicates whether to apply diminishing returns.

    Methods:
    __init__(): Initializes the ScoreRP1 instance with default attributes.
    __repr__(): Provides a string representation of the ScoreRP1 instance.
    get_indicator_variables(): Retrieves the list of indicator variables.
    assign_weight_distribution(): Assigns weights distributions for indicator variables.
    calculate_score(df: pd.DataFrame): Calculates the score for the RP1 indicator (indicator, checkbox_1, checkbox_2, checkbox_3) and returns a DataFrame.
    """

    def __init__(self, debugging_mode=False):
        """
        Initializes the ScoreRP1 instance with default attributes.
        """
        self.indicator_name = "RP_1"
        self.indicator_variables = self.get_indicator_variables()
        self.weight_distribution = self.assign_weight_distribution()
        self.debugging_mode = debugging_mode
        self.base_score = ScoreBase(
            self.indicator_name,
            self.indicator_variables,
            self.weight_distribution,
        )

    def __repr__(self):
        """
        Provides a string representation of the ScoreRP1 instance.
        """
        return (
            f"ScoreRP1(indicator_name={self.indicator_name}, "
            f"indicator_variables={self.indicator_variables}, "
            f"weight_distribution={self.weight_distribution}, "
            f"debugging_mode={self.debugging_mode}, "
            f"include_other_vld={self.base_score.include_other_vld}, "
            f"materiality={self.base_score.materiality}, "
            f"diminish={self.base_score.diminish})"
        )

    def get_indicator_variables(self) -> dict:
        """
        Retrieves the dictionary of indicator variables.
        """

        subindicator_1_variables_dict = {
            "subindicator_1": {
                "section_name": f"{self.indicator_name}_1",
            },
            "radiobutton_1_1": {
                "section_name": "radiobutton_1_1",
                "variable": f"{self.indicator_name}_A1_1_A",
            },
            "radiobutton_1_2": {
                "section_name": "radiobutton_1_2",
                "variable": f"{self.indicator_name}_A1_1_B1_A",
            },
            "checkbox_1": {
                "section_name": "checkbox_1",
                "primary": [
                    f"{self.indicator_name}_A1_1",
                    "SCORE.F_radiobutton_1_1",
                    "SCORE.F_radiobutton_1_2",
                ],
                "other": None,
            },
            "evidence_1": {
                "section_name": "evidence_1",
                "variable": f"{self.indicator_name}_A1_1_EVD_VLD",
            },
        }

        subindicator_2_variables_dict = {
            "subindicator_2": {
                "section_name": f"{self.indicator_name}_2",
            },
            "radiobutton_2_1": {
                "section_name": "radiobutton_2_1",
                "variable": f"{self.indicator_name}_A1_2_A",
            },
            "radiobutton_2_2": {
                "section_name": "radiobutton_2_2",
                "variable": f"{self.indicator_name}_A1_2_B1_A",
            },
            "guideline_2": {
                "section_name": "guideline_2",
                "variable": f"{self.indicator_name}_A1_2_GUIDELINE",
                "other": f"{self.indicator_name}_A1_2_GUIDELINE_OTHER_TEXT_VLD",
            },
            "checkbox_2": {
                "section_name": "checkbox_2",
                "primary": [
                    f"{self.indicator_name}_A1_2",
                    "SCORE.F_radiobutton_2_1",
                    "SCORE.F_guideline_2",
                    "SCORE.F_radiobutton_2_2",
                ],
                "other": None,
            },
            "evidence_2": {
                "section_name": "evidence_2",
                "variable": f"{self.indicator_name}_A1_2_EVD_VLD",
            },
        }

        subindicator_3_variables_dict = {
            "subindicator_3": {
                "section_name": f"{self.indicator_name}_3",
            },
            "radiobutton_3_1": {
                "section_name": "radiobutton_3_1",
                "variable": f"{self.indicator_name}_A1_3_A",
            },
            "radiobutton_3_2": {
                "section_name": "radiobutton_3_2",
                "variable": f"{self.indicator_name}_A1_3_B1_A",
            },
            "guideline_3": {
                "section_name": "guideline_3",
                "variable": f"{self.indicator_name}_A1_3_GUIDELINE",
                "other": f"{self.indicator_name}_A1_3_GUIDELINE_OTHER_TEXT_VLD",
            },
            "checkbox_3": {
                "section_name": "checkbox_3",
                "primary": [
                    f"{self.indicator_name}_A1_3",
                    "SCORE.F_radiobutton_3_1",
                    "SCORE.F_guideline_3",
                    "SCORE.F_radiobutton_3_2",
                ],
                "other": None,
            },
            "evidence_3": {
                "section_name": "evidence_3",
                "variable": f"{self.indicator_name}_A1_3_EVD_VLD",
            },
        }

        subindicator_4_variables_dict = {
            "subindicator_4": {
                "section_name": f"{self.indicator_name}_4",
            },
            "radiobutton_4": {
                "section_name": "radiobutton_4",
                "variable": f"{self.indicator_name}_A1_4_A",
            },
            "checkbox_4": {
                "section_name": "checkbox_4",
                "primary": [f"{self.indicator_name}_A1_4", "SCORE.F_radiobutton_4"],
                "other": None,
            },
            "evidence_4": {
                "section_name": "evidence_4",
                "variable": f"{self.indicator_name}_A1_4_EVD_VLD",
            },
        }

        subindicator_5_variables_dict = {
            "subindicator_5": {
                "section_name": f"{self.indicator_name}_5",
            },
            "guideline_5": {
                "section_name": "guideline_5",
                "variable": f"{self.indicator_name}_A1_5_GUIDELINE",
                "other": f"{self.indicator_name}_A1_5_GUIDELINE_OTHER_TEXT_VLD",
            },
            "radiobutton_5": {
                "section_name": "radiobutton_5",
                "variable": f"{self.indicator_name}_A1_5_B1_A",
            },
            "checkbox_5": {
                "section_name": "checkbox_5",
                "primary": [
                    f"{self.indicator_name}_A1_5",
                    "SCORE.F_guideline_5",
                    "SCORE.F_radiobutton_5",
                ],
                "other": None,
            },
            "evidence_5": {
                "section_name": "evidence_5",
                "variable": f"{self.indicator_name}_A1_5_EVD_VLD",
            },
        }

        subindicator_6_variables_dict = {
            "subindicator_6": {
                "section_name": f"{self.indicator_name}_6",
            },
            "radiobutton_6_1": {
                "section_name": "radiobutton_6_1",
                "variable": f"{self.indicator_name}_A1_6_A",
            },
            "radiobutton_6_2": {
                "section_name": "radiobutton_6_2",
                "variable": f"{self.indicator_name}_A1_6_B1_A",
            },
            "guideline_6": {
                "section_name": "guideline_6",
                "variable": f"{self.indicator_name}_A1_6_GUIDELINE",
                "other": f"{self.indicator_name}_A1_6_GUIDELINE_OTHER_TEXT_VLD",
            },
            "checkbox_6": {
                "section_name": "checkbox_6",
                "primary": [
                    f"{self.indicator_name}_A1_6",
                    "SCORE.F_radiobutton_6_1",
                    "SCORE.F_guideline_6",
                    "SCORE.F_radiobutton_6_2",
                ],
                "other": None,
            },
            "evidence_6": {
                "section_name": "evidence_6",
                "variable": f"{self.indicator_name}_A1_6_EVD_VLD",
            },
            "evidence_6_other": {
                "section_name": "evidence_6_other",
                "variable": f"{self.indicator_name}_A1_6_OTHER_VLD",
            },
        }

        indicator_variables_dict = {
            **subindicator_1_variables_dict,
            **subindicator_2_variables_dict,
            **subindicator_3_variables_dict,
            **subindicator_4_variables_dict,
            **subindicator_5_variables_dict,
            **subindicator_6_variables_dict,
        }
        return indicator_variables_dict

    def assign_weight_distribution(self):
        """
        Assigns weights distributions for indicator variables.

        Returns:
        dict:
            A dictionary defining weights distributions.
        """

        subindicator_1_weights = {
            "radiobutton_1_1": {
                "values": np.array([0, 1, 2]),
                "weights": np.array([0, 2.0 / 2, 1.0 / 2]),
            },
            "radiobutton_1_2": {
                "values": np.array([0, 1, 2, 3]),
                "weights": np.array([0, 1.0 / 3, 1, 1]),
            },
            "checkbox_1": {
                "primary": np.array([3.0 / 6, 1.0 / 6, 2.0 / 6]),
                "other": None,
            },
            "evidence_1": [1.0],
        }

        subindicator_2_weights = {
            "radiobutton_2_1": {
                "values": np.array([0, 1, 2]),
                "weights": np.array([0, 2.0 / 2, 1.0 / 2]),
            },
            "radiobutton_2_2": {
                "values": np.array([0, 1, 2, 3]),
                "weights": np.array([0, 1.0 / 3, 1, 1]),
            },
            "guideline_2": [1.0],
            "checkbox_2": {
                "primary": np.array([2.0 / 6, 1.0 / 6, 1.0 / 6, 2.0 / 6]),
                "other": None,
            },
            "evidence_2": [1.0],
        }

        subindicator_3_weights = {
            "radiobutton_3_1": {
                "values": np.array([0, 1, 2]),
                "weights": np.array([0, 2.0 / 2, 1.0 / 2]),
            },
            "radiobutton_3_2": {
                "values": np.array([0, 1, 2, 3]),
                "weights": np.array([0, 1.0 / 3, 1, 1]),
            },
            "guideline_3": [1.0],
            "checkbox_3": {
                "primary": np.array([2.0 / 6, 1.0 / 6, 1.0 / 6, 2.0 / 6]),
                "other": None,
            },
            "evidence_3": [1.0],
        }

        subindicator_4_weights = {
            "radiobutton_4": {
                "values": np.array([0, 1, 2]),
                "weights": np.array([0, 2.0 / 2, 1.0 / 2]),
            },
            "checkbox_4": {
                "primary": np.array([2.0 / 3, 1.0 / 3]),
                "other": None,
            },
            "evidence_4": [1.0],
        }

        subindicator_5_weights = {
            "guideline_5": [1.0],
            "radiobutton_5": {
                "values": np.array([0, 1, 2, 3]),
                "weights": np.array([0, 1.0 / 3, 1, 1]),
            },
            "checkbox_5": {
                "primary": np.array([4.0 / 6, 1.0 / 6, 1.0 / 6]),
                "other": None,
            },
            "evidence_5": [1.0],
        }

        subindicator_6_weights = {
            "radiobutton_6_1": {
                "values": np.array([0, 1, 2]),
                "weights": np.array([0, 2.0 / 2, 1.0 / 2]),
            },
            "radiobutton_6_2": {
                "values": np.array([0, 1, 2, 3]),
                "weights": np.array([0, 1.0 / 3, 1, 1]),
            },
            "guideline_6": [1.0],
            "checkbox_6": {
                "primary": np.array([2.0 / 6, 1.0 / 6, 1.0 / 6, 2.0 / 6]),
                "other": None,
            },
            "evidence_6": [1.0],
            "evidence_6_other": [1.0],
        }

        weight_distribution_dict = {
            **subindicator_1_weights,
            **subindicator_2_weights,
            **subindicator_3_weights,
            **subindicator_4_weights,
            **subindicator_5_weights,
            **subindicator_6_weights,
            "section": [5.0 / 5, 4.0 / 5, 3.0 / 5, 1.0 / 5, 2.0 / 5, 2.0 / 5],
        }

        return weight_distribution_dict

    def calculate_score(self, df: pd.DataFrame) -> pd.DataFrame:
        """

        Calculates indicator score as a weighted sum of corresponding subindicator scores.

        Parameters:
        df (pd.DataFrame): The DataFrame containing the response data.

        Returns:
        pd.DataFrame: A DataFrame with response IDs and their corresponding scores.
        """

        score_subindicator_1 = self.score_subindicator_1(df)
        score_subindicator_2 = self.score_subindicator_2(df)
        score_subindicator_3 = self.score_subindicator_3(df)
        score_subindicator_4 = self.score_subindicator_4(df)
        score_subindicator_5 = self.score_subindicator_5(df)
        score_subindicator_6 = self.score_subindicator_6(df)

        score_indicator = (
            self.weight_distribution["section"][0] * score_subindicator_1
            + self.weight_distribution["section"][1] * score_subindicator_2
            + self.weight_distribution["section"][2] * score_subindicator_3
            + self.weight_distribution["section"][3] * score_subindicator_4
            + self.weight_distribution["section"][4] * score_subindicator_5
            + self.weight_distribution["section"][5] * score_subindicator_6
        )
        score_indicator[score_indicator > 1] = 1

        indicator_score_name = f"SCORE.F_{self.indicator_name}"
        indicator_score_table = pd.DataFrame(
            {
                response_id: df[response_id],
                indicator_score_name: score_indicator,
            }
        )
        if self.debugging_mode == True:
            subindicator_1_name = self.base_score.get_section_score_name(
                "subindicator_1"
            )
            indicator_score_table[subindicator_1_name] = score_subindicator_1
            subindicator_2_name = self.base_score.get_section_score_name(
                "subindicator_2"
            )
            indicator_score_table[subindicator_2_name] = score_subindicator_2
            subindicator_3_name = self.base_score.get_section_score_name(
                "subindicator_3"
            )
            indicator_score_table[subindicator_3_name] = score_subindicator_3
            subindicator_4_name = self.base_score.get_section_score_name(
                "subindicator_4"
            )
            indicator_score_table[subindicator_4_name] = score_subindicator_4
            subindicator_5_name = self.base_score.get_section_score_name(
                "subindicator_5"
            )
            indicator_score_table[subindicator_5_name] = score_subindicator_5
            subindicator_6_name = self.base_score.get_section_score_name(
                "subindicator_6"
            )
            indicator_score_table[subindicator_6_name] = score_subindicator_6

        check_indicator_score_output(
            indicator_score_table, indicator_score_table.columns
        )
        return indicator_score_table

    def score_subindicator_1(self, df: pd.DataFrame) -> pd.DataFrame:
        """

        Calculates score for subindicator_1.

        Parameters:
        df (pd.DataFrame): The DataFrame containing the response data.

        Returns:
        pd.DataFrame: A DataFrame with response IDs and their corresponding scores.
        """
        section_item = "radiobutton_1_1"
        radiobutton_score_name = self.base_score.get_section_score_name(section_item)
        df[radiobutton_score_name] = self.base_score.calculate_radiobutton_score(
            section_item, df
        )

        section_item = "radiobutton_1_2"
        radiobutton_score_name = self.base_score.get_section_score_name(section_item)
        df[radiobutton_score_name] = self.base_score.calculate_radiobutton_score(
            section_item, df
        )

        section_item = "checkbox_1"
        score_checkbox_1 = self.base_score.prepare_section_score_checkbox(
            section_item, df
        )
        section_item = "evidence_1"
        score_evidence_1 = self.base_score.calculate_evidence_score(
            df, section_item, validation_str="RE3"
        )
        score_subindicator_1 = score_checkbox_1 * score_evidence_1
        return score_subindicator_1

    def score_subindicator_2(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculates score for subindicator_2.

        Parameters:
        df (pd.DataFrame): The DataFrame containing the response data.

        Returns:
        pd.DataFrame: A DataFrame with response IDs and their corresponding scores.
        """
        section_item = "radiobutton_2_1"
        radiobutton_score_name = self.base_score.get_section_score_name(section_item)
        df[radiobutton_score_name] = self.base_score.calculate_radiobutton_score(
            section_item, df
        )

        section_item = "radiobutton_2_2"
        radiobutton_score_name = self.base_score.get_section_score_name(section_item)
        df[radiobutton_score_name] = self.base_score.calculate_radiobutton_score(
            section_item, df
        )

        section_item = "guideline_2"
        guideline_score_name = self.base_score.get_section_score_name(section_item)
        df[guideline_score_name] = self.base_score.calculate_aligned_guidelines_score(
            section_item, df
        )

        section_item = "checkbox_2"
        score_checkbox_2 = self.base_score.prepare_section_score_checkbox(
            section_item, df
        )

        section_item = "evidence_2"
        score_evidence_2 = self.base_score.calculate_evidence_score(
            df, section_item, validation_str="RE3"
        )
        score_subindicator_2 = score_checkbox_2 * score_evidence_2

        return score_subindicator_2

    def score_subindicator_3(self, df: pd.DataFrame) -> pd.DataFrame:
        """

        Calculates score for subindicator_3.

        Parameters:
        df (pd.DataFrame): The DataFrame containing the response data.

        Returns:
        pd.DataFrame: A DataFrame with response IDs and their corresponding scores.
        """
        section_item = "radiobutton_3_1"
        radiobutton_score_name = self.base_score.get_section_score_name(section_item)
        df[radiobutton_score_name] = self.base_score.calculate_radiobutton_score(
            section_item, df
        )

        section_item = "radiobutton_3_2"
        radiobutton_score_name = self.base_score.get_section_score_name(section_item)
        df[radiobutton_score_name] = self.base_score.calculate_radiobutton_score(
            section_item, df
        )

        section_item = "guideline_3"
        guideline_score_name = self.base_score.get_section_score_name(section_item)
        df[guideline_score_name] = self.base_score.calculate_aligned_guidelines_score(
            section_item, df
        )

        section_item = "checkbox_3"
        score_checkbox_3 = self.base_score.prepare_section_score_checkbox(
            section_item, df
        )

        section_item = "evidence_3"
        score_evidence_3 = self.base_score.calculate_evidence_score(
            df, section_item, validation_str="RE3"
        )
        score_subindicator_3 = score_checkbox_3 * score_evidence_3

        return score_subindicator_3

    def score_subindicator_4(self, df: pd.DataFrame) -> pd.DataFrame:
        """

        Calculates score for subindicator_4.

        Parameters:
        df (pd.DataFrame): The DataFrame containing the response data.

        Returns:
        pd.DataFrame: A DataFrame with response IDs and their corresponding scores.
        """
        section_item = "radiobutton_4"
        radiobutton_score_name = self.base_score.get_section_score_name(section_item)
        df[radiobutton_score_name] = self.base_score.calculate_radiobutton_score(
            section_item, df
        )

        section_item = "checkbox_4"
        score_checkbox_4 = self.base_score.prepare_section_score_checkbox(
            section_item, df
        )
        section_item = "evidence_4"
        score_evidence_4 = self.base_score.calculate_evidence_score(
            df, section_item, validation_str="RE3"
        )
        score_subindicator_4 = score_checkbox_4 * score_evidence_4
        return score_subindicator_4

    def score_subindicator_5(self, df: pd.DataFrame) -> pd.DataFrame:
        """

        Calculates score for subindicator_5.

        Parameters:
        df (pd.DataFrame): The DataFrame containing the response data.

        Returns:
        pd.DataFrame: A DataFrame with response IDs and their corresponding scores.
        """
        section_item = "radiobutton_5"
        radiobutton_score_name = self.base_score.get_section_score_name(section_item)
        df[radiobutton_score_name] = self.base_score.calculate_radiobutton_score(
            section_item, df
        )

        section_item = "guideline_5"
        guideline_score_name = self.base_score.get_section_score_name(section_item)
        df[guideline_score_name] = self.base_score.calculate_aligned_guidelines_score(
            section_item, df
        )

        section_item = "checkbox_5"
        score_checkbox_5 = self.base_score.prepare_section_score_checkbox(
            section_item, df
        )

        section_item = "evidence_5"
        score_evidence_5 = self.base_score.calculate_evidence_score(
            df, section_item, validation_str="RE3"
        )
        score_subindicator_5 = score_checkbox_5 * score_evidence_5

        return score_subindicator_5

    def score_subindicator_6(self, df: pd.DataFrame) -> pd.DataFrame:
        """

        Calculates score for subindicator_6.

        Parameters:
        df (pd.DataFrame): The DataFrame containing the response data.

        Returns:
        pd.DataFrame: A DataFrame with response IDs and their corresponding scores.
        """
        section_item = "radiobutton_6_1"
        radiobutton_score_name = self.base_score.get_section_score_name(section_item)
        df[radiobutton_score_name] = self.base_score.calculate_radiobutton_score(
            section_item, df
        )

        section_item = "radiobutton_6_2"
        radiobutton_score_name = self.base_score.get_section_score_name(section_item)
        df[radiobutton_score_name] = self.base_score.calculate_radiobutton_score(
            section_item, df
        )

        section_item = "guideline_6"
        guideline_score_name = self.base_score.get_section_score_name(section_item)
        df[guideline_score_name] = self.base_score.calculate_aligned_guidelines_score(
            section_item, df
        )

        section_item = "checkbox_6"
        score_checkbox_6 = self.base_score.prepare_section_score_checkbox(
            section_item, df
        )

        section_item = "evidence_6"
        score_evidence_6 = self.base_score.calculate_evidence_score(
            df, section_item, validation_str="RE3"
        )

        section_item = "evidence_6_other"
        score_evidence_6_other = self.base_score.calculate_evidence_score(
            df, section_item, validation_str="RE2"
        )
        score_subindicator_6 = (
            score_checkbox_6 * score_evidence_6 * score_evidence_6_other
        )

        return score_subindicator_6
