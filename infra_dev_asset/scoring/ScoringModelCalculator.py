import pandas as pd

from infra_dev_asset.scoring.CalculateFractionalScoresPerIndicator import (
    calculate_fractional_scores_per_indicator,
)
from infra_dev_asset.scoring.AggregateScores import calculate_aggregated_scores


class ScoringModelCalculator:
    """
    ScoringModelCalculator calculates Infra Development scores for various indicators
    using the provided data.
    """

    @staticmethod
    def calculate_scores(
        dev_asset_data: pd.DataFrame,
        codebook_survey: pd.DataFrame,
        codebook_overview_indicators: pd.DataFrame,
        esg_topics_table: pd.DataFrame,
    ) -> pd.DataFrame:
        """
        Calculate scores for all indicators and merge them into a single DataFrame.

        Parameters:
        dev_asset_data (pd.DataFrame): Data containing development asset data.
        codebook_survey (pd.DataFrame): Data containing codebook survey data.
        esg_topics_table (pd.DataFrame): Data containing ESG topics data.

        Returns:
        pd.DataFrame: Data with calculated scores: per indicator, per aspect,
        per ESG topic, and per component.
        """

        scores_per_indicator_fractional = calculate_fractional_scores_per_indicator(
            dev_asset_data, codebook_survey, esg_topics_table
        )
        scores_all = calculate_aggregated_scores(
            dev_asset_data,
            scores_per_indicator_fractional,
            codebook_overview_indicators,
            esg_topics_table,
        )
        return scores_all
