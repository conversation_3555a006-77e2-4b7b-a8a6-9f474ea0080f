import pandas as pd
import numpy as np
from infra_dev_asset.schemas.mappings import response_id
from infra_dev_asset.scoring.CheckIndicatorOutput import check_indicator_score_output
from infra_dev_asset.scoring.ScoreBase import ScoreBase


class ScoreRM21(ScoreBase):
    """
    A class used to calculate scores for the RM2.1 indicator
    (Risk Management 2.1: Environmental risk assessment - design).
    Below are standard attributes with values specific to the class.

    Attributes
    indicator_name (str): Indicator name used for creating score column.
    indicator_variables (list): A dictionary defining 'primary' and 'other' indicator
    variable names.
    weight_distribution (dict): A dictionary defining weights distributions for each
    section.
    debugging_mode (bool, optional): In the debugging mode intermediate scores are saved
    to final DataFrame. Default is False.
    base_score (ScoreBase): An instance of the ScoreBase class initialized with
    indicator variables and weight distribution.
    include_other_vld (bool): Indicates whether to include other valid indicator
    variables.
    materiality (bool): Indicates whether materiality should be considered.
    diminish (bool): Transform fraction scores based on the diminishing increase in
    scoring curve.

    Methods:
    __repr__():  Provides a string representation of the ScoreRM21 instance.
    __init__(df): Initializes the ScoreRM21 instance with the given DataFrame.
    get_indicator_variables(): Retrieves the list of indicator variables.
    calculate_score(): Calculates the score for the RM2.1 indicator and returns a
    DataFrame.
    """

    def __init__(self, debugging_mode=False):
        """
        Initializes the ScoreRM21
        instance with default attributes.
        """
        self.indicator_name = "RM_2.1"
        self.indicator_variables = self.get_indicator_variables()
        self.weight_distribution = self.assign_weight_distribution()
        self.debugging_mode = debugging_mode
        self.base_score = ScoreBase(
            self.indicator_name,
            self.indicator_variables,
            self.weight_distribution,
        )

    def __repr__(self):
        """
        Provides a string representation of the ScoreRM21
        instance.
        """
        return (
            f"ScoreRM21(indicator_name={self.indicator_name}, "
            f"indicator_variables={self.indicator_variables}, "
            f"weight_distribution={self.weight_distribution}, "
            f"debugging_mode={self.debugging_mode}, "
            f"include_other_vld={self.include_other_vld}, "
            f"materiality={self.materiality}, "
            f"diminish={self.diminish})"
        )

    def get_indicator_variables(self) -> dict:
        """
        Retrieves the list of indicator variables.
        """
        indicator_variables_dict = {
            "radiobutton_1": {
                "section_name": f"{self.indicator_name}_1",
                "variable": f"{self.indicator_name}_A1_A",
            },
            "checkbox_1": {
                "section_name": f"{self.indicator_name}_2",
                "primary": [f"{self.indicator_name}_A1_{i}" for i in range(1, 15)],
                "other": [f"{self.indicator_name}_A1_15"],
            },
            "evidence_1": {
                "section_name": "evidence_1",
                "variable": f"{self.indicator_name}_A1_EVD_VLD",
            },
        }
        return indicator_variables_dict

    def assign_weight_distribution(self) -> dict:
        """
        Assigns weights distributions for indicator variables.

        Returns:
        dict: A dictionary defining weights distributions.
        """
        weights_distributions_dict = {
            "radiobutton_1": {
                "section": 2.0 / 5,
                "values": np.array([0, 1, 2, 3, 4]),
                "weights": np.array([0, 1.0 / 4, 2.0 / 4, 3.0 / 4, 4.0 / 4]),
            },
            "checkbox_1": {
                "section": 3.0 / 5,
                "primary": "materiality",
                "other": "materiality",
            },
            "evidence_1": [1.0],
        }
        return weights_distributions_dict

    def calculate_score(
        self,
        df: pd.DataFrame,
        codebook_survey: pd.DataFrame,
        esg_topics_table: pd.DataFrame,
    ) -> pd.DataFrame:
        """
        Calculates the score for the RM2.1
        indicator and returns a DataFrame.

        Parameters:
        df (pd.DataFrame): The DataFrame containing the response data.

        Returns:
        pd.DataFrame: A DataFrame with response IDs and their corresponding scores.
        """
        section_item = "radiobutton_1"
        score_radiobutton_1 = self.base_score.calculate_radiobutton_score(
            section_item, df
        )
        weight_radiobutton_1 = self.weight_distribution["radiobutton_1"]["section"]

        section_item = "checkbox_1"
        score_checkbox_1 = self.base_score.prepare_section_score_checkbox(
            section_item,
            df,
            codebook_survey=codebook_survey,
            esg_topics_table=esg_topics_table,
        )
        weight_checkbox_1 = self.weight_distribution["checkbox_1"]["section"]

        section_item = "evidence_1"
        score_evidence_1 = self.base_score.calculate_evidence_score(
            df, section_item, validation_str="RE3"
        )

        score_indicator = (
            score_radiobutton_1 * weight_radiobutton_1
            + score_checkbox_1 * weight_checkbox_1
        ) * score_evidence_1

        score_indicator[score_indicator > 1] = 1.0
        indicator_score_name = f"SCORE.F_{self.indicator_name}"

        indicator_score_table = pd.DataFrame(
            {
                response_id: df[response_id],
                indicator_score_name: score_indicator,
            }
        )
        if self.debugging_mode == True:
            radiobutton_1_name = self.base_score.get_section_score_name("radiobutton_1")
            indicator_score_table[radiobutton_1_name] = score_radiobutton_1
            checkbox_1_name = self.base_score.get_section_score_name("checkbox_1")
            indicator_score_table[checkbox_1_name] = score_checkbox_1

        check_indicator_score_output(
            indicator_score_table, indicator_score_table.columns
        )
        return indicator_score_table
