import pandas as pd
import numpy as np

from infra_dev_asset.schemas.mappings import response_id
from infra_dev_asset.scoring.CheckIndicatorOutput import check_indicator_score_output
from infra_dev_asset.scoring.ScoreBase import ScoreBase


class ScoreHS2(ScoreBase):
    """
    A class used to calculate scores for the HS2 indicator (Health and safety:
    contractors).
    Indicator consists of radiobuttons and a table with items weighted uniformly.
    Below are standard attributes with values specific to the class.

    Attributes
    indicator_name (str): Indicator name used for creating score column.
    indicator_variables (list): A dictionary defining 'primary' and 'other' indicator
    variable names.
    weight_distribution (dict): A dictionary defining weights distributions for each
    section.
    debugging_mode (bool, optional): In the debugging mode intermediate scores are saved
    to final DataFrame. Default is False.
    base_score (ScoreBase): An instance of the ScoreBase class initialized with
    indicator variables and weight distribution.
    include_other_vld (bool): Indicates whether to include other valid indicator
    variables.
    materiality (bool): Indicates whether materiality should be considered.
    diminish (bool): Transform fraction scores based on the diminishing increase in
    scoring curve.

    Methods
    __repr__():  Provides a string representation of the ScoreHS2 instance.
    __init__(df: pd.DataFrame): Initializes the ScoreHS2 instance with the given
    DataFrame.
    get_indicator_variables(): Retrieves the list of indicator variables.
    calculate_score(df: pd.DataFrame): Calculates the score for the HS2 indicator and
    returns a DataFrame.
    """

    def __init__(self, debugging_mode=False):
        """
        Initializes the ScoreHS2 instance with default attributes.
        """
        self.indicator_name = "HS_2"
        self.indicator_variables = self.get_indicator_variables()
        self.weight_distribution = self.assign_weight_distribution()
        self.debugging_mode = debugging_mode
        self.base_score = ScoreBase(
            self.indicator_name,
            self.indicator_variables,
            self.weight_distribution,
        )

    def __repr__(self):
        """
        Provides a string representation of the ScoreHS2 instance.
        """
        return (
            f"ScoreHS2(indicator_name={self.indicator_name}, "
            f"indicator_variables={self.indicator_variables}, "
            f"weight_distribution={self.weight_distribution}, "
            f"debugging_mode={self.debugging_mode}, "
            f"include_other_vld={self.include_other_vld}, "
            f"materiality={self.materiality}, "
            f"diminish={self.diminish})"
        )

    def get_indicator_variables(self) -> dict:
        """
        Retrieves the list of indicator variables.

        Returns:
        dict: A dictionary defining primary and other indicator variable names.
        """
        variables_lti_abs = [
            f"{self.indicator_name}_A1_TBL.ABS_PERF.CY_LTI",
            f"{self.indicator_name}_A1_TBL.ABS_TARG.CY_LTI",
            f"{self.indicator_name}_A1_TBL.ABS_TARG.FY_LTI",
        ]
        variables_tri_abs = [
            f"{self.indicator_name}_A1_TBL.ABS_PERF.CY_TRI",
            f"{self.indicator_name}_A1_TBL.ABS_TARG.CY_TRI",
            f"{self.indicator_name}_A1_TBL.ABS_TARG.FY_TRI",
        ]

        indicator_variables_dict = {
            "lost_time_injuries": {
                "section_name": "lost_time_injuries",
                "primary": variables_lti_abs,
            },
            "total_recordable_injuries": {
                "section_name": "total_recordable_injuries",
                "primary": variables_tri_abs,
            },
        }
        return indicator_variables_dict

    def assign_weight_distribution(self) -> dict:
        """
        Assigns weights distributions for indicator variables.

        Returns:
        dict: A dictionary defining weights distributions.
        """
        weights_distributions_dict = {
            "lost_time_injuries": {
                "primary": np.array([0.3, 0.1, 0.1]),
            },
            "total_recordable_injuries": {
                "primary": np.array([0.3, 0.1, 0.1]),
            },
        }
        return weights_distributions_dict

    def calculate_score(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculates the score for the HS2 indicator and returns a
        DataFrame.

        Parameters:
        df (pd.DataFrame): The DataFrame containing the response data.

        Returns:
        pd.DataFrame: A DataFrame with response IDs and their corresponding scores.
        """

        score_lti = self.base_score.calculate_performance_by_section(
            df, "lost_time_injuries"
        )
        score_tri = self.base_score.calculate_performance_by_section(
            df, "total_recordable_injuries"
        )

        score_indicator = score_lti + score_tri

        indicator_score_name = f"SCORE.F_{self.indicator_name}"

        indicator_score_table = pd.DataFrame(
            {
                response_id: df[response_id],
                indicator_score_name: score_indicator,
            }
        )

        check_indicator_score_output(
            indicator_score_table, indicator_score_table.columns
        )
        return indicator_score_table
