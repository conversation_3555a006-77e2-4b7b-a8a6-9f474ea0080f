# How to run the scoring model

The 'infraestructure development' scoring model main script is `infra_dev_asset > RunScoringModel.py`.
It takes a few arguments as input:

`survey_year` which is mandatory and requires a valid assessment year.
`--simulated` which is an optional argument set to true if specified. It indicates to the script that the data input should be simulated.
`--export-files` which is an optional argument set to true if specified. It indicates to the script that the scores, benchmark groups and memberships should be saved on S3.
`--debug` which is an optional argument set to true if specified. It indicates the script to import the data files locally rather than on S3 (it saves time) and disables result saving.
To run the infraestructure development the scoring model:

```
python -m infra_dev_asset.ScoringModelRunner 2024
```


*Note:* To run the infra dev asset scoring model locally, we need response data and the codebook.
Both the response data and the codebook come from https://github.com/GRESB/gresb-scoring.
If some data is missing when running the above code, you can check the details below to generate all required files.


## Generate simulated data in R

To use simulated as input to the scoring model, we need to generate it using the R code and save it locally.

Run this in the console to create a path for the data
```console
mkdir -p ~/scoring-models/infra_dev_asset/tmp/data/
```

```r
number_of_entries <- 1000
codebook <- gresb.rdatadir::LoadData("infra/2024/idev/1_cb")
sim_data <- gresb.sim.survey::SimulateSurveyData(codebook, 2024, number_of_entries)
sim_data <- gresb.scoring::AddSimulatedValidationData(sim_data, codebook)
# Update the path to match your directory structure.
nanoparquet::write_parquet(sim_data, "~/scoring-models/infra_dev_asset/tmp/data/simulated_survey_data.parquet")
```

*Note!*
If simulated data looks weird (e.g. missing some columns), try pulling main branch in the gresb-scoring and update R packages with:
`gresb.utils::UpdateLocalPackages()`

## Upload simulated data to S3

```
python -m infra_dev_asset.input.SimulatedSurveyDataExporter
```

## Save the codebook locally

The parsed codebook is also generated and saved as an rdd using the R code from gresb-scoring. To use it in the python model we have to save it locally. We are saving it as a json instead of a csv or parquet because the codebook is actually a nested list of data frames. The json file format allows us to continue to save the codebook as a single object.

```r
library(jsonlite)
codebook_json <- toJSON(codebook)
write(codebook_json, "~/scoring-models/infra_dev_asset/tmp/data/bronze_parsed_codebook.json")
```

# Comparing A and B models

## Run the secondary scoring model in R

```r
scoring_model <- gresb.scoring::CreateScoringModel("idev", 2024)
scored_data <- gresb.scoring::RunScoringModel(scoring_model, sim_data)
```

## Run the primary scoring model in python and save scores locally

The debug mode will save the scores file locally at `tmp/data/scores.csv`
```python
python -m infra_dev_asset.ScoringModelRunner 2024 --debug
```

---

*Note!*
This is a work in progress. So far the python model only outputs a subset of indicators.
Aggregation is currently being added.

## Compare scores to the primary R model:

Make sure that infra_dev_asset/tmp/data/simulated_survey_data.csv is the one uploaded to S3 [s3://gresb-dev-bronze-data/infra-dev-asset-2024/simulated_survey_data.csv], or use the local file directly to run.

In R environment in the gresb-scoring directory:
- make sure that the master branch is up-to-date
- run
```
gresb.utils::UpdateLocalPackages()
```
- run
```
data <- read.csv("../scoring-models/infra_dev_asset/tmp/data/simulated_survey_data.csv")
scoring_model <- gresb.scoring::CreateScoringModel("idev", 2024)
scores_A_model <- gresb.scoring::RunScoringModel(scoring_model, data)
scores_B_model <- read.csv("../scoring-models/infra_dev_asset/tmp/data/scores.csv")
scores_A_model <- scores_A_model[names(scores_B_model)]
gresb.utils::DfDiff(scores_A_model, scores_B_model)
