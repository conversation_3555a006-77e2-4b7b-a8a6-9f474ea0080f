import pytest
import pandas as pd
import numpy as np

from infra_dev_asset.schemas.mappings import response_id
from infra_dev_asset.scoring.Score_SE1 import ScoreSE1


@pytest.fixture
def setup_data():
    score_se1 = ScoreSE1(debugging_mode=True)
    responses = pd.DataFrame(
        {
            response_id: range(1, 18),
            # fmt: off
            "SE_1_A1_1": [np.nan, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0],
            "SE_1_A1_2": [np.nan, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0],
            "SE_1_A1_3": [np.nan, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0],
            "SE_1_A1_4": [np.nan, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0],
            "SE_1_A1_5": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0],
            "SE_1_A1_6": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0],
            "SE_1_A1_7": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0],
            "SE_1_A1_8": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0],
            "SE_1_A1_9": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0],
            "SE_1_A1_10": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0],
            "SE_1_A1_10_OTHER_VLD": [np.nan] * 12 + [5.0, 9.0, 3.0, 5.0, np.nan],

            "SE_1_A1_21": [np.nan, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0],
            "SE_1_A1_22": [np.nan, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 0.0],
            "SE_1_A1_23": [np.nan, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 1.0, 0.0],
            "SE_1_A1_24": [np.nan, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 0.0],
            "SE_1_A1_25": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0],
            "SE_1_A1_26": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 0.0],
            "SE_1_A1_27": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 0.0],
            "SE_1_A1_27_OTHER_VLD": [np.nan] * 9 + [5.0, 9.0, 3.0, 5.0, np.nan, 5.0, 5.0, np.nan],

            "SE_1_A1_A": [np.nan, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 2.0],
            "SE_1_A1_A1_GUIDELINE_OTHER_TEXT_VLD": [np.nan, np.nan] + [3.0, 5.0, 9.0, np.nan] * 3 + [3.0, 5.0, np.nan],
            # fmt: on
        }
    )
    return score_se1, responses


def test_calculate_score(setup_data):
    score_se1, responses = setup_data
    result = score_se1.calculate_score(responses)
    result_rounded = result.round(3)

    expected_result = pd.DataFrame(
        {
            response_id: range(1, 18),
            # fmt: off
            "SCORE.F_SE_1": [0.0, 0.2, 0.126, 0.326, 0.126, 0.326, 0.126, 0.326, 0.086, 0.326, 0.086, 0.2, 0.246, 0.32, 0.12, 1.0, 0.0],
            "SCORE.F_SE_1_1": [0.0, 0.0, 0.143, 0.143, 0.143, 0.143, 0.143, 0.143, 0.143, 0.143, 0.143, 0.0, 0.143, 0.0, 0.0, 1.0, 0.0],
            # we don't save it in model A
            # "SCORE.F_SE_1_A": [0.0, 1.0, 0.0, 1.0, 0.0, 1.0, 0.0, 1.0, 0.0, 1.0, 0.0, 1.0, 0.0, 1.0, 0.0, 1.0, 0.0],
            "SCORE.F_SE_1_2": [0.0, 0.0, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.0, 0.2, 0.0, 0.0, 0.8, 0.6, 0.6, 1.0, 0.0],
            # fmt: on
        }
    )
    pd.testing.assert_frame_equal(result_rounded, expected_result)
