import pytest
import pandas as pd
import numpy as np

from infra_dev_asset.schemas.mappings import response_id
from infra_dev_asset.scoring.Score_CO2 import ScoreCO2


@pytest.fixture
def setup_data():
    score_co2 = ScoreCO2()
    responses = pd.DataFrame(
        {
            response_id: range(1, 6),
            "CO_2_A1_3": [np.nan, 0.0, 1.0, 0.0, 1.0],
        }
    )
    return score_co2, responses


def test_calculate_score(setup_data):
    score_co2, responses = setup_data
    result = score_co2.calculate_score(responses)
    expected_result = pd.DataFrame(
        {
            response_id: range(1, 6),
            "SCORE.F_CO_2": [0.0, 0.0, 1.0, 0.0, 1.0],
        }
    )
    pd.testing.assert_frame_equal(result, expected_result)
