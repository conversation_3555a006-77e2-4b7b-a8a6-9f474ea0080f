import pytest
import pandas as pd

from infra_dev_asset.schemas.mappings import response_id
from infra_dev_asset.scoring.Score_PO2 import ScorePO2


@pytest.fixture
def setup_data():
    score_po2 = ScorePO2()
    responses = pd.DataFrame(
        {
            response_id: list(range(1, 8)),
            "PO_2_A": [2, 1, 1, 2, 1, 2, 2],
        }
    )
    return score_po2, responses


def test_calculate_score(setup_data):
    score_po2, responses = setup_data
    result = score_po2.calculate_score(responses)
    expected_result = pd.DataFrame(
        {
            response_id: [1, 2, 3, 4, 5, 6, 7],
            "SCORE.F_PO_2": [0.0, 1.0, 1.0, 0.0, 1.0, 0.0, 0.0],
        }
    )
    pd.testing.assert_frame_equal(result, expected_result)
