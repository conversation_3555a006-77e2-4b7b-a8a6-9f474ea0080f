import pytest
import pandas as pd
import numpy as np

from infra_dev_asset.schemas.mappings import response_id
from infra_dev_asset.scoring.Score_HS3 import ScoreHS3


@pytest.fixture
def setup_data():
    score_hs3 = ScoreHS3()
    responses = pd.DataFrame(
        {
            response_id: range(1, 9),
            # fmt: off
            # rate 1
            "HS_3_A1_TBL_PERF.CY_TRI": [np.nan, 0.06, 0.08, np.nan, np.nan, np.nan, np.nan, 0.06],
            "HS_3_A1_TBL_TARG.CY_TRI": [np.nan, np.nan, np.nan, 0.6, 0.8, np.nan, np.nan, 0.08],
            "HS_3_A1_TBL_TARG.FY_TRI": [np.nan, np.nan, np.nan, np.nan, np.nan, 0.6, 0.8, "Full coverage (100%)"],
            # fmt: on
        }
    )
    return score_hs3, responses


def test_calculate_score(setup_data):
    score_hs3, responses = setup_data
    result = score_hs3.calculate_score(responses)

    expected_result = pd.DataFrame(
        {
            response_id: range(1, 9),
            "SCORE.F_HS_3": [0.0, 0.6, 0.6, 0.2, 0.2, 0.0, 0.0, 1.0],
        }
    )
    pd.testing.assert_frame_equal(result, expected_result)
