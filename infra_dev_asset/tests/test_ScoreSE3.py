import pytest
import pandas as pd
import numpy as np

from infra_dev_asset.schemas.mappings import response_id
from infra_dev_asset.scoring.Score_SE3 import ScoreSE3


@pytest.fixture
def setup_data():
    score_se3 = ScoreSE3(debugging_mode=True)
    responses = pd.DataFrame(
        {
            response_id: range(1, 17),
            # fmt: off
            # checkbox 1
            "SE_3_A1_1": [np.nan, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_3_A1_2": [np.nan, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_3_A1_3": [np.nan, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_3_A1_4": [np.nan, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_3_A1_5": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_3_A1_6": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_3_A1_7": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_3_A1_8": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_3_A1_9": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_3_A1_10": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0],
            "SE_3_A1_10_OTHER_VLD": [np.nan] * 12 + [3.0, 9.0, 5.0, 5.0],
            # checkbox 2
            "SE_3_A1_21": [np.nan, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_3_A1_22": [np.nan, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_3_A1_23": [np.nan, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_3_A1_24": [np.nan, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_3_A1_25": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_3_A1_26": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_3_A1_27": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_3_A1_28": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_3_A1_29": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_3_A1_30": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0],
            "SE_3_A1_30_OTHER_VLD": [np.nan] * 12 + [3.0, 9.0, 5.0, 5.0],
            # fmt: on
        }
    )
    return score_se3, responses


def test_calculate_score(setup_data):
    score_se3, responses = setup_data
    result = score_se3.calculate_score(responses)
    result_rounded = result.round(3)

    expected_result = pd.DataFrame(
        {
            response_id: range(1, 17),
            # fmt: off
            "SCORE.F_SE_3": [0.0, 0.0] + [0.125] * 9 + [0.0, 0.0, 0.0, 0.125, 1.0],
            "SCORE.F_SE_3_1": [0.0, 0.0] + [0.125] * 9 + [0.0, 0.0, 0.0, 0.125, 1.0],
            "SCORE.F_SE_3_2": [0.0, 0.0] + [0.125] * 9 + [0.0, 0.0, 0.0, 0.125, 1.0],
            # fmt: on
        }
    )
    pd.testing.assert_frame_equal(result_rounded, expected_result)
