import pytest
import pandas as pd
import numpy as np

from infra_dev_asset.schemas.mappings import response_id
from infra_dev_asset.scoring.Score_MA1 import ScoreMA1


@pytest.fixture
def setup_data():
    score_ma1 = ScoreMA1(debugging_mode=True)
    responses = pd.DataFrame(
        {
            # fmt: off
            response_id: range(1, 17),
            # checkbox 1
            "MA_1_A1_1_1": [np.nan, 0.0, 1.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 0.0, 1.0, 1.0, 1.0],
            "MA_1_A1_1_2": [np.nan, 0.0, 0.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0],
            "MA_1_A1_1_3": [np.nan, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0],
            "MA_1_A1_1_3_OTHER_VLD": [np.nan] * 6 + [3.0, 9.0, 5.0, 3.0, 9.0, 5.0, 5.0, 3.0, 9.0, 5.0],
            # checkbox 2
            "MA_1_A1_2_1": [np.nan, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "MA_1_A1_2_2": [np.nan, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "MA_1_A1_2_3": [np.nan, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "MA_1_A1_2_4": [np.nan, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "MA_1_A1_2_5": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "MA_1_A1_2_6": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "MA_1_A1_2_7": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "MA_1_A1_2_8": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "MA_1_A1_2_9": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "MA_1_A1_2_10": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0],
            "MA_1_A1_2_10_OTHER_VLD": [np.nan] * 12 + [3.0, 9.0, 5.0, 5.0],
            # fmt: on
        }
    )
    return score_ma1, responses


def test_calculate_score(setup_data):
    score_ma1, responses = setup_data
    result = score_ma1.calculate_score(responses)
    result_rounded = result.round(3)

    expected_result = pd.DataFrame(
        {
            response_id: range(1, 17),
            # fmt: off
            "SCORE.F_MA_1": [0.0, 0.0, 0.333, 0.333, 0.5, 0.167, 0.167, 0.167] + [0.333] * 6 + [0.5, 1.0],
            "SCORE.F_MA_1_A1_1": [0.0, 0.0, 0.5, 0.5, 1.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5] + [1.0] * 5,
            "SCORE.F_MA_1_A1_2": [0.0, 0.0] + [0.25] * 9 + [0.0, 0.0, 0.0, 0.25, 1.0],
            # fmt: on
        }
    )
    pd.testing.assert_frame_equal(result_rounded, expected_result)
