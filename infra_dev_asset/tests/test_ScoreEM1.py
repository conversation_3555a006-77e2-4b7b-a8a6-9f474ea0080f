import pytest
import pandas as pd
import numpy as np

from infra_dev_asset.schemas.mappings import response_id
from infra_dev_asset.scoring.Score_EM1 import ScoreEM1


@pytest.fixture
def setup_data():
    score_em1 = ScoreEM1(debugging_mode=True)
    responses = pd.DataFrame(
        {
            response_id: range(1, 10),
            # percentage 1
            "EM_1_A1_A1_PROF": [0, 0, 0, 50, 50, 50, 100, 100, 100],
            "EM_1_A1_A1_ESG": [0, 50, 100] * 3,
            # percentage 2 - checkbox 1
            "EM_1_A1_B1_1": [np.nan] * 3 + [1.0] * 6,
            "EM_1_A1_B1_2": [np.nan, 1.0, 1.0] * 3,
            "EM_1_A1_B1_1_PCOV": [0, 0, 0, 50, 50, 50, 100, 100, 100],
            "EM_1_A1_B1_2_PCOV": [0, 50, 100] * 3,
            # percentage 2 - checkbox 2
            "EM_1_A1_B1_A1_1": [np.nan, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "EM_1_A1_B1_A1_2": [np.nan, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "EM_1_A1_B1_A1_3": [np.nan, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0],
            "EM_1_A1_B1_A1_3_OTHER_VLD": [np.nan] * 5 + [3.0, 9.0, 5.0, 5.0],
        }
    )
    return score_em1, responses


def test_calculate_score(setup_data):
    score_em1, responses = setup_data
    result = score_em1.calculate_score(responses)
    result_rounded = result.round(3)

    expected_result = pd.DataFrame(
        {
            response_id: range(1, 10),
            "SCORE.F_EM_1": [0.0, 0.458, 0.75, 0.458, 0.583, 0.708, 0.472, 0.819, 1.0],
            "SCORE.F_EM_1_1": [0.0, 0.25, 0.5, 0.25, 0.5, 0.75, 0.5, 0.75, 1.0],
            "SCORE.F_EM_1_2": [0.0, 0.667, 1.0, 0.667, 0.667, 0.667, 0.444, 0.889, 1.0],
            "SCORE.F_EM_1_2_1": [0.0, 1.0, 1.0, 0.667, 1.0, 1.0, 0.667, 1.0, 1.0],
            "SCORE.F_EM_1_2_2": [0.0, 0.0, 1.0, 0.667, 0.0, 0.0, 0.0, 0.667, 1.0],
        }
    )
    pd.testing.assert_frame_equal(result_rounded, expected_result)


def test_columns_present(setup_data):
    score_em1, responses = setup_data
    try:
        score_em1.calculate_score(responses)
    except ValueError:
        pytest.fail("check_required_columns() raised ValueError unexpectedly!")


def test_missing_columns(setup_data):
    score_em1, responses = setup_data
    responses = responses.drop(["EM_1_A1_B1_1", "EM_1_A1_B1_2"], axis="columns")
    result = score_em1.calculate_score(responses)
    result_rounded = result.round(3)

    expected_result = pd.DataFrame(
        {
            response_id: range(1, 10),
            "SCORE.F_EM_1": [0.0, 0.125, 0.417, 0.236, 0.25, 0.375, 0.25, 0.486, 0.667],
            "SCORE.F_EM_1_1": [0.0, 0.25, 0.5, 0.25, 0.5, 0.75, 0.5, 0.75, 1.0],
            "SCORE.F_EM_1_2": [0.0, 0.0, 0.333, 0.222, 0.0, 0.0, 0.0, 0.222, 0.333],
            "SCORE.F_EM_1_2_1": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
            "SCORE.F_EM_1_2_2": [0.0, 0.0, 1.0, 0.667, 0.0, 0.0, 0.0, 0.667, 1.0],
        }
    )
    pd.testing.assert_frame_equal(result_rounded, expected_result)
