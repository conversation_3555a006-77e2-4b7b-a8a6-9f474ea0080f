import pytest
import pandas as pd
import numpy as np

from infra_dev_asset.schemas.mappings import response_id
from infra_dev_asset.scoring.Score_HS1 import ScoreHS1


@pytest.fixture
def setup_data():
    score_hs1 = ScoreHS1()
    responses = pd.DataFrame(
        {
            response_id: range(1, 9),
            # fmt: off
            "HS_1_A1_TBL.ABS_PERF.CY_LTI": [np.nan, 6, 8, np.nan, np.nan, np.nan, np.nan, 6],
            "HS_1_A1_TBL.ABS_TARG.CY_LTI": [np.nan, np.nan, np.nan, 6, np.nan, np.nan, np.nan, 8],
            "HS_1_A1_TBL.ABS_TARG.FY_LTI": [np.nan, np.nan, np.nan, np.nan, np.nan, 1, np.nan, 1],
            "HS_1_A1_TBL.ABS_PERF.CY_TRI": [np.nan, 6, np.nan, np.nan, np.nan, np.nan, np.nan, 6],
            "HS_1_A1_TBL.ABS_TARG.CY_TRI": [np.nan, np.nan, np.nan, 6, 8, np.nan, np.nan, 8],
            "HS_1_A1_TBL.ABS_TARG.FY_TRI": [np.nan, np.nan, np.nan, np.nan, np.nan, 6, 8, 4],
            # fmt: on
        }
    )
    return score_hs1, responses


def test_calculate_score(setup_data):
    score_hs1, responses = setup_data
    result = score_hs1.calculate_score(responses)

    expected_result = pd.DataFrame(
        {
            response_id: range(1, 9),
            "SCORE.F_HS_1": [0.0, 0.6, 0.3, 0.2, 0.1, 0.2, 0.1, 1.0],
        }
    )
    pd.testing.assert_frame_equal(result, expected_result)
