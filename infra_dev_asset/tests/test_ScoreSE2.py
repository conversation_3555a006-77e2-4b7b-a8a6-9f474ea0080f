import pytest
import pandas as pd
import numpy as np

from infra_dev_asset.schemas.mappings import response_id
from infra_dev_asset.scoring.Score_SE2 import ScoreSE2


@pytest.fixture
def setup_data():
    score_se2 = ScoreSE2(debugging_mode=True)
    responses = pd.DataFrame(
        {
            response_id: range(1, 19),
            # fmt: off
            # checkbox 1
            "SE_2_A1_1": [np.nan, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 0.0, 1.0, 0.0, 1.0, 1.0],
            "SE_2_A1_2": [np.nan, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 0.0, 1.0, 1.0],
            "SE_2_A1_3": [np.nan, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 1.0, 1.0],
            "SE_2_A1_4": [np.nan, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0],
            "SE_2_A1_5": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 1.0, 0.0, 1.0],
            "SE_2_A1_6": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 0.0, 1.0],
            "SE_2_A1_7": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0, 1.0],
            "SE_2_A1_8": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0, 1.0],
            # checkbox 2
            "SE_2_A1_11": [np.nan, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_2_A1_12": [np.nan, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_2_A1_13": [np.nan, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_2_A1_14": [np.nan, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_2_A1_15": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_2_A1_16": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_2_A1_17": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_2_A1_18": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_2_A1_19": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_2_A1_20": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_2_A1_21": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0],
            "SE_2_A1_22": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0],
            "SE_2_A1_22_OTHER_VLD": [np.nan] * 14 + [3.0, 9.0, 5.0, 5.0],
            # checkbox 3
            "SE_2_A1_31": [np.nan, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0] * 2,
            "SE_2_A1_32": [np.nan, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0] * 2,
            "SE_2_A1_33": [np.nan, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0] * 2,
            "SE_2_A1_33_OTHER_VLD": ([np.nan] * 5 + [3.0, 9.0, 5.0, 5.0]) * 2,
            # fmt: on
        }
    )
    return score_se2, responses


def test_calculate_score(setup_data):
    score_se2, responses = setup_data
    result = score_se2.calculate_score(responses)
    result_rounded = result.round(3)

    expected_result = pd.DataFrame(
        {
            response_id: range(1, 19),
            # fmt: off
            "SCORE.F_SE_2": [0.0, 0.0, 0.27, 0.27, 0.103, 0.103, 0.103, 0.27, 0.437, 0.103, 0.27, 0.437, 0.492, 0.167, 0.222, 0.222, 0.437, 1.0] ,
            "SCORE.F_SE_2_1": [0.0, 0.0] + [0.167] * 8 + [0.667, 0.667, 0.833, 0.5, 0.667, 0.667, 0.667, 1.0],
            "SCORE.F_SE_2_2": [0.0, 0.0] + [0.143] * 11 + [0.0, 0.0, 0.0, 0.143, 1.0],
            "SCORE.F_SE_2_3": [0.0, 0.0, 0.5, 0.5, 0.0, 0.0, 0.0, 0.5, 1.0, 0.0, 0.0, 0.5, 0.5, 0.0, 0.0, 0.0, 0.5, 1.0],
            # fmt: on
        }
    )
    pd.testing.assert_frame_equal(result_rounded, expected_result)
