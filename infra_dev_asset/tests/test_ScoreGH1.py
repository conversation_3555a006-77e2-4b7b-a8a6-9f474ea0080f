import pytest
import pandas as pd

from infra_dev_asset.schemas.mappings import response_id
from infra_dev_asset.scoring.Score_GH1 import ScoreGH1


@pytest.fixture
def setup_data():
    score_gh1 = ScoreGH1(debugging_mode=True)
    responses = pd.DataFrame({response_id: range(1, 7), "GH_1_A": [1, 2, 1, 2, 1, 2]})
    return score_gh1, responses


def test_calculate_score(setup_data):
    score_gh1, responses = setup_data
    result = score_gh1.calculate_score(responses)
    expected_result = pd.DataFrame(
        {
            response_id: range(1, 7),
            "SCORE.F_GH_1": [1.0, 0.0, 1.0, 0.0, 1.0, 0.0],
        }
    )
    pd.testing.assert_frame_equal(result, expected_result)
