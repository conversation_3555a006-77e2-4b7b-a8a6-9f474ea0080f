import pytest
import pandas as pd
import numpy as np

from infra_dev_asset.schemas.mappings import response_id
from infra_dev_asset.scoring.Score_LE4 import ScoreLE4


@pytest.fixture
def setup_data():
    score_le4 = ScoreLE4(debugging_mode=True)
    responses = pd.DataFrame(
        {
            # 1st case: test when all selections are NaN
            # 2nd case: test when all selections are 0
            # 3rd-14th case: test the weight of each selection
            # 15th case: all selections are 1, and evidence will be 0
            # 16th case: all selections are 1, and evidence will be 1
            # fmt: off
            response_id: list(range(1,17)),

            "LE_4_A1_A_1": [np.nan, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0],
            "LE_4_A1_A_2": [np.nan, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0],
            "LE_4_A1_A_3": [np.nan, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0],
            "LE_4_A1_A_4": [np.nan, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0],
            "LE_4_A1_A_5": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0],
            "LE_4_A1_A_6": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0],
            "LE_4_A1_A_7": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0],
            "LE_4_A1_A_8": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0],
            "LE_4_A1_A_9": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 1.0],
            "LE_4_A1_A_10": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0],
            "LE_4_A1_A_11": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 1.0],
            "LE_4_A1_A_12": [np.nan, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0],
            "LE_4_A1_A_12_OTHER_VLD": [np.nan, 3.0, 3.0, 9.0, 5.0, np.nan, np.nan, 3.0, 3.0, 9.0, 9.0, 5.0, 5.0, 5.0, 3.0, 9.0],
            "LE_4_A1_A_EVD_VLD": [np.nan, 3.0, 4.0, 5.0, np.nan, 3.0, 4.0, 5.0, np.nan, 3.0, 4.0, 5.0, np.nan, 3.0, 4.0, 5.0]
            # fmt: on
        }
    )
    return score_le4, responses


def test_calculate_score(setup_data):
    score_le4, responses = setup_data
    result = score_le4.calculate_score(responses)

    expected_result = pd.DataFrame(
        {
            # fmt: off
            response_id: list(range(1,17)),
            "SCORE.F_LE_4": [
               0.0, 0.0, 0.25, 0.75, 0.0, 0.0, 0.25, 0.5, 0.0, 0.0, 0.25, 0.5, 0.0, 0.0, 0.5, 1.0
            ],
            "SCORE.F_LE_4_1": [0.0, 0.0, 0.5, 0.75, 0.75, 0.75, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.25, 1.0, 1.0],
            # fmt: on
        }
    )
    pd.testing.assert_frame_equal(result, expected_result)
