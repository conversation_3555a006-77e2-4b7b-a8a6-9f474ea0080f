import pytest
import pandas as pd
import numpy as np

from infra_dev_asset.schemas.mappings import response_id
from infra_dev_asset.scoring.Score_EM2 import ScoreEM2


@pytest.fixture
def setup_data():
    score_em2 = ScoreEM2()
    responses = pd.DataFrame(
        {
            response_id: range(1, 6),
            "EM_2_A1_1_4": [np.nan, 0.0, 1.0, 0.0, 1.0],
            "EM_2_A1_2_3": [np.nan, 0.0, 0.0, 1.0, 1.0],
        }
    )
    return score_em2, responses


def test_calculate_score(setup_data):
    score_em2, responses = setup_data
    result = score_em2.calculate_score(responses)
    expected_result = pd.DataFrame(
        {
            response_id: range(1, 6),
            "SCORE.F_EM_2": [0.0, 0.0, 0.5, 0.5, 1.0],
        }
    )
    pd.testing.assert_frame_equal(result, expected_result)
