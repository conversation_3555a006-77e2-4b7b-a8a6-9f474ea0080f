import pytest
import pandas as pd
from unittest.mock import MagicMock, patch
import argparse
import os

from infra_dev_asset.schemas.mappings import response_id
from data_io.s3_file import S3File
from data_io.data_s3_gateway import DataS3Gateway
from infra_dev_asset.scoring.ScoringModelCalculator import ScoringModelCalculator
from infra_dev_asset.ScoringModelRunner import ScoringModelRunner, main


@pytest.fixture(autouse=True)
def set_aws_profile_env(monkeypatch):
    """Fixture to set AWS_PROFILE environment variable for tests"""
    monkeypatch.setenv("AWS_PROFILE", "test-profile")


@pytest.fixture
def mock_data_s3_gateway():
    return MagicMock(spec=DataS3Gateway)


@pytest.fixture
def sample_data():
    return pd.DataFrame({response_id: [1, 2, 3], "data": ["A", "B", "C"]})


@pytest.fixture
def mock_import_data():
    def import_data_mock(data_type):
        sample_dict = {response_id: [1, 2, 3], "data": ["A", "B", "C"]}
        if data_type == "parsed_codebook.json":
            return pd.DataFrame(
                {"survey": [sample_dict], "overview": [{"indicators": [sample_dict]}]}
            )
        else:
            return pd.DataFrame(sample_dict)

    with patch(
        "infra_dev_asset.ScoringModelRunner.ScoringModelRunner.import_data"
    ) as mock:
        mock.side_effect = lambda data_type: import_data_mock(data_type)
        yield mock


def test_import_data_from_s3(mock_data_s3_gateway, sample_data):
    mock_data_s3_gateway.import_data.return_value = sample_data
    runner = ScoringModelRunner(
        survey_year=2021, simulated=False, debug=False, s3_gateway=mock_data_s3_gateway
    )

    with patch("infra_dev_asset.ScoringModelRunner.Path.exists", return_value=False):
        data = runner.import_data("survey_data.csv")

    mock_data_s3_gateway.import_data.assert_called_once_with(
        S3File(
            bucket_name="gresb-dev-bronze-data",
            base_filename="infra-dev-asset-2021/survey_data",
            format="csv",
        )
    )
    expected_result = pd.DataFrame({response_id: [1, 2, 3], "data": ["A", "B", "C"]})
    assert data.equals(expected_result)


def test_read_data_locally(mock_data_s3_gateway, sample_data):
    runner = ScoringModelRunner(
        survey_year=2021, simulated=False, debug=False, s3_gateway=mock_data_s3_gateway
    )

    with patch("infra_dev_asset.ScoringModelRunner.Path.exists", return_value=True):
        with patch(
            "infra_dev_asset.ScoringModelRunner.pd.read_csv", return_value=sample_data
        ):
            data = runner.import_data("survey_data.csv")

    expected_result = pd.DataFrame({response_id: [1, 2, 3], "data": ["A", "B", "C"]})
    assert data.equals(expected_result)


def test_calculate_scores(mock_data_s3_gateway, sample_data):
    runner = ScoringModelRunner(
        survey_year=2021, simulated=False, debug=False, s3_gateway=mock_data_s3_gateway
    )

    with patch("infra_dev_asset.ScoringModelRunner.Path.exists", return_value=True):
        with patch(
            "infra_dev_asset.ScoringModelRunner.pd.read_csv", return_value=sample_data
        ):
            runner.import_data("survey_data.csv")

    mock_calculator = MagicMock(spec=ScoringModelCalculator)
    mock_calculator.calculate_scores.return_value = sample_data

    with patch(
        "infra_dev_asset.ScoringModelRunner.ScoringModelCalculator", mock_calculator
    ):
        scores = mock_calculator.calculate_scores(sample_data, sample_data, sample_data)

    expected_result = pd.DataFrame({response_id: [1, 2, 3], "data": ["A", "B", "C"]})
    assert scores.equals(expected_result)


def test_main_export_files(mocker, mock_data_s3_gateway, sample_data, mock_import_data):
    args = argparse.Namespace(
        survey_year=2021, simulated=False, export_files=True, debug=False
    )

    mocker.patch(
        "infra_dev_asset.ScoringModelRunner.ScoringModelCalculator.calculate_scores",
        return_value=sample_data,
    )
    mocker.patch(
        "infra_dev_asset.ScoringModelRunner.DataS3Gateway",
        return_value=mock_data_s3_gateway,
    )

    main(args)

    mock_data_s3_gateway.export_data.assert_called_once_with(
        sample_data,
        S3File(
            bucket_name="gresb-dev-gold-data",
            base_filename="infra-dev-asset-2021/scores",
            format="csv",
        ),
    )


def test_main_debug_mode(mocker, mock_data_s3_gateway, sample_data, mock_import_data):
    args = argparse.Namespace(
        survey_year=2021, simulated=False, export_files=False, debug=True
    )

    mocker.patch(
        "infra_dev_asset.ScoringModelRunner.ScoringModelCalculator.calculate_scores",
        return_value=sample_data,
    )
    mocker.patch("infra_dev_asset.ScoringModelRunner.pd.DataFrame.to_csv")
    mocker.patch("infra_dev_asset.ScoringModelRunner.pd.DataFrame.to_parquet")
    mocker.patch("infra_dev_asset.ScoringModelRunner.os.makedirs")
    mocker.patch(
        "infra_dev_asset.ScoringModelRunner.os.path.exists", return_value=False
    )

    main(args)

    os.makedirs.assert_called_once_with("infra_dev_asset/tmp/data/")
    pd.DataFrame.to_csv.assert_called_once_with(
        "infra_dev_asset/tmp/data/scores.csv", index=False
    )
