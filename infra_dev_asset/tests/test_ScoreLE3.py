import pytest
import pandas as pd
import numpy as np

from infra_dev_asset.scoring.Score_LE3 import ScoreLE3


@pytest.fixture
def setup_data():
    score_le3 = ScoreLE3(debugging_mode=True)
    responses = pd.DataFrame(
        {
            # fmt: off
            "RESPONSE_ID": range(1, 9),
            # radiobutton 1
            "LE_3_A1_1_A": [np.nan, 1.0, 2.0, 3.0, 3.0, 3.0, 5.0, 6.0],
            "LE_3_A1_1_A3_OTHER_VLD": [
                np.nan, np.nan, np.nan, 5.0, 3.0, np.nan, np.nan, np.nan
            ],
            # radiobutton 2
            "LE_3_A1_2_A": [np.nan, 1.0, 2.0, 3.0, 4.0, 5.0, 5.0, 5.0],
            "LE_3_A1_2_A5_OTHER_VLD": [
                np.nan, np.nan, np.nan, np.nan, np.nan, 3.0, 5.0, np.nan
            ],
            # radiobutton 3
            "LE_3_A1_3_A": [np.nan, 1.0, 2.0, 3.0, 4.0, 5.0, 5.0, 5.0],
            "LE_3_A1_3_A5_OTHER_VLD": [
                np.nan, np.nan, np.nan, np.nan, np.nan, 5.0, 3.0, np.nan
            ],
            # radiobutton 4
            "LE_3_A1_4_A": [np.nan, 1.0, 2.0, 3.0, 4.0, 5.0, 5.0, 5.0],
            "LE_3_A1_4_A5_OTHER_VLD": [
                np.nan, np.nan, np.nan, np.nan, np.nan, 3.0, 5.0, np.nan
            ],
            # fmt: on
        }
    )
    return score_le3, responses


def test_calculate_score(setup_data):
    score_le3, responses = setup_data
    result = score_le3.calculate_score(responses)
    result_rounded = result.round(3)

    expected_result = pd.DataFrame(
        {
            "RESPONSE_ID": range(1, 9),
            "SCORE.F_LE_3": [0.0, 1.0, 1.0, 1.0, 0.5, 0.167, 0.833, 0.5],
            "SCORE.F_LE_3_1": [0.0, 1.0, 1.0, 1.0, 0.0, 0.0, 1.0, 1.0],
            "SCORE.F_LE_3_2": [0.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 0.0],
            "SCORE.F_LE_3_3": [0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0],
            "SCORE.F_LE_3_4": [0.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 0.0],
        }
    )
    pd.testing.assert_frame_equal(result_rounded, expected_result)
