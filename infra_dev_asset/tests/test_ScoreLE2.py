import pytest
import pandas as pd
import numpy as np

from infra_dev_asset.schemas.mappings import response_id
from infra_dev_asset.scoring.Score_LE2 import ScoreLE2


@pytest.fixture
def setup_data():
    score_le2 = ScoreLE2(debugging_mode=True)

    responses = pd.DataFrame(
        {
            # fmt: off
            response_id: list(range(1,18)),

            "LE_2_A1_1_2": [np.nan, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0],
            "LE_2_A1_1_3": [np.nan, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0],
            "LE_2_A1_1_4": [np.nan, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0],

            "LE_2_A1_2_1": [np.nan, 0.0, 1.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0],
            "LE_2_A1_2_2": [np.nan, 0.0, 0.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0],

            "LE_2_A1_A1_EVD_VLD": [3., 0.0, 4., 0.0, 5., 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3., 4., 5., 0.0, 0.0, 0.0],
            "LE_2_A1_A2_EVD_VLD": [np.nan, 3., 0.0, 4., 0.0, 5., 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3., 4., 5.],
            # fmt: on
        }
    )
    return score_le2, responses


def test_calculate_score(setup_data):
    score_le2, responses = setup_data
    result = score_le2.calculate_score(responses)
    # fmt: off
    expected_result = pd.DataFrame(
        {
            response_id: list(range(1,18)),
            "SCORE.F_LE_2": [0.0, 0.0, 0.05, 0.1375, 0.466667, 0.2, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 1.0, 0.0, 0.375, 0.75],
            "SCORE.F_LE_2_1": [
                0.0, 0.0, 0.0, 0.33333, 0.333333, 0.33333, 0.66667, 0.66667, 0.66667, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0
            ],
            "SCORE.F_LE_2_2": [0.0, 0.0, 0.5, 0.5, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0],
            "SCORE.F_LE_2_3": [0.0, 0.0, 0.5, 0.375, 1.0, 0.75, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 1.0, 0.0, 0.375, 0.75],
        }
    )
    # fmt: off
    pd.testing.assert_frame_equal(result, expected_result)
