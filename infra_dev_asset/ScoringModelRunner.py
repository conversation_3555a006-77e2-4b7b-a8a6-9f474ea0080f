import json
import os
import argparse
import logging
import pandas as pd
from pathlib import Path

from config import BRONZE_DATA_BUCKET, GOLD_DATA_BUCKET
from data_io.s3_file import S3File
from data_io.data_s3_gateway import DataS3Gateway
from infra_dev_asset.scoring.ScoringModelCalculator import ScoringModelCalculator

logger = logging.getLogger(__name__)


class ScoringModelRunner:
    """
    ScoringModelRunner handles the import and process required data for running
    Infrastructure Development scoring model and export its results.
    """

    def __init__(
        self, survey_year: int, simulated: bool, debug: bool, s3_gateway=DataS3Gateway()
    ):
        self.s3_gateway = s3_gateway
        self.survey_year = survey_year
        self.simulated = simulated
        self.debug = debug

    def import_data(self, file: str) -> pd.DataFrame:
        """
        Imports data from either local storage or S3.

        Parameters:
        file (str): The filename of the data file.

        Returns:
        pd.DataFrame: A pandas DataFrame containing the imported data.
        """
        filename, file_extension = os.path.splitext(file)
        filename = (
            "simulated_" + filename
            if self.simulated & filename.startswith("survey_data")
            else filename
        )

        data_path = Path(f"infra_dev_asset/tmp/data/{filename}{file_extension}")
        data_is_local = data_path.exists()

        if not data_is_local:
            logger.info(f"Importing {file} data from S3...")

            data = self.s3_gateway.import_data(
                S3File(
                    bucket_name=BRONZE_DATA_BUCKET,
                    base_filename=f"infra-dev-asset-{self.survey_year}/{filename}",
                    format=file_extension.lstrip("."),
                ),
            )
            if self.debug:
                logger.info("The data file was not saved locally. Saving now...")
                outdir = "infra_dev_asset/tmp/data/"
                if not os.path.exists(outdir):
                    os.makedirs(outdir)
                if file_extension == ".parquet":
                    data.to_parquet(data_path, index=False)
                elif file_extension == ".json":
                    with open(data_path, "w") as f:
                        json.dump(data, f)
                elif file_extension == ".csv":
                    data.to_csv(data_path, index=False)
        else:
            data = self._read_data_locally(filename, file_extension)
        return data

    def _read_data_locally(self, filename: str, file_extension: str) -> pd.DataFrame:
        """
        Reads data from local storage.

        Parameters:
        filename (str): The filename without extension.
        file_extension (str): The file extension (e.g., .csv, .parquet).

        Returns:
        pd.DataFrame: A pandas DataFrame containing the imported local data.
        """
        data_path = f"infra_dev_asset/tmp/data/{filename}{file_extension}"
        logger.info(f"Importing {filename} locally...")

        if file_extension == ".csv":
            return pd.read_csv(data_path)
        elif file_extension == ".parquet":
            return pd.read_parquet(data_path)
        elif file_extension == ".json":
            with open(data_path, "r") as f:
                return json.load(f)
        else:
            raise ValueError(f"Unsupported file extension: {file_extension}")


def main(args: argparse.Namespace) -> None:
    """
    Main function to run the scoring model.

    Parameters:
    args (argparse.Namespace): Command-line arguments parsed by argparse.

    Returns:
    None.
    """
    runner = ScoringModelRunner(args.survey_year, args.simulated, args.debug)

    dev_asset_data = runner.import_data("survey_data.parquet")
    codebook = runner.import_data("parsed_codebook.json")
    codebook_survey = pd.DataFrame(codebook.survey[0])
    codebook_overview_indicators = pd.DataFrame(codebook.overview[0]["indicators"])
    esg_topics_table = runner.import_data("esg_topics_table.parquet")

    scores = ScoringModelCalculator.calculate_scores(
        dev_asset_data, codebook_survey, codebook_overview_indicators, esg_topics_table
    )

    scores_filename = "simulated_scores" if args.simulated else "scores"

    if args.export_files:
        s3_gateway = DataS3Gateway()
        s3_gateway.export_data(
            scores,
            S3File(
                bucket_name=GOLD_DATA_BUCKET,
                base_filename=f"infra-dev-asset-{args.survey_year}/{scores_filename}",
                format="csv",
            ),
        )

    if args.debug:
        outdir = "infra_dev_asset/tmp/data/"
        if not os.path.exists(outdir):
            os.makedirs(outdir)
        scores.to_csv(f"{outdir}{scores_filename}.csv", index=False)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Run Infrastructure Development scoring model"
    )
    parser.add_argument(
        "survey_year", help="the reporting year of the data to score", type=int
    )
    parser.add_argument(
        "--simulated",
        action="store_true",
        help="whether the data should be simulated",
    )
    parser.add_argument(
        "--export-files",
        action="store_true",
        help="whether the output DataFrames should be saved on S3",
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="whether the script should be run for debug.",
    )

    args = parser.parse_args()
    main(args)
