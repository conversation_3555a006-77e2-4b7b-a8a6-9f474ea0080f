import sys
import json
import pandas as pd

from config import RUN<PERSON><PERSON>, BRONZE_DATA_BUCKET
from data_io.data_s3_gateway import DataS3Gateway
from data_io.s3_file import S3File


class LocalCodebookIO:
    """
    Class for loading the codebook from a local JSON file and converting it into a dictionary of pandas DataFrames.
    """

    def read(self, filename: str) -> dict:
        """
        Reads a JSON file and normalizes its contents into a dictionary of pandas DataFrames.

        Args:
            filename (str): The path to the JSON file to be read.

        Returns:
            dict: A dictionary where the keys are the keys of the JSON file and the values are
            pandas DataFrames. The key "overview" has two secondary keys, which are converted into two
            top-level keys "overview_aggregates" and "overview_indicators".

        Raises:
            FileNotFoundError: If the file is not found at the specified path.
            ValueError: If the file cannot be read as JSON.
        """

        try:
            with open(filename, "r") as file:
                codebook = json.load(file)
            codebook_normalized = {}
            for key in codebook.keys():
                if key == "overview":
                    for secondary_key in codebook[key].keys():
                        compound_key = f"{key}_{secondary_key}"
                        codebook_normalized[compound_key] = pd.json_normalize(
                            codebook[key][secondary_key]
                        )
                else:
                    codebook_normalized[key] = pd.json_normalize(codebook[key])
        except FileNotFoundError:
            raise FileNotFoundError(f"File not found at path '{filename}'")
        except ValueError:
            raise ValueError(f"Unable to read json file at path '{filename}'")

        return codebook_normalized


if __name__ == "__main__":

    codebook_data_location = (
        "scoring-models/infra_dev_asset/tmp/data/bronze_parsed_codebook.json"
    )

    # TODO: Use proper helpers + argument parsers
    if len(sys.argv) > 1:
        codebook_data_location = sys.argv[1]

    io = LocalCodebookIO()

    codebook = io.read(codebook_data_location)

    if RUNTIME != "dev":
        raise ValueError("Only run on dev")
    s3_gateway = DataS3Gateway()
    for key in codebook.keys():
        s3_gateway.export_data(
            codebook[key],
            S3File(
                bucket_name=BRONZE_DATA_BUCKET,
                base_filename="infra-dev-asset-2024/parsed_codebook_" + key,
                format="csv",
            ),
        )
