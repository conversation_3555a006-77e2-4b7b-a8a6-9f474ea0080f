import sys
import pandas as pd

from config import RUN<PERSON><PERSON>, BRONZE_DATA_BUCKET
from data_io.data_s3_gateway import DataS3Gateway
from data_io.s3_file import S3File
from infra_dev_asset.schemas.mappings import response_id


class SimulatedSurveyDataExporter:
    """
    Class used to be able to read and write to a local filesystem.
    """

    def export_simulated_survey_data_to_s3(
        self, bronze_simulated_data_location: str, s3_gateway=DataS3Gateway()
    ) -> None:
        """
        Export local file - simulated survey data to S3.

        Parameters:
        bronze_simulated_data_location (str): Local file path.
        s3_gateway (DataS3Gateway): S3 gateway.

        Returns:
        None.
        """
        # TODO: Use proper helpers + argument parsers
        if len(sys.argv) > 1:
            bronze_simulated_data_location = sys.argv[1]

        data = self.read(bronze_simulated_data_location)
        data = data.rename({"RESPONSE_ID": response_id}, axis="columns")

        s3_gateway.export_data(
            data,
            S3File(
                bucket_name=BRONZE_DATA_BUCKET,
                base_filename="infra-dev-asset-2024/simulated_survey_data",
                format="csv",
            ),
        )

    def read(self, path: str) -> pd.DataFrame:
        """
        Reads data from a csv file located at the specified path and returns it as a pandas DataFrame.

        Parameters:
        path: The file path to the csv file to be read.

        Returns:
        DataFrame: A pandas DataFrame containing the data read from the csv file.

        Raises:
        FileNotFoundError: If the specified file path does not exist.
        ValueError: If the file at the specified path is not a valid csv file or cannot be read.
        """
        try:
            return pd.read_csv(path)
        except FileNotFoundError:
            raise FileNotFoundError(f"File not found at path '{path}'")
        except ValueError:
            raise ValueError(f"Unable to read csv file at path '{path}'")


if __name__ == "__main__":

    if RUNTIME != "dev":
        raise ValueError("Only run on dev")

    exporter = SimulatedSurveyDataExporter()
    bronze_simulated_data_location = (
        "infra_dev_asset/tmp/data/simulated_survey_data.csv"
    )
    exporter.export_simulated_survey_data_to_s3(bronze_simulated_data_location)
