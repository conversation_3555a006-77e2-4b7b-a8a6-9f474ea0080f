import pandas as pd

from config import BRONZE_DATA_BUCKET
from data_io.data_postgres_gateway import DataPostgresGateway
from data_io.data_s3_gateway import DataS3Gateway
from data_io.s3_file import S3File
from infra_dev_asset.schemas.mappings import response_id


def get_esg_topics_table(postgres_gateway: DataPostgresGateway) -> pd.DataFrame:
    """
    Fetches the ESG topics table from the database and adjusts the content.

    Parameters:
    postgres_gateway (DataPostgresGateway): Instance of DataPostgresGateway for database access.

    Returns:
    pd.DataFrame: DataFrame with ESG topic IDs and names.
    """

    query = """
        SELECT et.id, et.name FROM esg_topics et;
    """
    esg_topics_table = postgres_gateway.import_db_data(query)
    # Adjust the name for consistency
    esg_topics_table.loc[
        esg_topics_table["name"] == "Inclusion and diversity", "name"
    ] = "Diversity, Equity, and Inclusion"
    esg_topics_table = esg_topics_table.rename(
        {"RESPONSE_ID": response_id}, axis="columns"
    )
    return esg_topics_table


if __name__ == "__main__":

    postgres_gateway = DataPostgresGateway()
    esg_topics_table = get_esg_topics_table(postgres_gateway)

    s3_gateway = DataS3Gateway()

    s3_gateway.export_data(
        esg_topics_table,
        S3File(
            bucket_name=BRONZE_DATA_BUCKET,
            base_filename="infra-dev-asset-2025/esg_topics_table",
            format="parquet",
        ),
    )
