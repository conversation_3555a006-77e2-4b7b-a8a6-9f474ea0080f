import os
from dotenv import load_dotenv

from gresb_utils.git_utils import get_current_branch


load_dotenv()

AWS_REGION = os.getenv("AWS_DEFAULT_REGION", "default-region")

RUNTIME = os.getenv("RUNTIME", "dev")
GOLD_DATA_BUCKET = f"gresb-{RUNTIME}-gold-data"
SILVER_DATA_BUCKET = f"gresb-{RUNTIME}-silver-data"
BRONZE_DATA_BUCKET = f"gresb-{RUNTIME}-bronze-data"
READ_FROM_MAIN_FOLDER = os.getenv("READ_FROM_MAIN_FOLDER", "False").lower() == "true"
BRANCH = get_current_branch()

# AWS profile for authentication with AWS services
AWS_PROFILE = os.getenv("AWS_PROFILE")
if AWS_PROFILE == "runtime":
    AWS_PROFILE = f"gresb-{RUNTIME}-developer-break-glass"
if AWS_PROFILE == "gresb-prd-developer-break-glass" and RUNTIME != "prd":
    raise ValueError("Running prd profile outside prd not allowed")

LOCAL_BUCKET = os.getenv("LOCAL_BUCKET")
