# scoring-models

This repository contains the different GRESB scoring models written in Python.

# Project Setup and Development Guide

This guide provides instructions for setting up Python, PyEnv, and Poetry on macOS, along with guidance for local development, testing, and our GitHub Actions workflow structured around trunk-based development with Pull Requests (PRs).

## Setting up the Development Environment

### Installing Python with PyEnv

1. **Install PyEnv**: First, you need to install PyEnv, which will allow you to manage multiple versions of Python easily. You can install PyEnv using Homebrew with the following command:

```sh
brew update
brew install pyenv
```

2. **Configure your shell**: Add PyEnv to your shell by adding the following lines to your `~/.bash_profile`, `~/.zprofile`, or `~/.bashrc`, depending on your shell:

```sh
export PYENV_ROOT="$HOME/.pyenv"
command -v pyenv >/dev/null || export PATH="$PYENV_ROOT/bin:$PATH"
eval "$(pyenv init --path)"
```

3. **Install Python**: Install the desired version of Python (e.g., Python 3.13) using PyEnv:

```sh
pyenv install 3.13
```

Verify the installation with `python --version`.


### Installing and Configuring Poetry

1. **Install Poetry**: Install Poetry for dependency management:

```sh
brew install poetry
```

2. **Configure Poetry**: Ensure Poetry is configured to create virtual environments within your project by running:

 ```sh
poetry config virtualenvs.in-project true
 ```

if you want to install the dependencies in the local environment add following option to above command
 ```sh
 --local
 ```

### Configure environment variables from .env file
Add a .env file to our project, similar to the setup we have in gresb-scoring. Use .env.example and get the secrets from 1password.
You can use the RUNTIME variable to switch between runtimes. In general, the AWS_PROFILE can be set to "runtime", so it selects the right value automatically.
To get things up and running, run the following commands:
```
set -a        # Automatically export all variables
source .env   # Grab the variables from the .env file
set +a        # Turn off auto-export
```

## Local Development

1. **Clone the Repository**: Clone the project repository to your local machine.

2. **Install Dependencies**: Navigate to your project directory and install the project dependencies with Poetry:

Check which python poetry is using:
```sh
poetry env info
```

If poetry is not using python 3.13, specify the version for poetry to use by running
```sh
poetry env use python3.13
```

Install the dependencies
```sh
poetry install
```
***Note:*** Ensure that poetry is using python 3.13. Higher python version will cause incompatibility.

3. **Activate the Virtual Environment**: You can activate the project's virtual environment created by Poetry by running:

Please use poetry > 2.0.

```sh
eval $(poetry env activate)
```

4. **Developing and Running Your Application**: Make your changes or additions to the project. You can run your application or scripts within the virtual environment created by Poetry.

## Install the git pre-commit hook

### Preparation
Make sure to run `pre-commit install` inside the VE (`poetry shell`) this will make sure pre-commit uses the correct python env.

Verify by running this command:
```sh
which pre-commit
```
It should point to the python of your virtual environment of this project, which is `<your_path>/scoring-models/.venv/bin/pre-commit`

### set up the git hook scripts
```sh
pre-commit install
```
You should see: `pre-commit installed at .git/hooks/pre-commit`.
Now `pre-commit` will run automatically on `git commit`!

Verify if the `INSTALL_PYTHON` is set correctly by running:
```sh
cat .git/hooks/pre-commit | grep INSTALL_PYTHON
```
And INSTALL_PYTHON should be `<your_path>/scoring-models/.venv/bin/python`

### Running pre-commit hook locally
Sometimes running pre-commit locally is required when your changes fails in the ci pipeline, you will need to fix it locally.
Running with poetry makes sure that you use the pre-commit that is consistent with this project's python environment.

```
poetry run pre-commit run --all-files
```

## Running Tests Locally

Ensure your code changes pass all tests before pushing to the repository. Run tests for one specific scoring model using the following command (for example for Real Estate):

```sh
poetry run pytest contexts/app-real-estate/tests/
```

## Understanding the GitHub Actions Workflow

Our project uses GitHub Actions for Continuous Integration (CI), structured around trunk-based development with PRs. The main.yml workflow consists of two main jobs: pre_commit and build_and_test.

- **pre_commit Job**: Runs on every push, except for changes in the analysis/** directory. It sets up Python, caches the Poetry virtual environment, installs dependencies, and runs pre-commit hooks.
- **build_and_test Job**: Depends on the successful completion of pre_commit. It also sets up Python, caches the Poetry virtual environment, installs dependencies, and runs tests with pytest.

### Workflow Structure

- **Trunk-Based Development**: We use trunk-based development, where the data team work in short-lived branches off the trunk/main branch. It allows for faster integration and requires that code changes are small and merged frequently.
- **Pull Requests (PRs)**: the Data Team must use PRs for code review and integration into the main branch. PRs trigger the main.yml workflow, ensuring that code is tested before merging.

This workflow promotes a healthy CI process, ensuring that our main branch remains stable and deployable at all times.
