repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.0.1  # Use the latest version
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: debug-statements
        exclude: 'app_real_estate/run_scoring_model.py'

  - repo: https://github.com/pycqa/flake8
    rev: 7.1.0  # Use the latest version
    hooks:
       - id: flake8
         args: [--max-line-length=120, --select=F401,E501]

  - repo: https://github.com/pycqa/autoflake
    rev: v2.3.1
    hooks:
      - id: autoflake
        args: [--in-place, --remove-all-unused-imports]

  - repo: https://github.com/psf/black
    rev: 24.2.0  # Use the latest version
    hooks:
      - id: black
        language_version: python3.13  # Ensure this matches your project's Python version

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v0.910  # Use the latest version
    hooks:
      - id: mypy
        additional_dependencies: [types-requests]

  # New local hook to run pytest
  - repo: local
    hooks:
      - id: pytest-check
        name: pytest-check
        # Avoid running component tests on every commit
        entry: poetry run pytest --verbose --ignore=contexts/app-real-score-contribution/tests/component/
        language: system
        pass_filenames: false
        always_run: true
