name: Deploy PR release

on:
  pull_request:
    types: [ labeled ]


jobs:
  prepare-pr-deployment:
    name: Prepare PR Deployment
    runs-on: ubuntu-latest
    # PR to main, previously labeled with 'tag' (so an RC is created), and currently labeled with 'deploy'
    if: ${{ github.event.pull_request.base.ref == 'main' && github.event.label.name == 'deploy' }}
    outputs:
      release-name: ${{ steps.read-pr-tag.outputs.tag }}
      pr-number: ${{ steps.read-pr-tag.outputs.pr-number }}
    steps:
      - name: Debug event
        run: |
          echo "Debug"
        env:
          event: ${{ toJSON(github.event) }}
      - name: Read PR Tag
        id: read-pr-tag
        uses: GRESB/action-git-tag@v1.13.0
        with:
          read: true
          pr-number: ${{ github.event.pull_request.number }}
      - uses: actions/checkout@v4

  deploy-pr-release:
    name: Deploy pr release to DEV
    uses: ./.github/workflows/deployment.yaml
    if: ${{ github.event.pull_request.base.ref == 'main' && github.event.label.name == 'deploy' }}
    needs:
      - prepare-pr-deployment
    with:
      release: ${{ needs.prepare-pr-deployment.outputs.release-name }}
      runtime: DEV
      issue-number: ${{ github.event.pull_request.number }}
      runtime-aws-role-arn: ${{ vars.DEV_SCORING_MODELS_AWS_IAM_ROLE_ARN }}

  remove-deploy-label:
    name: Remove deploy label
    if: ${{ always() && github.event.pull_request.base.ref == 'main' && github.event.label.name == 'deploy' }}
    runs-on: ubuntu-latest
    needs:
      - prepare-pr-deployment
      - deploy-pr-release
    steps:
      - name: Remove deploy label
        uses: actions-ecosystem/action-remove-labels@v1
        with:
          labels: deploy
          number: ${{ needs.prepare-deployment.outputs.pr-number }}
