name: Git Tag


on:
  push:
    branches:
      - main
  pull_request:
    types: [ labeled ]


jobs:
  tag:
    name: Create git tag
    runs-on: ubuntu-latest
    # Only run on push to main or when is PRs labeled with "tag"
    if: ${{ github.ref == 'refs/heads/main' || github.event.label.name == 'tag' }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: '0'
      - name: Git tag
        uses: GRESB/action-git-tag@v1.13.0
        with:
          create: true
          pr-number: ${{ github.event.pull_request.number }}
          github-token: ${{ secrets.BOT_PAT_REPO_ORG_ADMIN }}
          tag-comment-body: |
            Release: **not yet created**
            (Please don't label the PR with 'deploy' before this is updated with the release details)
      - name: Remove tag label
        uses: actions-ecosystem/action-remove-labels@v1
        if: always()
        with:
          labels: tag
          number: ${{ github.event.pull_request.number }}
