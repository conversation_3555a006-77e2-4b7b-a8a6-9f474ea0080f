name: Scoring models CI

on:
  push:
    branches:
      - main
    paths-ignore:
      - "analyses/**"
  pull_request:
    branches:
      - main
    types: [ opened, synchronize, reopened ]

permissions:
  id-token: write # Required for requesting the JWT
  contents: read  # Required for actions/checkout

env:
  aws_region: "eu-central-1"

jobs:
  pre-commit:
    name: Pre commit hooks
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: "3.13"
      - name: Install poetry
        run: pip install poetry
      - name: Cache Poetry virtualenv
        uses: actions/cache@v4
        with:
          path: ~/.cache/pypoetry/virtualenvs
          key: ${{ runner.os }}-poetry-${{ hashFiles('**/poetry.lock') }}
          restore-keys: |
            ${{ runner.os }}-poetry-
      - name: Install dependencies
        run: poetry install

      - name: Run pre-commit hooks (including run unittests)
        run: |
          poetry run pre-commit run --all-files --verbose

  build-and-test:
    name: Build and test
    needs:
      - pre-commit
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: "3.13"
      - name: Install poetry
        run: pip install poetry
      - name: Cache Poetry virtualenv
        uses: actions/cache@v4
        with:
          path: ~/.cache/pypoetry/virtualenvs
          key: ${{ runner.os }}-poetry-${{ hashFiles('**/poetry.lock') }}
          restore-keys: |
            ${{ runner.os }}-poetry-
      - name: Install dependencies
        run: poetry install

      - name: Configure AWS Credentials (OIDC)
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ${{env.aws_region}}
          # defined in gresb-cloud: upper("${var.environment}_${replace(upper(local.data_team_integration_test_role_name) , "-", "_")}_ROLE_ARN")
          # hardcoded to DEV because we only run integration tests using files on DEV
          role-to-assume: ${{vars.DEV_DATA_TEAM_INTEGRATION_TEST_ROLE_ARN}}
          role-session-name: github-oidc-data-team-integration-test

      - name: Run component tests
        run: |
          poetry run pytest --log-cli-level=INFO contexts/app-real-score-contribution/tests/component/
