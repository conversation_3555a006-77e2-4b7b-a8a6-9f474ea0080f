name: Create release


on:
  push:
    tags:
      - '*'


env:
  status_context: Create release (push tag)
  workflow_run_url: "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
  aws_region: eu-central-1
  ecr_url: 510016332031.dkr.ecr.eu-central-1.amazonaws.com/scoring-models
  app_name: scoring-models

permissions:
  id-token: write # Required for requesting the JWT
  contents: write
  pull-requests: write
  checks: write
  issues: write
  statuses: write

jobs:
  prepare-release:
    name: Prepare release
    runs-on: ubuntu-latest
    outputs:
      name: ${{ steps.release-parameters.outputs.tag }}
      is-final: ${{ steps.release-parameters.outputs.is-final }}
      sha: ${{ steps.release-parameters.outputs.sha }}
      pr-number: ${{ steps.release-parameters.outputs.pr-number }}
    steps:
      - name: Detect release parameters
        id: release-parameters
        uses: GRESB/action-git-tag@v1.13.0
        with:
          read: true
          ref: ${{ github.ref }}
      - name: Set commit status as pending
        uses: myrotvorets/set-commit-status-action@v2.0.1
        if: ${{ steps.release-parameters.outputs.is-final == 'false' }}
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          status: pending
          context: ${{ env.status_context }}
          sha: ${{ steps.release-parameters.outputs.sha }}
          targetUrl: ${{ env.workflow_run_url }}

  package-app:
    name: Package application
    runs-on: ubuntu-latest
    needs:
      - prepare-release
    outputs:
      container-image: ${{ steps.build-and-push-container.outputs.container-image }}
    env:
      release_name: ${{ needs.prepare-release.outputs.name }}
      tag_commit_sha: ${{ needs.prepare-release.outputs.sha }}
    steps:
      - run: |
          echo "DEBUG :: ${release_name}"
          echo "DEBUG :: ${tag_commit_sha}"
      - uses: actions/checkout@v4
      - name: AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        id: aws-credentials
        with:
          aws-region: ${{ env.aws_region }}
          role-to-assume: ${{ vars.SVC_SCORING_MODELS_ECR_PUSH_ROLE_ARN }}
          role-session-name: github-oidc-scoring-models
      - name: "Login to Amazon ECR"
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      - name: Build and upload docker container
        id: build-and-push-container
        run: |
          docker build -t ${{ env.app_name }}:${{ env.release_name }} .

          docker tag ${{ env.app_name }}:${{ env.release_name }} ${{ env.ecr_url }}:${{ env.release_name }}

          docker push ${{ env.ecr_url }}:${{ env.release_name }}

          container_image="${{ env.ecr_url }}:${{ env.release_name }}"
          echo "container-image=${container_image}" >> "${GITHUB_OUTPUT}"
          echo "DEBUG :: container-image = ${container_image}"
      - name: Set final commit status as error
        uses: myrotvorets/set-commit-status-action@v2.0.1
        if: ${{ failure() && env.release_is_final == 'false' }}
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          status: error
          context: ${{ env.status_context }}
          sha: ${{ env.tag_commit_sha }}
          targetUrl: ${{ env.workflow_run_url }}

  create-release:
    name: Release application
    runs-on: ubuntu-latest
    needs:
      - prepare-release
      - package-app
    env:
      release_name: ${{ needs.prepare-release.outputs.name }}
      release_is_final: ${{ needs.prepare-release.outputs.is-final }}
      release_pr_number: ${{ needs.prepare-release.outputs.pr-number }}
      tag_commit_sha: ${{ needs.prepare-release.outputs.sha }}
      container_image: ${{ needs.package-app.outputs.container-image }}
    steps:
      - uses: actions/checkout@v4
      - name: Create Release
        id: release
        uses: GRESB/action-release@v0.2.0
        with:
          name: ${{ needs.prepare-release.outputs.name }}
          is-final: ${{ needs.prepare-release.outputs.is-final }}
          pr-number: ${{ needs.prepare-release.outputs.pr-number }}
          body: |
            ## Artifacts

            - Scoring models container image:

              ```${{ env.container_image }}```
          github-token: ${{ secrets.GITHUB_TOKEN }}
      - name: Set final commit status
        uses: myrotvorets/set-commit-status-action@v2.0.1
        if: ${{ always() && env.release_is_final == 'false' }}
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          status: ${{ job.status }}
          context: ${{ env.status_context }}
          sha: ${{ env.tag_commit_sha }}
          targetUrl: ${{ env.workflow_run_url }}
      - name: "[ Clean up failed release ] AWS credentials"
        uses: aws-actions/configure-aws-credentials@v4
        id: aws-credentials
        if: ${{ failure() }}
        with:
          aws-region: ${{ env.aws_region }}
          role-to-assume: ${{ vars.SVC_SCORING_MODELS_ECR_PUSH_ROLE_ARN }}
          role-session-name: github-oidc-scoring-models
      - name: "[ Clean up failed release ] Delete artifacts"
        if: ${{ failure() }}
        run: |
          aws ecr batch-delete-image --repository-name "${{ env.app_name }}" --image-ids "imageTag=${release_name}"
        env:
          aws_account_id: ${{ steps.aws-credentials.outputs.aws-account-id }}

  deploy-dev:
    name: Deploy lambda to DEV
    uses: ./.github/workflows/deployment.yaml
    if: ${{ needs.prepare-release.outputs.is-final == 'true' }}
    needs:
      - prepare-release
      - create-release
    with:
      release: ${{ needs.prepare-release.outputs.name }}
      runtime: DEV
      runtime-aws-role-arn: ${{ vars.DEV_SCORING_MODELS_AWS_IAM_ROLE_ARN }}

  deploy-tst:
    name: Deploy lambda to TST
    uses: ./.github/workflows/deployment.yaml
    if: ${{ needs.prepare-release.outputs.is-final == 'true' }}
    needs:
      - prepare-release
      - deploy-dev
    with:
      release: ${{ needs.prepare-release.outputs.name }}
      previous-runtime: DEV
      runtime: TST
      previous-runtime-aws-role-arn: ${{ vars.DEV_SCORING_MODELS_AWS_IAM_ROLE_ARN }}
      runtime-aws-role-arn: ${{ vars.TST_SCORING_MODELS_AWS_IAM_ROLE_ARN }}

  deploy-acc:
    name: Deploy lambda to ACC
    uses: ./.github/workflows/deployment.yaml
    if: ${{ needs.prepare-release.outputs.is-final == 'true' }}
    needs:
      - prepare-release
      - deploy-tst
    with:
      release: ${{ needs.prepare-release.outputs.name }}
      previous-runtime: TST
      runtime: ACC
      previous-runtime-aws-role-arn: ${{ vars.TST_SCORING_MODELS_AWS_IAM_ROLE_ARN }}
      runtime-aws-role-arn: ${{ vars.ACC_SCORING_MODELS_AWS_IAM_ROLE_ARN }}

  set-commit-status:
    name: Set commit status
    runs-on: ubuntu-latest
    if: ${{ needs.prepare-release.outputs.is-final == 'true' }}
    needs:
      - prepare-release
      - deploy-acc
    env:
      if_final: ${{ needs.prepare-release.outputs.is-final }}
      tag_commit_sha: ${{ needs.prepare-release.outputs.tag_commit_sha }}
    steps:
      - name: Set final commit status
        uses: myrotvorets/set-commit-status-action@v2.0.1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          status: ${{ needs.deploy-acc.result }}
          context: "Deploy release to ACC"
          sha: ${{ env.tag_commit_sha }}
          targetUrl: ${{ env.workflow_run_url }}
