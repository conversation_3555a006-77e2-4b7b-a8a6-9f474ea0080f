name: Clean after PR


on:
  pull_request:
    types: [ closed, labeled ]


env:
  aws_region: eu-central-1

permissions:
  id-token: write # Required for requesting the JWT
  contents: write

jobs:
  delete-release-candidates:
    name: Delete PR release candidates
    runs-on: ubuntu-latest
    if: ${{ github.event.action == 'closed' || github.event.label.name == 'delete-releases' }}
    env:
      pr_number: ${{ github.event.number }}
    outputs:
      deleted-releases: ${{ steps.outputs.delete-releases.releases }}
    steps:
      - uses: GRESB/action-delete-release-candidates@v0.12.0
        id: delete-releases
        with:
          dry-run: false
          github-token: ${{ secrets.GITHUB_TOKEN }}
          release-identifier: "pr${{ env.pr_number }}-rc"
          ecr-name: scoring-models
          aws-role-arn: ${{ vars.SVC_SCORING_MODELS_ECR_PUSH_ROLE_ARN }}
          aws-region: ${{ env.aws_region }}

  remove-delete-release-label:
    name: Remove deploy label
    if: ${{ always() && github.event.label.name == 'delete-releases' }}
    runs-on: ubuntu-latest
    steps:
      - name: Remove deploy label
        uses: actions-ecosystem/action-remove-labels@v1
        with:
          labels: delete-releases
          number: ${{ github.event.pull_request.number }}
