name: Deploy to PRD

on:
  workflow_dispatch: # Allow for running this manually.
    inputs:
      release:
        description: "Release to be deployed"
        required: true
        default: "latest"

permissions:
  id-token: write # Required for requesting the JWT
  contents: read
  checks: write
  issues: write
  pull-requests: write

jobs:
  prepare-deployment:
    name: Prepare Deployment
    runs-on: ubuntu-latest
    outputs:
      release-name: ${{ steps.read-release.outputs.release }}
    steps:
      - uses: actions/checkout@v4
      - name: Read Release
        id: read-release
        run: |
          if [[ "${{ inputs.release }}" == "latest" ]]; then
            latest_release=$(gh release list -L 1 --json name | jq -r '.[0].name' | sed 's/^Release //')
            echo "release=${latest_release}" >> "${GITHUB_OUTPUT}"
          else
            echo "release=${{ inputs.release }}" >> "${GITHUB_OUTPUT}"
          fi
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  deploy-prd:
    name: Deploy lambda to PRD
    uses: ./.github/workflows/deployment.yaml
    needs:
      - prepare-deployment
    with:
      release: ${{ needs.prepare-deployment.outputs.release-name }}
      previous-runtime: ACC
      runtime: PRD
      previous-runtime-aws-role-arn: ${{ vars.ACC_SCORING_MODELS_AWS_IAM_ROLE_ARN }}
      runtime-aws-role-arn: ${{ vars.PRD_SCORING_MODELS_AWS_IAM_ROLE_ARN }}
