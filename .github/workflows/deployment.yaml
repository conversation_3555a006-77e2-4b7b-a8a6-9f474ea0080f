name: Deployment Workflow


on:
  workflow_call:
    inputs:
      release:
        required: true
        type: string
      runtime:
        required: true
        type: string
      previous-runtime:
        required: false
        type: string
      runtime-aws-role-arn:
        required: true
        type: string
      previous-runtime-aws-role-arn:
        required: false
        type: string
      issue-number:
        required: false
        type: string

env:
  image_url: 510016332031.dkr.ecr.eu-central-1.amazonaws.com/scoring-models
  aws_region: eu-central-1
  lambda_function_name: gresb-${{ inputs.runtime }}-scoring-models

permissions:
  id-token: write # Required for requesting the JWT
  contents: read  # Required for actions/checkout
  pull-requests: write
  issues: write
  checks: write

jobs:

  verify-previous:
    name: Verify Lambda version on ${{ inputs.previous-runtime }}
    if: ${{ inputs.previous-runtime }}
    runs-on: ubuntu-latest
    environment: ${{ inputs.runtime }}
    outputs:
      verify-previous-outcome: ${{ steps.verify-previous.outcome }}
    steps:
      - name: Checkout sources
        uses: actions/checkout@v4
        with:
          fetch-depth: 1
      - name: AWS credentials ${{ inputs.previous-runtime }}
        uses: aws-actions/configure-aws-credentials@v4
        if: ${{ inputs.previous-runtime }}
        with:
          aws-region: ${{ env.aws_region }}
          role-to-assume: ${{ inputs.previous-runtime-aws-role-arn }}
          role-session-name: github-oidc-scoring-models
      - name: Verify ${{ inputs.previous-runtime }} deployment
        if: ${{ inputs.previous-runtime }}
        id: verify-previous
        run: |
          # Convert lambda_function_name to lowercase
          lambda_function_name_lowercase="${{ env.lambda_function_name }}"
          lambda_function_name_lowercase="${lambda_function_name_lowercase,,}"

          # Get the previous runtime version from AWS Lambda
          previous_runtime_version=$(aws --region "${{ env.aws_region }}" lambda get-function --function-name "${lambda_function_name_lowercase}" | jq -r '.Code.ImageUri' | sed -E 's/.+://' | xargs)

          # Compare the runtime versions and set the outcome
          if [[ "${previous_runtime_version}" == "${{ inputs.release }}" ]]; then
              echo "outcome=success" >> "${GITHUB_OUTPUT}"
          else
              echo "outcome=failure" >> "${GITHUB_OUTPUT}"
          fi


  deploy-release:
    name: Deploy lambda to ${{ inputs.runtime }}
    runs-on: ubuntu-latest
    environment: ${{ inputs.runtime }}
    if: ${{ always() }}
    needs:
      - verify-previous
    steps:
      - name: AWS credentials ${{ inputs.runtime }}
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ${{ env.aws_region }}
          role-to-assume: ${{ inputs.runtime-aws-role-arn }}
          role-session-name: github-oidc-scoring-models
      - name: Deploy Lambda
        id: deploy
        run: |
          # Convert lambda_function_name to lowercase
          lambda_function_name_lowercase="${{ env.lambda_function_name }}"
          lambda_function_name_lowercase="${lambda_function_name_lowercase,,}"

          echo "==> Deploying function"
          deployment_json="$(aws --region "${{ env.aws_region }}" lambda update-function-code --function-name "${lambda_function_name_lowercase}" --image-uri "${{ env.image_url }}:${{ inputs.release }}")"
          echo "${deployment_json}"
          aws --region "${{ env.aws_region }}" lambda wait function-updated-v2 --function-name "${lambda_function_name_lowercase}"
          echo "==> Updating function version tag"
          function_arn="$(jq -r '.FunctionArn' <<< "${deployment_json}")"
          aws --region "${{ env.aws_region }}" lambda tag-resource --resource "${function_arn}" --tags "version=${{ inputs.release }}"
      - name: Deployment status comment
        uses: peter-evans/create-or-update-comment@v4
        if: ${{ always() && inputs.issue-number }}
        with:
          issue-number: ${{ inputs.issue-number }}
          body: |
              ### Image Tag: `${{ inputs.release }}`

              **Deployment status to DEV:**
              ${{ steps.deploy.outcome == 'success' && '✅ **Success**' || '❌ **Failure**' }}
